<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.wosai.trade</groupId>
        <artifactId>manage</artifactId>
        <version>1.13.15</version>
    </parent>
    <artifactId>manage-service</artifactId>
    <packaging>jar</packaging>

    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <nextgen.version>2.0-SNAPSHOT</nextgen.version>
        <sales-system-gateway.version>1.0.11</sales-system-gateway.version>
        <sales-system-service.version>3.56.0</sales-system-service.version>
        <merchant-contract.version>1.22.10</merchant-contract.version>
        <hera-toolkit.version>1.5.3</hera-toolkit.version>
        <crm-databus.version>0.7.0</crm-databus.version>
        <withdraw-service.version>1.2.16</withdraw-service.version>
        <period-pay.version>1.0.19</period-pay.version>
        <profit-sharing.version>1.25.62</profit-sharing.version>
        <business-logstash-api.version>1.14.0</business-logstash-api.version>
        <deposit-pay.version>0.2.3</deposit-pay.version>
        <upay-grayscale.version>1.1.18</upay-grayscale.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.wosai.pay</groupId>
            <artifactId>pay-common-sensitive-apollo</artifactId>
            <version>${pay-common.version}</version>
        </dependency>

        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>data-cooperation-api</artifactId>
            <version>1.1.70</version>
            <exclusions>
                <exclusion>
                        <groupId>javax.validation</groupId>
                        <artifactId>validation-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>pay-business-open-api</artifactId>
            <version>1.4.8</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wosai.middleware</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
                <!--context-->
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context</artifactId>
                </exclusion>
                <!--validator-->
                <exclusion>
                    <groupId>com.wosai.pantheon</groupId>
                    <artifactId>wosai-common-validation</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.sales</groupId>
                    <artifactId>sales-system-gateway-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.middleware</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.avro</groupId>
                    <artifactId>avro</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai</groupId>
                    <artifactId>shouqianba-sisyphus-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.nextgen</groupId>
                    <artifactId>data-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.mc</groupId>
                    <artifactId>merchant-center-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.sales</groupId>
                    <artifactId>third-api-spi</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.trade</groupId>
                    <artifactId>manage-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.bankbusiness</groupId>
            <artifactId>bank-business-api</artifactId>
            <version>1.4.4</version>
            <exclusions>
                <exclusion>
                    <groupId>com.baomidou</groupId>
                    <artifactId>mybatis-plus-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.sales</groupId>
                    <artifactId>sales-system-gateway-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--api-->
        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>transaction-report-api</artifactId>
            <version>1.6.9</version>
            <exclusions>
                <exclusion>
                    <artifactId>jsonrpc4j</artifactId>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context</artifactId>
                </exclusion>
                <!--validator-->
                <exclusion>
                    <groupId>com.wosai.pantheon</groupId>
                    <artifactId>wosai-common-validation</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.upay</groupId>
                    <artifactId>upay-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>lark-chatbot-sdk</artifactId>
            <version>0.1.5</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.pay</groupId>
            <artifactId>pay-common-state-manager</artifactId>
            <version>1.2.10</version>
            <exclusions>
                <exclusion>
                    <artifactId>spring-jdbc</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>upay-charging-api</artifactId>
            <version>1.3.14</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.uc</groupId>
            <artifactId>uc-user-api</artifactId>
            <version>1.14.2</version>
        </dependency>
        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>shouqianba-risk-api</artifactId>
            <version>1.2.8</version>
            <exclusions>
                <exclusion>
                    <artifactId>wosai-common</artifactId>
                    <groupId>com.wosai.common</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.ant</groupId>
            <artifactId>ant</artifactId>
            <version>1.10.1</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.nextgen</groupId>
            <artifactId>data-common</artifactId>
            <version>2.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.nextgen</groupId>
            <artifactId>data-jdbc</artifactId>
            <version>2.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>3.5.0</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.bsm</groupId>
            <artifactId>enterprise-api</artifactId>
            <version>1.1-SNAPSHOT</version>
        </dependency>


        <dependency>
            <groupId>com.wosai.sales</groupId>
            <artifactId>sales-system-gateway-api</artifactId>
            <version>${sales-system-gateway.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.datatype</groupId>
                    <artifactId>jackson-datatype-jsr310</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--解决 mac m1 kafka无法消费问题-->
        <dependency>
            <groupId>org.xerial.snappy</groupId>
            <artifactId>snappy-java</artifactId>
            <version>*******</version>
        </dependency>
        <!--api-->
        <dependency>
            <groupId>com.wosai.trade</groupId>
            <artifactId>manage-api</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <!--instrument-->
        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-common-instrumentation</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.ctrip.framework.apollo</groupId>
                    <artifactId>apollo-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai</groupId>
                    <artifactId>wosai-database-instrumentation-springboot</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>wosai-database-instrumentation-springboot</artifactId>
            <version>5.0.2</version>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.11.0</version>
        </dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
            <version>3.25.3</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.12.0</version>
        </dependency>

        <!--apollo-->
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>apollo-client</artifactId>
            <version>2.2.0</version>
        </dependency>
        <!--web rpc-->
        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-common-web-rpc</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.springfox</groupId>
                    <artifactId>springfox-swagger2</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>mysql</groupId>
                    <artifactId>mysql-connector-java</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--test-->
        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-test</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>core-business-api</artifactId>
            <version>${core-business.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.mockito</groupId>
                    <artifactId>mockito-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
            <version>${spring-boot.version}</version>
        </dependency>

        <dependency>
            <groupId>io.javaslang</groupId>
            <artifactId>javaslang</artifactId>
            <version>2.0.6</version>
        </dependency>


        <dependency>
            <groupId>com.shouqianba.workflow</groupId>
            <artifactId>sp-workflow-api</artifactId>
            <version>2.48.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>business-log-api</artifactId>
            <version>2.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.bsm</groupId>
            <artifactId>credit-pay-backend-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.0.5</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
            <version>1.20</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
            <version>1.3.10.RELEASE</version>
        </dependency>


        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
            <version>3.4.2</version>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <version>8.0.33</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>2.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <version>1.5.22.RELEASE</version>
        </dependency>


        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.10</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.12</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.data</groupId>
            <artifactId>crow-api</artifactId>
            <version>0.0.26</version>
        </dependency>


        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>2.0.7</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>2.0.7</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>remit-gateway-api</artifactId>
            <version>0.1.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--请求参数加密用-->
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>signature-proxy-api</artifactId>
            <version>1.0.1-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.marketing.prepaid</groupId>
            <artifactId>marketing-saas-prepaid-card-api</artifactId>
            <version>1.1.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wosai.middleware</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.pantheon</groupId>
                    <artifactId>wosai-common-validation</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.market</groupId>
                    <artifactId>trade-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wosai.market</groupId>
            <artifactId>tethys-api</artifactId>
            <version>1.6.7-SNAPSHOT</version>
            <exclusions>
                <!--JsonRpc4j-->
                <exclusion>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
                <!--context-->
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context</artifactId>
                </exclusion>
                <!--validator-->
                <exclusion>
                    <groupId>com.wosai.pantheon</groupId>
                    <artifactId>wosai-common-validation</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.pantheon</groupId>
                    <artifactId>wosai-common-web-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.jsoniter</groupId>
                    <artifactId>jsoniter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.codehaus.jackson</groupId>
                    <artifactId>jackson-core-asl</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.upay</groupId>
                    <artifactId>upay-activity-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.market</groupId>
                    <artifactId>trade-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.svc.access</groupId>
                    <artifactId>ka-svc-access-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.pub</groupId>
                    <artifactId>ecards-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai</groupId>
                    <artifactId>merchant-dmp-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.upay.upay-prepaid-card</groupId>
                    <artifactId>upay-prepaid-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.market</groupId>
                    <artifactId>merchant-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.market</groupId>
            <artifactId>merchant-api</artifactId>
            <version>2.11.0-SNAPSHOT</version>

            <exclusions>
                <!--JsonRpc4j-->
                <exclusion>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
                <!--context-->
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context</artifactId>
                </exclusion>
                <!--validator-->
                <exclusion>
                    <groupId>com.wosai.pantheon</groupId>
                    <artifactId>wosai-common-validation</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.upay</groupId>
                    <artifactId>upay-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.upay</groupId>
                    <artifactId>upay-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-lang</groupId>
                    <artifactId>commons-lang</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.pantheon</groupId>
                    <artifactId>uitem-core-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.market</groupId>
                    <artifactId>painter-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>aop-gateway-api</artifactId>
            <version>1.3.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>user-service-api</artifactId>
            <version>1.2.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wosai.upay</groupId>
                    <artifactId>upay-common</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>jsonrpc4j</artifactId>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wosai.app</groupId>
            <artifactId>merchant-user-api</artifactId>
            <version>1.10.12</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wosai.upay</groupId>
                    <artifactId>upay-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.middleware</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.pantheon</groupId>
                    <artifactId>wosai-common-web-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>shouqianba-withdraw-service-api</artifactId>
            <version>${withdraw-service.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>upay-common</artifactId>
                    <groupId>com.wosai.upay</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>wosai-common-databus-api</artifactId>
                    <groupId>com.wosai.pantheon</groupId>
                </exclusion>
            </exclusions>

        </dependency>


        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>shouqianba-terminal-service-api</artifactId>
            <version>1.0.8-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>wosai-common-databus</artifactId>
                    <groupId>com.wosai.pantheon</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai</groupId>
                    <artifactId>wosai-brave153-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.sales</groupId>
                    <artifactId>sales-system-common</artifactId>
                </exclusion>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-common-databus</artifactId>
            <version>1.1.8</version>
            <exclusions>
                <exclusion>
                    <artifactId>wosai-common-databus-api</artifactId>
                    <groupId>com.wosai.pantheon</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai</groupId>
                    <artifactId>wosai-brave153-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-common-databus-api</artifactId>
            <version>1.0.2</version>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>2.10.6</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.sales</groupId>
            <artifactId>sales-system-service-api</artifactId>
            <version>${sales-system-service.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>    
            <groupId>com.wosai.pantheon</groupId>   
            <artifactId>jjz-databus</artifactId>    
            <version>1.0.42-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>6.4</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>logging-api</artifactId>
            <version>1.7.0</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.sales</groupId>
            <artifactId>customer-relation-api</artifactId>
            <version>1.0.7-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
            <version>3.37.0</version>
        </dependency>
        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>face-recognition-service-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wosai.middleware</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.upay</groupId>
                    <artifactId>upay-common</artifactId>
                </exclusion>
                 <exclusion>
                      <groupId>org.springframework</groupId>
                      <artifactId>spring-context</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate.validator</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>hera-toolkit-metrics</artifactId>
            <version>${hera-toolkit.version}</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>hera-toolkit-trace</artifactId>
            <version>${hera-toolkit.version}</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>hera-toolkit-logback-1.x</artifactId>
            <version>${hera-toolkit.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
            <version>2.9.6</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>upay-transaction-api</artifactId>
            <version>1.8.6-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>upay-wallet-api</artifactId>
            <version>1.2.59</version>
        </dependency>
        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>upay-grayscale-api</artifactId>
            <version>${upay-grayscale.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>jsonrpc4j</artifactId>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jsonrpc4j</artifactId>
                    <groupId>com.wosai.middleware</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>clearance-service-api</artifactId>
            <version>1.4.88</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.deposit.pay</groupId>
            <artifactId>deposit-pay-api</artifactId>
            <version>${deposit-pay.version}</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>merchant-contract-job-api</artifactId>
            <version>4.25.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wosai.middleware</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.trade</groupId>
                    <artifactId>manage-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-web</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.shouqianba</groupId>
                    <artifactId>cua-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>merchant-contract-activity-api</artifactId>
            <version>${merchant-contract.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>sales-system-databus</artifactId>
                    <groupId>com.wosai</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.upay</groupId>
                    <artifactId>merchant-contract-job-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
           <groupId>com.ctrip.framework.apollo</groupId>
           <artifactId>apollo-openapi</artifactId>
           <version>1.7.0</version>
        </dependency>
        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>mpay-sdk-homebrew</artifactId>
            <version>1.88-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>wosai-common</artifactId>
                    <groupId>com.wosai.common</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mis</artifactId>
                    <groupId>com.ccb</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.ccb</groupId>
                    <artifactId>ccbpay-api-java</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>okhttp</artifactId>
                    <groupId>com.squareup.okhttp3</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>upay-side-api</artifactId>
            <version>1.0.4</version>
        </dependency>
        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>profit-sharing-api</artifactId>
            <version>${profit-sharing.version}</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>scene-api</artifactId>
            <version>1.2.7</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context</artifactId>
                </exclusion>
            </exclusions>

        </dependency>
        <dependency>
            <groupId>com.wosai.sales</groupId>
            <artifactId>crm-databus</artifactId>
            <version>${crm-databus.version}</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.period.pay</groupId>
            <artifactId>period-pay-api</artifactId>
            <version>${period-pay.version}</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.sp</groupId>
            <artifactId>business-logstash-api</artifactId>
            <version>${business-logstash-api.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>mybatis-plus-extension</artifactId>
                    <groupId>com.baomidou</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis-plus-core</artifactId>
                    <groupId>com.baomidou</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.shouqianba</groupId>
            <artifactId>cua-common</artifactId>
            <version>0.3.28</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wosai.common</groupId>
                    <artifactId>wosai-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.shouqianba</groupId>
            <artifactId>merchant-contract-access-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.cua</groupId>
            <artifactId>kafka-msg-entity</artifactId>
        </dependency>

    </dependencies>

    <build>
        <finalName>manage-service</finalName>
        <plugins>
            <!--maven install|deploy时，跳过本模块-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-install-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.1.17.RELEASE</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.2</version>
                <configuration>
                    <verbose>true</verbose>
                    <overwrite>true</overwrite>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                        <version>5.1.46</version>
                    </dependency>
                </dependencies>
            </plugin>

            <plugin>
                <groupId>com.wosai.middleware</groupId>
                <artifactId>wosai-logging-maven-plugin</artifactId>
                <version>1.7.0</version>
                <configuration>
                    <enableCallerData>true</enableCallerData>
                    <patternLayout>%d{yyyy-MM-dd HH:mm:ss.SSS} %contextName [%thread] [%tid] [%X{trace_id}] %-5level %logger{36}.%M - %msg%n
                    </patternLayout>
                    <profiles>
                        <profile>
                            <name>prod</name>
                            <level>INFO</level>
                            <references>
                                <ref>FT_CONSOLE_JSON</ref> <!-- 输出到标准输出，格式是JSON -->
                            </references>
                        </profile>
                        <profile>
                            <name>beta</name>
                            <level>INFO</level>
                            <references>
                                <ref>FT_FILE_PATTERN</ref> <!-- 输出到文件，格式是格式化字符串 -->
                                <ref>FT_CONSOLE_PATTERN</ref> <!-- 输出到标准输出，格式是格式化字符串 -->
                            </references>
                        </profile>
                        <profile>
                            <name>default</name>   <!-- 在本地开发调试时，在IDE中设置 active profile为default -->
                            <level>DEBUG</level>
                            <references>
                                <ref>FT_CONSOLE_PATTERN</ref> <!-- 输出到标准输出，格式是格式化字符串 (1.2.0新增功能) -->
                            </references>
                        </profile>
                    </profiles>
                    <scopes>
                        <scope>
                            <name>com.wosai.trade</name>
                            <level>DEBUG</level>
                        </scope>
                        <scope>
                            <name>com.wosai.trade.repository.dao</name>
                            <level>WARN</level>
                        </scope>
                        <scope>
                            <name>org.mybatis.spring</name>
                            <level>WARN</level>
                        </scope>
                        <scope>
                            <name>org.springframework.jdbc.datasource.DataSourceUtils</name>
                            <level>WARN</level>
                        </scope>
                    </scopes>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>generate-logback-spring</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>


</project>

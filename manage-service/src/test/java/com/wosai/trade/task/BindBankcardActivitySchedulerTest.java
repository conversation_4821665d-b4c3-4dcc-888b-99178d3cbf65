package com.wosai.trade.task;

import com.wosai.trade.repository.dao.entity.TradeActivityEntity;
import com.wosai.trade.service.TradeActivityService;
import com.wosai.trade.service.result.TradeActivityResult;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;


@RunWith(SpringRunner.class)
@SpringBootTest
public class BindBankcardActivitySchedulerTest {
    @Resource
    private BindBankcardActivityScheduler bindBankcardActivityScheduler;
    @Resource
    private TradeActivityService tradeActivityService;

//    public static final String MERCHANT_SN = "mch-*************";
    public static final String MERCHANT_SN = "**************";


    @Test
    public void finish() {
        TradeActivityResult result = tradeActivityService.getEnableActivityByMerchantSnAndType(MERCHANT_SN, TradeActivityEntity.ACTIVITY_BIND_BANKCARD);
        bindBankcardActivityScheduler.process(result);
    }

    /**
     * 绑卡活动-活动到期 通知
     */
    @Test
    public void notifyBindBankActivityExpiring() {
        bindBankcardActivityScheduler.notifyBindBankActivityExpiring(MERCHANT_SN);
    }

    @Test
    public void notifyBindBankActivityAssessFailing() {
        bindBankcardActivityScheduler.notifyBindBankActivityAssessFailing(MERCHANT_SN, 2000L);
    }
}
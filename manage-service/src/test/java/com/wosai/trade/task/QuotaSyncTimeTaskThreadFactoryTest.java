package com.wosai.trade.task;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.modules.junit4.PowerMockRunner;


/**
 * <AUTHOR> Date: 2020/7/7 Time: 3:18 下午
 */
@RunWith(PowerMockRunner.class)
public class QuotaSyncTimeTaskThreadFactoryTest {

    @InjectMocks
    private QuotaSyncTimeTaskThreadFactory quotaSyncTimeTaskThreadFactory;

    @Test
    public void newThread() {
        Thread thread = quotaSyncTimeTaskThreadFactory.newThread(() ->{});
        Assert.assertNotNull("线程创建失败", thread);
        Assert.assertEquals("线程创建失败", thread.getName(), "Quota-Sync-Task-0");
    }
}
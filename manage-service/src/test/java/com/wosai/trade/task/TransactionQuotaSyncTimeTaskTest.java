package com.wosai.trade.task;

import com.wosai.trade.biz.quota.BaseQuotaTest;
import com.wosai.trade.client.CoreBusinessClient;
import com.wosai.trade.client.LarkClient;
import com.wosai.trade.config.apollo.CommonConfig;
import com.wosai.trade.model.dal.TransactionQuotaSummaryDalDO;
import com.wosai.trade.repository.TransactionQuotaDetailRepository;
import com.wosai.trade.repository.TransactionQuotaSummaryRepository;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionTemplate;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;



/**
 * <AUTHOR> Date: 2020/7/7 Time: 3:29 下午
 */
@RunWith(PowerMockRunner.class)
public class TransactionQuotaSyncTimeTaskTest extends BaseQuotaTest {

    @InjectMocks
    private TransactionQuotaSyncTimeTask transactionQuotaSyncTimeTask;
    @Mock
    private TransactionQuotaDetailRepository detailRepository;
    @Mock
    private TransactionQuotaSummaryRepository summaryRepository;
    @Mock
    private TransactionTemplate transactionTemplate;
    @Mock
    private CoreBusinessClient coreBusinessClient;
    @Mock
    private LarkClient larkClient;
    @Mock
    private CommonConfig commonConfig;
    @Mock
    private String larkUrl;
    @Mock
    private String env;

    @Before
    public void setUp() throws Exception {

        Class<TransactionQuotaSyncTimeTask> transactionQuotaSyncTimeTaskClass
                = (Class<TransactionQuotaSyncTimeTask>) transactionQuotaSyncTimeTask.getClass();
        Field field = transactionQuotaSyncTimeTaskClass.getDeclaredField("transactionTemplate");
        field.setAccessible(true);
        field.set(transactionQuotaSyncTimeTask, new TransactionTemplate(){{
            setTransactionManager(new PlatformTransactionManager() {
                @Override
                public TransactionStatus getTransaction(TransactionDefinition definition) throws TransactionException {
                    return null;
                }

                @Override
                public void commit(TransactionStatus status) throws TransactionException {

                }

                @Override
                public void rollback(TransactionStatus status) throws TransactionException {

                }
            });
        }});
    }

    @Test
    public void process() {
        transactionQuotaSyncTimeTask.process();

        List<TransactionQuotaSummaryDalDO> transactionQuotaSummaryDalDOList = new ArrayList<TransactionQuotaSummaryDalDO>() {{
            add(TransactionQuotaSummaryDalDO.builder()
                    .id(1L).merchantSn("mch-123").merchantId("123")
                    .fixedQuota(8000L).temporaryQuota(2000L).totalQuota(10000L)
                    .nextComputeDate(LocalDate.now()).ext("")
                    .ctime(LocalDateTime.now()).mtime(LocalDateTime.now()).version(1L)
                    .build());
        }};
        PowerMockito.when(summaryRepository.list(Mockito.any()))
                .thenReturn(transactionQuotaSummaryDalDOList, new ArrayList<>());
        PowerMockito.when(detailRepository.list(Mockito.any()))
                .thenReturn(buildTransactionQuotaDetailDalDOList());
        transactionQuotaSyncTimeTask.process();

        PowerMockito.when(summaryRepository.list(Mockito.any()))
                .thenReturn(transactionQuotaSummaryDalDOList, new ArrayList<>());
        PowerMockito.when(summaryRepository.updateWithOptimisticLock(Mockito.any()))
                .thenReturn(1);
        transactionQuotaSyncTimeTask.process();
    }
}
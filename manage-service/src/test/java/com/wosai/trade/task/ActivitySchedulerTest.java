package com.wosai.trade.task;

import com.wosai.trade.BaseTest;
import com.wosai.trade.config.ActivityMonitorNoticeConfig;
import com.wosai.trade.impl.TradeCommonService;
import org.junit.Test;

import javax.annotation.Resource;

public class ActivitySchedulerTest extends BaseTest {

    @Resource
    private ActivityMonitorNoticeConfig config;
    @Resource
    private TradeCommonService tradeCommonService;

    @Test
    public void offlineTask() {
    }

    @Test
    public void prepOfflineTask() {
        System.out.println(config);
    }
}
package com.wosai.trade.repository.converter;

import com.wosai.trade.model.dal.TransactionQuotaDetailDalDO;
import com.wosai.trade.repository.dao.entity.TransactionQuotaDetailEntity;
import com.wosai.trade.service.enums.QuotaDetailStatusEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.modules.junit4.PowerMockRunner;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR> Date: 2020/7/7 Time: 10:53 上午
 */
@RunWith(PowerMockRunner.class)
public class TransactionQuotaDetailDalDOConverterTest {


    @Test
    public void convert() {
        TransactionQuotaDetailEntity entity = buildEntity();
        TransactionQuotaDetailDalDO detailDalDO = TransactionQuotaDetailDalDOConverter.convert(entity);
        Assert.assertNotNull("转换失败", detailDalDO);
        Assert.assertEquals("转换失败", entity.getId(), detailDalDO.getId());
        Assert.assertEquals("转换失败", entity.getMerchantSn(), detailDalDO.getMerchantSn());
        Assert.assertEquals("转换失败", entity.getQuota(), detailDalDO.getQuota());
        Assert.assertEquals("转换失败", entity.getType(), detailDalDO.getType().getCode());
        Assert.assertEquals("转换失败", entity.getSubType(), detailDalDO.getSubType());
        Assert.assertEquals("转换失败", entity.getBeginDate(), detailDalDO.getBeginDate());
        Assert.assertEquals("转换失败", entity.getEndDate(), detailDalDO.getEndDate());
        Assert.assertEquals("转换失败", entity.getStatus(), detailDalDO.getStatus().getCode());
        Assert.assertEquals("转换失败", entity.getCtime(), detailDalDO.getCtime());
        Assert.assertEquals("转换失败", entity.getMtime(), detailDalDO.getMtime());
    }

    @Test
    public void testConvert() {
        TransactionQuotaDetailEntity entity = buildEntity();
        List<TransactionQuotaDetailEntity> entities = new ArrayList<>();
        entities.add(entity);
        List<TransactionQuotaDetailDalDO> detailDalDOS = TransactionQuotaDetailDalDOConverter.convert(entities);
        Assert.assertTrue("转换失败", CollectionUtils.isNotEmpty(detailDalDOS));
        TransactionQuotaDetailDalDO detailDalDO = detailDalDOS.get(0);
        Assert.assertEquals("转换失败", entity.getId(), detailDalDO.getId());
        Assert.assertEquals("转换失败", entity.getMerchantSn(), detailDalDO.getMerchantSn());
        Assert.assertEquals("转换失败", entity.getQuota(), detailDalDO.getQuota());
        Assert.assertEquals("转换失败", entity.getType(), detailDalDO.getType().getCode());
        Assert.assertEquals("转换失败", entity.getSubType(), detailDalDO.getSubType());
        Assert.assertEquals("转换失败", entity.getBeginDate(), detailDalDO.getBeginDate());
        Assert.assertEquals("转换失败", entity.getEndDate(), detailDalDO.getEndDate());
        Assert.assertEquals("转换失败", entity.getStatus(), detailDalDO.getStatus().getCode());
        Assert.assertEquals("转换失败", entity.getCtime(), detailDalDO.getCtime());
        Assert.assertEquals("转换失败", entity.getMtime(), detailDalDO.getMtime());
    }

    private TransactionQuotaDetailEntity buildEntity() {
        TransactionQuotaDetailEntity entity = new TransactionQuotaDetailEntity();
        entity.setId(1L);
        entity.setMerchantSn("mch-123");
        entity.setQuota(10000L);
        entity.setType(Byte.parseByte("1"));
        entity.setSubType(Byte.parseByte("2"));
        entity.setBeginDate(LocalDate.now());
        entity.setEndDate(LocalDate.now());
        entity.setStatus(QuotaDetailStatusEnum.EFFECTIVE.getCode());
        entity.setCtime(LocalDateTime.now());
        entity.setMtime(LocalDateTime.now());
        return entity;
    }
}
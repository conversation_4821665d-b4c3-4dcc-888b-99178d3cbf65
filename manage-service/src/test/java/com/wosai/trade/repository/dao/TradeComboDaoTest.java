package com.wosai.trade.repository.dao;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.wosai.trade.model.dal.TradeComboCountDalParam;
import com.wosai.trade.model.dal.TradeComboQueryDalParam;
import com.wosai.trade.model.dal.TradeComboQueryDalResult;
import com.wosai.trade.model.dal.TradeComboUpsertDalParam;
import com.wosai.trade.repository.dao.entity.TradeComboEntity;
import junit.framework.TestCase;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

@PowerMockIgnore({"javax.management.*"})
@RunWith(PowerMockRunner.class)
public class TradeComboDaoTest {

    private static final List<TradeComboQueryDalResult> ENTITY_LIST = JSON.parseObject("[{\"ctime\":1611905950000,\"description\":\"2cfbda6d\",\"id\":2018,\"name\":\"05bdcde4\",\"shortName\":\"a65e90d0\",\"status\":1,\"tradeAppId\":1},{\"ctime\":1611819048000,\"description\":\"扫码点单费率活动中\",\"id\":2017,\"name\":\"扫码点单0费率套餐\",\"shortName\":\"扫码点单费率活动\",\"status\":1,\"tradeAppId\":1331}]", new TypeReference<ArrayList<TradeComboQueryDalResult>>() {
    });

    private static final TradeComboEntity TRADE_COMBO_ENTITY = JSON.parseObject("{\"comboConfigLevel\":\"merchant\",\"ctime\":1611905950000,\"description\":\"2cfbda6d\",\"expirationRuleExtra\":\"{\\\"is_long_term_effective\\\":1}\",\"id\":2018,\"markLabelExtra\":\"{}\",\"mutexTradeCombo\":\"null\",\"name\":\"05bdcde4\",\"shortName\":\"a65e90d0\",\"status\":1,\"subPaywayFlag\":1,\"tags\":\"[\\\"测试\\\"]\",\"tradeAppId\":1}", TradeComboEntity.class);

    private static final List<TradeComboEntity> TRADE_COMBO_ENTITY_LIST = JSON.parseObject("[{\"comboConfigLevel\":\"merchant\",\"ctime\":1611819048000,\"description\":\"扫码点单费率活动中\",\"expirationRuleExtra\":\"{\\\"is_long_term_effective\\\":1}\",\"id\":2017,\"markLabelExtra\":\"{}\",\"mutexTradeCombo\":\"[]\",\"name\":\"扫码点单0费率套餐\",\"shortName\":\"扫码点单费率活动\",\"status\":1,\"subPaywayFlag\":0,\"tags\":\"[\\\"\\\"]\",\"tradeAppId\":1331},{\"comboConfigLevel\":\"merchant\",\"ctime\":1611905950000,\"description\":\"2cfbda6d\",\"expirationRuleExtra\":\"{\\\"is_long_term_effective\\\":1}\",\"id\":2018,\"markLabelExtra\":\"{}\",\"mutexTradeCombo\":\"null\",\"name\":\"05bdcde4\",\"shortName\":\"a65e90d0\",\"status\":1,\"subPaywayFlag\":1,\"tags\":\"[\\\"测试\\\"]\",\"tradeAppId\":1}]", new TypeReference<ArrayList<TradeComboEntity>>() {
    });

    @Mock
    private TradeComboDao tradeComboDao;

    @Before
    public void setUp() {
        PowerMockito.when(tradeComboDao.count(Mockito.any(TradeComboCountDalParam.class)))
                .thenReturn(1);

        PowerMockito.when(tradeComboDao.insert(Mockito.any(TradeComboUpsertDalParam.class)))
                .thenReturn(1);

        PowerMockito.when(tradeComboDao.updateById(Mockito.any(TradeComboUpsertDalParam.class)))
                .thenReturn(1);

        PowerMockito.when(tradeComboDao.selectList(Mockito.any(TradeComboQueryDalParam.class)))
                .thenReturn(ENTITY_LIST);

        PowerMockito.when(tradeComboDao.selectById(Mockito.anyLong()))
                .thenReturn(TRADE_COMBO_ENTITY);

        PowerMockito.when(tradeComboDao.selectByIds(Mockito.anyList()))
                .thenReturn(TRADE_COMBO_ENTITY_LIST);
    }

    @Test
    public void count() {
        TradeComboCountDalParam countDalParam = new TradeComboCountDalParam();
        int counter = tradeComboDao.count(countDalParam);
        TestCase.assertEquals("单元测试失败", 1, counter);
    }

    @Test
    public void insert() {
        TradeComboUpsertDalParam upsertDalParam = TradeComboUpsertDalParam.builder().build();
        int counter = tradeComboDao.insert(upsertDalParam);
        TestCase.assertEquals("单元测试失败", 1, counter);
    }

    @Test
    public void updateById() {
        TradeComboUpsertDalParam upsertDalParam = TradeComboUpsertDalParam.builder().build();
        int counter = tradeComboDao.updateById(upsertDalParam);
        TestCase.assertEquals("单元测试失败", 1, counter);
    }

    @Test
    public void selectList() {
        TradeComboQueryDalParam queryDalParam = TradeComboQueryDalParam.builder().build();
        List<TradeComboQueryDalResult> result = tradeComboDao.selectList(queryDalParam);
        TestCase.assertEquals("单元测试失败", JSON.toJSONString(ENTITY_LIST), JSON.toJSONString(result));
    }

    @Test
    public void selectById() {
        TradeComboEntity result = tradeComboDao.selectById(666L);
        TestCase.assertEquals("单元测试失败", JSON.toJSONString(TRADE_COMBO_ENTITY), JSON.toJSONString(result));
    }

    @Test
    public void selectByIds() {
        List<TradeComboEntity> result = tradeComboDao.selectByIds(Lists.newArrayList(1L, 2L, 3L));
        TestCase.assertEquals("单元测试失败", JSON.toJSONString(TRADE_COMBO_ENTITY_LIST), JSON.toJSONString(result));
    }

}

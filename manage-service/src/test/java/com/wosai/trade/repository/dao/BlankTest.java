package com.wosai.trade.repository.dao;

import com.google.common.collect.ImmutableList;
import com.wosai.trade.BaseTest;
import com.wosai.trade.model.dal.MerchantFeeRateUpsertDalParam;
import com.wosai.trade.repository.dao.entity.ActivityApplyEntity;
import com.wosai.trade.repository.dao.entity.MerchantFeeRateEntity;
import com.wosai.trade.repository.dao.entity.QuotaActivityApplyEntity;
import com.wosai.trade.util.DateTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 空白测试
 */
@Slf4j
public class BlankTest extends BaseTest {

    @Resource
    private ActivityApplyDOMapper activityApplyDOMapper;
    @Resource
    private QuotaActivityApplyEntityMapper quotaActivityApplyEntityMapper;
    @Resource
    private MerchantFeeRateDao merchantFeeRateDao;

    @Test
    public void blankTest() {
        String blankName1 = "xxx "; //半角
        String blankName2 = "xxx　"; //全角
        testActivityApply(blankName1);
        testActivityApply(blankName2);

        testQuotaApply(blankName1);
        testQuotaApply(blankName2);

        testMerchantFeeRate(blankName1);
        testMerchantFeeRate(blankName2);

        testMerchantFeeRateBatch(blankName1);
        testMerchantFeeRateBatch(blankName2);
    }

    private void testActivityApply(String merchantSn) {
        ActivityApplyEntity record = new ActivityApplyEntity();
        record.setMerchant_sn(merchantSn);
        record.setActivity_id(5L);
        record.setBiz_id(6L);
        record.setActivity_name("blank tst");
        record.setMerchant_id("111-1111");
        record.setSn(merchantSn);
        int rows = activityApplyDOMapper.insertSelective(record);
        Assert.isTrue(rows == 1, "testActivityApply inset error.");
        record = activityApplyDOMapper.selectByPrimaryKey(record.getId());
        Assert.isTrue(StringUtils.normalizeSpace(merchantSn).equals(record.getMerchant_sn()), "testActivityApply replace error.");
        activityApplyDOMapper.deleteByPrimaryKey(record.getId());
        log.info("testActivityApply merchantSn:['{}'->'{}'], sn:['{}'->'{}']", merchantSn, record.getMerchant_sn(),merchantSn, record.getSn());
    }

    private void testQuotaApply(String merchantSn) {
        QuotaActivityApplyEntity record = new QuotaActivityApplyEntity();
        record.setMerchant_sn(merchantSn);
        record.setActivity_id(5L);
        record.setBiz_id(6L);
        record.setActivity_name("blank tst");
        record.setMerchant_id("111-1111");
        record.setSn(merchantSn);
        int rows = quotaActivityApplyEntityMapper.insertSelective(record);
        Assert.isTrue(rows == 1, "testQuotaApply insert error.");
        record = quotaActivityApplyEntityMapper.selectByPrimaryKey(record.getId());
        Assert.isTrue(StringUtils.normalizeSpace(merchantSn).equals(record.getMerchant_sn()), "testQuotaApply replace error.");
        activityApplyDOMapper.deleteByPrimaryKey(record.getId());
        log.info("testQuotaApply merchantSn:['{}'->'{}'], sn:['{}'->'{}']", merchantSn, record.getMerchant_sn(),merchantSn, record.getSn());
    }

    private void testMerchantFeeRate(String merchantSn) {
        MerchantFeeRateUpsertDalParam record = MerchantFeeRateUpsertDalParam.builder()
                .merchantSn(merchantSn)
                .tradeComboId(5L)
                .appId(6L)
                .payWay(2)
                .beginDate(DateTimeUtils.getFormatDateByDate(new Date(), DateTimeUtils.format))
                .endDate(DateTimeUtils.getFormatDateByDate(new Date(), DateTimeUtils.format))
                .b2cInUse(1)
                .c2bInUse(1)
                .appInUse(0)
                .miniInUse(1)
                .h5InUse(0)
                .wapInUse(1)
                .feeRateType("FIXED")
                .feeRate("0.2")
                .status(1)
                .build();
        int rows = merchantFeeRateDao.insert(record);
        Assert.isTrue(rows == 1, "testMerchantFeeRate insert error.");
        MerchantFeeRateEntity feeRateDo = merchantFeeRateDao.selectById(record.getId());
        Assert.isTrue(StringUtils.normalizeSpace(merchantSn).equals(feeRateDo.getMerchantSn()), "testMerchantFeeRate replace error.");
        activityApplyDOMapper.deleteByPrimaryKey(record.getId());
        log.info("testMerchantFeeRate ['{}'->'{}']", merchantSn, record.getMerchantSn());
    }

    private void testMerchantFeeRateBatch(String merchantSn) {
        MerchantFeeRateUpsertDalParam record = MerchantFeeRateUpsertDalParam.builder()
                .merchantSn(merchantSn)
                .tradeComboId(5L)
                .appId(6L)
                .payWay(2)
                .beginDate(DateTimeUtils.getFormatDateByDate(new Date(), DateTimeUtils.format))
                .endDate(DateTimeUtils.getFormatDateByDate(new Date(), DateTimeUtils.format))
                .b2cInUse(1)
                .c2bInUse(1)
                .appInUse(0)
                .miniInUse(1)
                .h5InUse(0)
                .wapInUse(1)
                .feeRateType("FIXED")
                .feeRate("0.2")
                .status(1)
                .build();
        int rows = merchantFeeRateDao.batchInsert(ImmutableList.of(record));
        Assert.isTrue(rows == 1, "testMerchantFeeRateBatch insert error.");
        MerchantFeeRateEntity feeRateDo = merchantFeeRateDao.selectById(record.getId());
        Assert.isTrue(StringUtils.normalizeSpace(merchantSn).equals(feeRateDo.getMerchantSn()), "testMerchantFeeRateBatch replace error.");
        activityApplyDOMapper.deleteByPrimaryKey(record.getId());
        log.info("testMerchantFeeRateBatch ['{}'->'{}']", merchantSn, record.getMerchantSn());
    }
}
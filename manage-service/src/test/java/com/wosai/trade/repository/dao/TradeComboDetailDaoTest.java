package com.wosai.trade.repository.dao;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.wosai.trade.model.dal.TradeComboDetailUpsertDalParam;
import com.wosai.trade.repository.dao.entity.TradeComboDetailEntity;
import junit.framework.TestCase;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

@PowerMockIgnore({"javax.management.*"})
@RunWith(PowerMockRunner.class)
public class TradeComboDetailDaoTest {

    private static final TradeComboDetailEntity ENTITY = JSON.parseObject("{\"comboId\":1,\"ctime\":1611737169000,\"extendInfo\":{\"a\":1,\"name\":\"name!\"},\"feeRateMax\":\"100\",\"feeRateMin\":\"0.1\",\"id\":1078,\"ladderFeeRates\":\"[{\\\\\\\"min\\\\\\\":0,\\\\\\\"max\\\\\\\":100,\\\\\\\"fee_rate\\\\\\\":\\\\\\\"0.1\\\\\\\"},{\\\\\\\"min\\\\\\\":100,\\\\\\\"fee_rate\\\\\\\":\\\\\\\"0.3\\\\\\\"}]\",\"mtime\":1611737169000,\"payway\":1,\"subPaywayStatus\":\"{\\\\\\\"b2c_status\\\\\\\":1,\\\\\\\"c2b_status\\\\\\\":1,\\\\\\\"wap_status\\\\\\\":1,\\\\\\\"mini_status\\\\\\\":1,\\\\\\\"h5_status\\\\\\\":1,\\\\\\\"app_status\\\\\\\":1}\"}", TradeComboDetailEntity.class);

    @Mock
    private TradeComboDetailDao tradeComboDetailDao;

    @Before
    public void setUp() {
        PowerMockito.when(tradeComboDetailDao.batchInsert(Mockito.anyList()))
                .thenReturn(1);

        PowerMockito.when(tradeComboDetailDao.updateById(Mockito.any(TradeComboDetailUpsertDalParam.class)))
                .thenReturn(1);

        PowerMockito.when(tradeComboDetailDao.selectById(Mockito.anyLong()))
                .thenReturn(ENTITY);

    }

    @Test
    public void batchInsert() {
        TradeComboDetailUpsertDalParam insertDalParam = TradeComboDetailUpsertDalParam.builder().build();
        int result = tradeComboDetailDao.batchInsert(Lists.newArrayList(insertDalParam));
        TestCase.assertEquals("单元测试失败", 1, result);
    }

    @Test
    public void updateById() {
        TradeComboDetailUpsertDalParam updateDalParam = TradeComboDetailUpsertDalParam.builder().build();
        int result = tradeComboDetailDao.updateById(updateDalParam);
        TestCase.assertEquals("单元测试失败", 1, result);
    }

    @Test
    public void selectById() {
        TradeComboDetailEntity tradeComboDetailEntity = tradeComboDetailDao.selectById(1007L);
        TestCase.assertEquals("单元测试失败", JSON.toJSONString(ENTITY), JSON.toJSONString(tradeComboDetailEntity));
    }

}

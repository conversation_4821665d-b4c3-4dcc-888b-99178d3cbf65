package com.wosai.trade.repository.dao;

import com.alibaba.fastjson.JSON;
import com.wosai.trade.model.dal.MchFeeRateApplyLogQueryDalParam;
import com.wosai.trade.model.dal.MchFeeRateApplyLogUpsertDalParam;
import com.wosai.trade.repository.dao.entity.MerchantFeeRateApplyLogEntity;
import junit.framework.TestCase;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.List;

@PowerMockIgnore({"javax.management.*"})
@RunWith(PowerMockRunner.class)
public class MerchantFeeRateApplyLogDaoTest {

    private static final List<MerchantFeeRateApplyLogEntity> ENTITY_LIST = JSON.parseArray("[{\"auditSn\":\"audi_sn-10001\",\"beginDate\":1577808000000,\"createAt\":1611313674000,\"description\":\"礼貌性描述一下\",\"endDate\":1640880000000,\"feeRate\":\"0.1\",\"feeRateType\":\"fixed\",\"id\":1,\"merchantSn\":\"mch-1680001369148\",\"payWay\":2,\"tradeAppName\":\"支付业务\",\"tradeComboShortName\":\"套餐简称1\"},{\"auditSn\":\"audi_sn-10012\",\"beginDate\":1580486400000,\"createAt\":1611313674000,\"description\":\"礼貌性描述一下\",\"endDate\":1640880000000,\"feeRate\":\"[{\\\\\\\"min\\\\\\\":0,\\\\\\\"max\\\\\\\":100,\\\\\\\"fee_rate\\\\\\\":\\\\\\\"0.1\\\\\\\"},{\\\\\\\"min\\\\\\\":100,\\\\\\\"fee_rate\\\\\\\":\\\\\\\"0.38\\\\\\\"}]\",\"feeRateType\":\"ladder\",\"id\":2,\"merchantSn\":\"mch-1680001369148\",\"payWay\":3,\"tradeAppName\":\"扫码点单\",\"tradeComboShortName\":\"套餐简称2\"}]", MerchantFeeRateApplyLogEntity.class);

    @Mock
    private MerchantFeeRateApplyLogDao merchantFeeRateApplyLogDao;

    @Before
    public void setUp() {
        PowerMockito.when(merchantFeeRateApplyLogDao.insert(Mockito.any(MchFeeRateApplyLogUpsertDalParam.class)))
                .thenReturn(1);

        PowerMockito.when(merchantFeeRateApplyLogDao.selectList(Mockito.any(MchFeeRateApplyLogQueryDalParam.class)))
                .thenReturn(ENTITY_LIST);
    }

    @Test
    public void insert() {
        MchFeeRateApplyLogUpsertDalParam insertDalParam = MchFeeRateApplyLogUpsertDalParam.builder().build();
        int result = merchantFeeRateApplyLogDao.insert(insertDalParam);
        TestCase.assertEquals("单元测试失败", 1, result);
    }

    @Test
    public void selectList() {
        MchFeeRateApplyLogQueryDalParam queryDalParam = MchFeeRateApplyLogQueryDalParam.builder()
                .build();
        List<MerchantFeeRateApplyLogEntity> result = merchantFeeRateApplyLogDao.selectList(queryDalParam);
        TestCase.assertEquals("单元测试失败", JSON.toJSONString(ENTITY_LIST), JSON.toJSONString(result));
    }

}

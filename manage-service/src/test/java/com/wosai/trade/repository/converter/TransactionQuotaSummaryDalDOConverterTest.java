package com.wosai.trade.repository.converter;

import com.wosai.trade.model.dal.TransactionQuotaSummaryDalDO;
import com.wosai.trade.repository.dao.entity.TransactionQuotaSummaryEntity;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.modules.junit4.PowerMockRunner;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;

/**
 * <AUTHOR> Date: 2020/7/7 Time: 11:21 上午
 */
@RunWith(PowerMockRunner.class)
public class TransactionQuotaSummaryDalDOConverterTest {

    @Test
    public void convert() {
        TransactionQuotaSummaryEntity entity = buildEntity();
        TransactionQuotaSummaryDalDO summaryDalDO = TransactionQuotaSummaryDalDOConverter.convert(entity);
        Assert.assertNotNull("转换失败", summaryDalDO);
        Assert.assertEquals("转换失败", entity.getId(), summaryDalDO.getId());
        Assert.assertEquals("转换失败", entity.getMerchantSn(), summaryDalDO.getMerchantSn());
        Assert.assertEquals("转换失败", entity.getMerchantId(), summaryDalDO.getMerchantId());
        Assert.assertEquals("转换失败", entity.getFixedQuota(), summaryDalDO.getFixedQuota());
        Assert.assertEquals("转换失败", entity.getTemporaryQuota(), summaryDalDO.getTemporaryQuota());
        Assert.assertEquals("转换失败", entity.getTotalQuota(), summaryDalDO.getTotalQuota());
        Assert.assertEquals("转换失败", entity.getNextComputeDate(), summaryDalDO.getNextComputeDate());
        Assert.assertEquals("转换失败", entity.getExt(), summaryDalDO.getExt());
        Assert.assertEquals("转换失败", entity.getCtime(), summaryDalDO.getCtime());
        Assert.assertEquals("转换失败", entity.getMtime(), summaryDalDO.getMtime());
        Assert.assertEquals("转换失败", entity.getVersion(), summaryDalDO.getVersion());
    }

    @Test
    public void testConvert() {
        TransactionQuotaSummaryEntity entity = buildEntity();
        List<TransactionQuotaSummaryEntity> entities = new ArrayList<>();
        entities.add(entity);
        List<TransactionQuotaSummaryDalDO> summaryDalDOList = TransactionQuotaSummaryDalDOConverter.convert(entities);
        Assert.assertTrue("转换失败", CollectionUtils.isNotEmpty(summaryDalDOList));
        TransactionQuotaSummaryDalDO summaryDalDO = summaryDalDOList.get(0);
        Assert.assertEquals("转换失败", entity.getId(), summaryDalDO.getId());
        Assert.assertEquals("转换失败", entity.getMerchantSn(), summaryDalDO.getMerchantSn());
        Assert.assertEquals("转换失败", entity.getMerchantId(), summaryDalDO.getMerchantId());
        Assert.assertEquals("转换失败", entity.getFixedQuota(), summaryDalDO.getFixedQuota());
        Assert.assertEquals("转换失败", entity.getTemporaryQuota(), summaryDalDO.getTemporaryQuota());
        Assert.assertEquals("转换失败", entity.getTotalQuota(), summaryDalDO.getTotalQuota());
        Assert.assertEquals("转换失败", entity.getNextComputeDate(), summaryDalDO.getNextComputeDate());
        Assert.assertEquals("转换失败", entity.getExt(), summaryDalDO.getExt());
        Assert.assertEquals("转换失败", entity.getCtime(), summaryDalDO.getCtime());
        Assert.assertEquals("转换失败", entity.getMtime(), summaryDalDO.getMtime());
        Assert.assertEquals("转换失败", entity.getVersion(), summaryDalDO.getVersion());
    }

    private TransactionQuotaSummaryEntity buildEntity() {
        TransactionQuotaSummaryEntity entity = new TransactionQuotaSummaryEntity();
        entity.setId(1L);
        entity.setMerchantSn("mch-123");
        entity.setMerchantId("123");
        entity.setFixedQuota(8000L);
        entity.setTemporaryQuota(2000L);
        entity.setTotalQuota(10000L);
        entity.setNextComputeDate(LocalDate.now().plusDays(1));
        entity.setCtime(LocalDateTime.now());
        entity.setMtime(LocalDateTime.now());
        entity.setVersion(1L);
        return entity;
    }
}
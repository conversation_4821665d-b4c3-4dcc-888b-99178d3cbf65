package com.wosai.trade.repository.impl;

import com.wosai.trade.repository.TradeAssessmentActivitySummaryRepository;
import com.wosai.trade.repository.dao.entity.TradeAssessmentActivitySummaryEntity;
import com.wosai.trade.BaseTest;
import org.junit.Test;


import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 费率套餐活动交易考核汇总表Repository测试类
 * 
 * <AUTHOR> Assistant
 */
public class TradeAssessmentActivitySummaryRepositoryImplTest extends BaseTest {
    
    @Resource
    private TradeAssessmentActivitySummaryRepository tradeAssessmentActivitySummaryRepository;
    
    /**
     * 测试根据主键ID查询
     */
    @Test
    public void testFindByPkId() {
        Long id = 1L;
        TradeAssessmentActivitySummaryEntity entity = tradeAssessmentActivitySummaryRepository.findByPkId(id);
        System.out.println("根据ID查询结果: " + entity);
    }
    
    /**
     * 测试根据商户号和月份查询
     */
    @Test
    public void testFindByMerchantSnAndMonth() {
        String merchantSn = "test_merchant_001";
        String tradeMonth = "202312";
        TradeAssessmentActivitySummaryEntity list = tradeAssessmentActivitySummaryRepository.findByMerchantSnAndMonth(merchantSn,22L, tradeMonth);
    }

    /**
     * 测试根据月份查询（ID偏移分页）
     */
    @Test
    public void testFindByMonth() {
        String tradeMonth = "202312";
        // 使用IdOffsetPageable构建分页参数
        com.wosai.trade.model.dal.IdOffsetPageable pageable = com.wosai.trade.model.dal.IdOffsetPageable.builder()
                .lastId(0L) // 首次查询
                .limit(10)
                .build();

        List<TradeAssessmentActivitySummaryEntity> list = tradeAssessmentActivitySummaryRepository.findByMonth(tradeMonth, pageable);
        System.out.println("根据月份ID偏移分页查询结果数量: " + (list != null ? list.size() : 0));

        // 如果有结果，测试下一页查询
        if (list != null && !list.isEmpty()) {
            Long nextLastId = list.get(list.size() - 1).getId();
            com.wosai.trade.model.dal.IdOffsetPageable nextPageable = com.wosai.trade.model.dal.IdOffsetPageable.builder()
                    .lastId(nextLastId)
                    .limit(10)
                    .build();
            List<TradeAssessmentActivitySummaryEntity> nextPageList = tradeAssessmentActivitySummaryRepository.findByMonth(tradeMonth, nextPageable);
            System.out.println("下一页查询结果数量: " + (nextPageList != null ? nextPageList.size() : 0));
        }
    }

    /**
     * 测试选择性插入记录
     */
    @Test
    public void testInsertSelective() {
        TradeAssessmentActivitySummaryEntity entity = new TradeAssessmentActivitySummaryEntity();
        entity.setMerchantSn("test_merchant_insert");
        entity.setMerchantId("test_merchant_id_001");
        entity.setOriginalAmount(100000L); // 1000.00元
        entity.setReceivedAmount(99500L);  // 995.00元
        entity.setTradeCount(10L);
        entity.setTradeMonth("202401");
        entity.setApplyId(1L);
        entity.setActivityId(1L);
        entity.setCtime(new Date());
        entity.setMtime(new Date());

        int result = tradeAssessmentActivitySummaryRepository.insertSelective(entity);
        System.out.println("插入结果: " + result + ", 生成的ID: " + entity.getId());
    }

    /**
     * 测试根据主键选择性更新记录
     */
    @Test
    public void testUpdateByPrimaryKeySelective() {
        // 先插入一条记录
        TradeAssessmentActivitySummaryEntity insertEntity = new TradeAssessmentActivitySummaryEntity();
        insertEntity.setMerchantSn("test_merchant_update");
        insertEntity.setMerchantId("test_merchant_id_002");
        insertEntity.setOriginalAmount(200000L);
        insertEntity.setReceivedAmount(199000L);
        insertEntity.setTradeCount(20L);
        insertEntity.setTradeMonth("202401");
        insertEntity.setApplyId(2L);
        insertEntity.setActivityId(2L);
        insertEntity.setCtime(new Date());
        insertEntity.setMtime(new Date());

        int insertResult = tradeAssessmentActivitySummaryRepository.insertSelective(insertEntity);
        System.out.println("插入结果: " + insertResult + ", 生成的ID: " + insertEntity.getId());

        // 更新记录
        if (insertEntity.getId() != null) {
            TradeAssessmentActivitySummaryEntity updateEntity = new TradeAssessmentActivitySummaryEntity();
            updateEntity.setId(insertEntity.getId());
            updateEntity.setOriginalAmount(250000L); // 更新金额
            updateEntity.setReceivedAmount(249000L);
            updateEntity.setTradeCount(25L); // 更新笔数
            updateEntity.setMtime(new Date()); // 更新时间

            int updateResult = tradeAssessmentActivitySummaryRepository.updateByPrimaryKeySelective(updateEntity);
            System.out.println("更新结果: " + updateResult);

            // 验证更新结果
            TradeAssessmentActivitySummaryEntity updatedEntity = tradeAssessmentActivitySummaryRepository.findByPkId(insertEntity.getId());
            System.out.println("更新后的记录: " + updatedEntity);
        }
    }
}

package com.wosai.trade.repository.dao;

import com.alibaba.fastjson.JSONObject;
import com.wosai.trade.BaseTest;
import com.wosai.trade.repository.dao.entity.ActivityEntity;
import junit.framework.TestCase;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class ActivityDOMapperTest extends BaseTest {

    @Autowired
    private ActivityDOMapper activityDOMapper;

    @Test
    public void testGetActivityDOByComboId() {
        ActivityEntity activityDOByComboId = activityDOMapper.getActivityDOByComboId(2752L);
        System.out.println(JSONObject.toJSONString(activityDOByComboId));
    }
}
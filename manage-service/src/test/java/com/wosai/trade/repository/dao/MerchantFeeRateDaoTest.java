package com.wosai.trade.repository.dao;

import com.alibaba.fastjson.JSON;
import com.wosai.trade.model.dal.MerchantFeeRateUpsertDalParam;
import com.wosai.trade.model.dal.MerchantFeeRateQueryDalParam;
import com.wosai.trade.repository.dao.entity.MerchantFeeRateEntity;
import junit.framework.TestCase;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.List;

@PowerMockIgnore({"javax.management.*"})
@RunWith(PowerMockRunner.class)
public class MerchantFeeRateDaoTest {

    private static final List<MerchantFeeRateEntity> ENTITY_LIST = JSON.parseArray("[{\"appId\":1,\"appInUse\":0,\"auditSn\":\"SP8120210125000009\",\"b2cInUse\":0,\"beginDate\":1611504000000,\"c2bInUse\":0,\"createAt\":1611548317000,\"endDate\":1616688000000,\"feeRate\":\"0.24\",\"feeRateType\":\"fixed\",\"h5InUse\":0,\"id\":44,\"merchantSn\":\"21690002897483\",\"miniInUse\":0,\"payWay\":2,\"status\":2,\"tradeComboId\":1967,\"updateAt\":1611552722000,\"wapInUse\":0},{\"appId\":1,\"appInUse\":0,\"auditSn\":\"SP8120210125000009\",\"b2cInUse\":0,\"beginDate\":1611504000000,\"c2bInUse\":0,\"createAt\":1611548317000,\"endDate\":1616688000000,\"feeRate\":\"0.24\",\"feeRateType\":\"fixed\",\"h5InUse\":0,\"id\":45,\"merchantSn\":\"21690002897483\",\"miniInUse\":0,\"payWay\":3,\"status\":2,\"tradeComboId\":1967,\"updateAt\":1611552722000,\"wapInUse\":0}]", MerchantFeeRateEntity.class);

    @Mock
    private MerchantFeeRateDao merchantFeeRateDao;

    @Before
    public void setUp() {
        PowerMockito.when(merchantFeeRateDao.insert(Mockito.any(MerchantFeeRateUpsertDalParam.class)))
                .thenReturn(1);

        PowerMockito.when(merchantFeeRateDao.selectList(Mockito.any(MerchantFeeRateQueryDalParam.class)))
                .thenReturn(ENTITY_LIST);
    }

    @Test
    public void insert() {
        MerchantFeeRateUpsertDalParam insertDalParam = MerchantFeeRateUpsertDalParam.builder().build();
        int result = merchantFeeRateDao.insert(insertDalParam);
        TestCase.assertEquals("单元测试失败", 1, result);
    }

    @Test
    public void selectList() {
        MerchantFeeRateQueryDalParam queryDalParam = MerchantFeeRateQueryDalParam.builder().build();
        List<MerchantFeeRateEntity> result = merchantFeeRateDao.selectList(queryDalParam);
        TestCase.assertEquals("单元测试失败", JSON.toJSONString(ENTITY_LIST), JSON.toJSONString(result));
    }

    @Test
    public void selectByMchSnAndComboIdAndPayWay() {
        MerchantFeeRateEntity merchantFeeRateEntity = merchantFeeRateDao.selectByMchSnAndComboIdAndPayWay("mch-001", 1, 1);

        System.out.println(JSON.toJSONString(merchantFeeRateEntity));
    }

    @Test
    public void updateById() {
        MerchantFeeRateUpsertDalParam updateDalParam = MerchantFeeRateUpsertDalParam.builder().build();
        int result = merchantFeeRateDao.updateById(updateDalParam);
    }

}

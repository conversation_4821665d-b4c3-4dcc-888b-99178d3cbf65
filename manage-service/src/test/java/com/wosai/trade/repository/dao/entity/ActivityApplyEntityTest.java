package com.wosai.trade.repository.dao.entity;

import com.wosai.trade.biz.activity.ApplyActivityBiz;
import com.wosai.trade.service.activity.enums.TradeAssessmentTypeEnum;
import com.wosai.trade.service.activity.model.TradeAssessmentRule;
import com.wosai.trade.service.activity.request.ActivityEffectiveRule;
import com.wosai.trade.service.activity.request.ActivityExpirationRule;
import com.wosai.trade.util.JsonUtil;
import com.wosai.trade.util.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.junit.Assert;
import org.junit.Test;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.Objects;

/**
 * ActivityApplyEntity.calcNextAssessmentTime() 方法测试类
 *
 * <AUTHOR> Assistant
 */
@Slf4j
public class ActivityApplyEntityTest {

    private static ApplyActivityBiz applyActivityBiz = new ApplyActivityBiz();

    @Test
    public void fetchAssessmentEndTime() {
        ActivityApplyEntity activityApplyEntity = new ActivityApplyEntity();
        activityApplyEntity.setId(1L);
        activityApplyEntity.setActivity_id(100L);

        int cycle = 3;
        // 设置基础数据
        TradeAssessmentRule assessmentRule = new TradeAssessmentRule();
        assessmentRule.setTradeAssessmentType(TradeAssessmentTypeEnum.ROLLING);
        assessmentRule.setCycle(cycle); // 3个月周期
        activityApplyEntity.setTrade_assessment_rule(JsonUtil.encode(assessmentRule));

        ActivityEffectiveRule effectiveRule = new ActivityEffectiveRule();
        ActivityEffectiveRule.TimeRule timeRule = new ActivityEffectiveRule.TimeRule();
        timeRule.setFixedTime(System.currentTimeMillis());
        effectiveRule.setByTimeRule(timeRule);
        activityApplyEntity.setEffective_rule(JsonUtil.encode(effectiveRule));

        LocalDateTime expected = LocalDateTimeUtil.getEndOfMonth(LocalDateTime.now().plusMonths(cycle + 1));
        LocalDateTime actual = LocalDateTimeUtil.valueOf(activityApplyEntity.fetchExpirationTime());
        log.info("executed={}", LocalDateTimeUtil.getFormatDateTime(expected));
        Assert.assertEquals(expected, actual);
    }


    @Test
    public void testCalcNextAssessmentTime() {
        ActivityApplyEntity activityApplyEntity = new ActivityApplyEntity();
        activityApplyEntity.setId(1L);
        activityApplyEntity.setActivity_id(100L);

        int cycle = 3;
        // 考核时间
        TradeAssessmentRule assessmentRule = new TradeAssessmentRule();
        assessmentRule.setTradeAssessmentType(TradeAssessmentTypeEnum.ROLLING);
        assessmentRule.setCycle(cycle); // 3个月周期
        activityApplyEntity.setTrade_assessment_rule(JsonUtil.encode(assessmentRule));
        //　生效时间
        ActivityEffectiveRule effectiveRule = new ActivityEffectiveRule();
        ActivityEffectiveRule.TimeRule timeRule = new ActivityEffectiveRule.TimeRule();
        timeRule.setFixedTime(System.currentTimeMillis());
        effectiveRule.setByTimeRule(timeRule);
        activityApplyEntity.setEffective_rule(JsonUtil.encode(effectiveRule));

        ActivityExpirationRule expirationRule = new ActivityExpirationRule();
        ActivityExpirationRule.TimeRule expirationTimeRule = new ActivityExpirationRule.TimeRule();
        expirationTimeRule.setRollMonth(3);
        expirationRule.setByTimeRule(expirationTimeRule);
        activityApplyEntity.setExpiration_rule(JsonUtil.encode(expirationRule));

        for (int i = 1; i <= cycle; i++) {
            Date assessmentTime = applyActivityBiz.calcNextAssessmentTime(activityApplyEntity, false);
            activityApplyEntity.setAssessment_time(assessmentTime);
            log.info("第{}次考核时间: {}", i, LocalDateTimeUtil.getFormatDateTime(assessmentTime));
        }
    }


    @Test
    public void calcRollingNextAssessmentTime() {
        // 活动周期
        int cycle = 3;
        ActivityApplyEntity activityApplyEntity = new ActivityApplyEntity();
        activityApplyEntity.setId(1L);
        activityApplyEntity.setActivity_id(100L);
        // 设置基础数据
        TradeAssessmentRule assessmentRule = new TradeAssessmentRule();
        assessmentRule.setTradeAssessmentType(TradeAssessmentTypeEnum.ROLLING);
        assessmentRule.setCycle(cycle);
        activityApplyEntity.setTrade_assessment_rule(JsonUtil.encode(assessmentRule));
        // 设置生效时间
        ActivityEffectiveRule effectiveRule = new ActivityEffectiveRule();
        ActivityEffectiveRule.TimeRule timeRule = new ActivityEffectiveRule.TimeRule();
        timeRule.setFixedTime(System.currentTimeMillis());
        effectiveRule.setByTimeRule(timeRule);
        activityApplyEntity.setEffective_rule(JsonUtil.encode(effectiveRule));

        for (int cycleNo = 1; cycleNo <= cycle; cycleNo++) {
            // 期望值
            LocalDateTime expected = LocalDateTimeUtil.getStartOfMonth(LocalDateTime.now().plusMonths(2));
            // 实际值
            LocalDateTime actual = LocalDateTimeUtil.valueOf(applyActivityBiz.calcNextAssessmentTime(activityApplyEntity, false));
            activityApplyEntity.setAssessment_time(applyActivityBiz.calcNextAssessmentTime(activityApplyEntity, false));
            log.info("cycleNo={},executed={}", cycleNo, LocalDateTimeUtil.getFormatDateTime(expected));
            Assert.assertEquals(expected, actual);
        }
    }


    /**
     * 计算下一次考核时间
     *
     * @return
     */
    public LocalDateTime calcNextAssessmentTime(LocalDateTime currentAssessmentTime, LocalDateTime currentTime) {
        LocalDateTime assessmentTime = currentAssessmentTime;
        if (Objects.isNull(assessmentTime)) {
            //计算至下一月
            assessmentTime = currentTime.plusMonths(NumberUtils.LONG_ONE);
        }
        // 下一次考核时间
        return LocalDateTimeUtil.getStartOfMonth(assessmentTime.plusMonths(1));
    }

    @Test
    public void testExpireTime() {
        ActivityApplyEntity activityApplyEntity = new ActivityApplyEntity();
        activityApplyEntity.setId(1L);
        activityApplyEntity.setActivity_id(100L);
        // 设置生效时间
        ActivityEffectiveRule effectiveRule = new ActivityEffectiveRule();
        ActivityEffectiveRule.TimeRule timeRule = new ActivityEffectiveRule.TimeRule();
        timeRule.setFixedTime(System.currentTimeMillis());
        effectiveRule.setByTimeRule(timeRule);
        activityApplyEntity.setEffective_rule(JsonUtil.encode(effectiveRule));

        Date expire = activityApplyEntity.fetchExpirationTime();
        Assert.assertNull(expire);
    }

    /**
     * 测试calcNextAssessmentTime方法 - 3个月考核周期
     * 测试场景：
     * 1. 首次：当前时间为2025.3.15，考核时间为空 → 下一次考核时间为2025.5.1
     * 2. 考核时间为2025.5.1 → 下一次考核时间为2025.6.1
     * 3. 考核时间为2025.6.1 → 下一次考核时间为2025.7.1
     * 4. 考核时间为2025.7.1 → 下一次考核时间为空（结束考核）
     */
    @Test
    public void testCalcNextAssessmentTimeFor3Months() {
        log.info("开始测试calcNextAssessmentTime方法 - 3个月考核周期");

        // 创建ActivityApplyEntity实例
        ActivityApplyEntity activityApplyEntity = createTestActivityApplyEntity();

        // 测试场景1：首次计算，当前时间为2025.3.15，考核时间为空
        log.info("=== 测试场景1：首次计算 ===");
        activityApplyEntity.setAssessment_time(null); // 考核时间为空

        // 模拟calcNextAssessmentTime方法的逻辑，但使用固定的当前时间
        LocalDateTime mockCurrentTime = LocalDateTime.of(2025, 3, 15, 0, 0, 0);
        Date nextAssessmentTime1 = calcNextAssessmentTimeWithMockTime(activityApplyEntity, mockCurrentTime);
        String exp = LocalDateTimeUtil.getFormatDateTime(activityApplyEntity.fetchExpirationTime());
        log.info("失效时间: {}", exp);
        // 期望值：2025.5.1 00:00:00
        LocalDateTime expected1 = LocalDateTime.of(2025, 5, 1, 0, 0, 0);
        LocalDateTime actual1 = LocalDateTimeUtil.valueOf(nextAssessmentTime1);

        log.info("首次计算 - 当前时间: {}, 当前考核时间: null, 下一次考核时间: {}, 期望时间: {}",
                LocalDateTimeUtil.getFormatDateTime(mockCurrentTime),
                LocalDateTimeUtil.getFormatDateTime(actual1),
                LocalDateTimeUtil.getFormatDateTime(expected1));
        Assert.assertEquals("首次计算下一次考核时间应为2025.5.1", expected1, actual1);

        // 测试场景2：考核时间为2025.5.1
        log.info("=== 测试场景2：第二次计算 ===");
        activityApplyEntity.setAssessment_time(nextAssessmentTime1);
        Date nextAssessmentTime2 = calcNextAssessmentTimeWithMockTime(activityApplyEntity, mockCurrentTime);

        // 期望值：2025.6.1 00:00:00
        LocalDateTime expected2 = LocalDateTime.of(2025, 6, 1, 0, 0, 0);
        LocalDateTime actual2 = LocalDateTimeUtil.valueOf(nextAssessmentTime2);

        log.info("第二次计算 - 当前考核时间: {}, 下一次考核时间: {}, 期望时间: {}",
                LocalDateTimeUtil.getFormatDateTime(LocalDateTimeUtil.valueOf(nextAssessmentTime1)),
                LocalDateTimeUtil.getFormatDateTime(actual2),
                LocalDateTimeUtil.getFormatDateTime(expected2));
        Assert.assertEquals("第二次计算下一次考核时间应为2025.6.1", expected2, actual2);

        // 测试场景3：考核时间为2025.6.1
        log.info("=== 测试场景3：第三次计算 ===");
        activityApplyEntity.setAssessment_time(nextAssessmentTime2);
        Date nextAssessmentTime3 = calcNextAssessmentTimeWithMockTime(activityApplyEntity, mockCurrentTime);

        // 期望值：2025.7.1 00:00:00
        LocalDateTime expected3 = LocalDateTime.of(2025, 7, 1, 0, 0, 0);
        LocalDateTime actual3 = LocalDateTimeUtil.valueOf(nextAssessmentTime3);

        log.info("第三次计算 - 当前考核时间: {}, 下一次考核时间: {}, 期望时间: {}",
                LocalDateTimeUtil.getFormatDateTime(LocalDateTimeUtil.valueOf(nextAssessmentTime2)),
                LocalDateTimeUtil.getFormatDateTime(actual3),
                LocalDateTimeUtil.getFormatDateTime(expected3));
        Assert.assertEquals("第三次计算下一次考核时间应为2025.7.1", expected3, actual3);

        // 测试场景4：考核时间为2025.7.1，应该结束考核
        log.info("=== 测试场景4：第四次计算（结束考核）===");
        activityApplyEntity.setAssessment_time(nextAssessmentTime3);
        Date nextAssessmentTime4 = calcNextAssessmentTimeWithMockTime(activityApplyEntity, mockCurrentTime);

        log.info("第四次计算 - 当前考核时间: {}, 下一次考核时间: {}",
                LocalDateTimeUtil.getFormatDateTime(LocalDateTimeUtil.valueOf(nextAssessmentTime3)),
                nextAssessmentTime4 == null ? "null（考核结束）" : LocalDateTimeUtil.getFormatDateTime(nextAssessmentTime4));
        Assert.assertNull("第四次计算应该返回null，表示考核结束", nextAssessmentTime4);

        log.info("测试完成：3个月考核周期测试通过");
    }

    /**
     * 模拟calcNextAssessmentTime方法，但使用mock的当前时间
     * 这是为了避免使用LocalDateTime.now()导致的时间不确定性
     */
    private Date calcNextAssessmentTimeWithMockTime(ActivityApplyEntity entity, LocalDateTime mockCurrentTime) {
        TradeAssessmentRule rule = entity.buildTradeAssessmentRule();
        if (rule == null || rule.getTradeAssessmentType() == null) {
            return null;
        }
        if (Objects.isNull(rule.getCycle())) {
            return null; // 简化异常处理
        }

        LocalDateTime assessmentTime = LocalDateTimeUtil.valueOf(entity.getAssessment_time());
        if (Objects.isNull(assessmentTime)) {
            // 使用mock的当前时间计算至下一月
            assessmentTime = mockCurrentTime.plusMonths(NumberUtils.LONG_ONE);
        }

        // 下一次考核时间
        Date nextAssessmentTime = LocalDateTimeUtil.toDate(LocalDateTimeUtil.getStartOfMonth(assessmentTime.plusMonths(1)));

        // 获取失效时间
        Date expireTime = entity.fetchExpirationTime();

        // 如果下一次考核时间大于等于失效时间，则返回null
        if (nextAssessmentTime.getTime() >= expireTime.getTime()) {
            return null;
        }

        return nextAssessmentTime;
    }

    /**
     * 创建测试用的ActivityApplyEntity实例
     * 设置3个月考核周期，生效时间为2025.3.15，失效时间为2025.8.15
     */
    private ActivityApplyEntity createTestActivityApplyEntity() {
        ActivityApplyEntity entity = new ActivityApplyEntity();
        entity.setId(1L);
        entity.setActivity_id(100L);

        // 设置交易考核规则：3个月周期
        TradeAssessmentRule assessmentRule = new TradeAssessmentRule();
        assessmentRule.setTradeAssessmentType(TradeAssessmentTypeEnum.ROLLING);
        assessmentRule.setCycle(3); // 3个月周期
        entity.setTrade_assessment_rule(JsonUtil.encode(assessmentRule));

        // 设置生效时间：2025.3.15 00:00:00
        ActivityEffectiveRule effectiveRule = new ActivityEffectiveRule();
        ActivityEffectiveRule.TimeRule effectiveTimeRule = new ActivityEffectiveRule.TimeRule();
        LocalDateTime effectiveDateTime = LocalDateTime.of(2025, 3, 15, 0, 0, 0);
        effectiveTimeRule.setFixedTime(LocalDateTimeUtil.toEpochMilliSecond(effectiveDateTime));
        effectiveRule.setByTimeRule(effectiveTimeRule);
        entity.setEffective_rule(JsonUtil.encode(effectiveRule));

        // 设置失效时间：2025.8.15 00:00:00（通过滚动时间设置，从生效时间开始滚动5个月）
        // 这样可以确保考核能够完整进行3个月（5月、6月、7月）
        ActivityExpirationRule expirationRule = new ActivityExpirationRule();
        ActivityExpirationRule.TimeRule expirationTimeRule = new ActivityExpirationRule.TimeRule();
        expirationTimeRule.setRollMonth(5); // 从生效时间滚动5个月到2025.8.15
        expirationRule.setByTimeRule(expirationTimeRule);
        entity.setExpiration_rule(JsonUtil.encode(expirationRule));

        return entity;
    }

    public static void main(String[] args) {
        System.out.println(LocalDateTimeUtil.getFormatDateTime(new Date()));

        ActivityApplyEntity activityApplyEntity = new ActivityApplyEntity();
        activityApplyEntity.setId(1L);
        activityApplyEntity.setActivity_id(100L);

        int cycle = 3;
        // 考核时间
        TradeAssessmentRule assessmentRule = new TradeAssessmentRule();
        assessmentRule.setTradeAssessmentType(TradeAssessmentTypeEnum.ROLLING);
        assessmentRule.setCycle(cycle); // 3个月周期
        activityApplyEntity.setTrade_assessment_rule(JsonUtil.encode(assessmentRule));
        //　生效时间
        ActivityEffectiveRule effectiveRule = new ActivityEffectiveRule();
        ActivityEffectiveRule.TimeRule timeRule = new ActivityEffectiveRule.TimeRule();
        timeRule.setFixedTime(System.currentTimeMillis());
        effectiveRule.setByTimeRule(timeRule);
        activityApplyEntity.setEffective_rule(JsonUtil.encode(effectiveRule));

        ActivityExpirationRule expirationRule = new ActivityExpirationRule();
        ActivityExpirationRule.TimeRule expirationTimeRule = new ActivityExpirationRule.TimeRule();
        expirationTimeRule.setRollMonth(3);
        expirationRule.setByTimeRule(expirationTimeRule);
        activityApplyEntity.setExpiration_rule(JsonUtil.encode(expirationRule));

        Date nextAssessmentTime = applyActivityBiz.calcNextAssessmentTime(activityApplyEntity, false);
        activityApplyEntity.setAssessment_time(nextAssessmentTime);
        log.info("下一次考核时间: {}", LocalDateTimeUtil.getFormatDateTime(nextAssessmentTime));
    }
}

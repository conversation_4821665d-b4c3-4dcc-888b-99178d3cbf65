package com.wosai.trade.repository.impl;

import com.wosai.trade.BaseTest;
import com.wosai.trade.repository.dao.entity.CrmFeeRateChangeEntity;
import com.wosai.upay.common.util.JacksonUtil;
import org.junit.Test;
import org.springframework.data.domain.PageRequest;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

public class CrmFeeRateChangeRepositoryImplTest extends BaseTest {
    @Resource
    private CrmFeeRateChangeRepositoryImpl crmFeeRateExchangeRepository;
    @Test
    public void findByStatusList() {
        Date endTime = Date.from(LocalDateTime.now()
                .plusMinutes(-30)
                .atZone(ZoneId.systemDefault()).toInstant());
        List<CrmFeeRateChangeEntity> list = crmFeeRateExchangeRepository.findLastUpdateInitMerchantList(endTime, new PageRequest(3, 200));
        System.out.println(JacksonUtil.toJsonString(list));
    }
    @Test
    public void findUnDoneMerchantSnList() {
        List<String> list = crmFeeRateExchangeRepository.findUnDoneMerchantSnList(new PageRequest(3, 200));
    }
    public static void main(String[] args) {
        PageRequest request = new PageRequest(2, 100);
        System.out.println(request.getOffset());
    }

}
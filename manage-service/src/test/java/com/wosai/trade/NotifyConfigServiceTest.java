package com.wosai.trade;


import com.wosai.trade.biz.audit.NotifyTradeBiz;
import com.wosai.upay.user.api.service.GroupService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.List;


/**
 *<AUTHOR>
 *@Date 2:18 下午
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class NotifyConfigServiceTest {




    @Autowired
    GroupService groupService;

    @Autowired
    NotifyTradeBiz notifyTradeBiz;
    @Test
    public void Base64Test() {
        String str = "test";
        String encodeString = Base64.getEncoder().encodeToString(str.getBytes(StandardCharsets.UTF_8));
        byte[] decode = Base64.getDecoder().decode(encodeString);
        Assert.assertEquals("test", new String(decode,StandardCharsets.UTF_8));
    }

    @Test
    public  void testEmail() {
        notifyTradeBiz.createNotifyConfigAndSendEmail("1","1","<EMAIL>","1","1","1",1,"12","11",true);
    }



}

package com.wosai.trade.controller;

import com.wosai.trade.BaseTest;
import com.wosai.trade.biz.activity.model.UpdateActivityApplyExpirationRuleRequest;
import com.wosai.trade.repository.dao.ActivityApplyDOMapper;
import com.wosai.trade.repository.dao.entity.ActivityApplyEntity;
import com.wosai.trade.util.JsonUtil;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Date;

public class ActivityControllerTest extends BaseTest {
    @Resource
    private ActivityController controller;
    @Resource
    private ActivityApplyDOMapper activityApplyDOMapper;

    @Test
    public void updateActivityApplyExpirationRule() {
        Long id = 44587L;
        ActivityApplyEntity before = activityApplyDOMapper.selectByPrimaryKey(id);
        UpdateActivityApplyExpirationRuleRequest request = new UpdateActivityApplyExpirationRuleRequest();
        request.setId(44587L);
        request.setMerchantSn("1680004113859");
        request.setProcessInfo("上游行业活动时间延长，xxx");
        controller.updateActivityApplyExpirationRule(request);
        ActivityApplyEntity afterDo = activityApplyDOMapper.selectByPrimaryKey(id);
        ActivityApplyEntity update = new ActivityApplyEntity();
        update.setId(before.getId());
        update.setExpiration_rule(before.getExpiration_rule());
        update.setExpiration_time(before.getExpiration_time());
        update.setProcess(before.getProcess());
        activityApplyDOMapper.updateByPrimaryKeySelective(update);
    }
}
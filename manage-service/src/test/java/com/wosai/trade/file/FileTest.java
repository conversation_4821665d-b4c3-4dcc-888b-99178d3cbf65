package com.wosai.trade.file;

import com.wosai.trade.util.UpayUtil;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.LineIterator;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * @Description:
 * <AUTHOR>
 * @Date: 2022/9/5 9:15 下午
 */
public class FileTest {


    public static void main(String[] args) {
        List<Long> a = Arrays.asList(109900L, 80000L, 38000L, 39800L, 5600L, 27300L);

        long amount=0;
        for (Long aLong : a) {

            amount += UpayUtil.calFee(aLong, "0.3");
        }
        System.out.println(amount);


    }


    public void test() throws IOException {
        String in = "";
        LineIterator iterator = FileUtils.lineIterator(new File(in));
        while (iterator.hasNext()) {
            String s = iterator.nextLine();
            String[] split = s.split(",");


        }


    }


}

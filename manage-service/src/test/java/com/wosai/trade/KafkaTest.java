package com.wosai.trade;

import com.wosai.trade.biz.activity.quota.PublishApplyActivityBiz;
import com.wosai.trade.model.dal.BankRecommendPayMsg;
import com.wosai.trade.producer.BankRecommendQuotaSyncProducer;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @Date 2024/3/18、15:56
 **/


@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class KafkaTest {


    @Autowired
    BankRecommendQuotaSyncProducer bankRecommendQuotaSyncProducer;

    @Autowired
    PublishApplyActivityBiz publishApplyActivityBiz;


    @Test
    public void test() throws InterruptedException {
        BankRecommendPayMsg bankRecommendPayMsg1 = new BankRecommendPayMsg();
        bankRecommendPayMsg1.setBizKey("key");
        bankRecommendPayMsg1.setBizValue("value");
        bankRecommendPayMsg1.setMerchantId("819fd855-f6ba-4210-bf6c-cdbf3ce1c294");
        bankRecommendPayMsg1.setMerchantSn("**************");
        bankRecommendPayMsg1.setTimestamp(System.currentTimeMillis());

//        bankReQuotaSyncProducer.sendQuta(bankRecommendPayMsg1);

    }


    @Test
    public void kafkaTest2() {
//        publishApplyActivityBiz.execute();
    }
}

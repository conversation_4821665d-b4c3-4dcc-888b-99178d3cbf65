package com.wosai.trade.util;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.modules.junit4.PowerMockRunner;

import java.time.LocalDate;

import static org.junit.Assert.*;

/**
 * <AUTHOR> Date: 2020/7/7 Time: 2:24 下午
 */
@RunWith(PowerMockRunner.class)
public class DateTimeUtilsTest {

    @Test
    public void isFirstAfterOrEqualSecond() {
        boolean result = DateTimeUtils.isFirstAfterOrEqualSecond(LocalDate.now().plusDays(1), LocalDate.now());
        Assert.assertTrue("判断失败", result);
        result = DateTimeUtils.isFirstAfterOrEqualSecond(LocalDate.now(), LocalDate.now());
        Assert.assertTrue("判断失败", result);
        result = DateTimeUtils.isFirstAfterOrEqualSecond(LocalDate.now(), LocalDate.now().plusDays(1));
        Assert.assertFalse("判断失败", result);
    }
}
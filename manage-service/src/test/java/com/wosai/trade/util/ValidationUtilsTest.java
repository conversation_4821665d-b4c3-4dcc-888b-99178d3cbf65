package com.wosai.trade.util;

import com.wosai.trade.service.request.TempQuotaAddRequest;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.modules.junit4.PowerMockRunner;


/**
 * <AUTHOR> Date: 2020/7/7 Time: 2:44 下午
 */
@RunWith(PowerMockRunner.class)
public class ValidationUtilsTest {

    @Test
    public void validateWithFailFast() {
        TempQuotaAddRequest quotaAddRequest = new TempQuotaAddRequest();
        quotaAddRequest.setQuota(1L);
        quotaAddRequest.setType(Byte.parseByte("1"));
        quotaAddRequest.setBeginDate("2020-07-08");
        quotaAddRequest.setEndDate("2020-08-09");
        ValidationUtils.ValidationResult validationResult = ValidationUtils.validateWithFailFast(quotaAddRequest);
        Assert.assertNotNull("参数校验失败", validationResult);
        Assert.assertFalse("参数校验失败", validationResult.isPass());
        Assert.assertEquals("参数校验失败", validationResult.getMsg(), "商户号不能为空");

        quotaAddRequest.setMerchantSn("mch-123");
        quotaAddRequest.setQuota(0L);
        quotaAddRequest.setType(Byte.parseByte("1"));
        quotaAddRequest.setBeginDate("2020-07-08");
        quotaAddRequest.setEndDate("2020-08-09");
        validationResult = ValidationUtils.validateWithFailFast(quotaAddRequest);
        Assert.assertNotNull("参数校验失败", validationResult);
        Assert.assertFalse("参数校验失败", validationResult.isPass());
        Assert.assertEquals("参数校验失败", validationResult.getMsg(), "临时额度不能小于1");

        quotaAddRequest.setQuota(1L);
        quotaAddRequest.setBeginDate("2020-07-32");
        validationResult = ValidationUtils.validateWithFailFast(quotaAddRequest);
        Assert.assertNotNull("参数校验失败", validationResult);
        Assert.assertFalse("参数校验失败", validationResult.isPass());
        Assert.assertEquals("参数校验失败", validationResult.getMsg(), "临时额度生效日期格式有误");

        quotaAddRequest.setBeginDate("2020-07-08");
        quotaAddRequest.setEndDate("2020-13-09");
        validationResult = ValidationUtils.validateWithFailFast(quotaAddRequest);
        Assert.assertNotNull("参数校验失败", validationResult);
        Assert.assertFalse("参数校验失败", validationResult.isPass());
        Assert.assertEquals("参数校验失败", validationResult.getMsg(), "临时额度失效日期格式有误");

    }

    @Test
    public void validateTempQuotaRuleBatchUpsert() {
        TempQuotaAddRequest quotaAddRequest = new TempQuotaAddRequest();
        quotaAddRequest.setMerchantSn("mch-123");
        quotaAddRequest.setQuota(0L);
        quotaAddRequest.setType(Byte.parseByte("1"));
        quotaAddRequest.setBeginDate("2020-07-80");
        quotaAddRequest.setEndDate("2020-08-09");
        ValidationUtils.TempQuotaRuleBatchUpsertValidateResult validationResult
                = ValidationUtils.validateTempQuotaRuleBatchUpsert(quotaAddRequest);
        Assert.assertNotNull("参数校验失败", validationResult);
        Assert.assertFalse("参数校验失败", validationResult.isPass());
        Assert.assertTrue("参数校验失败", MapUtils.isNotEmpty(validationResult.getFailReason()));
        Assert.assertTrue("参数校验失败", StringUtils.isNoneBlank(validationResult.getFailReason().get("mch-123")));
        Assert.assertTrue("参数校验失败", validationResult.getFailReason().get("mch-123").contains("临时额度不能小于1")&&validationResult.getFailReason().get("mch-123").contains("临时额度生效日期格式有误"));
    }
}
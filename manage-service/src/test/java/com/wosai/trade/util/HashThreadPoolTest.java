package com.wosai.trade.util;

import com.wosai.trade.repository.dao.entity.ActivityApplyEntity;
import com.wosai.trade.task.ActivityApplyEffectScheduler;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.Date;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;


@Slf4j
public class HashThreadPoolTest {


    private static final HashThreadPool executorService = HashThreadPool.of(ActivityApplyEffectScheduler.class.getSimpleName(), 10);
    private static final ExecutorService executorService2 = Executors.newFixedThreadPool(10);

    @Test
    public void test() throws InterruptedException {
        HashThreadPool.HashRunnable runnable = new HashThreadPool.HashRunnable() {
            @Override
            public String getKey() {
                return "a";
            }

            @Override
            public void run() {
                log.info("key={}", getKey());
            }
        };
        for (int i = 0; i < 10; i++) {
            int finalI = i;
            executorService.execute(runnable);
            executorService2.execute(runnable);
        }
        TimeUnit.SECONDS.sleep(10);
    }

    @Test
    public void testApply() {
        for (int i = 0; i < 10; i++) {
            ActivityApplyEntity activityApplyEntity = new ActivityApplyEntity();
            activityApplyEntity.setCreate_at(new Date());
        }
    }

}
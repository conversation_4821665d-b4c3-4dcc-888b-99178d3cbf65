package com.wosai.trade.util;

import com.wosai.databus.event.AbstractEvent;
import org.junit.Test;

import static org.junit.Assert.*;

public class JsonUtilTest {


    public static void main(String[] args) {
        String json = "{\"module\":\"ORDER\",\"object_type\":\"ORDER_BASIC\",\"event_type\":\"DELIVERY_INSPECT\",\"operator_id\":\"system\",\"operator_type\":null,\"operator_platform\":\"OMS\",\"sn\":\"25924907190862\",\"source\":\"zd\",\"source_type\":null,\"biz_owner\":\"bd\",\"biz_business\":null,\"event_time\":1740563615895,\"compensate_times\":0,\"delivery_no\":null,\"audit_status\":\"PASS\",\"fail_reason\":null,\"shouqianba_sn\":\"sqbzd25924907190862\"}";
        String okjson = "{\"compensateTimes\":0,\"event_time\":1740564938544,\"event_type\":\"PAY\",\"module\":\"ORDER\",\"object_type\":\"ORDER_BASIC\",\"outerTsn\":\"wanmi_no_cash_tsn\",\"payway\":100,\"seq\":1740564939055,\"shouqianbaSn\":\"sqbwhjfO202502261815370001\",\"sn\":\"O202502261815370001\",\"source\":\"whjf\",\"timestamp\":1740564939055,\"tsn\":\"wanmi_no_cash_tsn\"}";
        //String dec = JsonUtil.encode(JsonUtil.decode(okjson, FTOrderBasicRefundEvent.class));
        AbstractEvent str = JsonUtil.fromEventJsonBytes(okjson.getBytes(), AbstractEvent.class);
        System.out.println(str);
    }
}
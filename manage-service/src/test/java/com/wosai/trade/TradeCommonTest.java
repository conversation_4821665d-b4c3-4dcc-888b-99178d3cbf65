package com.wosai.trade;

import com.wosai.pantheon.util.MapUtil;
import com.wosai.trade.impl.TradeCommonService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;

@RunWith(SpringRunner.class)
@SpringBootTest
public class TradeCommonTest {
    @Autowired
    private TradeCommonService commonService;

    @Test
    public void testQuotaMerchantCancel() {
        commonService.sendAopNotice("860e638e-659d-49f1-bfa6-71e95c922eb1",
                Arrays.asList("TERMINALAPP"), "I0UDXL7CVVCT", "AB35JQLMWLYW",
                "", MapUtil.hashMap("notice", "失效4"));
    }

    @Test
    public void testQuotaByMerchantApply() {
        commonService.sendAopNotice("860e638e-659d-49f1-bfa6-71e95c922eb1",
                Arrays.asList("TERMINALAPP"), "I0UDXL7CVVCT", "25XANWIGMX8J",
                "", MapUtil.hashMap("notice", "生效哦"));
    }
}

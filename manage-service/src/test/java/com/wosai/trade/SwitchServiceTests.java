package com.wosai.trade;

import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.TradeConfigService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * Created by dabuff on 19/12/19.
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class SwitchServiceTests {
    @Autowired
    TradeConfigService tradeConfigService;

    @Test
    public void openMerchantPay(){
        tradeConfigService.switchStatus("000011dd27c4-9599-1174-4184-e1cfd033", TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED);
    }

    @Test
    public void closeMerchantPay(){
        tradeConfigService.switchStatus("000011dd27c4-9599-1174-4184-e1cfd033", TransactionParam.PAY_STATUS, TransactionParam.STATUS_CLOSED);
    }

    @Test
    public void queryMerchantPayStatus(){
        System.out.println("Status ==> " + tradeConfigService.queryStatus("000011dd27c4-9599-1174-4184-e1cfd033", TransactionParam.PAY_STATUS));
    }
}

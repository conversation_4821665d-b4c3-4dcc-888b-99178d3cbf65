package com.wosai.trade;

import com.alibaba.fastjson.JSONObject;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/21.
 */
@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@PowerMockIgnore({"javax.management.*", "sun.security.*", "javax.net.*", "javax.net.ssl.*","javax.crypto.*"}) //为了解决使用powermock后，提示classloader错误
@SpringBootTest
public class BaseTest {


    @Test
    public void test(){

    }

}

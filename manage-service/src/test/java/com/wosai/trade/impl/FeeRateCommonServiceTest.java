package com.wosai.trade.impl;

import com.wosai.trade.BaseTest;
import com.wosai.trade.biz.audit.model.ApplyLadderFeeRate;
import com.wosai.trade.model.biz.ApplyComboDetailParam;
import com.wosai.trade.util.JsonUtil;
import com.wosai.upay.core.meta.Payway;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

public class FeeRateCommonServiceTest extends BaseTest {

    @Resource
    private FeeRateCommonService feeRateCommonService;


    @Test
    public void testBuildRestoreFromOrganizationConfig(){
        String configStr = "[\n" +
                "    {\n" +
                "        \"sub_payway_flag\":0,\n" +
                "        \"tags\":[\n" +
                "            \"PQPBJGNJC26N\"\n" +
                "        ],\n" +
                "        \"name\":\"间连扫码新规则套餐\",\n" +
                "        \"trade_app_id\":1,\n" +
                "        \"id\":6364,\n" +
                "        \"mutex_trade_combos\":[\n" +
                "\n" +
                "        ],\n" +
                "        \"short_name\":\"间连扫码新套餐\",\n" +
                "        \"combo_config_level\":\"merchant\",\n" +
                "        \"is_long_term_effective\":1,\n" +
                "        \"description\":\"间连扫码费率新规则套餐，支持单独设置云闪付分级费率\",\n" +
                "        \"records\":[\n" +
                "            {\n" +
                "                \"mutex_trade_combo\":[\n" +
                "\n" +
                "                ],\n" +
                "                \"paywayName\":\"支付宝\",\n" +
                "                \"fee_rate_min_value\":\"0.2\",\n" +
                "                \"payway\":2,\n" +
                "                \"c2b_status\":1,\n" +
                "                \"h5_status\":0,\n" +
                "                \"fee_rate_max\":\"5\",\n" +
                "                \"ladder_fee_rates\":[\n" +
                "\n" +
                "                ],\n" +
                "                \"b2c_status\":1,\n" +
                "                \"fee_rate_default_value\":\"0.38\",\n" +
                "                \"app_status\":0,\n" +
                "                \"wap_status\":1,\n" +
                "                \"trade_combo_id\":6364,\n" +
                "                \"mini_status\":1,\n" +
                "                \"fee_rate_max_value\":\"5\",\n" +
                "                \"id\":7625,\n" +
                "                \"fee_rate_min\":\"0.2\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"mutex_trade_combo\":[\n" +
                "\n" +
                "                ],\n" +
                "                \"paywayName\":\"微信\",\n" +
                "                \"fee_rate_min_value\":\"0.2\",\n" +
                "                \"payway\":3,\n" +
                "                \"c2b_status\":1,\n" +
                "                \"h5_status\":0,\n" +
                "                \"fee_rate_max\":\"5\",\n" +
                "                \"ladder_fee_rates\":[\n" +
                "\n" +
                "                ],\n" +
                "                \"b2c_status\":1,\n" +
                "                \"fee_rate_default_value\":\"0.38\",\n" +
                "                \"app_status\":0,\n" +
                "                \"wap_status\":1,\n" +
                "                \"trade_combo_id\":6364,\n" +
                "                \"mini_status\":1,\n" +
                "                \"fee_rate_max_value\":\"5\",\n" +
                "                \"id\":7626,\n" +
                "                \"fee_rate_min\":\"0.2\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"mutex_trade_combo\":[\n" +
                "\n" +
                "                ],\n" +
                "                \"paywayName\":\"银联云闪付\",\n" +
                "                \"payway\":17,\n" +
                "                \"c2b_status\":1,\n" +
                "                \"h5_status\":0,\n" +
                "                \"ladder_fee_rates\":[\n" +
                "                    {\n" +
                "                        \"min\":0,\n" +
                "                        \"fee_rate_default_value\":0.38,\n" +
                "                        \"max\":1000,\n" +
                "                        \"fee_rate_min_value\":\"0.2\",\n" +
                "                        \"fee_rate_max_value\":\"5.0\",\n" +
                "                        \"fee_rate_min\":\"0.2\",\n" +
                "                        \"fee_rate_max\":\"5.0\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"min\":1000,\n" +
                "                        \"fee_rate_min_value\":\"0.6\",\n" +
                "                        \"fee_rate_max_value\":\"0.6\",\n" +
                "                        \"fee_rate_min\":\"0.6\",\n" +
                "                        \"fee_rate_max\":\"0.60\"\n" +
                "                    }\n" +
                "                ],\n" +
                "                \"b2c_status\":1,\n" +
                "                \"app_status\":0,\n" +
                "                \"wap_status\":1,\n" +
                "                \"trade_combo_id\":6364,\n" +
                "                \"mini_status\":1,\n" +
                "                \"id\":7627\n" +
                "            }\n" +
                "        ],\n" +
                "        \"total\":3\n" +
                "    }\n" +
                "]";
        Map<Integer, ApplyComboDetailParam> map  = feeRateCommonService.buildComboConfig(JsonUtil.decode(configStr, List.class));
        Assert.assertEquals("数量不相等" ,3, map.keySet().size());
        ApplyComboDetailParam alipay = map.get(Payway.ALIPAY2.getCode());
        Assert.assertEquals("支付宝默认费率不相等", "0.38", alipay.getFeeRate());

        ApplyComboDetailParam weixin = map.get(Payway.WEIXIN.getCode());
        Assert.assertEquals("微信默认费率不相等", "0.38", weixin.getFeeRate());

        ApplyComboDetailParam unionpay = map.get(Payway.UNIONPAY.getCode());
        ApplyLadderFeeRate ladderFeeRate = JsonUtil.decode(unionpay.getFeeRate(), ApplyLadderFeeRate.class);
        Assert.assertNotNull("阶梯费率不为空", ladderFeeRate != null);
        List<ApplyLadderFeeRate.LadderFeeRate> value = ladderFeeRate.getValue();
        Assert.assertEquals("阶梯档位数量不一致", 2, value.size());
        for (ApplyLadderFeeRate.LadderFeeRate ladder : value) {
            Double min = ladder.getMin();
            if(min == 0){
                Assert.assertEquals("0-1000档位费率错误", "0.38", ladder.getFeeRate());
            }else if(min == 1000){
                Assert.assertEquals("1000以上档位费率错误", "0.6", ladder.getFeeRate());
            }
        }
    }

    @Test
    public void sendBusinessLog() {
        List list = JsonUtil.decode(argsStr, List.class);
        String merchantId = (String) list.get(0);
        String platform = (String) list.get(1);
        String operator = (String) list.get(2);
        String operatorName = (String) list.get(3);
        String remark = (String) list.get(4);
        Map before = (Map) list.get(5);
        Map after = (Map) list.get(6);
        feeRateCommonService.sendBusinessLog(merchantId, platform, operator, operatorName, remark, before, after);
    }

    public static final String argsStr = "[\n" +
            "    \"5e8b2166-c3d6-402d-85ef-0c6eebcd5030\",\n" +
            "    null,\n" +
            "    \"c0a87336-7ac1-1abc-817a-c1cbd1340000\",\n" +
            "    \"韩淑莹\",\n" +
            "    \"审批编号：SP1953420221025000010 \",\n" +
            "    {\n" +
            "        \"payway\": 21,\n" +
            "        \"merchant_id\": \"5e8b2166-c3d6-402d-85ef-0c6eebcd5030\",\n" +
            "        \"b2c_fee_rate\": \"0.6\",\n" +
            "        \"b2c_formal\": false,\n" +
            "        \"b2c_status\": 0,\n" +
            "        \"b2c_agent_name\": null,\n" +
            "        \"c2b_fee_rate\": \"0.38\",\n" +
            "        \"c2b_formal\": false,\n" +
            "        \"c2b_status\": 1,\n" +
            "        \"c2b_agent_name\": \"1034_21_false_true_0001\",\n" +
            "        \"wap_fee_rate\": \"0.6\",\n" +
            "        \"wap_formal\": false,\n" +
            "        \"wap_status\": 0,\n" +
            "        \"wap_agent_name\": null,\n" +
            "        \"mini_fee_rate\": \"0.6\",\n" +
            "        \"mini_formal\": false,\n" +
            "        \"mini_status\": 0,\n" +
            "        \"mini_agent_name\": null,\n" +
            "        \"h5_formal\": false,\n" +
            "        \"h5_status\": 0,\n" +
            "        \"h5_agent_name\": null,\n" +
            "        \"provider\": 1034,\n" +
            "        \"params.bankcard_fee.credit.fee\": 0.58,\n" +
            "        \"params.bankcard_fee.credit.max\": 1500,\n" +
            "        \"params.bankcard_fee.debit.fee\": 0.48,\n" +
            "        \"params.bankcard_fee.debit.max\": 1800,\n" +
            "        \"params\": {\n" +
            "            \"ladder_status\": 0,\n" +
            "            \"fee_rate_tag\": {\n" +
            "                \"2\": \"3723:\"\n" +
            "            },\n" +
            "            \"bankcard_fee\": {\n" +
            "                \"credit\": {\n" +
            "                    \"fee\": \"0.58\",\n" +
            "                    \"max\": 1500\n" +
            "                },\n" +
            "                \"debit\": {\n" +
            "                    \"fee\": \"0.48\",\n" +
            "                    \"max\": 1800\n" +
            "                }\n" +
            "            },\n" +
            "            \"lakala_open_trade_params\": {\n" +
            "                \"merc_id\": \"8223050581201MT\",\n" +
            "                \"term_id\": \"\",\n" +
            "                \"term_no\": \"\"\n" +
            "            },\n" +
            "            \"lakala_trade_params\": {\n" +
            "                \"lakala_term_id\": \"lkl*************\",\n" +
            "                \"lakala_merc_id\": \"*************\"\n" +
            "            },\n" +
            "            \"store_daily_max_sum_of_trans\": \"100000\",\n" +
            "            \"merchant_daily_max_credit_limit_trans\": null,\n" +
            "            \"merchant_monthly_max_credit_limit_trans\": 2,\n" +
            "            \"merchant_daily_max_sum_of_trans\": 10000,\n" +
            "            \"merchant_single_max_of_tran\": {\n" +
            "                \"2\": {\n" +
            "                    \"1\": 999999999999,\n" +
            "                    \"2\": 999999999999\n" +
            "                },\n" +
            "                \"3\": {}\n" +
            "            },\n" +
            "            \"deposit\": {\n" +
            "                \"weixin\": 1,\n" +
            "                \"alipay\": 1\n" +
            "            },\n" +
            "            \"merchant_daily_payway_max_sum_of_trans\": {\n" +
            "                \"3\": {\n" +
            "                    \"\": \"999999999999\"\n" +
            "                }\n" +
            "            },\n" +
            "            \"category_merchant_single_max_of_tran\": {\n" +
            "                \"106\": \"9010.00\"\n" +
            "            },\n" +
            "            \"payway_day_credit_limits\": {\n" +
            "                \"2\": null,\n" +
            "                \"3\": null\n" +
            "            },\n" +
            "            \"payway_month_credit_limits\": {\n" +
            "                \"2\": null,\n" +
            "                \"3\": null\n" +
            "            },\n" +
            "            \"credit_pay\": null,\n" +
            "            \"use_client_store_sn\": null,\n" +
            "            \"is_sent_store_id\": true,\n" +
            "            \"clearance_provider\": 2,\n" +
            "            \"is_need_refund_fee_flag\": null\n" +
            "        },\n" +
            "        \"app_formal\": false,\n" +
            "        \"app_status\": 0,\n" +
            "        \"ladder_status\": 0,\n" +
            "        \"ladder_fee_rates\": null,\n" +
            "        \"ladder_fee_rate_tag\": null,\n" +
            "        \"fee_rate_tag\": null,\n" +
            "        \"merchant_sn\": \"21690003403111\",\n" +
            "        \"merchant_name\": \"有咖\",\n" +
            "        \"solicitor_id\": \"c97fcf9b-b300-11e5-9987-6c92bf21bb99\",\n" +
            "        \"vendor_id\": \"859d9f5f-af99-11e5-9ec3-00163e00625b\",\n" +
            "        \"solicitor_sn\": \"sctc-**********\",\n" +
            "        \"solicitor_name\": \"测试你好啊1\",\n" +
            "        \"vendor_sn\": \"1\",\n" +
            "        \"vendor_name\": \"shouqianba223\"\n" +
            "    },\n" +
            "    {\n" +
            "        \"payway\": 21,\n" +
            "        \"merchant_id\": \"5e8b2166-c3d6-402d-85ef-0c6eebcd5030\",\n" +
            "        \"b2c_fee_rate\": \"0.6\",\n" +
            "        \"b2c_formal\": false,\n" +
            "        \"b2c_status\": 0,\n" +
            "        \"b2c_agent_name\": null,\n" +
            "        \"c2b_fee_rate\": \"0.38\",\n" +
            "        \"c2b_formal\": false,\n" +
            "        \"c2b_status\": 1,\n" +
            "        \"c2b_agent_name\": \"1034_21_false_true_0001\",\n" +
            "        \"wap_fee_rate\": \"0.6\",\n" +
            "        \"wap_formal\": false,\n" +
            "        \"wap_status\": 0,\n" +
            "        \"wap_agent_name\": null,\n" +
            "        \"mini_fee_rate\": \"0.6\",\n" +
            "        \"mini_formal\": false,\n" +
            "        \"mini_status\": 0,\n" +
            "        \"mini_agent_name\": null,\n" +
            "        \"h5_formal\": false,\n" +
            "        \"h5_status\": 0,\n" +
            "        \"h5_agent_name\": null,\n" +
            "        \"provider\": 1034,\n" +
            "        \"params.bankcard_fee.credit.fee\": 0.55,\n" +
            "        \"params.bankcard_fee.credit.max\": 1500,\n" +
            "        \"params.bankcard_fee.debit.fee\": 0.44,\n" +
            "        \"params.bankcard_fee.debit.max\": 2500,\n" +
            "        \"params\": {\n" +
            "            \"ladder_status\": 0,\n" +
            "            \"fee_rate_tag\": {\n" +
            "                \"2\": \"3723:\"\n" +
            "            },\n" +
            "            \"bankcard_fee\": {\n" +
            "                \"credit\": {\n" +
            "                    \"fee\": \"0.55\",\n" +
            "                    \"max\": 1500\n" +
            "                },\n" +
            "                \"debit\": {\n" +
            "                    \"fee\": \"0.44\",\n" +
            "                    \"max\": 2500\n" +
            "                }\n" +
            "            },\n" +
            "            \"lakala_open_trade_params\": {\n" +
            "                \"merc_id\": \"8223050581201MT\",\n" +
            "                \"term_id\": \"\",\n" +
            "                \"term_no\": \"\"\n" +
            "            },\n" +
            "            \"lakala_trade_params\": {\n" +
            "                \"lakala_term_id\": \"lkl*************\",\n" +
            "                \"lakala_merc_id\": \"*************\"\n" +
            "            },\n" +
            "            \"store_daily_max_sum_of_trans\": \"100000\",\n" +
            "            \"merchant_daily_max_credit_limit_trans\": null,\n" +
            "            \"merchant_monthly_max_credit_limit_trans\": 2,\n" +
            "            \"merchant_daily_max_sum_of_trans\": 10000,\n" +
            "            \"merchant_single_max_of_tran\": {\n" +
            "                \"2\": {\n" +
            "                    \"1\": 999999999999,\n" +
            "                    \"2\": 999999999999\n" +
            "                },\n" +
            "                \"3\": {}\n" +
            "            },\n" +
            "            \"deposit\": {\n" +
            "                \"weixin\": 1,\n" +
            "                \"alipay\": 1\n" +
            "            },\n" +
            "            \"merchant_daily_payway_max_sum_of_trans\": {\n" +
            "                \"3\": {\n" +
            "                    \"\": \"999999999999\"\n" +
            "                }\n" +
            "            },\n" +
            "            \"category_merchant_single_max_of_tran\": {\n" +
            "                \"106\": \"9010.00\"\n" +
            "            },\n" +
            "            \"payway_day_credit_limits\": {\n" +
            "                \"2\": null,\n" +
            "                \"3\": null\n" +
            "            },\n" +
            "            \"payway_month_credit_limits\": {\n" +
            "                \"2\": null,\n" +
            "                \"3\": null\n" +
            "            },\n" +
            "            \"credit_pay\": null,\n" +
            "            \"use_client_store_sn\": null,\n" +
            "            \"is_sent_store_id\": true,\n" +
            "            \"clearance_provider\": 2,\n" +
            "            \"is_need_refund_fee_flag\": null\n" +
            "        },\n" +
            "        \"app_formal\": false,\n" +
            "        \"app_status\": 0,\n" +
            "        \"ladder_status\": 0,\n" +
            "        \"ladder_fee_rates\": null,\n" +
            "        \"ladder_fee_rate_tag\": null,\n" +
            "        \"fee_rate_tag\": null,\n" +
            "        \"merchant_sn\": \"21690003403111\",\n" +
            "        \"merchant_name\": \"有咖\",\n" +
            "        \"solicitor_id\": \"c97fcf9b-b300-11e5-9987-6c92bf21bb99\",\n" +
            "        \"vendor_id\": \"859d9f5f-af99-11e5-9ec3-00163e00625b\",\n" +
            "        \"solicitor_sn\": \"sctc-**********\",\n" +
            "        \"solicitor_name\": \"测试你好啊1\",\n" +
            "        \"vendor_sn\": \"1\",\n" +
            "        \"vendor_name\": \"shouqianba223\"\n" +
            "    }\n" +
            "]";
}
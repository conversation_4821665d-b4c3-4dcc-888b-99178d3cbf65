package com.wosai.trade.impl.bank;

import com.google.common.collect.ImmutableList;
import com.wosai.trade.BaseTest;
import com.wosai.trade.service.FeeRateService;
import com.wosai.trade.service.bank.entity.FeeRateSnapshot;
import com.wosai.trade.service.bank.entity.request.GetCurrentSnapshotRequest;
import com.wosai.trade.service.bank.entity.request.RestoreFeeRateSnapshotRequest;
import com.wosai.trade.service.bank.entity.response.CurrentFeeRateSnapshotResponse;
import com.wosai.trade.service.request.CancelFeeRateRequest;
import com.wosai.trade.util.ConstantUtil;
import com.wosai.trade.util.JsonUtil;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public class BankFeeRateServiceImplTest extends BaseTest {

    @Resource
    private BankFeeRateServiceImpl bankFeeRateService;

    @Resource
    private FeeRateService feeRateService;

    @Test
    public void getFeeRateSnapshot() {
        GetCurrentSnapshotRequest request = new GetCurrentSnapshotRequest();
        request.setMerchantSn("**************");
        CurrentFeeRateSnapshotResponse response = bankFeeRateService.getFeeRateSnapshot(request);
        System.out.println(JsonUtil.encode(response));
    }

    @Test
    public void restoreSnapshot() {
        String merchantSn = "**************";
        GetCurrentSnapshotRequest getRequest = new GetCurrentSnapshotRequest();
        getRequest.setMerchantSn(merchantSn);
        CurrentFeeRateSnapshotResponse currentResponse = bankFeeRateService.getFeeRateSnapshot(getRequest);

        //取消
        List<FeeRateSnapshot> list = currentResponse.getFeeRateSnapshotList();
        list.forEach(snapshot -> {
            CancelFeeRateRequest request = new CancelFeeRateRequest();
            request.setMerchantSn(merchantSn);
            request.setTradeComboId(snapshot.getComboId());
            request.setPayWays(ImmutableList.of(snapshot.getPayWay()));
            request.setAuditSn("取消");
            request.setOperator(ConstantUtil.SYSTEM_NAME);
            request.setOperatorName(ConstantUtil.SYSTEM_NAME);
            feeRateService.cancelFeeRate(request);
        });

        //恢复
        RestoreFeeRateSnapshotRequest restoreRequest = new RestoreFeeRateSnapshotRequest();
        restoreRequest.setMerchantSn(merchantSn);
        restoreRequest.setFeeRateSnapshotList(currentResponse.getFeeRateSnapshotList());
        bankFeeRateService.restoreFeeRateSnapshot(restoreRequest);
        CurrentFeeRateSnapshotResponse currentResponse2 = bankFeeRateService.getFeeRateSnapshot(getRequest);

        Map<Integer, FeeRateSnapshot> resultMap = currentResponse2.getFeeRateSnapshotList().stream().collect(Collectors.toMap(FeeRateSnapshot::getPayWay, Function.identity()));
        for (FeeRateSnapshot snapshot : currentResponse.getFeeRateSnapshotList()) {
            FeeRateSnapshot current = resultMap.get(snapshot.getPayWay());
            Assert.assertNotNull("目标未恢复成功payWay:" + snapshot.getPayWay(), current);
            snapshot.setFeeRateId(null);
            current.setFeeRateId(null);
            Assert.assertEquals("目标未恢复成功payWay:" + snapshot.getPayWay(), snapshot, current);
        }
    }

    @Test
    public void restoreSnapshot0() {
        String str = "{\"feeRateSnapshotList\":[{\"activityId\":3732,\"feeRateId\":9763824,\"level\":\"MERCHANT\",\"comboId\":6749,\"payWay\":2}]}";
        RestoreFeeRateSnapshotRequest restoreRequest = JsonUtil.decode(str, RestoreFeeRateSnapshotRequest.class);
        restoreRequest.setMerchantSn("**************");
        bankFeeRateService.restoreFeeRateSnapshot(restoreRequest);
    }

}
package com.wosai.trade.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.wosai.trade.BaseTest;
import com.wosai.trade.biz.audit.model.ApplyChannelFeeRate;
import com.wosai.trade.repository.dao.MerchantFeeRateDao;
import com.wosai.trade.repository.dao.entity.FeeRateEntity;
import com.wosai.trade.repository.dao.entity.MerchantFeeRateEntity;
import com.wosai.trade.service.request.ActivityBaseRequest;
import com.wosai.trade.service.request.ApplyFeeRateRequest;
import com.wosai.trade.service.request.CancelFeeRateRequest;
import com.wosai.trade.service.result.ListMchFeeRateResult;
import com.wosai.trade.service.result.ListMchFeeStatusResult;
import com.wosai.trade.util.JsonUtil;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

public class FeeRateServiceImplTest extends BaseTest {

    @Resource
    private FeeRateServiceImpl feeRateService;
    @Resource
    private MerchantFeeRateDao merchantFeeRateDao;

    @Test
    public void applyFeeRateOne() {
        ApplyFeeRateRequest request = JsonUtil.decode(applyFeeRateOneRequestStr, ApplyFeeRateRequest.class);
        feeRateService.applyFeeRateOne(request);
    }

    @Test
    public void applyChannelFeeRate() {
        ApplyFeeRateRequest request = JsonUtil.decode(applyChannelFeeRateOneRequestStr, ApplyFeeRateRequest.class);
        feeRateService.applyFeeRateOne(request);
        MerchantFeeRateEntity feeRate = merchantFeeRateDao.selectByMchSnAndComboIdAndPayWay(request.getMerchantSn(), request.getTradeComboId(), 21);
        ApplyChannelFeeRate fee = JsonUtil.decode(request.getApplyFeeRateMap().get("21"), ApplyChannelFeeRate.class);
        List<FeeRateEntity.ChannelFeeRate> src = JsonUtil.decode(JsonUtil.encode(fee.getValue()), new TypeReference<List<FeeRateEntity.ChannelFeeRate>>() {
        });
        Assert.assertEquals(feeRate.buildChannelFeeRates(), src);
    }


    @Test
    public void listMchEffectFeeRates() {
        List<ListMchFeeRateResult> result = feeRateService.listMchEffectFeeRates("21690003218289");
        System.out.println(JsonUtil.encode(result));
        Assert.assertNotNull(result);
    }

    @Test
    public void listStoreEffectFeeRates() {
        List<ListMchFeeRateResult> result = feeRateService.listStoreEffectFeeRates("21690003218289",  "21590000000854327");
        System.out.println(JsonUtil.encode(result));
        Assert.assertNotNull(result);
    }

    @Test
    public void getMerchantBasicAgreementFeeRates() {
        ActivityBaseRequest request = new ActivityBaseRequest();
        request.setMerchantId("3f5cc7109756-cc9a-4834-8342-e479dfac");
        List<ListMchFeeStatusResult> result = feeRateService.getMerchantBasicAgreementFeeRates(request);
        System.out.println(JsonUtil.encode(result));
        Assert.assertNotNull(result);
    }

    @Test
    public void cancelFeeRate() {
        CancelFeeRateRequest request = new CancelFeeRateRequest();
        request.setMerchantSn("21690003462460");
        request.setTradeComboId(5038L);
        request.setOperator("system");
        request.setOperatorName("套餐测试取消");
        request.setAuditSn("取消活动无原因");
        request.setSubStatusId(179L);
        feeRateService.cancelFeeRate(request);
    }

    public static final String applyFeeRateOneRequestStr = "{\n" +
            "            \"merchantSn\": \"21690003254043\",\n" +
            "            \"storeSn\": null,\n" +
            "            \"terminalSn\": null,\n" +
            "            \"auditSn\": \"新增商户入网\",\n" +
            "            \"auditTemplateId\": null,\n" +
            "            \"tradeComboId\": 6916,\n" +
            "            \"applyPartialPayway\": true,\n" +
            "            \"applyFeeRateMap\": {\n" +
            "                \"2\": \"1.5\",\n" +
            "                \"3\": \"{\\\"fee_type\\\":\\\"ladder\\\",\\\"value\\\":[{\\\"fee_rate\\\":\\\"0.25\\\",\\\"max\\\":300,\\\"min\\\":0},{\\\"fee_rate\\\":\\\"0.38\\\",\\\"max\\\":2147483647,\\\"min\\\":300}]}\",\n" +
            "                \"17\":\"{\\\"fee_type\\\":\\\"ladder\\\",\\\"value\\\":[{\\\"fee_rate\\\":\\\"0.25\\\",\\\"max\\\":1000,\\\"min\\\":0},{\\\"fee_rate\\\":\\\"0.38\\\",\\\"max\\\":2147483647,\\\"min\\\":1000}]}\"\n" +
            "            },\n" +
            "            \"platform\": null,\n" +
            "            \"operator\": null,\n" +
            "            \"operatorName\": null,\n" +
            "            \"applyTimeMillis\": null,\n" +
            "            \"check\": true\n" +
            "        }";


    public static final String applyChannelFeeRateOneRequestStr = "{\n" +
            "    \"merchantSn\":\"21690003266766\",\n" +
            "    \"storeSn\":null,\n" +
            "    \"terminalSn\":null,\n" +
            "    \"auditSn\":\"更新银行卡费率值\",\n" +
            "    \"auditTemplateId\":null,\n" +
            "    \"tradeComboId\":3486,\n" +
            "    \"applyPartialPayway\":true,\n" +
            "    \"applyFeeRateMap\":{\n" +
            "        \"21\":\"{\\\"fee_type\\\":\\\"channel\\\",\\\"value\\\":[{\\\"type\\\":\\\"credit\\\",\\\"fee\\\":\\\"0.5\\\",\\\"max\\\":\\\"18\\\"},{\\\"type\\\":\\\"debit\\\",\\\"fee\\\":\\\"0.5\\\",\\\"max\\\":\\\"18\\\"}]}\"\n" +
            "    },\n" +
            "    \"platform\":null,\n" +
            "    \"operator\":null,\n" +
            "    \"operatorName\":null,\n" +
            "    \"applyTimeMillis\":null,\n" +
            "    \"check\":true\n" +
            "}\n";

    public static void main(String[] args) {
        System.out.println(applyChannelFeeRateOneRequestStr);
    }

}
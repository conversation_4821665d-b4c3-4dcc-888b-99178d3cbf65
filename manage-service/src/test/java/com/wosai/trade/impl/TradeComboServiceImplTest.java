package com.wosai.trade.impl;

import com.wosai.trade.BaseTest;
import com.wosai.trade.repository.dao.TradeComboDetailDao;
import com.wosai.trade.repository.dao.entity.TradeComboDetailEntity;
import com.wosai.trade.service.request.AddTradeComboRequest;
import com.wosai.trade.service.request.FilterComboByOrgRequest;
import com.wosai.trade.service.request.ListTradeCombosRequest;
import com.wosai.trade.service.request.UpdateTradeComboRequest;
import com.wosai.trade.service.result.AddTradeComboResult;
import com.wosai.trade.service.result.ComboByOrgResult;
import com.wosai.trade.service.result.ListResult;
import com.wosai.trade.service.result.ListTradeCombosResult;
import com.wosai.trade.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import vo.ApiRequestParam;

import javax.annotation.Resource;

@Slf4j
public class TradeComboServiceImplTest extends BaseTest {

    @Resource
    private TradeComboServiceImpl tradeComboService;
    @Resource
    private TradeComboDetailDao tradeComboDetailDao;

    @Test
    public void tradeComboDetail() {
        TradeComboDetailEntity s = tradeComboDetailDao.selectById(1L);
        System.out.println(s);
        Assert.assertNotNull(s);
    }


    @Test
    public void addTradeCombo() {
        AddTradeComboRequest request = JsonUtil.decode(addJson, AddTradeComboRequest.class);
        AddTradeComboResult result = tradeComboService.addTradeCombo(request);
        System.out.println(result);
        Assert.assertNotNull(result);
    }

    @Test
    public void updateTradeCombo() {
        UpdateTradeComboRequest request = JsonUtil.decode(updateJson, UpdateTradeComboRequest.class);
        boolean result = tradeComboService.updateTradeCombo(request);
        System.out.println(result);
        Assert.assertTrue(result);
    }

    public static final String updateJson = "{\"id\":6226,\"description\":\"阶梯5\",\"short_name\":\"阶梯5\",\"is_long_term_effective\":1,\"expiration_rule_type\":null,\"expiration_date\":null,\"effective_days\":null,\"mutex_trade_combos\":[],\"trade_combo_details\":[{\"id\":7476,\"mutex_trade_combos\":[6184],\"fee_rate_min\":\"0.24\",\"fee_rate_max\":\"0.4\",\"ladder_fee_rates\":null,\"channel_fee_rates\":null,\"b2c_status\":1,\"c2b_status\":1,\"wap_status\":1,\"mini_status\":1,\"h5_status\":1,\"app_status\":1},{\"id\":7477,\"mutex_trade_combos\":[6182],\"fee_rate_min\":null,\"fee_rate_max\":null,\"ladder_fee_rates\":[{\"min\":0.0,\"max\":300.0,\"fee_rate\":null,\"fee_rate_min\":0.25,\"fee_rate_max\":0.38},{\"min\":300.0,\"max\":null,\"fee_rate\":null,\"fee_rate_min\":0.4,\"fee_rate_max\":0.52}],\"channel_fee_rates\":null,\"b2c_status\":1,\"c2b_status\":1,\"wap_status\":1,\"mini_status\":1,\"h5_status\":1,\"app_status\":1},{\"id\":7478,\"mutex_trade_combos\":[6170],\"fee_rate_min\":null,\"fee_rate_max\":null,\"ladder_fee_rates\":null,\"channel_fee_rates\":[{\"type\":\"credit\",\"high_min\":\"100\",\"high_max\":null,\"fee_rate_min\":\"0.3\",\"fee_rate_max\":\"0.4\"},{\"type\":\"debit\",\"high_min\":\"2\",\"high_max\":\"100\",\"fee_rate_min\":\"0.2\",\"fee_rate_max\":\"0.3\"}],\"b2c_status\":0,\"c2b_status\":1,\"wap_status\":0,\"mini_status\":0,\"h5_status\":0,\"app_status\":0}]}";

    public static final String addJson = "{\n" +
            "    \"id\":3890,\n" +
            "    \"description\":\"测试用\",\n" +
            "    \"short_name\":\"测试用\",\n" +
            "    \"is_long_term_effective\":1,\n" +
            "    \"expiration_rule_type\":null,\n" +
            "    \"expiration_date\":null,\n" +
            "    \"effective_days\":null,\n" +
            "    \"mutex_trade_combos\":[\n" +
            "        3791\n" +
            "    ],\n" +
            "    \"trade_combo_details\":[\n" +
            "        {\n" +
            "            \"id\":4715,\n" +
            "            \"mutex_trade_combos\":[\n" +
            "\n" +
            "            ],\n" +
            "            \"fee_rate_min\":null,\n" +
            "            \"fee_rate_max\":null,\n" +
            "            \"ladder_fee_rates\":null,\n" +
            "            \"channel_fee_rates\":[\n" +
            "                {\n" +
            "                    \"type\":\"credit\",\n" +
            "                    \"high_min\":null,\n" +
            "                    \"high_max\":null,\n" +
            "                    \"fee_rate_min\":\"0.1\",\n" +
            "                    \"fee_rate_max\":\"1\"\n" +
            "                },\n" +
            "                {\n" +
            "                    \"type\":\"debit\",\n" +
            "                    \"high_min\":\"100\",\n" +
            "                    \"high_max\":null,\n" +
            "                    \"fee_rate_min\":\"0.4\",\n" +
            "                    \"fee_rate_max\":\"0.4\"\n" +
            "                }\n" +
            "            ],\n" +
            "            \"b2c_status\":0,\n" +
            "            \"c2b_status\":1,\n" +
            "            \"wap_status\":0,\n" +
            "            \"mini_status\":0,\n" +
            "            \"h5_status\":0,\n" +
            "            \"app_status\":0\n" +
            "        }\n" +
            "    ]\n" +
            "}";

    @Test
    public void listTradeCombos() {
        String jsonStr = "{\n" +
                "\"page\":1,\n" +
                "\"page_size\":999,\n" +
                "\"status\":1,\n" +
                "\"type\":2\n" +
                "}";
        ListTradeCombosRequest request = JsonUtil.decode(jsonStr, ListTradeCombosRequest.class);
        ListResult<ListTradeCombosResult> list = tradeComboService.listTradeCombos(request);
        log.info("list:{}", JsonUtil.encode(list));
        Assert.assertNotNull("套餐列表页为空", list);
    }

    @Test
    public void filterComboByOrg() {
        FilterComboByOrgRequest request = new FilterComboByOrgRequest();
        request.setOrgId("85bdb5889fdba96980ebbf75");
        ApiRequestParam requestParam = new ApiRequestParam();
        requestParam.setBodyParams(request);
        ComboByOrgResult result = tradeComboService.filterComboByOrg(requestParam);
        System.out.println(JsonUtil.encode(result));
        Assert.assertNotNull("结果为空", result);
    }
}
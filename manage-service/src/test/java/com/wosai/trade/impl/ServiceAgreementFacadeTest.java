package com.wosai.trade.impl;

import com.google.common.collect.ImmutableList;
import com.wosai.trade.BaseTest;
import com.wosai.trade.service.result.StoreBizServiceAgreement;
import com.wosai.trade.service.servicefee.model.ServiceFeeAgreement;
import com.wosai.trade.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;

@Slf4j
public class ServiceAgreementFacadeTest extends BaseTest {

    @Resource
    private ServiceAgreementFacade facade;


    @Test
    public void merge() {

    }

    @Test
    public void testMerge() {
        String merchantId = "1234";
        Map<String, String> storeIdNameMap = new HashMap<>();
        storeIdNameMap.put("store1", "门店一");
        storeIdNameMap.put("store2", "门店二");

        List<StoreBizServiceAgreement> oldAgreementList = new ArrayList<>();
        StoreBizServiceAgreement oldAgreement1 = new StoreBizServiceAgreement("store2", "门店二");
        ServiceFeeAgreement oldRecord1 = new ServiceFeeAgreement();
        oldRecord1.setTradeAppId(1L);
        oldRecord1.setTradeAppName("应用一");
        oldRecord1.setServiceFeeName("费用一");
        oldRecord1.setProfitShareRatio("0.37");
        oldAgreement1.getRecords().add(oldRecord1);
        oldAgreementList.add(oldAgreement1);

        List<StoreBizServiceAgreement> newAgreementList = facade.merge(merchantId, storeIdNameMap, oldAgreementList);
        Map<String, StoreBizServiceAgreement> actualMap = new HashMap<>();
        for (StoreBizServiceAgreement agreement : newAgreementList) {
            actualMap.put(agreement.getStoreId(), agreement);
        }

        assertEquals(actualMap, actualMap);
    }

    @Test
    public void getMerchantServiceAgreementFeeRates() {
        List<StoreBizServiceAgreement> list = facade.getMerchantServiceAgreementFeeRates("c1749c56-c417-4f44-a09c-cc1f13fcc95f", ImmutableList.of("d672831b-ff84-40eb-80db-adc0517a1ded"));
        log.info("reuslt:{}", JsonUtil.encode(list));
    }
}
package com.wosai.trade.impl;

import com.wosai.app.dto.pwd.PasswordTokenReq;
import com.wosai.app.dto.pwd.UserManagerPasswordReq;
import com.wosai.app.service.ManagerPasswordService;
import com.wosai.trade.BaseTest;
import com.wosai.trade.model.dal.RefundApprovalQueryDalParam;
import com.wosai.trade.repository.dao.RefundApprovalRecordEntityMapper;
import com.wosai.trade.repository.dao.entity.RefundApprovalRecordEntity;
import com.wosai.trade.service.SwitchService;
import com.wosai.trade.service.activity.request.*;
import com.wosai.trade.service.activity.response.RefundApprovalResp;
import com.wosai.trade.service.enums.RefundEnum;
import com.wosai.trade.service.request.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class RefundApprovalImplTest extends BaseTest {

    @Autowired
    private RefundApprovalServiceImpl refundApprovalImpl;


    @Autowired
    private SwitchService switchService;

    @Autowired
    private ManagerPasswordService managerPasswordService;


    @Test
    public void applyRefund() {
        RefundOrderRequest request = RefundOrderRequest.builder()
                .businessOrderSn("7894259210667897")
                .orderStoreSn("21590000000739848")
                .orderSnList(Arrays.asList("7894259210667897"))
                .refundStoreSn("21590000000739848")
                .applyAmount(1L)
                .tradeApp(1)
                .paySource("MULTI_FUNCTION_CASH")
                .remark("退款备注")
                .businessExtra(null)
                .businessShowInfo(null)
                .build();
        HashMap userInfo = new HashMap();
        userInfo.put("uc_user_id", "494003ba-cd36-4831-ad0c-89f409e44f7d");
        userInfo.put("merchant_id", "00be709835ef-887b-0b84-cce4-2da0906b");
        refundApprovalImpl.applyRefund(request,userInfo);
    }




    @Test
    public void auditRefund() {
        AuditRefundRequest request = AuditRefundRequest.builder()
                .applyId(330L)
                .status(2)
                .build();
        HashMap userInfo = new HashMap();
        userInfo.put("uc_user_id", "30a9c201-2f75-4c97-bd78-a77377c1db2f");
        userInfo.put("merchant_id", "00be709835ef-887b-0b84-cce4-2da0906b");
        refundApprovalImpl.auditRefund(request, userInfo);
    }


    @Test
    public void queryRefundApproval() {
        QueryRefundApprovalRequest request = QueryRefundApprovalRequest.builder()
                .applyId(330L)
                .tradeApp(1)
                .page(1)
                .pageSize(100)
                .uc_user_id("359299de-e991-434c-bda9-b27da562b68b")
                .build();
        List<RefundApprovalResp> refundApprovalResps = refundApprovalImpl.queryRefundApproval(request);
        log.info("refundApprovalResps:{}", Arrays.toString(refundApprovalResps.toArray()));

    }


    @Test
    public void applyRefund2() {
        RefundOrderRequest request = RefundOrderRequest.builder()
                .businessOrderSn("7894259299544674")
                .orderStoreSn("21590000000854327")
                .refundStoreSn("21590000000854327")
                .applyAmount(1L)
                .tradeApp(1)
                .remark("退款备注")
                .businessExtra(null)
                .businessShowInfo(null)
                .build();
        HashMap userInfo = new HashMap();
        userInfo.put("uc_user_id", "fc87e85e-6d82-4893-976c-6c4a18bf5407");
        userInfo.put("merchant_id", "3498bc4b-1181-4a49-961e-5bd2102d5906");
        refundApprovalImpl.applyRefund(request,userInfo);
    }

    @Test
    public void cancelRefundApproval() {
        CancelRefundRequest request = CancelRefundRequest.builder()
                .applyId(204L)
                .build();
        HashMap userInfo = new HashMap();
        userInfo.put("uc_user_id", "fc87e85e-6d82-4893-976c-6c4a18bf5407");
        refundApprovalImpl.cancelRefund(request,userInfo);
    }



    @Test
    public void innerCancelRefund() {
        refundApprovalImpl.innerCancelRefund(330L);
    }


    @Test
    public void queryExpireRefundApproval() {
        refundApprovalImpl.queryExpireRefundApproval();
    }



    @Test
    public void getMerchantUserSimpleInfo() {
        MerchantRequest request = MerchantRequest.builder()
                .merchant_id("3498bc4b-1181-4a49-961e-5bd2102d5906")
                .build();
        refundApprovalImpl.getMerchantUserSimpleInfo(request);
    }


    @Test
    public void isBoss() {
        refundApprovalImpl.isBoss("065bacdb-ca50-44b7-9f29-a3712a7b9d08", "3498bc4b-1181-4a49-961e-5bd2102d5906");
    }



    @Test
    public void testQueryRefundApprovalSwitch(){
        int status = switchService.queryRefundApprovalSwitch("000011dd27c4-9599-1174-4184-e1cfd033");
        log.info("status:{}", status);

    }

    @Test

    public void testOpenRefundApprovalSwitch(){
        Map<String, Object> request = new HashMap<>();
        request.put("merchant_id","000011dd27c4-9599-1174-4184-e1cfd03");
        request.put("uc_user_id","zpf");
        request.put("account_phone","***********");
        switchService.openRefundApprovalSwitch(request);

    }


    @Test

    public void testCloseRefundApprovalSwitch(){

        Map<String, Object> request = new HashMap<>();
        request.put("merchant_id","000011dd27c4-9599-1174-4184-e1cfd03");
        request.put("uc_user_id","zpf");
        request.put("account_phone","***********");

        switchService.closeRefundApprovalSwitch(request);

    }


    @Test
    public void testSubmitRefundApproval() {
        SubmitRefundRequest request = SubmitRefundRequest.builder().applyId(14L).build();
        refundApprovalImpl.submitRefundApproval(request);
    }

    @Test
    public void testRefundPassWord(){
        UserManagerPasswordReq userManagerPasswordReq = new UserManagerPasswordReq();
        userManagerPasswordReq.setUc_user_id("359299de-e991-434c-bda9-b27da562b68b");
        userManagerPasswordReq.setMerchant_id("b6b9eee6-9182-46f3-9c39-ae01eaedc31e");
        userManagerPasswordReq.setMerchant_user_id("'f3ba3276-925e-4911-b967-e938c337ed71'");
        userManagerPasswordReq.setRole("cashier");
        userManagerPasswordReq.setPassword("147258");
        userManagerPasswordReq.setScene("refund");
//        Map map = managerPasswordService.checkPassword(userManagerPasswordReq);
        PasswordTokenReq passWordTokenReq = PasswordTokenReq.builder()
                .pwd_token("aead17b4-3c14-4695-af08-a22484347ac0").build();
        boolean b = managerPasswordService.checkToken(passWordTokenReq);
        log.info("校验结果:{}", b);


    }








}


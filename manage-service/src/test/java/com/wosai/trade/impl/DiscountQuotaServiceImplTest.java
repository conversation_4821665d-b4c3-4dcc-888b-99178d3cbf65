//package com.wosai.trade.impl;
//
//import com.alibaba.fastjson.JSONObject;
//import com.fasterxml.jackson.core.JsonProcessingException;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.wosai.common.utils.WosaiMapUtils;
//import com.wosai.data.bean.BeanUtil;
//import com.wosai.data.dao.DaoConstants;
//import com.wosai.trade.BaseTest;
//import com.wosai.trade.biz.activity.TransactionBiz;
//import com.wosai.trade.constant.TransactionConstants;
//import com.wosai.trade.service.activity.response.DiscountQuotaResponse;
//import com.wosai.trade.util.AvroBeanHelper;
//import com.wosai.upay.core.model.TransactionParam;
//import com.wosai.upay.merchant.contract.exception.ContractBizException;
//import com.wosai.upay.model.kafka.Transaction;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//
//import java.nio.ByteBuffer;
//import java.util.HashMap;
//import java.util.Map;
//
//import static com.wosai.upay.core.model.TransactionParam.DISCOUNT_QUOTA_FEE_RATE_TAG;
//
//@Slf4j
//public class DiscountQuotaServiceImplTest extends BaseTest {
//    @Autowired
//    private DiscountQuotaServiceImpl service;
//
//    @Autowired
//    private TransactionBiz applyBiz;
//
//
//    @Test
//    public void test() {
//        Map<String, Object> transaction = new HashMap<>();
//        transaction.put("id", "t000018090221");
//        transaction.put("original_amount", 10000);
//
//        Map<String, Object> config = new HashMap<>();
//
//
//        config.put("trade_app", "1");
//        config.put("merchant_sn", "21690003219419");
//        config.put("store_sn", "");
//        config.put("terminal_sn", "");
//
//        transaction.put("config_snapshot", config);
//        transaction.put("merchant_id", "e3bbc5ce-b9ee-403d-b3c6-480c807462f4");
//
//        DiscountQuotaResponse response = service.getDiscountQuota(2357L, transaction);
//        System.out.println(JSONObject.toJSONString(response));
//    }
//
//    @Test
//    public void test001() {
//        Map<String, Object> transaction = new HashMap<>();
//        transaction.put("id", "t000018090221");
//        transaction.put("original_amount", 10000);
//
//        Map<String, Object> config = new HashMap<>();
//
//
//        config.put("trade_app", "1");
//        config.put("merchant_sn", "21690003219419");
//        config.put("store_sn", "");
//        config.put("terminal_sn", "");
//
//        transaction.put("config_snapshot", config);
//        transaction.put("merchant_id", "e3bbc5ce-b9ee-403d-b3c6");
//
//        DiscountQuotaResponse response = service.canUsable(2357L, transaction);
//        System.out.println(JSONObject.toJSONString(response));
//    }
//
//
//    @Test
//    public void test002() {
//        Map<String, Object> transaction = new HashMap<>();
//        transaction.put("id", "t000018090221002");
//        transaction.put("original_amount", 10000);
//
//        Map<String, Object> config = new HashMap<>();
//
//
//        config.put("trade_app", "1");
//        config.put("merchant_sn", "21690003219419");
//        config.put("store_sn", "");
//        config.put("terminal_sn", "");
//
//        transaction.put("config_snapshot", config);
//        transaction.put("merchant_id", "e3bbc5ce-b9ee-403d-b3c6-480c807462f4");
//
//        DiscountQuotaResponse response = service.canUsable(2357L, transaction);
//        System.out.println(JSONObject.toJSONString(response));
//    }
//
//
//    @Test
//    public void payFail() {
//
//        Transaction transaction = new Transaction();
//
//        transaction.setId("t0000164");
//        Map tradeParams = new HashMap();
//        tradeParams.put(DISCOUNT_QUOTA_FEE_RATE_TAG, "10011");
//
//        Map map = new HashMap();
//        map.put("id", "t0000164");
//        applyBiz.payFail(tradeParams, map);
//    }
//
//
//    @Test
//    public void paySuccess() {
//
//        Transaction transaction = new Transaction();
//
//        transaction.setId("t000018090");
//        transaction.setOriginalAmount(1000L);
//        transaction.setMerchantId("43602521-7d64-438d-8a21-4b72260cd754");
//
//        Map tradeParams = new HashMap();
//        tradeParams.put(DISCOUNT_QUOTA_FEE_RATE_TAG, "10011");
//        tradeParams.put(TransactionParam.DISCOUNT_QUOTA_FEE_RATE, "0.1");
//
//
//        Map snp = new HashMap();
//        snp.put("trade_app", "1");
//        snp.put("merchant_sn", "21690003336060");
//
//        Map extraOutFields = new HashMap();
//        extraOutFields.put("combo_id", "2396");
//
//
//        applyBiz.paySuccess(AvroBeanHelper.getTransactionMapFromBean(transaction), snp, extraOutFields);
//    }
//
//    /**
//     * 活动报名通过 未生效
//     */
//    @Test
//    public void paySuccess001() {
//
//        Transaction transaction = new Transaction();
//
//        transaction.setId("t0000165001");
//        transaction.setOriginalAmount(1000L);
//        transaction.setMerchantId("f835bfe3-74c6-498e-8c14-df700de9858c");
//
//        Map tradeParams = new HashMap();
//
//        Map snp = new HashMap();
//        snp.put("trade_app", "1");
//        snp.put("merchant_sn", "21690003336094");
//
//        Map extraOutFields = new HashMap();
//        extraOutFields.put("combo_id", "1");
//
////        for (int i = 0; i < 99; i++) {
//        applyBiz.paySuccess(AvroBeanHelper.getTransactionMapFromBean(transaction), snp, extraOutFields);
//
////        }
//
//    }
//
//    /**
//     * 活动报名通过 未生效
//     */
//    @Test
//    public void paySuccess002() {
//
//        Transaction transaction = new Transaction();
//
//        transaction.setId("t0000165001");
//        transaction.setOriginalAmount(1000L);
//        transaction.setMerchantId("aae8abbb-7888-4732-8231-225852762a2e");
//
//        Map tradeParams = new HashMap();
//
//        Map snp = new HashMap();
//        snp.put("trade_app", "1");
//        snp.put("merchant_sn", "21690003336056");
//
//        Map extraOutFields = new HashMap();
//        extraOutFields.put("combo_id", "2616");
//
////        for (int i=0;i<450;i++){
//        applyBiz.paySuccess(AvroBeanHelper.getTransactionMapFromBean(transaction), snp, extraOutFields);
////        }
//
//    }
//
//    @Test
//    public void paySuccess003() throws JsonProcessingException {
//
//        Map transactionMap = JSONObject.parseObject(s, Map.class);
//
//        log.info("流水信息{}", JSONObject.toJSONString(transactionMap));
//        String tid = WosaiMapUtils.getString(transactionMap, com.wosai.upay.common.dao.DaoConstants.ID);
//        try {
//            Integer type = WosaiMapUtils.getInteger(transactionMap, "type");
//            //支付状态
//            Integer status = WosaiMapUtils.getInteger(transactionMap, "status");
//            if (TransactionConstants.TYPE_PAYMENT != type) {
//                log.info("tid {},{}类型不处理", tid, type);
//                return;
//            }
//
//            //config_snapshot
//            Map sna = WosaiMapUtils.getMap(transactionMap, "config_snapshot");
//            //额外字段
//            Map extraOutFields = WosaiMapUtils.getMap(transactionMap, "extra_out_fields");
//
//            //支付成功
//            if (TransactionConstants.STATUS_SUCCESS == status) {
//                applyBiz.paySuccess(transactionMap, sna, extraOutFields);
//
//            } else {
//                //支付失败
//                applyBiz.payFail(extraOutFields, transactionMap);
//            }
//        } catch (ContractBizException e) {
//            log.error("tid {} 业务异常", tid, e);
//        } catch (Exception e) {
//            log.error("tid {} 系统异常", tid, e);
//            throw e;
//        }
//
//    }
//
//    @Test
//    public void paySuccess004() throws JsonProcessingException {
//
//        Map transactionMap = JSONObject.parseObject(t****************, Map.class);
//
//        log.info("流水信息{}", JSONObject.toJSONString(transactionMap));
//        String tid = WosaiMapUtils.getString(transactionMap, com.wosai.upay.common.dao.DaoConstants.ID);
//        try {
//            Integer type = WosaiMapUtils.getInteger(transactionMap, "type");
//            //支付状态
//            Integer status = WosaiMapUtils.getInteger(transactionMap, "status");
//            if (TransactionConstants.TYPE_PAYMENT != type) {
//                log.info("tid {},{}类型不处理", tid, type);
//                return;
//            }
//
//            //config_snapshot
//            Map sna = WosaiMapUtils.getMap(transactionMap, "config_snapshot");
//            //额外字段
//            Map extraOutFields = WosaiMapUtils.getMap(transactionMap, "extra_out_fields");
//
//            //支付成功
//            if (TransactionConstants.STATUS_SUCCESS == status) {
//                applyBiz.paySuccess(transactionMap, sna, extraOutFields);
//
//            } else {
//                //支付失败
//                applyBiz.payFail(extraOutFields, transactionMap);
//            }
//        } catch (ContractBizException e) {
//            log.error("tid {} 业务异常", tid, e);
//        } catch (Exception e) {
//            log.error("tid {} 系统异常", tid, e);
//            throw e;
//        }
//
//    }
//
//
//    String t**************** = "{\n" +
//            "    \"subject\": \"pay test\",\n" +
//            "    \"received_amount\": 19000,\n" +
//            "    \"buyer_login\": \"wx1234567890\",\n" +
//            "    \"merchant_id\": \"15ac0b8f-ef6f-4ee2-9ce2-f0da90725c11\",\n" +
//            "    \"mtime\": *************,\n" +
//            "    \"type\": 30,\n" +
//            "    \"extended_params\": {\n" +
//            "        \n" +
//            "    },\n" +
//            "    \"tsn\": \"****************\",\n" +
//            "    \"extra_out_fields\": {\n" +
//            "        \"quota_fee_rate_tag\": \"10104\",\n" +
//            "        \"payments\": [\n" +
//            "            {\n" +
//            "                \"type\": \"BANKCARD_CREDIT\",\n" +
//            "                \"origin_type\": \"ICBC_CREDIT\",\n" +
//            "                \"amount\": 18900\n" +
//            "            },\n" +
//            "            {\n" +
//            "                \"type\": \"DISCOUNT_CHANNEL\",\n" +
//            "                \"origin_type\": \"COUPON\",\n" +
//            "                \"amount\": 100,\n" +
//            "                \"source\": \"93879387941887891360890082239798\"\n" +
//            "            }\n" +
//            "        ],\n" +
//            "        \"combo_id\": \"2629\",\n" +
//            "        \"weixin_appid\": \"wx72534f3638c59073\",\n" +
//            "        \"quota_fee_rate\": \"0.4\"\n" +
//            "    },\n" +
//            "    \"provider\": 1016,\n" +
//            "    \"original_amount\": 19000,\n" +
//            "    \"ctime\": *************,\n" +
//            "    \"id\": \"t****************\",\n" +
//            "    \"terminal_id\": \"8b723b1b-485a-4c9b-a873-d8f263537151\",\n" +
//            "    \"client_tsn\": \"1642063921133662496\",\n" +
//            "    \"store_id\": \"c72cb599-4f72-403b-bad1-825b40e329d7\",\n" +
//            "    \"provider_error_info\": {\n" +
//            "        \"pay\": {\n" +
//            "            \"return_msg\": \"处理成功\",\n" +
//            "            \"result_code\": \"SUCCESS\",\n" +
//            "            \"return_code\": \"SUCCESS\"\n" +
//            "        }\n" +
//            "    },\n" +
//            "    \"extra_params\": {\n" +
//            "        \"barcode\": \"1323179243438001109\"\n" +
//            "    },\n" +
//            "    \"payway\": 3,\n" +
//            "    \"version\": 0,\n" +
//            "    \"finish_time\": 1657595292076,\n" +
//            "    \"sub_payway\": 1,\n" +
//            "    \"config_snapshot\": {\n" +
//            "        \"clearance_provider\": 2,\n" +
//            "        \"latitude\": \"30.274084\",\n" +
//            "        \"merchant_id\": \"15ac0b8f-ef6f-4ee2-9ce2-f0da90725c11\",\n" +
//            "        \"merchant_sn\": \"21690003271590\",\n" +
//            "        \"up_direct_trade_params\": {\n" +
//            "            \"public_key\": \"enn@1Zynb<e\\\"ZbZZh8b8y1fbf\\\"@e1[h<e'z`\",\n" +
//            "            \"weixin_sub_appsecret\": \"1e@\\\"e'''y8eh}8Z}y`1yy[h@h[e8eZe[\",\n" +
//            "            \"fee_rate_tag\": {\n" +
//            "                \"1\": \"2629:\"\n" +
//            "            },\n" +
//            "            \"fee\": 8,\n" +
//            "            \"rece_org_no\": \"36002013293\",\n" +
//            "            \"cert_id\": \"**********\",\n" +
//            "            \"active\": true,\n" +
//            "            \"weixin_appid\": \"wx3819a8bec7f2861b\",\n" +
//            "            \"weixin_mini_sub_appid\": \"\",\n" +
//            "            \"fee_rate\": \"0.04\",\n" +
//            "            \"weixin_sub_mch_id\": \"1642124712004\",\n" +
//            "            \"liquidation_next_day\": true,\n" +
//            "            \"weixin_sub_appid\": \"wx72534f3638c59073\",\n" +
//            "            \"quota_fee_rate_tag\": \"10104\",\n" +
//            "            \"up_private_key\": \"`'`'1`h}b[fn<bZZhyb[<<hbf\\\"@e1[\\\"<hn<y\",\n" +
//            "            \"weixin_mch_id\": \"**********\",\n" +
//            "            \"weixin_mini_sub_appsecret\": \"\",\n" +
//            "            \"weixin_appkey\": \"&H51$f&GH1U8yfUZ5U8`Z&H`n<nH$<U8\",\n" +
//            "            \"channel_id\": \"32631798\",\n" +
//            "            \"provider_mch_id\": \"2c5314d3-b07d-4858-a394-618b4da056c8\",\n" +
//            "            \"quota_fee_rate\": \"0.4\"\n" +
//            "        },\n" +
//            "        \"pay_status\": 1,\n" +
//            "        \"store_name\": \"业务开通门店名称01140944\",\n" +
//            "        \"currency\": \"CNY\",\n" +
//            "        \"terminal_id\": \"8b723b1b-485a-4c9b-a873-d8f263537151\",\n" +
//            "        \"terminal_sn\": \"210100001634700002417245\",\n" +
//            "        \"longitude\": \"120.15507\",\n" +
//            "        \"store_id\": \"c72cb599-4f72-403b-bad1-825b40e329d7\",\n" +
//            "        \"store_sn\": \"21590000000900184\",\n" +
//            "        \"channel_name\": \"上海收钱吧互联网科技股份有限公司\",\n" +
//            "        \"store_city\": \"绵阳市\",\n" +
//            "        \"trade_app\": \"1\",\n" +
//            "        \"terminal_vendor_app_appid\": \"2020091800000001\",\n" +
//            "        \"merchant_name\": \"业务开通01140944\",\n" +
//            "        \"terminal_name\": \"test-1117\",\n" +
//            "        \"district_code\": \"510700\",\n" +
//            "        \"vendor_id\": \"859d9f5f-af99-11e5-9ec3-00163e00625b\",\n" +
//            "        \"common_switch\": \"00000012222222222222222222222222\",\n" +
//            "        \"merchant_country\": \"CHN\"\n" +
//            "    },\n" +
//            "    \"deleted\": false,\n" +
//            "    \"effective_amount\": 19000,\n" +
//            "    \"paid_amount\": 18900,\n" +
//            "    \"trade_no\": \"12522411939547481290409257623646\",\n" +
//            "    \"channel_finish_time\": 1657595292000,\n" +
//            "    \"order_id\": \"o****************\",\n" +
//            "    \"buyer_uid\": \"1234567890987654321\",\n" +
//            "    \"order_sn\": \"****************\",\n" +
//            "    \"status\": 2000\n" +
//            "}";
//
//
//    public static final ObjectMapper objectMapper = new ObjectMapper();
//
//
//    public static Transaction getTransactionBeanFromMap(Map<String, Object> transactionMap) throws JsonProcessingException {
//        Transaction transaction = new Transaction();
//        transaction.setId(BeanUtil.getPropString(transactionMap, DaoConstants.ID));
//        transaction.setTsn(BeanUtil.getPropString(transactionMap, com.wosai.upay.model.dao.Transaction.TSN));
//        transaction.setClientTsn(BeanUtil.getPropString(transactionMap, com.wosai.upay.model.dao.Transaction.CLIENT_TSN));
//        transaction.setType(BeanUtil.getPropInt(transactionMap, com.wosai.upay.model.dao.Transaction.TYPE));
//        transaction.setSubject(BeanUtil.getPropString(transactionMap, com.wosai.upay.model.dao.Transaction.SUBJECT));
//        transaction.setBody(BeanUtil.getPropString(transactionMap, com.wosai.upay.model.dao.Transaction.BODY));
//        transaction.setStatus(BeanUtil.getPropInt(transactionMap, com.wosai.upay.model.dao.Transaction.STATUS));
//        transaction.setEffectiveAmount(BeanUtil.getPropLong(transactionMap, com.wosai.upay.model.dao.Transaction.EFFECTIVE_AMOUNT));
//        transaction.setOriginalAmount(BeanUtil.getPropLong(transactionMap, com.wosai.upay.model.dao.Transaction.ORIGINAL_AMOUNT));
//        transaction.setPaidAmount(BeanUtil.getPropLong(transactionMap, com.wosai.upay.model.dao.Transaction.PAID_AMOUNT));
//        transaction.setReceivedAmount(BeanUtil.getPropLong(transactionMap, com.wosai.upay.model.dao.Transaction.RECEIVED_AMOUNT));
//        transaction.setItems(ByteBuffer.wrap(objectMapper.writeValueAsBytes(transactionMap.get(com.wosai.upay.model.dao.Transaction.ITEMS))));
//        transaction.setBuyerUid(BeanUtil.getPropString(transactionMap, com.wosai.upay.model.dao.Transaction.BUYER_UID));
//        transaction.setBuyerLogin(BeanUtil.getPropString(transactionMap, com.wosai.upay.model.dao.Transaction.BUYER_LOGIN));
//        transaction.setMerchantId(BeanUtil.getPropString(transactionMap, com.wosai.upay.model.dao.Transaction.MERCHANT_ID));
//        transaction.setStoreId(BeanUtil.getPropString(transactionMap, com.wosai.upay.model.dao.Transaction.STORE_ID));
//        transaction.setTerminalId(BeanUtil.getPropString(transactionMap, com.wosai.upay.model.dao.Transaction.TERMINAL_ID));
//        transaction.setOperator(BeanUtil.getPropString(transactionMap, com.wosai.upay.model.dao.Transaction.OPERATOR));
//        transaction.setOrderSn(BeanUtil.getPropString(transactionMap, com.wosai.upay.model.dao.Transaction.ORDER_SN));
//        transaction.setOrderId(BeanUtil.getPropString(transactionMap, com.wosai.upay.model.dao.Transaction.ORDER_ID));
//        transaction.setProvider(BeanUtil.getPropInt(transactionMap, com.wosai.upay.model.dao.Transaction.PROVIDER));
//        transaction.setPayway(BeanUtil.getPropInt(transactionMap, com.wosai.upay.model.dao.Transaction.PAYWAY));
//        transaction.setSubPayway(BeanUtil.getPropInt(transactionMap, com.wosai.upay.model.dao.Transaction.SUB_PAYWAY));
//        transaction.setTradeNo(BeanUtil.getPropString(transactionMap, com.wosai.upay.model.dao.Transaction.TRADE_NO));
//        transaction.setProductFlag(BeanUtil.getPropString(transactionMap, com.wosai.upay.model.dao.Transaction.PRODUCT_FLAG));
//        transaction.setExtraParams(ByteBuffer.wrap(objectMapper.writeValueAsBytes(transactionMap.get(com.wosai.upay.model.dao.Transaction.EXTRA_PARAMS))));
//        transaction.setExtraOutFields(ByteBuffer.wrap(objectMapper.writeValueAsBytes(transactionMap.get(com.wosai.upay.model.dao.Transaction.EXTRA_OUT_FIELDS))));
//        transaction.setExtendedParams(ByteBuffer.wrap(objectMapper.writeValueAsBytes(transactionMap.get(com.wosai.upay.model.dao.Transaction.EXTENDED_PARAMS))));
//        transaction.setReflect(ByteBuffer.wrap(objectMapper.writeValueAsBytes(transactionMap.get(com.wosai.upay.model.dao.Transaction.REFLECT))));
//        transaction.setConfigSnapshot(ByteBuffer.wrap(objectMapper.writeValueAsBytes(transactionMap.get(com.wosai.upay.model.dao.Transaction.CONFIG_SNAPSHOT))));
//        transaction.setFinishTime(BeanUtil.getPropLong(transactionMap, com.wosai.upay.model.dao.Transaction.FINISH_TIME));
//        transaction.setChannelFinishTime(BeanUtil.getPropLong(transactionMap, com.wosai.upay.model.dao.Transaction.CHANNEL_FINISH_TIME));
//        transaction.setBizErrorCode(ByteBuffer.wrap(objectMapper.writeValueAsBytes(transactionMap.get(com.wosai.upay.model.dao.Transaction.BIZ_ERROR_CODE))));
//        transaction.setProviderErrorInfo(ByteBuffer.wrap(objectMapper.writeValueAsBytes(transactionMap.get(com.wosai.upay.model.dao.Transaction.PROVIDER_ERROR_INFO))));
//        transaction.setCtime(BeanUtil.getPropLong(transactionMap, DaoConstants.CTIME));
//        transaction.setMtime(BeanUtil.getPropLong(transactionMap, DaoConstants.MTIME));
//        transaction.setDeleted(BeanUtil.getPropBoolean(transactionMap, DaoConstants.DELETED));
//        transaction.setVersion(BeanUtil.getPropLong(transactionMap, DaoConstants.VERSION));
//        transaction.setNfcCard(BeanUtil.getPropString(transactionMap, com.wosai.upay.model.dao.Transaction.NFC_CARD));
//        return transaction;
//    }
//
//
//    String s = "{\n" +
//            "    \"subject\": \"paytest\",\n" +
//            "    \"buyer_login\": \"wx1234567890\",\n" +
//            "    \"received_amount\": 8000,\n" +
//            "    \"merchant_id\": \"68470bb1-f848-4e9f-902a-533f52d5b523\",\n" +
//            "    \"type\": 30,\n" +
//            "    \"body\": null,\n" +
//            "    \"mtime\": *************,\n" +
//            "    \"extended_params\": {\n" +
//            "        \n" +
//            "    },\n" +
//            "    \"tsn\": \"****************\",\n" +
//            "    \"operator\": null,\n" +
//            "    \"product_flag\": null,\n" +
//            "    \"extra_out_fields\": {\n" +
//            "        \"quota_fee_rate_tag\": \"10076\",\n" +
//            "        \"payments\": [\n" +
//            "            {\n" +
//            "                \"type\": \"BANKCARD_CREDIT\",\n" +
//            "                \"origin_type\": \"ICBC_CREDIT\",\n" +
//            "                \"amount\": 7900\n" +
//            "            },\n" +
//            "            {\n" +
//            "                \"type\": \"DISCOUNT_CHANNEL\",\n" +
//            "                \"origin_type\": \"COUPON\",\n" +
//            "                \"amount\": 100,\n" +
//            "                \"source\": \"34484020221110979465990239236046\"\n" +
//            "            }\n" +
//            "        ],\n" +
//            "        \"combo_id\": \"2621\",\n" +
//            "        \"weixin_appid\": \"wx72534f3638c59073\",\n" +
//            "        \"quota_fee_rate\": \"0.5\"\n" +
//            "    },\n" +
//            "    \"reflect\": null,\n" +
//            "    \"provider\": 1033,\n" +
//            "    \"original_amount\": 8000,\n" +
//            "    \"ctime\": *************,\n" +
//            "    \"id\": \"t****************\",\n" +
//            "    \"terminal_id\": \"3e1e12b2-fdb3-42b3-9c74-10f9c17b095d\",\n" +
//            "    \"client_tsn\": \"1642063921133662412\",\n" +
//            "    \"store_id\": \"4c547dc1-0210-4bd2-90f9-b44536d82eb3\",\n" +
//            "    \"provider_error_info\": {\n" +
//            "        \"pay\": {\n" +
//            "            \"return_msg\": \"处理成功\",\n" +
//            "            \"result_code\": \"SUCCESS\",\n" +
//            "            \"return_code\": \"SUCCESS\"\n" +
//            "        }\n" +
//            "    },\n" +
//            "    \"extra_params\": {\n" +
//            "        \"barcode\": \"133218397040401362\"\n" +
//            "    },\n" +
//            "    \"payway\": 3,\n" +
//            "    \"finish_time\": 1657335125464,\n" +
//            "    \"sub_payway\": 1,\n" +
//            "    \"config_snapshot\": {\n" +
//            "        \"clearance_provider\": 2,\n" +
//            "        \"latitude\": \"31.309188\",\n" +
//            "        \"store_client_sn\": null,\n" +
//            "        \"merchant_id\": \"68470bb1-f848-4e9f-902a-533f52d5b523\",\n" +
//            "        \"merchant_sn\": \"21690003344787\",\n" +
//            "        \"pay_status\": 1,\n" +
//            "        \"store_name\": \"业务开通门店名称07071915\",\n" +
//            "        \"currency\": \"CNY\",\n" +
//            "        \"terminal_id\": \"3e1e12b2-fdb3-42b3-9c74-10f9c17b095d\",\n" +
//            "        \"terminal_sn\": \"210100001634700002416844\",\n" +
//            "        \"longitude\": \"120.776461\",\n" +
//            "        \"lkl_up_trade_params\": {\n" +
//            "            \"fee_rate_tag\": {\n" +
//            "                \"1\": \"2621: \"\n" +
//            "            },\n" +
//            "            \"fee\": 4,\n" +
//            "            \"cert_id\": \"**********\",\n" +
//            "            \"active\": true,\n" +
//            "            \"weixin_appid\": \"wxd23604aba7ed0487\",\n" +
//            "            \"weixin_mini_sub_appid\": \"wxccbcac9a3ece5112\",\n" +
//            "            \"fee_rate\": \"0.05\",\n" +
//            "            \"weixin_sub_mch_id\": \"1657192598551\",\n" +
//            "            \"liquidation_next_day\": true,\n" +
//            "            \"weixin_sub_appid\": \"wx72534f3638c59073\",\n" +
//            "            \"quota_fee_rate_tag\": \"10076\",\n" +
//            "            \"channel_id\": \"32631798\",\n" +
//            "            \"provider_mch_id\": \"cup_no1657192593268\",\n" +
//            "            \"quota_fee_rate\": \"0.5\"\n" +
//            "        },\n" +
//            "        \"store_id\": \"4c547dc1-0210-4bd2-90f9-b44536d82eb3\",\n" +
//            "        \"store_sn\": \"21590000000961301\",\n" +
//            "        \"channel_name\": \"上海收钱吧互联网科技股份有限公司\",\n" +
//            "        \"store_city\": \"苏州市\",\n" +
//            "        \"trade_app\": \"1\",\n" +
//            "        \"terminal_vendor_app_appid\": \"2020091800000001\",\n" +
//            "        \"terminal_category\": null,\n" +
//            "        \"merchant_name\": \"业务开通07071915\",\n" +
//            "        \"terminal_name\": \"test-1115\",\n" +
//            "        \"district_code\": \"320506\",\n" +
//            "        \"vendor_id\": \"859d9f5f-af99-11e5-9ec3-00163e00625b\",\n" +
//            "        \"common_switch\": \"00000012222222222222222222222222\",\n" +
//            "        \"merchant_country\": \"CHN\",\n" +
//            "        \"is_need_refund_fee_flag\": null,\n" +
//            "        \"hit_payway\": null\n" +
//            "    },\n" +
//            "    \"effective_amount\": 8000,\n" +
//            "    \"paid_amount\": 7900,\n" +
//            "    \"trade_no\": \"61782725189946902814020851123221\",\n" +
//            "    \"channel_finish_time\": 1657335125000,\n" +
//            "    \"order_id\": \"o****************\",\n" +
//            "    \"order_sn\": \"****************\",\n" +
//            "    \"buyer_uid\": \"1234567890987654321\",\n" +
//            "    \"status\": 2000\n" +
//            "}";
//
//
//}
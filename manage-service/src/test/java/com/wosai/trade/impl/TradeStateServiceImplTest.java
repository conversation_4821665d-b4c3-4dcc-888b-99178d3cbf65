package com.wosai.trade.impl;

import com.wosai.trade.BaseTest;
import com.wosai.trade.constant.ObjectTypeConstants;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.Assert.*;

public class TradeStateServiceImplTest extends BaseTest {

    @Autowired
    private TradeStateServiceImpl tradeStateService;

    @Test
    public void publishVolcanoEvent() {
        long state = Long.MAX_VALUE & (~(1L << (5 - 1)));
        tradeStateService.publishVolcanoEvent("3333", ObjectTypeConstants.TYPE_MERCHANT,
                1, state, Boolean.FALSE, "TEST");
    }
}
package com.wosai.trade.impl;

import com.google.common.collect.Sets;
import com.wosai.trade.BaseTest;
import com.wosai.trade.service.activity.response.DiscountQuotaResponse;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import redis.clients.jedis.JedisCommands;
import redis.clients.jedis.MultiKeyCommands;
import redis.clients.jedis.ScanParams;
import redis.clients.jedis.ScanResult;

import java.util.List;
import java.util.Set;
import java.util.concurrent.CountDownLatch;

public class CacheServiceImplTest extends BaseTest {

    @Autowired
    private CacheService cacheService;

    @Test
    public void testCacheQuotaResponse() {
        String mId = "test";
        DiscountQuotaResponse re = new DiscountQuotaResponse();

        cacheService.cacheQuotaResponse(mId, 111L, null);
    }

    @Test
    public void  test(){
        cacheService.paySuccessPreUsableHandel(10006L,"1234",10);

    }
    @Test
    public void  test001(){
        cacheService.paySuccessPreUsableHandel(10006L,"t00001809022",10);

    }

    @Test
    public void  test002(){
        cacheService.delQuotaInfo(10006L);

    }

    @Test
    public void testDelQuotaResponse() {

        cacheService.delQuotaResponse("6d317a15-5348-4f26-a3e0-38240eb895db");


    }

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Test
    public void testDel() throws InterruptedException {

        CountDownLatch count = new CountDownLatch(1);

        Set<String> keys1 = redisTemplate.execute((RedisCallback<Set<String>>) connection -> {
            Set<String> keys = Sets.newHashSet();

            JedisCommands commands = (JedisCommands) connection.getNativeConnection();
            MultiKeyCommands multiKeyCommands = (MultiKeyCommands) commands;

            ScanParams scanParams = new ScanParams();
            scanParams.match("discount_quota_record" + "*");
            scanParams.count(1000); // 这个不是返回结果的数量，应该是每次scan的数量
            ScanResult<String> scan = multiKeyCommands.scan("0", scanParams);
            List<String> result = scan.getResult();
            keys.addAll(result);

            count.countDown();
            return keys;
        });

        System.out.println("=====");
        count.await();

        for (String s : keys1) {
            redisTemplate.delete(s);
        }

    }


}
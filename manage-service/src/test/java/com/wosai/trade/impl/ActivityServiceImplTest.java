package com.wosai.trade.impl;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.trade.BaseTest;
import com.wosai.trade.model.dal.ActivityConditionDalResult;
import com.wosai.trade.model.dal.ActivityQueryParam;
import com.wosai.trade.model.enums.PayWayEnum;
import com.wosai.trade.repository.dao.ActivityDOMapper;
import com.wosai.trade.repository.dao.DiscountQuotaRuleMapper;
import com.wosai.trade.repository.dao.entity.ActivityEntity;
import com.wosai.trade.repository.dao.entity.DiscountQuotaRuleEntity;
import com.wosai.trade.service.ActivityService;
import com.wosai.trade.service.activity.request.*;
import com.wosai.trade.service.activity.response.ActivityConditionQueryResponse;
import com.wosai.trade.service.activity.response.ActivityDetailsQueryResponse;
import com.wosai.trade.service.activity.response.ActivityResponse;
import com.wosai.trade.service.request.AddTradeComboRequest;
import com.wosai.trade.service.request.PageInfo;
import com.wosai.trade.service.result.ListResult;
import com.wosai.trade.util.JsonUtil;
import com.wosai.trade.util.PageInfoUtil;
import com.wosai.upay.core.model.log.TaskApplyLog;
import com.wosai.upay.core.service.LogService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

@Slf4j
public class ActivityServiceImplTest extends BaseTest {

    @Resource
    private DiscountQuotaRuleMapper mapper;
    @Resource
    private ActivityDOMapper activityDOMapper;

    @Test
    public void testDingAuditApproved() {

        DiscountQuotaRuleEntity ruleDO = DiscountQuotaRuleEntity.builder()
                .activity_id(123L)
                .quota_max(50000L)
                .quota_min(5000L)
                .quota_fee_rate_max("0.1")
                .quota_fee_rate_min("0.1")
                .build();


        int i = mapper.insertSelective(ruleDO);

        System.out.println(ruleDO.getId());
    }


    @Test
    public void test() {
        ActivityQueryParam queryParam = new ActivityQueryParam();
        queryParam.setName("45");
        PageInfo pageInfo = PageInfoUtil.extractPageInfoCreateAt(null);
        queryParam.setLimit(pageInfo.getPageSize());
        queryParam.setStart(pageInfo.getPageStart());
        queryParam.setOrderBy(pageInfo.getOrderBy());

        List<ActivityConditionDalResult> activityEntities = activityDOMapper.queryByCondition(queryParam);
        System.out.println(activityEntities.size());

    }

    @Test
    public void getActivityByComboId() {
        ActivityEntity activityEntity = activityDOMapper.getActivityDOByComboId(2357L);

    }


    @Resource
    private LogService logService;

    @Test
    public void testTaskLogService() {
        Map<String, Integer> map = MapUtil.hashMap(
                TaskApplyLog.TYPE, 453,
                TaskApplyLog.APPLY_SYSTEM, 2,
                TaskApplyLog.APPLY_DATE, "2022-06-15 14:38:30",
                TaskApplyLog.USER_ID, "system");


        Map<String, Object> taskApplyLog = logService.createTaskApplyLog(map);

        System.out.println(JSONObject.toJSONString(taskApplyLog));
    }


    @Autowired
    private ActivityService activityService;


    //区间费率和优惠额度
    @Test
    public void testCreate() {

        AddActivityRequest request = new AddActivityRequest();

        ActivityInfo info = new ActivityInfo();
        info.setName("费率套餐活动3.2测试");
        info.setType(3);
        info.setDesc("活动描述");
        info.setTradeAppId(1L);

        List<String> ac = Arrays.asList("lkl", "tonglian");

        info.setAcquirers(ac);
        info.setOperator("system");
        ActivityInfo.ActivityLabel tags = new ActivityInfo.ActivityLabel();
        tags.setLabelId("1110");
        tags.setEffectiveLabelValue("1112");
        tags.setCancelLabelValue("1113");
        tags.setEndLabelValue("1114");
        info.setTags(tags);

        ActivityInfo.Notice sale = new ActivityInfo.Notice();
        sale.setApplySuccess("销售通知活动报名成功");
        sale.setApplyCancel("销售通知活动取消成功");
        info.setSaleNotice(sale);

        ActivityInfo.Notice me = new ActivityInfo.Notice();
        me.setApplySuccess("商户通知活动报名成功");
        me.setApplyCancel("商户通知活动取消成功");
        info.setMerchantNotice(me);


        ActivityRule rule = new ActivityRule();

        rule.setConfigLevel("merchant");


        //生效规则
        ActivityEffectiveRule effectiveRule = new ActivityEffectiveRule();

        ActivityEffectiveRule.TimeRule timeRule = new ActivityEffectiveRule.TimeRule();
        timeRule.setFixedTime(1654617600000L);


        rule.setEffectiveRules(effectiveRule);


        //失效规则
        ActivityExpirationRule expirationRule = new ActivityExpirationRule();

        ActivityExpirationRule.TimeRule stimeRule = new ActivityExpirationRule.TimeRule();
        stimeRule.setRollMonth(2);
        expirationRule.setByTimeRule(stimeRule);

        rule.setExpirationRules(expirationRule);


        Range lowest = new Range();
        lowest.setMax("0.6");
        lowest.setMin("0.1");
        rule.setLowestFeeRate(lowest);

        //报名时间
        LocalDateTime localDate = LocalDate.now().atStartOfDay();
        rule.setApplyTime(new ActivityRule.ApplyTime()
                .setBegin(Date.from(localDate.atZone(ZoneId.systemDefault()).toInstant()))
                .setEnd(Date.from(localDate.plusDays(1).atZone(ZoneId.systemDefault()).toInstant()))
        );
        //城市
        rule.setCities(ImmutableList.of("上海", "北京"));
        rule.setOrganizationList(ImmutableList.of("00001"));
        //行业
        rule.setIndustryRule(new ActivityRule.IndustryRule()
                .setAllowList(ImmutableList.of("7198ee72-7371-422e-833c-eb6ddd9be5b1")) //快餐
                .setDenyList(ImmutableList.of("e6b8d357-312d-11e6-aebb-ecf4bbdee2f0"))  //职能教育
        );
        //取消后套餐
        ActivityRule.AfterCancelComboExt afterCancelComboExt = new ActivityRule.AfterCancelComboExt();
        afterCancelComboExt.setComboId(33L);
        afterCancelComboExt.setFeeRate("0.5");
        rule.setAfterCancelComboExt(afterCancelComboExt);
        //子状态列表
        List<ActivityRule.SubStatusDetail> subStatusDetails = ImmutableList.of(
                buildSubStatusDetail("报名成功"),
                buildSubStatusDetail("返佣成功"),
                buildSubStatusDetail("返佣失败")
        );
        //套餐列表
        List<ActivityRule.TradeCombo> tradeComboList = ImmutableList.of(
                buildLadderTradeCombo("阶梯费率", "[{\"fee_rate\":\"0.1\",\"max\":100,\"min\":0},{\"fee_rate\":\"0.38\",\"max\":2147483647,\"min\":100}]"),
                buildFixedTradeCombo("区间费率", "0", "0.38")
        );
        rule.setTradeCombos(tradeComboList);
        rule.setSubStatusDetails(subStatusDetails);
        info.setOperator("system");
        request.setRule(rule);
        request.setInfo(info);

        ActivityResponse response = activityService.create(request);
        Assert.assertTrue("创建活动失败", Objects.nonNull(response) && Objects.nonNull(response.getActivityId()));
        log.info("测试创建活动成功 activityId:{}", response.getActivityId());
    }

    @NotNull
    private ActivityRule.SubStatusDetail buildSubStatusDetail(String name) {
        ActivityRule.SubStatusDetail subStatusDetail = new ActivityRule.SubStatusDetail();
        subStatusDetail.setName(name);
        return subStatusDetail;
    }

    private ActivityRule.TradeCombo buildFixedTradeCombo(String name, String min, String max) {
        ActivityRule.TakeEffectNotice notice = new ActivityRule.TakeEffectNotice();
        notice.setMerchant("商户，套餐生效时通知{{merchantSn}}");
        notice.setSale("销售，套餐生效时通知{{merchantSn}}");
        ActivityRule.TradeCombo tradeCombo = new ActivityRule.TradeCombo();
        tradeCombo.setName(name);
        tradeCombo.setTakeEffectNotice(notice);
        ActivityRule.TradeComboDetail tradeComboDetail = new ActivityRule.TradeComboDetail();
        tradeComboDetail.setPayWay(PayWayEnum.WEIXIN.getCode());
        tradeComboDetail.setFeeRateMin(min);
        tradeComboDetail.setFeeRateMax(max);
        tradeCombo.setTradeComboDetails(Lists.newArrayList(tradeComboDetail));
        return tradeCombo;
    }

    private ActivityRule.TradeCombo buildLadderTradeCombo(String name, String ladder) {
        ActivityRule.TakeEffectNotice notice = new ActivityRule.TakeEffectNotice();
        notice.setMerchant("商户，套餐生效时通知{{merchantSn}}");
        notice.setSale("销售，套餐生效时通知{{merchantSn}}");
        ActivityRule.TradeCombo tradeCombo = new ActivityRule.TradeCombo();
        tradeCombo.setName(name);
        tradeCombo.setTakeEffectNotice(notice);
        ActivityRule.TradeComboDetail tradeComboDetail = new ActivityRule.TradeComboDetail();
        tradeComboDetail.setPayWay(PayWayEnum.WEIXIN.getCode());
        tradeComboDetail.setLadderFeeRates(JsonUtil.decode(ladder, new TypeReference<List<AddTradeComboRequest.LadderFeeRate>>() {
        }));
        tradeCombo.setTradeComboDetails(Lists.newArrayList(tradeComboDetail));
        return tradeCombo;
    }

    @Test
    public void testUpdate() {
        UpdateActivityRequest request = JsonUtil.decode(updateStr, UpdateActivityRequest.class);
        Boolean result = activityService.update(request);
        Assert.assertTrue("更新活动失败", result);
    }

    @Test
    public void testUpdate001() {

        UpdateActivityRequest request = new UpdateActivityRequest();
        request.setId(1029L);
        request.setMutexActivityIds(Arrays.asList(1008L));

        List<UpdateActivityRequest.PaywayMutexActivity> list=new ArrayList<>();
        UpdateActivityRequest.PaywayMutexActivity  payWay=new UpdateActivityRequest.PaywayMutexActivity();
        payWay.setPayway(2);
        payWay.setMutexActivityIds(Arrays.asList(1008L));
        list.add(payWay);
        request.setPaywayMutexActivityIds(list);

        activityService.update(request);

    }


    @Test
    public void testQueryByActivityId() {
        ActivityDetailsQueryResponse response = activityService.queryByActivityId(13417L);
        System.out.println(JSONObject.toJSONString(response));
    }

    @Test
    public void testActivityConditionQuery() {
        String str = "{\"type\":null,\"status\":1,\"name\":\"\",\"comboId\":null,\"comboName\":\"测试活动\",\"queryCombo\":true,\"pageStart\":0,\"page_size\":10,\"page\":1,\"date_start\":null,\"date_end\":null,\"order_by\":null}";
        ActivityConditionQueryRequest request = JsonUtil.decode(str, ActivityConditionQueryRequest.class);
        //request.setComboName("返佣");
        ListResult<ActivityConditionQueryResponse> listResult = activityService.activityConditionQuery(request);
        System.out.println(JSONObject.toJSONString(listResult));
    }

    @Test
    public void testActivityConditionQuery2() {
        ActivityConditionQueryRequest request = ActivityConditionQueryRequest.builder()
                .status(2)
                .build();
        request.setPage(1);
        request.setPageSize(10);
        ListResult<ActivityConditionQueryResponse> listResult = activityService.activityConditionQuery(request);
        System.out.println(JSONObject.toJSONString(listResult));
    }

    @Test
    public void testQueryUsableActivity() {
        List<ActivityConditionQueryResponse> usableActivity = activityService.queryUsableActivity(null);
        System.out.println(JSONObject.toJSONString(usableActivity));
    }

    @Test
    public void testCreateActivityAuditApproved() {
        activityService.createActivityAuditApproved("442983");
    }
    //147430
    @Test
    public void auditUpdateActivityApproved() {
        activityService.auditUpdateActivityApproved("547696", 11616L);
    }

    public static final String updateStr = "{\"id\":11616,\"applyTime\":{\"begin\":\"2023-06-01 00:00:00\",\"end\":\"2028-06-30 23:59:59\"},\"industryRule\":{\"allowList\":[],\"denyList\":[]},\"cities\":[\"北京市\",\"天津市\",\"上海市\",\"江苏省\",\"广东省\"],\"acquirers\":[\"cmbc\"],\"organizationList\":[\"02763\"],\"desc\":\"自动化测试-勿删\",\"tradeCombos\":[{\"id\":21370,\"take_effect_notice\":{\"merchant\":null,\"sale\":null}}],\"effectiveRules\":{\"byTimeRule\":{\"fixedTime\":1682870400770}},\"expirationRules\":{\"byTimeRule\":{\"fixedTime\":1877443200106}}}";
}
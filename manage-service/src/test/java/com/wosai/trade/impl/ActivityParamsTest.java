package com.wosai.trade.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.wosai.databus.event.audit.AuditInstanceApproveEvent;
import com.wosai.databus.event.audit.AuditInstanceRejectEvent;
import com.wosai.trade.BaseTest;
import com.wosai.trade.service.activity.request.*;
import com.wosai.trade.service.activity.response.ActivityConditionQueryResponse;
import com.wosai.trade.service.activity.response.ApplyConditionQueryResponse;
import org.junit.Test;

/**
 * @Description:
 * <AUTHOR>
 * @Date: 2022/5/30 10:50 上午
 */
public class ActivityParamsTest extends BaseTest {

    @Test
   public void test(){
        UpdateActivityRequest updateActivityRequest = new UpdateActivityRequest();
        System.out.println(JSONObject.toJSONString(updateActivityRequest, SerializerFeature.WriteMapNullValue));

        System.out.println(JSONObject.toJSONString(new ActivityExpirationRule(), SerializerFeature.WriteMapNullValue));
        System.out.println(JSONObject.toJSONString(new ActivityInfo.Notice(), SerializerFeature.WriteMapNullValue));
        System.out.println(JSONObject.toJSONString(new UpdateActivityRequest.PaywayMutexActivity(), SerializerFeature.WriteMapNullValue));
    }

    @Test
    public void test001(){
        AddActivityRequest addActivityRequest = new AddActivityRequest();
        addActivityRequest.setInfo(new ActivityInfo());
        addActivityRequest.setRule(new ActivityRule());
        System.out.println(JSONObject.toJSONString(addActivityRequest, SerializerFeature.WriteMapNullValue));

        System.out.println(JSONObject.toJSONString(new ActivityExpirationRule(), SerializerFeature.WriteMapNullValue));
        System.out.println(JSONObject.toJSONString(new ActivityInfo.Notice(), SerializerFeature.WriteMapNullValue));
        System.out.println(JSONObject.toJSONString(new ActivityRule.DiscountQuota(), SerializerFeature.WriteMapNullValue));
        System.out.println(JSONObject.toJSONString(new ActivityRule.FeeRateDetails(), SerializerFeature.WriteMapNullValue));
        System.out.println("=====");
        System.out.println(JSONObject.toJSONString(new ActivityConditionQueryResponse(), SerializerFeature.WriteMapNullValue));
        System.out.println(JSONObject.toJSONString(new ApplyConditionQueryResponse(), SerializerFeature.WriteMapNullValue));
    }

    @Test
    public void test002() {
        System.out.println(JSONObject.toJSONString(new ApplyActivityRequest(), SerializerFeature.WriteMapNullValue));
        System.out.println(JSONObject.toJSONString(new AuditInstanceApproveEvent(), SerializerFeature.WriteMapNullValue));
        System.out.println(JSONObject.toJSONString(new AuditInstanceRejectEvent(), SerializerFeature.WriteMapNullValue));

    }
}

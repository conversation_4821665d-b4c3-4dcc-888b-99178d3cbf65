package com.wosai.trade.impl;

import com.wosai.trade.BaseTest;
import com.wosai.trade.service.activity.response.CrmTwoLevelOuterBizEnumResponse;
import com.wosai.trade.service.request.CrmOrgIdAndBizUpRequest;
import com.wosai.trade.service.request.FeeRateUpdateRequest;
import com.wosai.trade.service.result.GetUpdateFeeRateExecuteTypeResult;
import com.wosai.upay.common.util.JacksonUtil;
import com.wosai.trade.service.request.ActivityBaseRequest;
import com.wosai.trade.service.result.CrmMerchantBasicAgreementFeeRateInfo;
import com.wosai.trade.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import model.businessprivilege.response.BusinessPrivilegeResponseEntity;
import org.junit.Assert;
import org.junit.Test;
import vo.ApiRequestParam;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Map;

import static org.junit.Assert.*;

@Slf4j
public class CrmServiceImplTest extends BaseTest {

    @Resource
    private CrmServiceImpl crmService;

    public static void main(String[] args) throws InterruptedException {
        System.out.println(Thread.currentThread().isInterrupted());
    }

    @Test
    public void feeRateUpdate() throws IOException {
        String json = "{\n" +
                "  \"merchant_id\": \"679b3b1f-534b-4be1-8ac1-d6728e584876\",\n" +
                "  \"payway\": 18,\n" +
                "  \"bsc_fee_rate\": 0.36,\n" +
                "  \"csb_fee_rate\": 0.36,\n" +
                "  \"wap_fee_rate\": 0.36,\n" +
                "  \"mini_fee_rate\": 0.36\n" +
                "}";
        FeeRateUpdateRequest request = JacksonUtil.toBean(json, FeeRateUpdateRequest.class);
        crmService.feeRateUpdate(new ApiRequestParam<>(null, null, request, null));
    }

    @Test
    public void notifyUpdateFeeRate() {
        crmService.notifyUpdateFeeRate();
    }

    @Test
    public void getUpdateFeeRateExecuteType() throws IOException {
        String json = "{\n" +
                "        \"remark\": null,\n" +
                "        \"merchant_id\": \"2f7381fb-aace-4749-b41c-9fd3793e164d\",\n" +
                "        \"payway\": 2,\n" +
                "        \"bsc_fee_rate\": \"0.43\",\n" +
                "        \"csb_fee_rate\": \"0.43\",\n" +
                "        \"wap_fee_rate\": \"0.43\",\n" +
                "        \"mini_fee_rate\": \"0.43\"\n" +
                "    }";
        FeeRateUpdateRequest request = JacksonUtil.toBean(json, FeeRateUpdateRequest.class);
        GetUpdateFeeRateExecuteTypeResult result = crmService.getUpdateFeeRateExecuteType(new ApiRequestParam<>(null, null, request, null));
        System.out.println(JacksonUtil.toJsonString(result));
        Assert.assertNotNull(result);
    }

    @Test
    public void crmAdjustFeeRateExecute() {
        crmService.crmAdjustFeeRateExecute();
    }

    @Test
    public void getMerchantBasicAgreementFeeRateInfo() {
        ActivityBaseRequest request = new ActivityBaseRequest();
        request.setMerchantId("679b3b1f-534b-4be1-8ac1-d6728e584876");
        CrmMerchantBasicAgreementFeeRateInfo result = crmService.getMerchantBasicAgreementFeeRateInfo(new ApiRequestParam<>(null, null, request, null));
        System.out.println(JsonUtil.encode(result));
        Assert.assertNotNull(result);
    }

    @Test
    public void bankCardBarDisplay() {

        BusinessPrivilegeResponseEntity result = crmService.bankCardBarDisplay(null);
        Assert.assertNotNull(result);
    }

    @Test
    public void getByOrgIdAndBizUp() {
        CrmOrgIdAndBizUpRequest request = new CrmOrgIdAndBizUpRequest();
        request.setBiz("SaaS收费规则配置");
        request.setOrganizationId("4ab7497e-abca-4f12-9bb3-cdf71c4a65ff");
        ApiRequestParam<CrmOrgIdAndBizUpRequest, Map> parm = new ApiRequestParam<CrmOrgIdAndBizUpRequest, Map>();
        parm.setBodyParams(request);
        Map<String, Object> result = crmService.getByOrgIdAndBizUp(parm);
        log.info("result:{}", JsonUtil.encode(result));
    }

    @Test
    public void queryActivityByFilterConfig() {
        CrmTwoLevelOuterBizEnumResponse list = crmService.queryActivityByFilterConfig(null);
        System.out.println(JsonUtil.encode(list));
    }
}
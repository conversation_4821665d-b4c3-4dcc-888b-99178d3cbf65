package com.wosai.trade.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.ImmutableMap;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.wosai.trade.biz.fee.MerchantBasicAgreementFeeRateBiz;
import com.wosai.trade.model.enums.PayWayEnum;
import com.wosai.trade.service.result.ListMchFeeStatusResult;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.service.AcquirerService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class FeeRateServiceImplV2Test {

    @InjectMocks
    private MerchantBasicAgreementFeeRateBiz merchantBasicAgreementFeeRateBiz;

    @Mock
    private MerchantService merchantService;
    @Mock
    private AcquirerService acquirerService;

    @Test
    public void useRealtimeFeeRateOfProviderIfNeed() {
        String merchantId = "440fdc7c-b968-444b-b6b1-2906398f9104";
        String merchantSn = "21690003629637";

       /* //没有需要查询实时费率的通道
        List<ListMchFeeStatusResult> merchantFeeRateList = buildInValidMerchantFeeRateList();
        merchantBasicAgreementFeeRateBiz.useRealtimeFeeRateOfProviderIfNeed(merchantId, merchantFeeRateList);
        assertNull(merchantFeeRateList.get(0).getIsLadderFeeRate());

        //未查到商户信息
        when(merchantService.getMerchant(merchantId)).thenReturn(null);
        merchantFeeRateList = buildValidMerchantFeeRateList();
        merchantBasicAgreementFeeRateBiz.useRealtimeFeeRateOfProviderIfNeed(merchantId, merchantFeeRateList);
        assertNull(merchantFeeRateList.get(0).getIsLadderFeeRate());

        //固定费率
        when(acquirerService.getFeeRateByAcquirer(merchantSn, AcquirerTypeEnum.FU_YOU.getValue())).thenReturn(buildFixedFeeRateConfigList());
        when(merchantService.getMerchant(merchantId)).thenReturn(ImmutableMap.of(Merchant.SN, merchantSn));
        merchantBasicAgreementFeeRateBiz.useRealtimeFeeRateOfProviderIfNeed(merchantId, merchantFeeRateList);
        assertNotNull(merchantFeeRateList.get(0).getCsbFeeRate());
        assertNull(merchantFeeRateList.get(0).getLadderFeeRates());

        //阶梯费率
        when(acquirerService.getFeeRateByAcquirer(merchantSn, AcquirerTypeEnum.FU_YOU.getValue())).thenReturn(buildLadderFeeRateConfigList());
        merchantBasicAgreementFeeRateBiz.useRealtimeFeeRateOfProviderIfNeed(merchantId, merchantFeeRateList);
        assertNull(merchantFeeRateList.get(0).getCsbFeeRate());
        assertFalse(merchantFeeRateList.get(0).getLadderFeeRates().isEmpty());*/
    }


    private List<ListMchFeeStatusResult> buildInValidMerchantFeeRateList() {
        List<ListMchFeeStatusResult> merchantFeeRateList = new ArrayList<>();
        ListMchFeeStatusResult fuyouFeeRate = new ListMchFeeStatusResult();
        merchantFeeRateList.add(fuyouFeeRate);
        return merchantFeeRateList;
    }

    private List<ListMchFeeStatusResult> buildValidMerchantFeeRateList() {
        List<ListMchFeeStatusResult> merchantFeeRateList = new ArrayList<>();
        ListMchFeeStatusResult fuyouFeeRate = new ListMchFeeStatusResult();
        fuyouFeeRate.setProvider(ProviderEnum.PROVIDER_FUYOU.getValue());
        fuyouFeeRate.setPayWay(PayWayEnum.WEIXIN.getCode());
        merchantFeeRateList.add(fuyouFeeRate);

        ListMchFeeStatusResult haikeFeeRate = new ListMchFeeStatusResult();
        haikeFeeRate.setProvider(ProviderEnum.PROVIDER_HAIKE.getValue());
        haikeFeeRate.setPayWay(PayWayEnum.WEIXIN.getCode());
        merchantFeeRateList.add(haikeFeeRate);
        return merchantFeeRateList;
    }

    private List<Map> buildFixedFeeRateConfigList() {
        String val = "[\n" +
                "  {\n" +
                "    \"payway\": 2,\n" +
                "    \"merchant_name\": \"富友连生产测试商户\",\n" +
                "    \"merchant_sn\": \"21690003629637\",\n" +
                "    \"merchant_id\": \"440fdc7c-b968-444b-b6b1-2906398f9104\",\n" +
                "    \"fee_rate_type\": \"fixed\",\n" +
                "    \"b2c_fee_rate\": \"0.55\",\n" +
                "    \"c2b_fee_rate\": \"0.55\",\n" +
                "    \"wap_fee_rate\": \"0.55\",\n" +
                "    \"mini_fee_rate\": \"0.55\",\n" +
                "    \"app_fee_rate\": \"0.6\",\n" +
                "    \"h5_fee_rate\": \"0.39\",\n" +
                "    \"provider\": 1038\n" +
                "  },\n" +
                "  {\n" +
                "    \"payway\": 3,\n" +
                "    \"merchant_name\": \"富友连生产测试商户\",\n" +
                "    \"merchant_sn\": \"21690003629637\",\n" +
                "    \"merchant_id\": \"440fdc7c-b968-444b-b6b1-2906398f9104\",\n" +
                "    \"fee_rate_type\": \"fixed\",\n" +
                "    \"b2c_fee_rate\": \"0.5\",\n" +
                "    \"c2b_fee_rate\": \"0.5\",\n" +
                "    \"wap_fee_rate\": \"0.5\",\n" +
                "    \"mini_fee_rate\": \"0.5\",\n" +
                "    \"app_fee_rate\": \"0.6\",\n" +
                "    \"h5_fee_rate\": \"0.39\",\n" +
                "    \"provider\": 1038\n" +
                "  }\n" +
                "]";
        return JSON.parseObject(val, new TypeReference<List<Map>>() {});
    }

    private List<Map> buildLadderFeeRateConfigList() {
        String val = "[\n" +
                "  {\n" +
                "    \"payway\": 2,\n" +
                "    \"merchant_name\": \"小陈的店有限公司\",\n" +
                "    \"merchant_sn\": \"21690003355258\",\n" +
                "    \"merchant_id\": \"058c3ddc-5905-44c6-a43e-08c9af3b87c0\",\n" +
                "    \"provider\": 1033,\n" +
                "    \"fee_rate_type\": \"ladder\",\n" +
                "    \"ladder_fee_rates\": [\n" +
                "      {\n" +
                "        \"b2c_fee_rate\": \"0.25\",\n" +
                "        \"c2b_fee_rate\": \"0.25\",\n" +
                "        \"max\": 300.0,\n" +
                "        \"min\": 0.0,\n" +
                "        \"mini_fee_rate\": \"0.25\",\n" +
                "        \"wap_fee_rate\": \"0.25\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"b2c_fee_rate\": \"0.38\",\n" +
                "        \"c2b_fee_rate\": \"0.38\",\n" +
                "        \"max\": null,\n" +
                "        \"min\": 300.0,\n" +
                "        \"mini_fee_rate\": \"0.38\",\n" +
                "        \"wap_fee_rate\": \"0.38\"\n" +
                "      }\n" +
                "    ]\n" +
                "  },\n" +
                "  {\n" +
                "    \"payway\": 3,\n" +
                "    \"merchant_name\": \"小陈的店有限公司\",\n" +
                "    \"merchant_sn\": \"21690003355258\",\n" +
                "    \"merchant_id\": \"058c3ddc-5905-44c6-a43e-08c9af3b87c0\",\n" +
                "    \"provider\": 1033,\n" +
                "    \"fee_rate_type\": \"ladder\",\n" +
                "    \"ladder_fee_rates\": [\n" +
                "      {\n" +
                "        \"b2c_fee_rate\": \"0.25\",\n" +
                "        \"c2b_fee_rate\": \"0.25\",\n" +
                "        \"max\": 300.0,\n" +
                "        \"min\": 0.0,\n" +
                "        \"mini_fee_rate\": \"0.25\",\n" +
                "        \"wap_fee_rate\": \"0.25\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"b2c_fee_rate\": \"0.38\",\n" +
                "        \"c2b_fee_rate\": \"0.38\",\n" +
                "        \"max\": null,\n" +
                "        \"min\": 300.0,\n" +
                "        \"mini_fee_rate\": \"0.38\",\n" +
                "        \"wap_fee_rate\": \"0.38\"\n" +
                "      }\n" +
                "    ]\n" +
                "  }\n" +
                "]";
        return JSON.parseObject(val, new TypeReference<List<Map>>() {});
    }

}
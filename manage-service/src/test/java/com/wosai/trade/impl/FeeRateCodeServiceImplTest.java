package com.wosai.trade.impl;

import com.wosai.trade.BaseTest;
import com.wosai.trade.service.ItemCodeService;
import com.wosai.trade.service.result.ItemCodeResult;
import com.wosai.trade.service.result.ListResult;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Objects;

public class FeeRateCodeServiceImplTest extends BaseTest {

    @Resource
    private ItemCodeService itemCodeService;

    @Test
    public void queryByItemCode() {
        ListResult<ItemCodeResult> list = itemCodeService.queryByItemCode("ChargeCycle");
        Assert.assertTrue("查询列表失败", Objects.nonNull(list) && CollectionUtils.isNotEmpty(list.getRecords()));
    }
}
package com.wosai.trade.impl;

import com.wosai.trade.BaseTest;
import com.wosai.trade.service.AppService;
import com.wosai.trade.service.CrmService;
import com.wosai.trade.service.request.ActivityBaseRequest;
import com.wosai.trade.service.result.CrmMerchantBasicAgreementFeeRateInfo;
import com.wosai.trade.service.result.MerchantBasicAgreementFeeRateInfo;
import com.wosai.trade.util.JsonUtil;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import vo.ApiRequestParam;

public class AppServiceImplTest extends BaseTest {

    @Autowired
    private AppService appService;
    @Autowired
    private CrmService crmService;



    @Test
    public void getMerchantBasicAgreementFeeRateInfo() {
        String merchantId = "48c6d86a-7115-4a00-ba13-055c8602ec0e";
        String str = "{\"merchant_id\":\"3498bc4b-1181-4a49-961e-5bd2102d5906\",\"uc_user_id\":\"d2576c18-8770-40f8-9299-d38aae58c5f1\",\"store_ids\":null}";
        ActivityBaseRequest activityBaseRequest = JsonUtil.decode(str, ActivityBaseRequest.class);
        MerchantBasicAgreementFeeRateInfo result = appService.getMerchantBasicAgreementFeeRateInfo(activityBaseRequest);
        System.out.println("result="+JsonUtil.encode(result));
    }

    @Test
    public void getMerchantBasicAgreementFeeRateInfoCrm() {
        ActivityBaseRequest request = new ActivityBaseRequest();
        request.setMerchantId("1e222518-5600-4928-bef4-653f69429a5d");
        CrmMerchantBasicAgreementFeeRateInfo result = crmService.getMerchantBasicAgreementFeeRateInfo(new ApiRequestParam<>(null, null, request, null));
        System.out.println(JsonUtil.encode(result));
        Assert.assertNotNull(result);
    }
}
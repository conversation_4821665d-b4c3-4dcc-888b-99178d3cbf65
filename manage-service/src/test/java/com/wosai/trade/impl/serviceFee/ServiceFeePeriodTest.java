package com.wosai.trade.impl.serviceFee;

import com.google.common.collect.ImmutableList;
import com.wosai.databus.event.AbstractEvent;
import com.wosai.databus.jackson.EventAwareJackson2PersistenceHelper;
import com.wosai.trade.BaseTest;
import com.wosai.trade.consumer.PeriodPayCompleteConsumer;
import com.wosai.trade.model.NumberRange;
import com.wosai.trade.service.AppServiceFeePeriodService;
import com.wosai.trade.service.ServiceFeeEffectiveService;
import com.wosai.trade.service.ServiceFeePeriodService;
import com.wosai.trade.service.ServiceFeeService;
import com.wosai.trade.service.enums.*;
import com.wosai.trade.service.servicefee.model.ServiceFeeBaseInfo;
import com.wosai.trade.service.servicefee.model.ServiceFeeDirectInfo;
import com.wosai.trade.service.servicefee.model.ServiceFeeProfitShareInfo;
import com.wosai.trade.service.servicefee.reponse.CreateServiceFeeResponse;
import com.wosai.trade.service.servicefee.reponse.QueryByDetailServiceFeeResponse;
import com.wosai.trade.service.servicefee.reponse.RenewableServiceFeeResponse;
import com.wosai.trade.service.servicefee.reponse.RenewalAdjustServiceFeeEffectiveResponse;
import com.wosai.trade.service.servicefee.request.CreateServiceFeeRequest;
import com.wosai.trade.service.servicefee.request.PeriodApplyOneRequest;
import com.wosai.trade.service.servicefee.request.RenewalRedirectLinkRequest;
import com.wosai.trade.service.servicefee.request.TransferServiceFeeRequest;
import com.wosai.trade.service.servicefee.request.app.AppGetRenewableServiceFeeRequest;
import com.wosai.trade.service.servicefee.request.app.AppQueryByDetailServiceFeeRequest;
import com.wosai.trade.service.servicefee.request.app.AppRenewAdjustServiceFeeEffectiveRequest;
import com.wosai.trade.util.JsonUtil;
import com.wosai.upay.core.service.BusinssCommonService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.test.context.TestPropertySource;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * Description: 按时段付费测试用例
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/1/26
 */
@TestPropertySource(properties = {
        "spring.server.period-pay=http://period-pay-saas.iwosai.com/",
        "flags.consumer.run=true"
})
@Slf4j
public class ServiceFeePeriodTest extends BaseTest {
    @Resource
    private BusinssCommonService businssCommonService;
    @Resource
    private ServiceFeeService serviceFeeService;
    @Resource
    private ServiceFeeEffectiveService serviceFeeEffectiveService;
    @Resource
    private AppServiceFeePeriodService appServiceFeePeriodService;
    @Resource
    private ServiceFeePeriodService serviceFeePeriodService;
    @Resource
    private PeriodPayCompleteConsumer periodPayCompleteConsumer;

    private static final EventAwareJackson2PersistenceHelper persistenceHelper = new EventAwareJackson2PersistenceHelper();

    /**
     * 创建服务费规则
     */
    @Test
    public void createPeriodServiceFee() {
        CreateServiceFeeRequest request = buildCreateServiceFeeRequest(ImmutableList.of(ChargeModeEnum.PERIOD.getByCode()));
        log.info("serviceFeeService.create request: {}", JsonUtil.encode(request));
        CreateServiceFeeResponse response = serviceFeeService.create(request);
        log.info("serviceFeeService.create response: {}", response);
        Assert.assertNotNull("创建收费规则失败 response为空", response);
        Assert.assertNotNull("创建收费规则失败. id为空", response.getId());
    }

    @Test
    public void apply() {
        PeriodApplyOneRequest request = new PeriodApplyOneRequest();
        request.setMerchantSn("21690003215621");
        request.setStoreSn("21590000000852445");
        request.setServiceFeeId(6923L);
        request.setRemark("按时段收费");
        request.setPlatform("SP");
        request.setChargeAmount(199L);
        request.setOperatorId("867bcbe2-c78a-4a9e-bd7f-a506e9213c2f");
        request.setOperatorName("tywei测试账号");
        request.setOrderSn("o202401291026001");
        serviceFeePeriodService.applyOne(request);
    }

    @Test
    public void renewalAdjust() {
        String merchantId = businssCommonService.getMerchantIdBySn("21690003215621");
        AppRenewAdjustServiceFeeEffectiveRequest request = new AppRenewAdjustServiceFeeEffectiveRequest();
        request.setServiceFeeId(7873L);
        request.setDimensionNo("89cf78fecc912f33d8d75a19753f0ae4");
        request.setUcUserId("867bcbe2-c78a-4a9e-bd7f-a506e9213c2f");
        request.setMerchantId(merchantId);
        RenewalAdjustServiceFeeEffectiveResponse response = appServiceFeePeriodService.renewAdjust(request);
        Assert.assertNotNull("无响应", response);
        Assert.assertNotNull("订单号不存在", response.getOrderSn());
    }
    @Test
    public void listenerRenewalAdjustComplete() {
        AbstractEvent event = (AbstractEvent) persistenceHelper.fromJsonBytes(eventStr.getBytes(StandardCharsets.UTF_8), AbstractEvent.class);
        periodPayCompleteConsumer.handleEvent(event);
    }
    @Test
    public void queryByDetail() {
        String merchantId = businssCommonService.getMerchantIdBySn("21690003215621");
        AppQueryByDetailServiceFeeRequest request = new AppQueryByDetailServiceFeeRequest();
        request.setDimensionNo("dbb7d19d1dd38923d7c5a786c60eacf3");
        request.setMerchantId(merchantId);
        QueryByDetailServiceFeeResponse list = appServiceFeePeriodService.queryByDetail(request);
        log.info("list:{}", JsonUtil.encode(list));
        Assert.assertNotNull(list);
    }

    @Test
    public void getRenewableServiceFeeList() {
        String merchantId = businssCommonService.getMerchantIdBySn("21690003215621");
        AppGetRenewableServiceFeeRequest request = new AppGetRenewableServiceFeeRequest();
        request.setDimensionNo("e98f6e9e76e9eb371f6611e508379e30");
        request.setMerchantId(merchantId);
        List<RenewableServiceFeeResponse> list = appServiceFeePeriodService.getRenewableServiceFeeList(request);
        log.info("list:{}", JsonUtil.encode(list));
        Assert.assertNotNull(list);
    }
    @Test
    public void generateRenewalRedirectLink() {
        RenewalRedirectLinkRequest request = new RenewalRedirectLinkRequest();
        request.setMerchantSn("21690003215621");
        request.setDimensionNo("dbb7d19d1dd38923d7c5a786c60eacf3");
        String url = serviceFeePeriodService.generateRenewalRedirectLink(request);
        log.info("url:{}", url);
    }

    /**
     * 对象交换
     */
    @Test
    public void transferObject() {
        TransferServiceFeeRequest.SrcTransferObject srcObj = new TransferServiceFeeRequest.SrcTransferObject();
        srcObj.setDimensionNo("89cf78fecc912f33d8d75a19753f0ae4");
        srcObj.setMerchantSn("21690003215621");
        TransferServiceFeeRequest.DstTransferObject dstObj = new TransferServiceFeeRequest.DstTransferObject();
        dstObj.setMerchantSn("21690003291354");
        dstObj.setStoreSn("21590000000917141");
        TransferServiceFeeRequest request = new TransferServiceFeeRequest();
        request.setAppId("储值年卡");
        request.setSrc(srcObj);
        request.setDst(dstObj);
        serviceFeePeriodService.transferObject(request);
    }

    @Test
    public void transferConsumer() {
        String eventStr = "{\"module\":\"PAY\",\"object_type\":\"period-pay\",\"event_type\":\"subscribe_complete\",\"operator_id\":null,\"operator_type\":\"SUBSCRIBE\",\"operator_platform\":null,\"status\":1,\"biz_type\":\"SAAS\",\"biz_sn\":\"8888569dc55790ffb200d20d8b497dbf\",\"biz_item_code\":\"6922\",\"merchant_sn\":\"21690003291354\",\"store_sn\":\"21590000000917141\",\"level\":\"STORE\",\"charge_cycle\":\"YEAR\",\"charge_number\":1,\"begin_date\":1708249977000,\"end_date\":1762876799000,\"life_time\":null,\"extra\":null}";
        AbstractEvent event = (AbstractEvent) persistenceHelper.fromJsonBytes(eventStr.getBytes(StandardCharsets.UTF_8), AbstractEvent.class);
        periodPayCompleteConsumer.handleEvent(event);
    }


    private CreateServiceFeeRequest buildCreateServiceFeeRequest(List<String> chargeModeList) {
        ServiceFeeBaseInfo serviceFeeBaseInfo = ServiceFeeBaseInfo.builder()
                .name("测试服务费")
                .tradeAppId(7149L)
                .level(ServiceFeeLevelEnum.STORE)
                .type(ServiceFeeTypeEnum.BASE)
                //.scenesType(ServiceFeeScenesTypeEnum.CAMPUS)
                //.scenesCodes(ImmutableList.of("34592", "34591"))
                .author("tywei")
                .receiverNameList(ImmutableList.of("测试"))
                .description("测试服务费说明部分")
                .tags(ImmutableList.of("E1QDEFPQUJWB"))
                .build();
        ServiceFeeProfitShareInfo profitShareInfo = null;
        ServiceFeeDirectInfo directInfo = null;
        if (chargeModeList.contains(ChargeModeEnum.USAGE.getByCode()) || chargeModeList.contains(ChargeModeEnum.ALL.getByCode())) {
            profitShareInfo = ServiceFeeProfitShareInfo.builder()
                    .chargeMethod(ChargeMethodEnum.PROFIT)
                    .profitShareRatio(new NumberRange<>("0.3", "0.6"))
                    .minCharge(new NumberRange<>(100L, 2000L))
                    .commission(new NumberRange<>(100L, 2000L))
                    .feeModeIdList(ImmutableList.of("m1000017518"))
                    .profitShareBase(ServiceFeeProfitShareInfo.ProfitShareBase.builder()
                            .type(ProfitShareBaseTypeEnum.CUSTOM)
                            .metrics(ImmutableList.of("settlement_amount", "effective_amount", "delivery_amount"))
                            .build())
                    .build();
        }
        if (chargeModeList.contains(ChargeModeEnum.PERIOD.getByCode()) || chargeModeList.contains(ChargeModeEnum.ALL.getByCode())) {
            directInfo = ServiceFeeDirectInfo.builder()
                    .itemCode("50945")
                    .chargeCycle(ChargeCycleEnum.YEAR)
                    .chargeCycleNumber(3)
                    .chargeMethod(ChargeMethodEnum.DIRECT)
                    .chargeAmount(new NumberRange<>(1L, 2000L))
//                    .unrenewedId(1L)
//                    .unrenewedProfitShareRatio("0.3")
//                    .unrenewedMinCharge(150L)
                    .renewalNotification(ServiceFeeDirectInfo.RenewalNotification.builder()
                            .remind("提醒一下")
                            .success("成功了")
                            .failure("失败了")
                            .build())
                    .build();
        }
        if (chargeModeList.contains(ChargeModeEnum.ALL.getByCode())) {
            chargeModeList = ImmutableList.of(ChargeModeEnum.USAGE.getByCode(), ChargeModeEnum.PERIOD.getByCode());
        }
        CreateServiceFeeRequest createServiceFeeRequest = new CreateServiceFeeRequest();
        createServiceFeeRequest.setInfo(serviceFeeBaseInfo);
        createServiceFeeRequest.setChargeModeList(chargeModeList);
        createServiceFeeRequest.setProfitShare(profitShareInfo);
        createServiceFeeRequest.setDirect(directInfo);
        return createServiceFeeRequest;
    }


    String eventStr = "{\n" +
            "    \"module\":\"PAY\",\n" +
            "    \"object_type\":\"period-pay\",\n" +
            "    \"event_type\":\"renewal_adjust_complete\",\n" +
            "    \"operator_id\":null,\n" +
            "    \"operator_type\":\"RENEWAL_ADJUST\",\n" +
            "    \"operator_platform\":null,\n" +
            "    \"status\":1,\n" +
            "    \"biz_type\":\"SAAS\",\n" +
            "    \"biz_sn\":\"dbb7d19d1dd38923d7c5a786c60eacf3\",\n" +
            "    \"biz_item_code\":\"6534\",\n" +
            "    \"merchant_sn\":\"21690003215621\",\n" +
            "    \"store_sn\":\"21590000000852445\",\n" +
            "    \"level\":\"STORE\",\n" +
            "    \"charge_cycle\":\"YEAR\",\n" +
            "    \"charge_number\":3,\n" +
            "    \"begin_date\":1706496288000,\n" +
            "    \"end_date\":1806422399000,\n" +
            "    \"life_time\":null,\n" +
            "    \"extra\":null\n" +
            "}";

}

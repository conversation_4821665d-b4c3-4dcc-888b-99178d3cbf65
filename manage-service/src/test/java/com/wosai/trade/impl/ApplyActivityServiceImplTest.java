package com.wosai.trade.impl;

import com.alibaba.fastjson.JSONObject;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.databus.event.audit.AuditInstanceApproveEvent;
import com.wosai.databus.event.audit.AuditInstanceEvent;
import com.wosai.trade.BaseTest;
import com.wosai.trade.biz.activity.ModifyApplyActivityBiz;
import com.wosai.trade.model.constant.ManageConstant;
import com.wosai.trade.repository.dao.ActivityApplyDOMapper;
import com.wosai.trade.repository.dao.ActivityDOMapper;
import com.wosai.trade.repository.dao.entity.ActivityApplyEntity;
import com.wosai.trade.repository.dao.entity.ActivityEntity;
import com.wosai.trade.service.ApplyActivityService;
import com.wosai.trade.service.FeeRateService;
import com.wosai.trade.service.activity.request.*;
import com.wosai.trade.service.activity.response.ApplyActivityResponse;
import com.wosai.trade.service.activity.response.ApplyConditionQueryResponse;
import com.wosai.trade.service.activity.response.ApplyInfoResponse;
import com.wosai.trade.service.activity.response.RiskRestoreResponse;
import com.wosai.trade.service.enums.ComboConfigLevelEnum;
import com.wosai.trade.service.enums.RiskActivityProcessTypeEnum;
import com.wosai.trade.service.request.ApplyFeeRateRequest;
import com.wosai.trade.service.result.ListResult;
import com.wosai.trade.util.ConstantUtil;
import com.wosai.trade.util.JsonUtil;
import com.wosai.trade.util.LocalDateTimeUtil;
import com.wosai.upay.core.exception.CoreInvalidParameterException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;

import java.util.*;

@Slf4j
public class ApplyActivityServiceImplTest extends BaseTest {
    @Autowired
    private ApplyActivityService applyService;
    @Autowired
    private ActivityDOMapper activityDOMapper;
    @Autowired
    private FeeRateService feeRateService;
    @Autowired
    private ActivityApplyDOMapper applyDOMapper;
    @Autowired
    private ModifyApplyActivityBiz modifyApplyActivityBiz;

    /*@Mock
    private PaywayActivityService paywayActivityService;

    @InjectMocks
    private RiskActivityProcessBiz riskActivityProcessBiz;

    private void beforeMockPaywayActivityService() {
        //mock cancel
        PaySourceHandelRequest paySourceHandelRequest = new PaySourceHandelRequest();
        paySourceHandelRequest.setStatus(PaySourceHandleConstants.HANDLE_SUCCESS);
        paySourceHandelRequest.setType(2);
        paySourceHandelRequest.setMsg("mock success");
        CommonResult<PaySourceHandelRequest> cancelResult = new CommonResult<>();
        cancelResult.setCode(CommonResult.SUCCESS);
        cancelResult.setMsg("mock success");
        cancelResult.setBiz_response(paySourceHandelRequest);
        PowerMockito.when(paywayActivityService.cancel(Mockito.any())).thenReturn(null);

        //mock
        PowerMockito.when(paywayActivityService.getTobeRestoredApplyId(Mockito.any())).thenReturn(null);


        PowerMockito.when(paywayActivityService.restore(Mockito.any(PayWayActivityCancelAndRestoreRequest.class))).thenReturn(cancelResult);


    }*/

    @Test
    public void test() {
        ActivityApplyEntity lst = applyDOMapper.selectByPrimaryKey(98159420851L);
        Date expire = lst.fetchExpirationTime();
        System.out.println(LocalDateTimeUtil.getFormatDateTime(expire));
    }


    @Test
    public void testPayWay() {

        ActivityApplyEntity activityApplyEntity = applyDOMapper.selectByPrimaryKey(258L);

        ApplyFeeRateRequest request = new ApplyFeeRateRequest();
        request.setMerchantSn(activityApplyEntity.getMerchant_sn());
        ActivityEntity activityEntity = activityDOMapper.selectByPrimaryKey(activityApplyEntity.getActivity_id());
        if (activityEntity == null) {
            throw new CoreInvalidParameterException("活动不存在");
        }
        if (ComboConfigLevelEnum.MERCHANT.name().equalsIgnoreCase(activityEntity.getConfig_level())) {
            request.setMerchantSn(activityApplyEntity.getMerchant_sn());
        } else if (ComboConfigLevelEnum.STORE.name().equalsIgnoreCase(activityEntity.getConfig_level())) {
            request.setStoreSn(activityApplyEntity.getSn());
        } else if (ComboConfigLevelEnum.TERMINAL.name().equalsIgnoreCase(activityEntity.getConfig_level())) {
            request.setTerminalSn(activityApplyEntity.getSn());
        }
        request.setTradeComboId(activityEntity.getCombo_id());
        request.setOperator(ManageConstant.USER_SYSTEM);
        request.setOperatorName(ManageConstant.USER_SYSTEM);
        request.setAuditSn("达到活动生效规则自动生效");
        request.setCheck(true);
        List<Integer> payWayList = activityApplyEntity.buildPayWay();
        if (CollectionUtils.isNotEmpty(payWayList)) {
            request.setApplyPartialPayway(true);
            Map<String, String> feeRateMap = activityApplyEntity.buildFeeRate();
            if (WosaiMapUtils.isNotEmpty(feeRateMap)) {
                request.setApplyFeeRateMap(feeRateMap);
            } else {
                Map<String, String> wayNoFeeRate = new HashMap<>();
                for (Integer way : payWayList) {
                    wayNoFeeRate.put(String.valueOf(way), "");
                }
                request.setApplyFeeRateMap(wayNoFeeRate);
            }
        }
        feeRateService.applyFeeRateOne(request);

    }

    @Test
    public void testApply() {

        ApplyActivityRequest request = JSONObject.parseObject(applyInfo, ApplyActivityRequest.class);

        AuditInstanceEvent audit = new AuditInstanceApproveEvent();
        audit.setAuditId("1334");
        audit.setOperatorName("system");

        ApplyActivityResponse apply = applyService.apply(request);
        System.out.println(JSONObject.toJSONString(apply));

    }

    /**
     * 接口申请
     */
    @Test
    public void testApply001() {
        String merchantSn = "21690003629637";
        log.info("富友-报名 begin");
        ApplyActivityRequest applyRequest = new ApplyActivityRequest();
        applyRequest.setMerchantSn(merchantSn);
        applyRequest.setActivityId(7944L);
        applyRequest.setOperator("风控处置");
        applyRequest.setSubMerchantSn("20712112424");
        applyRequest.setSubStatusDetailId(5077L);
        ApplyActivityResponse apply = modifyApplyActivityBiz.apply(applyRequest);
        log.info("富友-报名 end");
        Assert.assertTrue("活动申请失败", Objects.nonNull(apply) && apply.isSuccess());
    }

    @Test
    public void testApply002() {
        String str = "{\n" +
                "            \"activityId\": 10112,\n" +
                "            \"merchantSn\": \"21690003629637\",\n" +
                "            \"applyPayFeeRates\": [\n" +
                "                {\n" +
                "                    \"feeRate\": \"0.5\",\n" +
                "                    \"payWay\": 2\n" +
                "                }\n" +
                "            ]\n" +
                "        }";
        ApplyActivityRequest request = JsonUtil.decode(str, ApplyActivityRequest.class);


        ApplyActivityResponse apply = modifyApplyActivityBiz.apply(request);
        System.out.println(JSONObject.toJSONString(apply));

    }





    @Test
    public void conditionQuery() {
        ApplyConditionQueryRequest request = new ApplyConditionQueryRequest();
        request.setPage(1);
        request.setPageSize(100);
        request.setSn("21690003336056");
        //request.setRiskActivityProcessType(RiskActivityProcessTypeEnum.WECHAT_UNIVERSITY);

        ListResult<ApplyConditionQueryResponse> result =
                applyService.conditionQuery(request);
        System.out.println(JSONObject.toJSONString(result));
    }

    @Test
    public void takeEffect() {
        applyService.takeEffect(98159425091L);
    }


    @Test
    public void invalid() {
        applyService.invalid(112L);
    }

    @Test
    public void processInfo() {
        ApplyInfoResponse response = applyService.applyInfo(15L);
        System.out.println(JSONObject.toJSONString(response));

    }


    @Test
    public void cancel() {
        String str = "{\"cancelDescribe\":{\"operator\":\"28302628071218739\",\"operatorName\":\"陈旻\",\"reason\":\"测试\",\"operatorType\":null},\"cancelEntity\":{\"occurSource\":null,\"cancelByActivityApply\":{\"activityApplyId\":98159415632,\"payways\":null},\"cancelByFile\":null},\"extra\":null}";
        CancelActivityApplyRequest request = JsonUtil.decode(str, CancelActivityApplyRequest.class);
        applyService.cancel(request);

    }

    @Test
    public void restoreByActivityId() {
        //beforeMockPaywayActivityService();

        StopWatch stopWatch = new StopWatch();
        String merchantSn = "21690003344394";
        stopWatch.start();
        log.info("风控处置-恢复 begin");
        RestoreByActivityIdRequest restoreRequest = new RestoreByActivityIdRequest();
        RestoreByActivityIdRequest.RestoreEntity restore = new RestoreByActivityIdRequest.RestoreEntity();
        restore.setMerchantSn(merchantSn);
        restore.setRiskActivityProcessTypeEnum(RiskActivityProcessTypeEnum.WECHAT_UNIVERSITY);
        RestoreByActivityIdRequest.RestoreDescribe restoreDescribe = new RestoreByActivityIdRequest.RestoreDescribe();
        restoreDescribe.setOperator("风控平台tst");
        restoreDescribe.setOperatorName("风控平台tst");
        restoreDescribe.setReason("test恢复原因说明");
        restoreRequest.setRestoreEntity(restore);
        restoreRequest.setDescribe(restoreDescribe);
        RiskRestoreResponse response = applyService.restoreByActivityId(restoreRequest);
        stopWatch.stop();
        log.info("风控处置-恢复 end. cost:{}ms response:{}", stopWatch.getTotalTimeMillis(), JsonUtil.encode(response));
        Assert.assertNotNull("恢复处置失败", response);
    }

    @Test
    public void cancelByActivityId() {
        StopWatch stopWatch = new StopWatch();
        String merchantSn = "21690003344394";
        stopWatch.start();
        log.info("风控处置-取消 begin");
        CancelByActivityIdRequest cancelRequest = new CancelByActivityIdRequest();
        CancelByActivityIdRequest.CancelEntity cancel = new CancelByActivityIdRequest.CancelEntity();
        cancel.setMerchantSn(merchantSn);
        cancel.setRiskActivityProcessTypeEnum(RiskActivityProcessTypeEnum.WECHAT_UNIVERSITY);
        CancelByActivityIdRequest.CancelDescribe describe = new CancelByActivityIdRequest.CancelDescribe();
        describe.setOperator("风控平台tst");
        describe.setOperatorName("风控平台tst");
        describe.setReason("test取消原因说明");
        cancelRequest.setCancelEntity(cancel);
        cancelRequest.setDescribe(describe);
        applyService.cancelByActivityId(cancelRequest);
        stopWatch.stop();
        log.info("风控处置-取消 end. cost:{}ms", stopWatch.getTotalTimeMillis());
    }

    /**
     * 风控处置测试用例
     */
    @Test
    public void risk() {
        StopWatch stopWatch = new StopWatch();
        String merchantSn = "21690003344394";
        /*log.info("风控处置-报名 begin");
        ApplyActivityRequest applyRequest = new ApplyActivityRequest();
        applyRequest.setMerchantSn(merchantSn);
        applyRequest.setActivityId(3562L);
        applyRequest.setOperator("风控处置");
        applyRequest.setSubMerchantSn("20712112424");
        applyRequest.setSubStatusDetailId(996L);
        ApplyActivityResponse apply = applyService.apply(applyRequest);
        log.info("风控处置-报名 end");
        Assert.assertTrue("活动申请失败", Objects.nonNull(apply) && apply.isSuccess());
*/
        stopWatch.start();
        log.info("风控处置-取消 begin");
        CancelByActivityIdRequest cancelRequest = new CancelByActivityIdRequest();
        CancelByActivityIdRequest.CancelEntity cancel = new CancelByActivityIdRequest.CancelEntity();
        cancel.setMerchantSn(merchantSn);
        cancel.setRiskActivityProcessTypeEnum(RiskActivityProcessTypeEnum.WECHAT_UNIVERSITY);
        CancelByActivityIdRequest.CancelDescribe cancelDescribe = new CancelByActivityIdRequest.CancelDescribe();
        cancelDescribe.setOperator("风控平台tst");
        cancelDescribe.setOperatorName("风控平台tst");
        cancelDescribe.setReason("test取消原因说明");
        cancelRequest.setCancelEntity(cancel);
        cancelRequest.setDescribe(cancelDescribe);
        applyService.cancelByActivityId(cancelRequest);
        stopWatch.stop();
        log.info("风控处置-取消 end. cost:{}ms", stopWatch.getTotalTimeMillis());

        stopWatch.start();
        log.info("风控处置-恢复 begin");
        RestoreByActivityIdRequest restoreRequest = new RestoreByActivityIdRequest();
        RestoreByActivityIdRequest.RestoreEntity restore = new RestoreByActivityIdRequest.RestoreEntity();
        restore.setMerchantSn(merchantSn);
        restore.setRiskActivityProcessTypeEnum(RiskActivityProcessTypeEnum.WECHAT_UNIVERSITY);
        RestoreByActivityIdRequest.RestoreDescribe restoreDescribe = new RestoreByActivityIdRequest.RestoreDescribe();
        restoreDescribe.setOperator("风控平台tst");
        restoreDescribe.setOperatorName("风控平台tst");
        restoreDescribe.setReason("test恢复原因说明");
        restoreRequest.setRestoreEntity(restore);
        restoreRequest.setDescribe(restoreDescribe);
        RiskRestoreResponse response = applyService.restoreByActivityId(restoreRequest);
        stopWatch.stop();
        log.info("风控处置-恢复 end. cost:{}ms response:{}", stopWatch.getTotalTimeMillis(), JsonUtil.encode(response));
        Assert.assertNotNull("恢复处置失败", response);
    }

    @Test
    public void cancelByActivityIdJson() {
        String json = "{\"cancelEntity\":{\"merchantSn\":\"21690003488751\",\"activityId\":2741,\"riskActivityProcessTypeEnum\":null},\"describe\":{\"operator\":\"风控平台人\",\"operatorName\":\"syf\",\"reason\":\"多次取消返佣未知\"}}";
        CancelByActivityIdRequest request = JSONObject.parseObject(json, CancelByActivityIdRequest.class);
        applyService.cancelByActivityId(request);
    }


    @Test
    public void changeByActivityCombo() {
        ChangeActivityComboRequest request = ChangeActivityComboRequest.builder()
                .combo(ChangeActivityComboRequest.ComboEntity.builder()
                        .merchantSn("21690003488744")
                        .auditSn("返佣成功")
                        .activityId(2660L)
                        .subStatusId(93L)
                        .build())
                .describe(ChangeActivityComboRequest.ComboDescribe.builder()
                        .operator(ConstantUtil.SYSTEM_NAME)
                        .operatorName(ConstantUtil.SYSTEM_NAME)
                        .reason("返佣成功套餐切换")
                        .build())
                .build();
        applyService.changeByActivityCombo(request);
    }

    @Test
    public void changeByActivityCombo1() {
        String json = "{\"combo\":{\"auditSn\":\"活动套餐切换通用申请,SP723820230331000013\",\"merchantSn\":\"21690003486042\",\"activityId\":3599,\"subStatusId\":null,\"comboId\":6607,\"applyFeeRateMap\":{\"2\":\"0.28\"}},\"describe\":{\"operator\":\"system\",\"operatorName\":\"system\",\"reason\":\"活动套餐切换通用申请\"}}";
        ChangeActivityComboRequest request =JsonUtil.decode(json, ChangeActivityComboRequest.class);
        applyService.changeByActivityCombo(request);
    }

    String applyInfo = "{\n" +
            "    \"activityId\": 1003,\n" +
            "    \"applyPayFeeRates\": [\n" +
            "        {\n" +
            "            \"feeRate\": \"0.39\",\n" +
            "            \"payWay\": 2\n" +
            "        },\n" +
            "        {\n" +
            "            \"feeRate\": \"0.39\",\n" +
            "            \"payWay\": 3\n" +
            "        }\n" +
            "    ],\n" +
            "    \"auditId\": 102407,\n" +
            "    \"discountQuota\": 700,\n" +
            "    \"discountQuotaFeeRate\": \"0.1\",\n" +
            "    \"merchantSn\": \"21690003324156\",\n" +
            "    \"sn\": \"21690003324156\"\n" +
            "}";
    String auditInfo = "{\n" +
            "    \"auditCallBackProperty\": {\n" +
            "        \"resultFail\": 1\n" +
            "    },\n" +
            "    \"auditCommentProperty\": {\n" +
            "        \"id\": \"64669579\",\n" +
            "        \"operator\": \"0af4121d-6cf5-19cc-816c-f66ffddc0000\",\n" +
            "        \"operatorName\": \"公用测试账号\",\n" +
            "        \"platform\": \"SP\"\n" +
            "    },\n" +
            "    \"auditId\": \"102407\",\n" +
            "    \"auditSn\": \"SP723820220609000005\",\n" +
            "    \"auditTemplateId\": \"46301\",\n" +
            "    \"bizKey\": \"terminal_5180000427119\",\n" +
            "    \"businessMap\": {\n" +
            "        \"feet_rate_list\": [\n" +
            "            {\n" +
            "                \"rate\": 0.39,\n" +
            "                \"pay_way_list\": [\n" +
            "                    \"支付宝\",\n" +
            "                    \"微信\"\n" +
            "                ]\n" +
            "            }\n" +
            "        ],\n" +
            "        \"apply_merchant\": \"single\",\n" +
            "        \"quota\": 700,\n" +
            "        \"activity_id\": \"1003\",\n" +
            "        \"action\": \"apply\",\n" +
            "        \"merchant\": {\n" +
            "            \"merch_info.sn\": {\n" +
            "                \"keyName\": \"商户号\",\n" +
            "                \"value\": \"21690003333385\"\n" +
            "            },\n" +
            "            \"merch_info.name\": {\n" +
            "                \"keyName\": \"商户名称\",\n" +
            "                \"value\": \"业务开通进件测试拉卡拉\"\n" +
            "            }\n" +
            "        },\n" +
            "        \"commentdetails\": {\n" +
            "            \"id\": \"64669579\",\n" +
            "            \"operatorName\": \"公用测试账号\",\n" +
            "            \"operator\": \"0af4121d-6cf5-19cc-816c-f66ffddc0000\",\n" +
            "            \"platform\": \"SP\"\n" +
            "        },\n" +
            "        \"audit_info\": {\n" +
            "            \"id\": \"102407\",\n" +
            "            \"sn\": \"SP723820220609000005\",\n" +
            "            \"operator\": \"0af4121d-6cf5-19cc-816c-f66ffddc0000\",\n" +
            "            \"platform\": \"SP\",\n" +
            "            \"status\": \"2\",\n" +
            "            \"time\": 1654755237000,\n" +
            "            \"template_id\": 46301,\n" +
            "            \"template_name\": \"费率活动通用申请\"\n" +
            "        },\n" +
            "        \"quota_fee_rate\": 0.1\n" +
            "    },\n" +
            "    \"ctime\": 1654755226000,\n" +
            "    \"eventType\": \"AUDIT_APPROVE\",\n" +
            "    \"finishTime\": 1654755240240,\n" +
            "    \"formId\": \"63582\",\n" +
            "    \"module\": \"CRM\",\n" +
            "    \"objectType\": \"AUDIT\",\n" +
            "    \"operatorId\": \"0af4121d-6cf5-19cc-816c-f66ffddc0000\",\n" +
            "    \"operatorName\": \"公用测试账号\",\n" +
            "    \"operatorPlatform\": \"SP\",\n" +
            "    \"seq\": 977323,\n" +
            "    \"templateEvent\": \"fix_or_range_fee_rate_activity_apply\",\n" +
            "    \"templateId\": \"68002\",\n" +
            "    \"timestamp\": 1654755242263\n" +
            "}";

    @Test
    public void paySourceAuditApproved() {
        ApplyActivityResponse response = applyService.paySourceAuditApproved(134597L, JSONObject.parseObject(auditApproved, AuditInstanceApproveEvent.class));
        Assert.assertTrue("审批完成失败", Objects.nonNull(response) && response.isSuccess());
    }

    public static final String auditApproved = "{\"module\":\"CRM\",\"objectType\":\"AUDIT\",\"eventType\":\"AUDIT_APPROVE\",\"operatorId\":\"c0a885db-81d1-1f61-8181-d2f7b93c0000\",\"operatorType\":null,\"operatorPlatform\":\"SP\",\"auditId\":\"134597\",\"auditSn\":\"SP1953420221213000019\",\"auditTemplateId\":\"65802\",\"templateId\":\"95140\",\"formId\":\"2440058\",\"operator\":null,\"operatorName\":\"李芸测试\",\"operatorOrgCode\":null,\"operatorOrgNamePath\":null,\"ctime\":1670918193000,\"finishTime\":1670918216466,\"templateEvent\":\"wechat_university_activity_rate_platform_23\",\"bizKey\":\"SP_7f93f1a5-07e9-4967-88ec-1455885ff409\",\"businessMap\":{\"inner_photo\":[\"http://wosai-sales.oss-cn-hangzhou.aliyuncs.com/workflow/05%E5%8D%A0%E4%BD%8D%E5%9B%BE2-140fb51c9815c5abc0bde9a8f11407e1.jpg?type=file\"],\"school_qualification_photo_4\":[\"http://wosai-sales.oss-cn-hangzhou.aliyuncs.com/workflow/05%E5%8D%A0%E4%BD%8D%E5%9B%BE2-140fb51c9815c5abc0bde9a8f11407e1.jpg?type=file\"],\"school_attribute\":\"公立非高校\",\"zero_fee_rate_promise_photo\":[\"http://wosai-sales.oss-cn-hangzhou.aliyuncs.com/workflow/05%E5%8D%A0%E4%BD%8D%E5%9B%BE2-140fb51c9815c5abc0bde9a8f11407e1.jpg?type=file\"],\"activity_id\":\"2661\",\"header_photo\":[\"http://wosai-sales.oss-cn-hangzhou.aliyuncs.com/workflow/05%E5%8D%A0%E4%BD%8D%E5%9B%BE2-140fb51c9815c5abc0bde9a8f11407e1.jpg?type=file\"],\"commentdetails\":{\"comment\":\"同意\",\"id\":\"64731441\",\"operatorName\":\"李芸测试\",\"operator\":\"c0a885db-81d1-1f61-8181-d2f7b93c0000\",\"platform\":\"SP\"},\"cooperation_evidence\":[\"http://wosai-sales.oss-cn-hangzhou.aliyuncs.com/workflow/05%E5%8D%A0%E4%BD%8D%E5%9B%BE2-140fb51c9815c5abc0bde9a8f11407e1.jpg?type=file\"],\"merchant_sn\":{\"merch_info.sn\":{\"keyName\":\"商户号\",\"value\":\"21690003484210\"},\"extra.weixin_school_sn\":{\"keyName\":\"微信校园商户号\",\"value\":null},\"merch_info.name\":{\"keyName\":\"商户名称\",\"value\":\"业务开通1213pCrb\"}},\"food_business_license\":[\"http://wosai-sales.oss-cn-hangzhou.aliyuncs.com/workflow/05%E5%8D%A0%E4%BD%8D%E5%9B%BE2-140fb51c9815c5abc0bde9a8f11407e1.jpg?type=file\"],\"audit_info\":{\"id\":\"134597\",\"sn\":\"SP1953420221213000019\",\"operator\":\"c0a885db-81d1-1f61-8181-d2f7b93c0000\",\"platform\":\"SP\",\"comment\":\"同意\",\"status\":\"2\",\"time\":1670918217000,\"template_id\":65802,\"template_name\":\"23年微信间连高校食堂活动报名\"}},\"auditCommentProperty\":{\"id\":\"64731441\",\"operator\":\"c0a885db-81d1-1f61-8181-d2f7b93c0000\",\"operatorName\":\"李芸测试\",\"platform\":\"SP\",\"comment\":\"同意\"},\"auditCallBackProperty\":{\"resultFail\":1}}";
}
package com.wosai.trade.impl;

import com.alibaba.fastjson.JSONObject;
import com.wosai.trade.BaseTest;
import com.wosai.trade.repository.dao.QuotaActivityEntityMapper;
import com.wosai.trade.service.QuotaActivityService;
import com.wosai.trade.service.activity.request.AddQuotaActivityRequest;
import com.wosai.trade.service.activity.request.QuotaActivityConditionQueryRequest;
import com.wosai.trade.service.activity.request.UpdateQuotaActivityRequest;
import com.wosai.trade.service.activity.response.ComboInfo;
import com.wosai.trade.service.activity.response.QuotaActivityConditionQueryResponse;
import com.wosai.trade.service.activity.response.QuotaActivityDetailsQueryResponse;
import com.wosai.trade.service.activity.response.QuotaActivityResponse;
import com.wosai.trade.service.enums.QuotaPaywayCategoryEnum;
import com.wosai.trade.service.result.ListResult;
import com.wosai.trade.util.JsonUtil;
import com.wosai.trade.util.OssUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class QuotaActivityServiceImplTest extends BaseTest {
    @Autowired
    private QuotaActivityService quotaActivityService;
    @Autowired
    private QuotaActivityEntityMapper activityMapper;
    @Autowired
    private TradeCommonService tradeCommonService;

    @Test
    public void testQueryUsableCombo() {
        List<ComboInfo> comboInfos = quotaActivityService.queryUsableCombo("支付源");
        System.out.println(JSONObject.toJSONString(comboInfos));
    }

    @Test
    public void create() {
        AddQuotaActivityRequest request = JSONObject.parseObject(s, AddQuotaActivityRequest.class);
        QuotaActivityResponse response = quotaActivityService.create(request);
        System.out.println(JSONObject.toJSONString(response));
    }

    @Test
    public void create001() {
        AddQuotaActivityRequest request = JSONObject.parseObject(s, AddQuotaActivityRequest.class);
        request.getInfo().setName("减免额度测试");
        QuotaActivityResponse response = quotaActivityService.create(request);
        System.out.println(JSONObject.toJSONString(response));
    }

    @Test
    public void query() {
        QuotaActivityConditionQueryRequest queryRequest = new QuotaActivityConditionQueryRequest();
        ListResult<QuotaActivityConditionQueryResponse> result = quotaActivityService.conditionQuery(queryRequest);
        System.out.println(JSONObject.toJSONString(result));
    }

    @Test
    public void query001() {
        QuotaActivityConditionQueryRequest queryRequest = new QuotaActivityConditionQueryRequest();
        //queryRequest.setName("额度测试");
        queryRequest.setPaywayCategory(QuotaPaywayCategoryEnum.ALL);
        ListResult<QuotaActivityConditionQueryResponse> result = quotaActivityService.conditionQuery(queryRequest);
        System.out.println("result=" + JsonUtil.encode(result));
    }

    @Test
    public void queryById() {
        QuotaActivityDetailsQueryResponse result = quotaActivityService.queryById(9L);
        System.out.println(JSONObject.toJSONString(result));
    }

    @Test
    public void expire() {
        List<Long> expireActivity = activityMapper.getExpireActivity();

        for (Long aLong : expireActivity) {
            System.out.println(aLong);
        }
    }


    @Test
    public void tag() {
        tradeCommonService.tag("c3b75338-81d0-47ca-8f1e-5d224902bd8d",
                "9de2599c-8a83-42f5-8f28-a8d1f2355b39", true);
    }


    @Test
    public void uploadFile() {
        String path = "/Users/<USER>/Desktop/需求开发2022/10/支付源活动迁移/存量活动迁移列表.xlsx";
        String s = OssUtil.uploadFileToOSS(path);
        System.out.println(s);
    }


    String s = "{\"info\":{\"name\":\"自动化用例创建活动名称2025-04-02\",\"type\":\"TOTAL_BRANCH\",\"desc\":\"自动化用例创建活动描述2025-04-02\",\"tradeAppId\":1,\"tradeAppName\":null,\"configLevel\":\"merchant\",\"status\":null,\"operator\":\"自动化用例创建活动操作人2025-04-02\",\"operatorId\":null,\"tags\":{\"labelId\":\"18d6debf\",\"effectiveLabelValue\":\"9970c702\",\"cancelLabelValue\":\"811a390d\",\"endLabelValue\":\"e0cdef0c\"},\"merchantNotice\":{\"applySuccess\":\"商户通知活动报名成功\",\"applyCancel\":\"商户通知活动取消成功\",\"activityExpired\":null,\"bankcardInvalid\":null,\"discountQuotaUseUp\":null,\"manualCancel\":null},\"saleNotice\":{\"applySuccess\":\"销售通知活动报名成功\",\"applyCancel\":\"销售通知活动取消成功\",\"activityExpired\":null,\"bankcardInvalid\":null,\"discountQuotaUseUp\":null,\"manualCancel\":null},\"quotaType\":null},\"rule\":{\"startTime\":*************,\"endTime\":*************,\"cancelReApply\":1,\"bandComboIds\":[12,34],\"acquirers\":[\"lkl\",\"tonglian\"],\"bankName\":\"中国建设\",\"cities\":[\"上海市\",\"北京市\"],\"discountQuota\":{\"type\":\"fix\",\"quota\":{\"max\":\"760\",\"min\":\"36\"},\"feeRate\":{\"max\":\"0.6\",\"min\":\"0.1\"}},\"paywayCategory\":\"MOBILE\",\"mobileBankProvider\":null,\"scenesTypeList\":null,\"singleOriginalAmount\":null}}";

    @Test
    public void update() {
        String req = "{\"id\":1437,\"startTime\":*************,\"endTime\":***************,\"bandComboIds\":[],\"cities\":[\"广东省\",\"深圳市\",\"罗湖区\",\"峨山路91弄98号1号楼2层\",\"苏州市\",\"镇江市\"],\"merchantNotice\":{\"applySuccess\":\"1\",\"bankcardInvalid\":null,\"discountQuotaUseUp\":null,\"activityExpired\":\"测一下活动到期失效通知222\"},\"saleNotice\":{\"applySuccess\":\"5\",\"activityExpired\":null,\"bankcardInvalid\":null,\"discountQuotaUseUp\":null}}";
        UpdateQuotaActivityRequest request = JsonUtil.decode(req, UpdateQuotaActivityRequest.class);
        quotaActivityService.update(request);

    }
}
package com.wosai.trade.impl;

import cn.hutool.core.collection.ListUtil;
import com.google.common.collect.Lists;
import com.wosai.databus.event.audit.AuditInstanceCreateEvent;
import com.wosai.trade.BaseTest;
import com.wosai.trade.biz.activity.ActivityApplyAuditEventBiz;
import com.wosai.trade.biz.activity.AuditHandleBiz;
import com.wosai.trade.biz.activity.SyncApplyActivityAuditBiz;
import com.wosai.trade.biz.activity.quota.PublishApplyActivityBiz;
import com.wosai.trade.repository.dao.ActivityApplyAuditMapper;
import com.wosai.trade.service.ApplyActivityService;
import com.wosai.trade.service.activity.request.*;
import com.wosai.trade.service.activity.response.*;
import com.wosai.trade.service.request.PageInfo;
import com.wosai.trade.service.result.ListResult;
import com.wosai.trade.util.JsonUtil;
import com.wosai.upay.core.service.BusinssCommonService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import vo.ApiRequestParam;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

public class CrmActivityServiceImplTest extends BaseTest {

    @Autowired
    private CrmActivityServiceImpl crmActivityService;
    @Autowired
    private ActivityApplyAuditMapper activityApplyAuditMapper;
    @Autowired
    private ActivityApplyAuditEventBiz activityApplyAuditEventBiz;
    @Autowired
    private ApplyActivityService applyActivityService;
    @Resource
    private AuditHandleBiz auditHandleBiz;
    @Resource
    private PublishApplyActivityBiz publishApplyActivityBiz;
    @Resource
    private BusinssCommonService businssCommonService;

    @Test
    public void create() {
        AuditInstanceCreateEvent event = JsonUtil.decode(createEventStr, AuditInstanceCreateEvent.class);
        activityApplyAuditEventBiz.doEventBefore(event);
    }

    @Test
    public void apply() {
        AuditInstanceCreateEvent event = JsonUtil.decode(createEventStr, AuditInstanceCreateEvent.class);
        ApplyActivityRequest request = auditHandleBiz.buildPaySourceApplyActivityRequest(event);
        applyActivityService.apply(request);
    }

    @Test
    public void updateAudit() {
        AuditInstanceCreateEvent event = JsonUtil.decode(moStr2, AuditInstanceCreateEvent.class);
        activityApplyAuditEventBiz.doEventBefore(event);
    }

    @Test
    public void cancelAudit() {
        AuditInstanceCreateEvent event = JsonUtil.decode(cancelEventStr, AuditInstanceCreateEvent.class);
        activityApplyAuditEventBiz.doEventBefore(event);
    }

    @Test
    public void publishApply() {
        publishApplyActivityBiz.publishSourceApplyActivity(981351L);
    }

    @Test
    public void getAuditByApplyList() {
        String merchantSn = "21690003676321";
        String merchantId = businssCommonService.getMerchantIdBySn("21690003623851");
        GetAuditByApplyListRequest request = new GetAuditByApplyListRequest();
       // request.setMerchantId("5d6f8f93-8dc8-4388-ac93-19a8db92b63e");
        request.setMerchantId(merchantId);
        request.setStatus(GetAuditByApplyListRequest.APPLYING);
        ApiRequestParam<GetAuditByApplyListRequest, Map> param = new ApiRequestParam<>();
        param.setBodyParams(request);
        ListResult<GetAuditByApplyListResponse> list = crmActivityService.getAuditByApplyList(param);
        System.out.println(JsonUtil.encode(list));
    }


    @Test
    public void getAvailableActivityList() {
        String merchantId = businssCommonService.getMerchantIdBySn("21690003623851");
        GetAvailableActivityListRequest request = new GetAvailableActivityListRequest();
//        request.setMerchantId("34e86a08-8f10-4826-a1e3-84fbeecb3c7b");
        request.setMerchantId(merchantId);
        ApiRequestParam<GetAvailableActivityListRequest, Map> param = new ApiRequestParam<>();
        param.setBodyParams(request);
        List<GetAvailableActivityListResponse> list = crmActivityService.getAvailableActivityList(param);
        System.out.println(JsonUtil.encode(list));
    }

    @Test
    public void getAuditList() {
        String merchantId = businssCommonService.getMerchantIdBySn("21690003676321");
        GetAuditListRequest request = new GetAuditListRequest();
        request.setMerchantId(merchantId);
        request.setAuditId("422543");
        ApiRequestParam<GetAuditListRequest, Map> param = new ApiRequestParam<>();
        param.setBodyParams(request);
        List<GetAuditListResponse> list = crmActivityService.getAuditList(param);
        System.out.println(JsonUtil.encode(list));
    }

    @Test
    public void getChangeFeeRateHistory() {
        String merchantId = businssCommonService.getMerchantIdBySn("21690003827892");
        GetChangeFeeRateHistoryRequest request = new GetChangeFeeRateHistoryRequest();
//        request.setMerchantId("34e86a08-8f10-4826-a1e3-84fbeecb3c7b");
        request.setMerchantId(merchantId);
        ApiRequestParam<GetChangeFeeRateHistoryRequest, Map> param = new ApiRequestParam<>();
        param.setBodyParams(request);
        List<GetChangeFeeRateHistoryResponse> list = crmActivityService.getChangeFeeRateHistory(param);
        System.out.println(JsonUtil.encode(list));
    }

    @Test
    public void getAuditTemplateConfigDefault() {
        String merchantId = businssCommonService.getMerchantIdBySn("21690003827892");
        AuditTemplateConfigDefaultRequest request = new AuditTemplateConfigDefaultRequest();
        request.setMerchantId(merchantId);
        request.setActivityId(3612L);
        request.setAuditTemplateId("208349");
        ApiRequestParam<AuditTemplateConfigDefaultRequest, Map> param = new ApiRequestParam<>();
        param.setBodyParams(request);
        List<AuditTemplateConfigDefaultResponse> list = crmActivityService.getAuditTemplateConfigDefault(param);
        System.out.println(JsonUtil.encode(list));
    }

    @Resource
    private SyncApplyActivityAuditBiz syncApplyActivityBiz;
    @Test
    public void sync() {
//        9e1f7f53-1298-46dd-9ef6-e2d0639d333c
        syncApplyActivityBiz.execute("21690003523098");
    }




    public static void main(String[] args) {
        List<Integer> list = Lists.newArrayList(1,2,3,4,5,6,7);
        PageInfo request = PageInfo.of(9, 2);
        List<Integer> pageList = ListUtil.page(request.getPage() - 1, request.getPageSize(), list);
        System.out.println(pageList);
    }

    public static final String moStr2 = "{\"auditId\":\"434717\",\"auditSn\":\"SP23015720240813000003\",\"formId\":\"2740118\",\"operatorPlatform\":\"CRM\",\"module\":\"CRM\",\"auditTemplateId\":\"213449\",\"operatorOrgNamePath\":\"直营\",\"eventType\":\"AUDIT_CREATE\",\"templateId\":\"305625\",\"operatorName\":\"核心业务CRM账号\",\"objectType\":\"AUDIT\",\"operatorOrgCode\":\"00003\",\"bizKey\":\"CRM_03eb209f-aa9f-4fa7-bb0d-aefaa50bcc48\",\"templateEvent\":\"modify_pay_source_fee_rate_by_alipay\",\"ctime\":1723554132000,\"auditCallBackProperty\":{\"resultFail\":0},\"businessMap\":{\"ladder_list\":[{\"ladder:0-30\":\"0.2\",\"ladder:30-**********\":\"0.3\",\"pay_way_list\":[\"支付宝\"]}],\"activity_info\":\"9254:6450\",\"merchant\":{\"business_license.license_name\":{\"keyName\":\"营业执照名称\"},\"merch_info.sn\":{\"keyName\":\"商户号\",\"value\":\"21690003623851\"},\"acquirer\":{\"keyName\":\"收单机构\",\"value\":\"lkl\"},\"merch_info.name\":{\"keyName\":\"商户名称\",\"value\":\"业务开通1007LXwi\"}},\"action\":\"apply\",\"audit_info\":{\"id\":\"434717\",\"sn\":\"SP23015720240813000003\",\"operator\":\"3685adda-0c78-4236-b6f0-cbc2477b6789\",\"platform\":\"CRM\",\"comment\":\"\",\"status\":\"1\",\"time\":1723554132000,\"template_id\":213449,\"template_name\":\"支付宝高校食堂调价审批\"}},\"operatorId\":\"3685adda-0c78-4236-b6f0-cbc2477b6789\",\"seq\":0,\"timestamp\":1723554132219}";


    String createEventStr = "{\n" +
            "    \"auditId\": \"418162\",\n" +
            "    \"auditSn\": \"SP2820240712000004\",\n" +
            "    \"formId\": \"4681237\",\n" +
            "    \"operatorPlatform\": \"CRM\",\n" +
            "    \"module\": \"CRM\",\n" +
            "    \"auditTemplateId\": \"68225\",\n" +
            "    \"operatorOrgNamePath\": \"直营/华西/昆明/昆明/小掌柜勇者无敌战队\",\n" +
            "    \"eventType\": \"AUDIT_CREATE\",\n" +
            "    \"templateId\": \"23472\",\n" +
            "    \"operatorName\": \"杨月圆\",\n" +
            "    \"objectType\": \"AUDIT\",\n" +
            "    \"operatorOrgCode\": \"64523\",\n" +
            "    \"bizKey\": \"CRM_179e5668-cd62-4789-8400-263bd45cc514\",\n" +
            "    \"templateEvent\": \"wechat_university_activity_rate_platform_23\",\n" +
            "    \"ctime\": 1720766216000,\n" +
            "    \"auditCallBackProperty\": {\n" +
            "        \"resultFail\": 0\n" +
            "    },\n" +
            "    \"businessMap\": {\n" +
            "        \"business_2\": \"769\",\n" +
            "        \"business\": \"762\",\n" +
            "        \"header_photo\": [\n" +
            "            \"https://private-images.shouqianba.com/sales-system-gateway/2024-07-12/0e30c08e11d747918ef27fb2257cc6d8.jpg\"\n" +
            "        ],\n" +
            "        \"merchant_sn\": {\n" +
            "            \"business_license.license_name\": {\n" +
            "                \"keyName\": \"营业执照名称\",\n" +
            "                \"value\": \"昆明经济技术开发区国秀餐饮店\"\n" +
            "            },\n" +
            "            \"merch_info.sn\": {\n" +
            "                \"keyName\": \"商户号\",\n" +
            "                \"value\": \"21690003583065\"\n" +
            "            },\n" +
            "            \"extra.weixin_school_sn\": {\n" +
            "                \"keyName\": \"微信校园商户号\",\n" +
            "                \"value\": \"668718923\"\n" +
            "            },\n" +
            "            \"acquirer\": {\n" +
            "                \"keyName\": \"收单机构\",\n" +
            "                \"value\": \"haike\"\n" +
            "            },\n" +
            "            \"merch_info.name\": {\n" +
            "                \"keyName\": \"商户名称\",\n" +
            "                \"value\": \"三食堂\"\n" +
            "            },\n" +
            "            \"extra.industry_name\": {\n" +
            "                \"keyName\": \"所属行业\",\n" +
            "                \"value\": \"餐饮食堂\"\n" +
            "            },\n" +
            "            \"business_license.license_number\": {\n" +
            "                \"keyName\": \"营业执照号码\",\n" +
            "                \"value\": \"92530100MA6Q7WD93N\"\n" +
            "            }\n" +
            "        },\n" +
            "        \"audit_info\": {\n" +
            "            \"id\": \"7759641\",\n" +
            "            \"sn\": \"SP2820240712000004\",\n" +
            "            \"operator\": \"99a497c4-7e52-47da-accd-854c7fc70d56\",\n" +
            "            \"platform\": \"CRM\",\n" +
            "            \"comment\": \"\",\n" +
            "            \"status\": \"1\",\n" +
            "            \"time\": 1720766216000,\n" +
            "            \"template_id\": 2235,\n" +
            "            \"template_name\": \"微信间连高校食堂活动\"\n" +
            "        },\n" +
            "        \"license_number\": \"92530100MA6Q7WD93N\",\n" +
            "        \"inner_photo\": [\n" +
            "            \"https://private-images.shouqianba.com/sales-system-gateway/2024-07-12/3643b3e7b20a44099c1cb748854d931d.jpg\",\n" +
            "            \"https://private-images.shouqianba.com/sales-system-gateway/2024-07-12/259e269cf8104c39be3e2beefc7b7460.jpg\"\n" +
            "        ],\n" +
            "        \"school_qualification_photo\": [\n" +
            "            \"https://private-images.shouqianba.com/sales-system-gateway/2024-07-12/c6659fea7f304cf98113f9098c53f52b.png\"\n" +
            "        ],\n" +
            "        \"school_qualification_photo_1\": [\n" +
            "            \"https://private-images.shouqianba.com/sales-system-gateway/2024-07-12/ade3af4a9e9c431b8ae5c0ae5c5809f1.png\"\n" +
            "        ],\n" +
            "        \"activity_id\": \"3612:849\",\n" +
            "        \"name\": \"昆明经济技术开发区国秀餐饮店\",\n" +
            "        \"alias\": \"云南经济管理学院合作食堂\",\n" +
            "        \"cooperation_evidence\": [\n" +
            "            \"https://private-images.shouqianba.com/sales-system-gateway/2024-07-12/86e80e5e384a4a03ba1151a62048178e.jpg\"\n" +
            "        ]\n" +
            "    },\n" +
            "    \"operatorId\": \"99a497c4-7e52-47da-accd-854c7fc70d56\",\n" +
            "    \"seq\": 0,\n" +
            "    \"timestamp\": 1720766216182\n" +
            "}";

    String updateEventStr = "{\n" +
            "    \"auditId\": \"7754844\",\n" +
            "    \"auditSn\": \"SP2820240711000010\",\n" +
            "    \"formId\": \"4676439\",\n" +
            "    \"finishTime\": 1720679964644,\n" +
            "    \"operatorPlatform\": \"CRM\",\n" +
            "    \"module\": \"CRM\",\n" +
            "    \"auditTemplateId\": \"71119\",\n" +
            "    \"operatorOrgNamePath\": \"线上业务/线上业务\",\n" +
            "    \"eventType\": \"AUDIT_CREATE\",\n" +
            "    \"templateId\": \"17119\",\n" +
            "    \"operatorName\": \"李玉芹\",\n" +
            "    \"objectType\": \"AUDIT\",\n" +
            "    \"auditCommentProperty\": {\n" +
            "        \"id\": \"79956515\",\n" +
            "        \"operatorName\": \"王鹏\",\n" +
            "        \"operator\": \"5895f8f5c016979ca3f02d16\",\n" +
            "        \"platform\": \"CRM\"\n" +
            "    },\n" +
            "    \"operatorOrgCode\": \"03276\",\n" +
            "    \"bizKey\": \"CRM_58c9d2ce-4d1a-493b-8d9b-4091e2b9f647\",\n" +
            "    \"templateEvent\": \"modify_pay_source_fee_rate\",\n" +
            "    \"ctime\": 1720676341000,\n" +
            "    \"auditCallBackProperty\": {\n" +
            "        \"resultFail\": 0\n" +
            "    },\n" +
            "    \"businessMap\": {\n" +
            "        \"activity_info\": \"3612\",\n" +
            "        \"merchant\": {\n" +
            "            \"business_license.license_name\": {\n" +
            "                \"keyName\": \"营业执照名称\",\n" +
            "                \"value\": \"广州丹云文化传播有限公司\"\n" +
            "            },\n" +
            "            \"merch_info.sn\": {\n" +
            "                \"keyName\": \"商户号\",\n" +
            "                \"value\": \"21690003583065\"\n" +
            "            },\n" +
            "            \"acquirer\": {\n" +
            "                \"keyName\": \"收单机构\",\n" +
            "                \"value\": \"lkl\"\n" +
            "            },\n" +
            "            \"merch_info.name\": {\n" +
            "                \"keyName\": \"商户名称\",\n" +
            "                \"value\": \"丹云少儿美术广州中景四街店\"\n" +
            "            }\n" +
            "        },\n" +
            "        \"commentdetails\": {\n" +
            "            \"id\": \"79956515\",\n" +
            "            \"operatorName\": \"王鹏\",\n" +
            "            \"operator\": \"5895f8f5c016979ca3f02d16\",\n" +
            "            \"platform\": \"CRM\"\n" +
            "        },\n" +
            "        \"feeRate\": \"0.45\",\n" +
            "        \"feeRateType\": \"FIXED\",\n" +
            "        \"audit_info\": {\n" +
            "            \"id\": \"7754844\",\n" +
            "            \"sn\": \"SP2820240711000010\",\n" +
            "            \"operator\": \"7459323d-aee7-45b5-9f45-b11660f74772\",\n" +
            "            \"platform\": \"CRM\",\n" +
            "            \"status\": \"2\",\n" +
            "            \"time\": 1720679965000,\n" +
            "            \"template_id\": 2280,\n" +
            "            \"template_name\": \"微信间连行业活动费率变更审批\\t\"\n" +
            "        }\n" +
            "    },\n" +
            "    \"operatorId\": \"7459323d-aee7-45b5-9f45-b11660f74772\",\n" +
            "    \"seq\": 0,\n" +
            "    \"timestamp\": 1720679964701\n" +
            "}";

    String cancelEventStr = "{\n" +
            "    \"auditId\": \"7771229\",\n" +
            "    \"auditSn\": \"SP2820240715000017\",\n" +
            "    \"formId\": \"4692826\",\n" +
            "    \"finishTime\": 1721048019295,\n" +
            "    \"operatorPlatform\": \"CRM\",\n" +
            "    \"module\": \"CRM\",\n" +
            "    \"auditTemplateId\": \"15662\",\n" +
            "    \"operatorOrgNamePath\": \"服务商/华中/湖南/湖南爱迪斯/郑云凯\",\n" +
            "    \"eventType\": \"AUDIT_CREATE\",\n" +
            "    \"templateId\": \"23079\",\n" +
            "    \"operatorName\": \"艾欧尼亚\",\n" +
            "    \"objectType\": \"AUDIT\",\n" +
            "    \"auditCommentProperty\": {\n" +
            "        \"id\": \"80009376\",\n" +
            "        \"operatorName\": \"艾欧尼亚\",\n" +
            "        \"operator\": \"a0167d79-3426-42b6-88bd-38b1bae2dee5\",\n" +
            "        \"platform\": \"CRM\"\n" +
            "    },\n" +
            "    \"operatorOrgCode\": \"75512\",\n" +
            "    \"bizKey\": \"CRM_e5c3ed96-28ad-4abf-b01b-f76c077a2599\",\n" +
            "    \"templateEvent\": \"wechat_university_activity_rate_platform\",\n" +
            "    \"ctime\": 1721048016000,\n" +
            "    \"auditCallBackProperty\": {\n" +
            "        \"resultFail\": 1\n" +
            "    },\n" +
            "    \"businessMap\": {\n" +
            "        \"activity_id\": \"3612\",\n" +
            "        \"action\": \"cancel\",\n" +
            "        \"commentdetails\": {\n" +
            "            \"id\": \"80009376\",\n" +
            "            \"operatorName\": \"艾欧尼亚\",\n" +
            "            \"operator\": \"a0167d79-3426-42b6-88bd-38b1bae2dee5\",\n" +
            "            \"platform\": \"CRM\"\n" +
            "        },\n" +
            "        \"merchant_sn\": {\n" +
            "            \"merch_info.sn\": {\n" +
            "                \"keyName\": \"商户号\",\n" +
            "                \"value\": \"21690003583065\"\n" +
            "            }\n" +
            "        },\n" +
            "        \"audit_info\": {\n" +
            "            \"id\": \"7771229\",\n" +
            "            \"sn\": \"SP2820240715000017\",\n" +
            "            \"operator\": \"a0167d79-3426-42b6-88bd-38b1bae2dee5\",\n" +
            "            \"platform\": \"CRM\",\n" +
            "            \"status\": \"2\",\n" +
            "            \"time\": 1721048019000,\n" +
            "            \"template_id\": 2053,\n" +
            "            \"template_name\": \"微信间连高校0费率活动取消申请\"\n" +
            "        }\n" +
            "    },\n" +
            "    \"operatorId\": \"a0167d79-3426-42b6-88bd-38b1bae2dee5\",\n" +
            "    \"seq\": 7446807,\n" +
            "    \"timestamp\": 1721048019361\n" +
            "}";
}
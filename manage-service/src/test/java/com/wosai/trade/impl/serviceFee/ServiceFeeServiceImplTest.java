package com.wosai.trade.impl.serviceFee;

import com.google.common.collect.ImmutableList;
import com.wosai.trade.BaseTest;
import com.wosai.trade.model.NumberRange;
import com.wosai.trade.service.ServiceFeeEffectiveService;
import com.wosai.trade.service.ServiceFeeService;
import com.wosai.trade.service.enums.*;
import com.wosai.trade.service.result.ListResult;
import com.wosai.trade.service.servicefee.model.ProfitShareMetricsResponse;
import com.wosai.trade.service.servicefee.model.ServiceFeeBaseInfo;
import com.wosai.trade.service.servicefee.model.ServiceFeeDirectInfo;
import com.wosai.trade.service.servicefee.model.ServiceFeeProfitShareInfo;
import com.wosai.trade.service.servicefee.reponse.*;
import com.wosai.trade.service.servicefee.request.*;
import com.wosai.trade.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import vo.ApiRequestParam;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
public class ServiceFeeServiceImplTest extends BaseTest {

    @Resource
    private ServiceFeeService serviceFeeService;
    @Resource
    private ServiceFeeEffectiveService serviceFeeEffectiveService;


    @Test
    public void create2() {
        String str = "{\"info\":{\"id\":null,\"name\":\"自测乐学\",\"tradeAppId\":4,\"receiverNameList\":[\"打包服务费\"],\"level\":\"STORE\",\"type\":null,\"scenesType\":\"CAMPUS\",\"scenesCodes\":[\"all\"],\"tags\":[\"NQUOA1SN1FKM\"],\"markLabelExtra\":null,\"author\":\"公用测试账号\",\"description\":\"自测乐学\"},\"chargeModeList\":[\"USAGE\"],\"profitShare\":{\"chargeMethod\":\"PROFIT\",\"receiverAccountBusinessType\":\"BUSINESS_TYPE_OTHER\",\"feeModeIdList\":[\"m1000009442\",\"m1000017640\",\"m1000012889\"],\"profitShareRatio\":{\"min\":\"0\",\"max\":\"99\"},\"profitShareBase\":{\"type\":\"CUSTOM\",\"metrics\":[\"packaging_charge_fee\"]},\"minCharge\":null,\"commission\":{\"min\":0,\"max\":10000}},\"direct\":null,\"chargeMode\":\"USAGE\"}";
        CreateServiceFeeRequest request = JsonUtil.decode(str, CreateServiceFeeRequest.class);
        CreateServiceFeeResponse response = serviceFeeService.create(request);
        Assert.assertNotNull("创建收费规则失败 response为空", response);
        Assert.assertNotNull("创建收费规则失败. id为空", response.getId());
    }

    @Test
    public void create() {
        CreateServiceFeeRequest request = buildCreateServiceFeeRequest(ImmutableList.of(ChargeModeEnum.USAGE.getByCode()));
        log.info("serviceFeeService.create request: {}", JsonUtil.encode(request));
        CreateServiceFeeResponse response = serviceFeeService.create(request);
        log.info("serviceFeeService.create response: {}", response);
        Assert.assertNotNull("创建收费规则失败 response为空", response);
        Assert.assertNotNull("创建收费规则失败. id为空", response.getId());
    }

    @Test
    public void update() {
        UpdateServiceFeeRequest request = new UpdateServiceFeeRequest();
        request.setId(6L);
        request.setOperatorName("tywei");
        request.setInfo(UpdateServiceFeeRequest.BaseInfo.builder()
                        .description("abcd")
                .build());
        serviceFeeService.update(request);
    }

    @Test
    public void update2() {
        String str = "{\n" +
                "    \"id\":340,\n" +
                "    \"tradeAppId\": 4,\n" +
                "    \"operatorName\":\"公用测试账号\",\n" +
                "    \"info\":{\n" +
                "        \"scenesCodes\":[\n" +
                "\n" +
                "        ],\n" +
                "        \"markLabelExtra\":null,\n" +
                "        \"description\":\"流量服务费0-15%，起步价0-0.1，固定佣金0-0.2 v\"\n" +
                "    },\n" +
                "    \"profitShareBaseMetrics\":[\"settlement_amount\",\n" +
                "    \"ps_delivery_amount\",\n" +
                "    \"delivery_amount\",\n" +
                "    \"discount_delivery_amount\",\n" +
                "    \"commission\"]\n" +
                "}";
        UpdateServiceFeeRequest request = JsonUtil.decode(str, UpdateServiceFeeRequest.class);
        serviceFeeService.update(request);
    }

    @Test
    public void conditionQuery() {
        ServiceFeeConditionQueryRequest request = new ServiceFeeConditionQueryRequest();
        request.setPage(1);
        request.setPageSize(10);
        ListResult<ServiceFeeConditionQueryResponse> list = serviceFeeService.conditionQuery(request);
        log.info("serviceFeeService.conditionQuery response: {}", JsonUtil.encode(list));
        Assert.assertNotNull("查询列表不可为null", list);
    }

    @Test
    public void queryById() {
        ServiceFeeResponse info = serviceFeeService.queryById(51409L);
        log.info("serviceFeeService.queryById response: {}", JsonUtil.encode(info));
        Assert.assertNotNull("规则记录不存在", info);
    }

    @Test
    public void getProfitShareMetrics() {
        List<ProfitShareMetricsResponse> list = serviceFeeService.getProfitShareMetrics();
        Assert.assertTrue("获取分账指标配置为空", CollectionUtils.isNotEmpty(list));
    }

    @Test
    public void testOnOff() {
        UpdateServiceFeeStatusRequest request = new UpdateServiceFeeStatusRequest();
        request.setId(47L);
        request.setOperatorName("tywei测试账号");
        serviceFeeService.disabled(request);
        serviceFeeService.enabled(request);
    }

    /**
     * 按使用收费流程测试
     */
    @Test
    public void profitSharingFlow() {
        ApplyServiceFeeRequest request = new ApplyServiceFeeRequest();
        request.setMerchantSn("21690003344394");
        request.setSn("21590000000960962");
        request.setServiceFeeId(51090L);
//        request.setScenesCode("34592");
        request.setRemark("开通按使用收费");
        request.setPlatform("APP");
        request.setProfitShareRatio("0.38");
        request.setMinCharge(200L);
        request.setCommission(300L);
        request.setScenesCode("51090");
        request.setOperatorId("8ebaf1d0-6a1a-4550-b7de-ea54aceed166");
        request.setOperatorName("tywei测试账号");
        //-------　报名申请
        ApplyServiceFeeResponse applyResponse = serviceFeeEffectiveService.applyOne(request);
        Assert.assertNotNull("报名失败", applyResponse.getId());
        log.info("按使用收费服务费------> 报名成功. applyId:{}", applyResponse.getId());
    }

    /**
     * 按时段收费流程测试
     */
    @Test
    public void periodFlow() {
        /*CreateServiceFeeRequest request = buildCreateServiceFeeRequest(ImmutableList.of(ChargeModeEnum.PERIOD.getByCode()));
        CreateServiceFeeResponse serviceFeeResponse = serviceFeeService.create(request);
        Assert.assertNotNull("创建收费规则失败 response为空", serviceFeeResponse);
        Assert.assertNotNull("创建收费规则失败. id为空", serviceFeeResponse.getId());
        log.info("按时段收费服务费------> 创建成功. feeId:{}", serviceFeeResponse.getId());*/
        //-------　报名申请
        ApplyServiceFeeRequest applyServiceFeeRequest = new ApplyServiceFeeRequest();
        applyServiceFeeRequest.setMerchantSn("21690003344394");
        applyServiceFeeRequest.setSn("21690003344394");
        applyServiceFeeRequest.setServiceFeeId(190L);
        //applyServiceFeeRequest.setScenesCode("34592");
        applyServiceFeeRequest.setRemark("开通按时段收费");
        applyServiceFeeRequest.setPlatform("SP");
        applyServiceFeeRequest.setChargeAmount(1000L);
        applyServiceFeeRequest.setOperatorId("4fa00de6-afb9-4236-93ee-b86b93ded7cc");
        applyServiceFeeRequest.setOperatorName("tywei测试账号");
        ApplyServiceFeeResponse applyResponse = serviceFeeEffectiveService.applyOne(applyServiceFeeRequest);
        Assert.assertNotNull("报名失败", applyResponse.getId());
        log.info("按时段收费服务费------> 报名成功. applyId:{}", applyResponse.getId());
    }

    /**
     * 按时段收费+分账流程测试
     */
    @Test
    public void allFlow() {
        /*CreateServiceFeeRequest request = buildCreateServiceFeeRequest(ImmutableList.of(ChargeModeEnum.ALL.getByCode()));
        CreateServiceFeeResponse serviceFeeResponse = serviceFeeService.create(request);
        Assert.assertNotNull("创建收费规则失败 response为空", serviceFeeResponse);
        Assert.assertNotNull("创建收费规则失败. id为空", serviceFeeResponse.getId());*/
        log.info("按时段收费服务费------> 创建成功");
        ApplyServiceFeeRequest applyServiceFeeRequest = new ApplyServiceFeeRequest();
        applyServiceFeeRequest.setMerchantSn("21690003344394");
        applyServiceFeeRequest.setSn("21690003344394");
        applyServiceFeeRequest.setServiceFeeId(26L);
        applyServiceFeeRequest.setScenesCode("34592");
        applyServiceFeeRequest.setRemark("按时段收费+分账");
        applyServiceFeeRequest.setPlatform("SP");
        applyServiceFeeRequest.setChargeAmount(1000L);
        applyServiceFeeRequest.setProfitShareRatio("0.39");
        applyServiceFeeRequest.setMinCharge(130L);
        applyServiceFeeRequest.setOperatorId("8ebaf1d0-6a1a-4550-b7de-ea54aceed166");
        applyServiceFeeRequest.setOperatorName("tywei测试账号");
        //-------　报名申请
        ApplyServiceFeeResponse applyResponse = serviceFeeEffectiveService.applyOne(applyServiceFeeRequest);
        Assert.assertNotNull("报名失败", applyResponse.getId());
        log.info("按时段收费服务费------> 报名成功");
    }

    // ------------------------------------- private -------------------------------------
    private CreateServiceFeeRequest buildCreateServiceFeeRequest(List<String> chargeModeList) {
        ServiceFeeBaseInfo serviceFeeBaseInfo = ServiceFeeBaseInfo.builder()
                .name("测试服务费")
                .tradeAppId(6L)
                .level(ServiceFeeLevelEnum.MERCHANT)
                .type(ServiceFeeTypeEnum.BASE)
                //.scenesType(ServiceFeeScenesTypeEnum.CAMPUS)
                //.scenesCodes(ImmutableList.of("34592", "34591"))
                .author("tywei")
                .receiverNameList(ImmutableList.of("测试"))
                .description("测试服务费说明部分")
                .tags(ImmutableList.of("E1QDEFPQUJWB"))
                .build();
        ServiceFeeProfitShareInfo profitShareInfo = null;
        ServiceFeeDirectInfo directInfo = null;
        if (chargeModeList.contains(ChargeModeEnum.USAGE.getByCode()) || chargeModeList.contains(ChargeModeEnum.ALL.getByCode())) {
            profitShareInfo = ServiceFeeProfitShareInfo.builder()
                    .chargeMethod(ChargeMethodEnum.PROFIT)
                    .profitShareRatio(new NumberRange<>("0.3", "0.6"))
                    .minCharge(new NumberRange<>(100L, 2000L))
                    .commission(new NumberRange<>(100L, 2000L))
                    .feeModeIdList(ImmutableList.of("m1000017518"))
                    .receiverAccountBusinessType("BUSINESS_TYPE_SQB")
                    .profitShareBase(ServiceFeeProfitShareInfo.ProfitShareBase.builder()
                            .type(ProfitShareBaseTypeEnum.CUSTOM)
                            .metrics(ImmutableList.of("settlement_amount", "effective_amount", "delivery_amount"))
                            .build())
                    .build();
        }
        if (chargeModeList.contains(ChargeModeEnum.PERIOD.getByCode()) || chargeModeList.contains(ChargeModeEnum.ALL.getByCode())) {
            directInfo = ServiceFeeDirectInfo.builder()
                    .itemCode("50945")
                    .chargeCycle(ChargeCycleEnum.MONTH)
                    .chargeMethod(ChargeMethodEnum.DIRECT)
                    .chargeAmount(new NumberRange<>(100L, 2000L))
                    .unrenewedId(1L)
                    .unrenewedProfitShareRatio("0.3")
                    .unrenewedMinCharge(150L)
                    .renewalNotification(ServiceFeeDirectInfo.RenewalNotification.builder()
                            .remind("提醒一下")
                            .success("成功了")
                            .failure("失败了")
                            .build())
                    .build();
        }
        if (chargeModeList.contains(ChargeModeEnum.ALL.getByCode())) {
            chargeModeList = ImmutableList.of(ChargeModeEnum.USAGE.getByCode(), ChargeModeEnum.PERIOD.getByCode());
        }
        CreateServiceFeeRequest createServiceFeeRequest = new CreateServiceFeeRequest();
        createServiceFeeRequest.setInfo(serviceFeeBaseInfo);
        createServiceFeeRequest.setChargeModeList(chargeModeList);
        createServiceFeeRequest.setProfitShare(profitShareInfo);
        createServiceFeeRequest.setDirect(directInfo);
        return createServiceFeeRequest;
    }

    @Test
    public void filterServiceFeeByOrg() {
        FilterServiceFeeByOrgRequest request = new FilterServiceFeeByOrgRequest();
        request.setOrgId("4ab7497e-abca-4f12-9bb3-cdf71c4a65ff");
        //request.setCampusIds(ImmutableList.of("34592"));
        ApiRequestParam<FilterServiceFeeByOrgRequest, Map> param = new ApiRequestParam<>();
        param.setBodyParams(request);
        ServiceFeeByOrgResponse list = serviceFeeService.filterServiceFeeByOrg(param);
        System.out.println(JsonUtil.encode(list));
    }

    @Test
    public void queryTradeAppList() {
        SharingModeResponse info = serviceFeeService.querySharingModeInfo(7150L);
        System.out.println(JsonUtil.encode(info));
    }


    @Test
    public void queryListByTag() {
        List<ServiceFeeResponse> list = serviceFeeService.queryListByTag("SMWCPGPIZNDU");
        System.out.println(list);
    }
}
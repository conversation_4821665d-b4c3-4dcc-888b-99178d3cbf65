package com.wosai.trade.impl;

import com.google.common.collect.Lists;
import com.wosai.databus.event.audit.AuditInstanceApproveEvent;
import com.wosai.trade.BaseTest;
import com.wosai.trade.biz.audit.StoreSceneSwitchBiz;
import com.wosai.trade.util.AttachmentFileUtil;
import com.wosai.trade.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Slf4j
public class StoreSceneSwitchBizTest extends BaseTest {

    @Resource
    private StoreSceneSwitchBiz storeSceneSwitchBiz;

    @Test
    public void testMultiStoreSwitch() {
        String param = "{\n" +
                "    \"auditId\": \"525643\",\n" +
                "    \"auditSn\": \"SP1953420250109000029\",\n" +
                "    \"formId\": \"2831044\",\n" +
                "    \"finishTime\": 1736415068683,\n" +
                "    \"operatorPlatform\": \"SP\",\n" +
                "    \"module\": \"CRM\",\n" +
                "    \"auditTemplateId\": \"250610\",\n" +
                "    \"eventType\": \"AUDIT_APPROVE\",\n" +
                "    \"templateId\": \"354655\",\n" +
                "    \"operatorName\": \"公用测试账号\",\n" +
                "    \"objectType\": \"AUDIT\",\n" +
                "    \"auditCommentProperty\": {\n" +
                "        \"id\": \"65621251\",\n" +
                "        \"operatorName\": \"公用测试账号\",\n" +
                "        \"operator\": \"0af4121d-6cf5-19cc-816c-f66ffddc0000\",\n" +
                "        \"platform\": \"SP\"\n" +
                "    },\n" +
                "    \"bizKey\": \"SP_1aee8d5c-da40-4004-88d1-02f3dbfa58a2\",\n" +
                "    \"templateEvent\": \"handle_store_scene_switch\",\n" +
                "    \"ctime\": 1736415065000,\n" +
                "    \"auditCallBackProperty\": {\n" +
                "        \"resultFail\": 0\n" +
                "    },\n" +
                "    \"businessMap\": {\n" +
                "        \"commentdetails\": {\n" +
                "            \"id\": \"65621251\",\n" +
                "            \"operatorName\": \"公用测试账号\",\n" +
                "            \"operator\": \"0af4121d-6cf5-19cc-816c-f66ffddc0000\",\n" +
                "            \"platform\": \"SP\"\n" +
                "        },\n" +
                "        \"remark\": \"开通微信直连交易上送\",\n" +
                "        \"type\": \"multi_store\",\n" +
                "        \"attach_file_url\": [\n" +
                "            \"http://wosai-sales.oss-cn-hangzhou.aliyuncs.com/workflow/test1736415061581.xlsx?type=file\"\n" +
                "        ],\n" +
                "        \"audit_info\": {\n" +
                "            \"id\": \"525643\",\n" +
                "            \"sn\": \"SP1953420250109000029\",\n" +
                "            \"operator\": \"0af4121d-6cf5-19cc-816c-f66ffddc0000\",\n" +
                "            \"platform\": \"SP\",\n" +
                "            \"status\": \"2\",\n" +
                "            \"time\": 1736415069000,\n" +
                "            \"template_id\": 250610,\n" +
                "            \"template_name\": \"微信直连场景信息上送\"\n" +
                "        },\n" +
                "        \"apply_type\": \"close_scene_switch\"\n" +
                "    },\n" +
                "    \"operatorId\": \"0af4121d-6cf5-19cc-816c-f66ffddc0000\",\n" +
                "    \"seq\": 0,\n" +
                "    \"timestamp\": 1736415068717\n" +
                "}";
        AuditInstanceApproveEvent auditInstance = JsonUtil.decode(param, AuditInstanceApproveEvent.class);
        storeSceneSwitchBiz.handle(auditInstance);

    }

    @Test
    public void testSingleStoreSwitch() {
        String param = "{\n" +
                "    \"auditId\": \"529022\",\n" +
                "    \"auditSn\": \"SP1953420250114000029\",\n" +
                "    \"formId\": \"2834423\",\n" +
                "    \"finishTime\": 1736843794877,\n" +
                "    \"operatorPlatform\": \"SP\",\n" +
                "    \"module\": \"CRM\",\n" +
                "    \"auditTemplateId\": \"250610\",\n" +
                "    \"eventType\": \"AUDIT_APPROVE\",\n" +
                "    \"templateId\": \"356593\",\n" +
                "    \"operatorName\": \"公用测试账号\",\n" +
                "    \"objectType\": \"AUDIT\",\n" +
                "    \"auditCommentProperty\": {\n" +
                "        \"id\": \"65628395\",\n" +
                "        \"operatorName\": \"公用测试账号\",\n" +
                "        \"operator\": \"0af4121d-6cf5-19cc-816c-f66ffddc0000\",\n" +
                "        \"platform\": \"SP\"\n" +
                "    },\n" +
                "    \"bizKey\": \"SP_7814fa82-25eb-4923-b478-7d3c98802b45\",\n" +
                "    \"templateEvent\": \"handle_store_scene_switch\",\n" +
                "    \"ctime\": 1736843791000,\n" +
                "    \"auditCallBackProperty\": {\n" +
                "        \"resultFail\": 0\n" +
                "    },\n" +
                "    \"businessMap\": {\n" +
                "        \"store_info\": {\n" +
                "            \"store_sn\": {\n" +
                "                \"keyName\": \"门店号\",\n" +
                "                \"value\": \"21590000000854327\"\n" +
                "            }\n" +
                "        },\n" +
                "        \"commentdetails\": {\n" +
                "            \"id\": \"65628395\",\n" +
                "            \"operatorName\": \"公用测试账号\",\n" +
                "            \"operator\": \"0af4121d-6cf5-19cc-816c-f66ffddc0000\",\n" +
                "            \"platform\": \"SP\"\n" +
                "        },\n" +
                "        \"type\": \"single_store\",\n" +
                "        \"audit_info\": {\n" +
                "            \"id\": \"529022\",\n" +
                "            \"sn\": \"SP1953420250114000029\",\n" +
                "            \"operator\": \"0af4121d-6cf5-19cc-816c-f66ffddc0000\",\n" +
                "            \"platform\": \"SP\",\n" +
                "            \"status\": \"2\",\n" +
                "            \"time\": 1736843795000,\n" +
                "            \"template_id\": 250610,\n" +
                "            \"template_name\": \"微信直连场景信息上送\"\n" +
                "        },\n" +
                "        \"apply_type\": \"open_scene_switch\"\n" +
                "    },\n" +
                "    \"operatorId\": \"0af4121d-6cf5-19cc-816c-f66ffddc0000\",\n" +
                "    \"seq\": 1199230,\n" +
                "    \"timestamp\": 1736843795013\n" +
                "}";
        AuditInstanceApproveEvent auditInstance = JsonUtil.decode(param, AuditInstanceApproveEvent.class);
        storeSceneSwitchBiz.handle(auditInstance);

    }

    @Test
    public void testExcelToJson() {


        List<String> attchUrl = Arrays.asList("http://wosai-sales.oss-cn-hangzhou.aliyuncs.com/workflow/store1736472676432.xlsx?type=file");

        List<String> columns = Lists.newArrayList("id","name");
        List<Map> storeConfigList = AttachmentFileUtil.getInstance().read(columns, attchUrl);

        List<String> storeSnList = Lists.newArrayList();

        for(Map storeConfig : storeConfigList) {
            String storeSn = (String) storeConfig.get("id");
            storeSnList.add(storeSn);
        }
        System.out.println(JsonUtil.encode(storeSnList));


    }

}

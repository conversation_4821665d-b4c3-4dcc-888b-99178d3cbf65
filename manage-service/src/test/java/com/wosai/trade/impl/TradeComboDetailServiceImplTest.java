package com.wosai.trade.impl;

import com.wosai.trade.BaseTest;
import com.wosai.trade.service.result.TradeComboDetailResult;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.Assert.*;

public class TradeComboDetailServiceImplTest extends BaseTest {

    @Resource
    private TradeComboDetailServiceImpl tradeComboDetailService;
    @Test
    public void listByComboId() {
        List<TradeComboDetailResult> result = tradeComboDetailService.listByComboId(6184L);
        Assert.assertNotNull("套餐明细结果为空", result);
    }

    @Test
    public void testListByComboId() {
    }
}
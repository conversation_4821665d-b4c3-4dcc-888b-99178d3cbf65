package com.wosai.trade.impl;

import com.alibaba.fastjson.JSONObject;
import com.wosai.trade.BaseTest;
import com.wosai.trade.repository.dao.QuotaActivityApplyEntityMapper;
import com.wosai.trade.repository.dao.entity.QuotaActivityApplyEntity;
import com.wosai.trade.service.DiscountQuotaService;
import com.wosai.trade.service.QuotaActivityApplyService;
import com.wosai.trade.service.activity.constant.ActivityConstants;
import com.wosai.trade.service.activity.request.*;
import com.wosai.trade.service.activity.response.QuotaApplyActivityResponse;
import com.wosai.trade.service.activity.response.QuotaApplyConditionQueryResponse;
import com.wosai.trade.service.activity.response.QuotaApplyInfoResponse;
import com.wosai.trade.service.activity.response.quota.TransactionQuotaInfoResponse;
import com.wosai.trade.service.enums.QuotaActivityTypeEnum;
import com.wosai.trade.service.enums.QuotaPaywayCategoryEnum;
import com.wosai.trade.service.request.PreDeductDiscountQuotaRequest;
import com.wosai.trade.service.request.RollbackDiscountQuotaRequest;
import com.wosai.trade.service.result.ListResult;
import com.wosai.trade.util.JsonUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.concurrent.TimeUnit;

public class QuotaActivityApplyServiceImplTest extends BaseTest {

    @Autowired
    private QuotaActivityApplyService applyService;

    @Autowired
    private QuotaActivityApplyEntityMapper quotaActivityApplyEntityMapper;
    @Autowired
    private DiscountQuotaService discountQuotaService;

    /**
     * 多类型活动报名共存
     */
    @Test
    public void multiApply() {
        QuotaActivityApplyRequest request = new QuotaActivityApplyRequest();
        request.setActivityId(1922L);
        request.setApplyType(1);
        request.setStartTime("2025-01-01");
        request.setEndTime("2025-03-01");
        request.setMerchantSn("21690003218289");
        request.setFeeRate("0.8");
        request.setQuota(1D);
        request.setOpenAutoD1Withdraw(true);
        QuotaApplyActivityResponse apply = applyService.apply(request);
        Assert.assertNotNull(apply);
        Assert.assertTrue(CollectionUtils.isNotEmpty(apply.getApplyIds()));
        QuotaActivityApplyEntity applyDo = quotaActivityApplyEntityMapper.selectByPrimaryKey(apply.getApplyIds().get(0));
        Assert.assertNotNull(applyDo);
        Assert.assertEquals("状态比对", ActivityConstants.EFFECT, (int) applyDo.getStatus());

        /**
         * NPOS活动
         */
//        request = new QuotaActivityApplyRequest();
//        request.setActivityId(1922L);
//        request.setApplyType(1);
//        request.setStartTime("2024-10-15");
//        request.setEndTime("2024-11-01");
//        request.setMerchantSn("**************");
//        request.setFeeRate("0.2");
//        request.setQuota(100000d);
//        apply = applyService.apply(request);
//        Assert.assertNotNull(apply);
//        Assert.assertTrue(CollectionUtils.isNotEmpty(apply.getApplyIds()));
//        applyDo = quotaActivityApplyEntityMapper.selectByPrimaryKey(apply.getApplyIds().get(0));
//        Assert.assertNotNull(applyDo);
//        Assert.assertEquals("状态比对", ActivityConstants.EFFECT, (int) applyDo.getStatus());
    }

    @Test
    public void pre() throws InterruptedException {
        PreDeductDiscountQuotaRequest request = new PreDeductDiscountQuotaRequest();
        request.setProvider(1038);
        request.setPayWay(2);
        request.setFeeRate("0.8");
        request.setOriginalAmount(1000000000L);
        request.setTid("t78952test1111111");
        request.setMerchantId("6526bbc2-2c0c-426c-a51e-26893c10665a");
        discountQuotaService.preDeductDiscountQuota(request);
        TimeUnit.SECONDS.sleep(3L);
    }

    @Test
    public void rollback() throws InterruptedException {
        RollbackDiscountQuotaRequest request = new RollbackDiscountQuotaRequest();
        request.setQuotaId(67350L);
        request.setAmount(1000L);
        request.setTsn("t78952test1111121");
        request.setType(11);
        request.setProvider(1038);
        request.setMerchantId("6526bbc2-2c0c-426c-a51e-26893c10665a");
        discountQuotaService.rollbackDiscountQuota(request);
    }

    @Test
    public void testApply001() {
        String str = "{\n" +
                "    \"activityId\": 2528,\n" +
                "    \"merchantSn\": \"21690004003714\",\n" +
                "    \"applyType\": 1,\n" +
                "    \"startTime\": \"2025-03-02\",\n" +
                "    \"endTime\": \"2025-03-31\",\n" +
                "    \"quota\": 30,\n" +
                "    \"feeRate\": \"0.3\"\n" +
                "}";
        QuotaActivityApplyRequest request = JsonUtil.decode(str, QuotaActivityApplyRequest.class);
        QuotaApplyActivityResponse apply = applyService.apply(request);
        System.out.println(JSONObject.toJSONString(apply));
    }

    @Test
    public void cancelByApplyId() {
        QuotaActivityApplyEntity apply = quotaActivityApplyEntityMapper.getEffectIngApplyBySn("1680004700600", QuotaActivityTypeEnum.NFC_POS.getType());
        QuotaActivityCancelRequest request = new QuotaActivityCancelRequest();
        request.setApplyId(apply.getId());
        applyService.cancelByApplyId(request);
    }


    @Test
    public void testBulk() {
        String str = "{\"activityId\":2640,\"externalActivityId\":\"7488\",\"merchantSnList\":[\"**************\"],\"auditId\":null,\"auditSn\":null,\"applyType\":1,\"startTime\":\"2025-04-01\",\"endTime\":\"2025-04-30\",\"rolling\":0,\"quota\":6500.0,\"feeRate\":\"0.6\",\"msg\":\"bank-business批量导入\",\"operator\":\"核心业务CRM账号\",\"openAutoD1Withdraw\":null}";
        QuotaShareApplyBulkRequest request = JsonUtil.decode(str, QuotaShareApplyBulkRequest.class);
        applyService.applyBulkByApi(request);
    }

    @Test
    public void conditionQuery() {
        QuotaApplyConditionQueryRequest request = JsonUtil.decode("{\"status\":4,\"activityName\":null,\"sn\":null,\"quotaId\":null,\"merchantSnList\":[\"**************\"],\"pageStart\":0,\"page_size\":null,\"page\":null,\"date_start\":null,\"date_end\":null,\"order_by\":null}",
                QuotaApplyConditionQueryRequest.class);
        ListResult<QuotaApplyConditionQueryResponse> result = applyService.conditionQuery(request);
        System.out.println(JSONObject.toJSONString(result));

    }

    @Test
    public void applyInfo() {
        QuotaApplyInfoResponse applyDo = applyService.applyInfo(46602L);
        System.out.println(JsonUtil.encode(applyDo));
    }

    @Test
    public void conditionQueryByMerchantSnList() {
        QuotaApplyConditionQueryRequest request = new QuotaApplyConditionQueryRequest();
        /*request.setSn("21690003987725");
        request.setPageSize(1000);*/
        //request.setStatus(1);
        request.setActivityType(QuotaActivityTypeEnum.CAMPUS);
        request.setPaywayCategory(QuotaPaywayCategoryEnum.ALL);
        ListResult<QuotaApplyConditionQueryResponse> result = applyService.conditionQuery(request);
        System.out.println(JsonUtil.encode(result));

    }

    @Test
    public void appendQuota() {
        AppendQuotaRequest request = new AppendQuotaRequest();
        request.setApplyId(53892L);
        request.setAppendQuota(100D);
        applyService.appendQuota(request);
    }

    @Test
    public void invalid() {
        applyService.invalid(6189L);
    }

    @Test
    public void takeEffect() {
        applyService.takeEffect(283681L);
    }


    @Test
    public void queryTransactionQuota() {
        List<TransactionQuotaInfoResponse> result = applyService.queryTransactionQuota(57306L);
        System.out.println(JsonUtil.encode(result));
    }
}
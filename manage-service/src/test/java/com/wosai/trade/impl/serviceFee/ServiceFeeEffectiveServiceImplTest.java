package com.wosai.trade.impl.serviceFee;

import cn.hutool.core.date.DatePattern;
import com.google.common.collect.ImmutableList;
import com.wosai.databus.event.audit.AuditInstanceApproveEvent;
import com.wosai.trade.BaseTest;
import com.wosai.trade.biz.audit.AuditBiz;
import com.wosai.trade.model.dal.ServiceFeeEffectiveQueryParam;
import com.wosai.trade.repository.dao.ServiceFeeEffectiveMapper;
import com.wosai.trade.repository.dao.entity.ServiceFeeEffectiveEntity;
import com.wosai.trade.service.enums.ChargeCycleEnum;
import com.wosai.trade.service.enums.ChargeMethodEnum;
import com.wosai.trade.service.enums.ComboConfigLevelEnum;
import com.wosai.trade.service.enums.ServiceFeeEffectiveStatusEnum;
import com.wosai.trade.service.result.ListResult;
import com.wosai.trade.service.result.StoreBizServiceAgreement;
import com.wosai.trade.service.servicefee.reponse.ApplyServiceFeeResponse;
import com.wosai.trade.service.servicefee.reponse.ServiceFeeEffectiveConditionQueryResponse;
import com.wosai.trade.service.servicefee.request.*;
import com.wosai.trade.util.DateTimeUtils;
import com.wosai.trade.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
public class ServiceFeeEffectiveServiceImplTest extends BaseTest {

    @Resource
    private ServiceFeeEffectiveServiceImpl serviceFeeEffectiveService;
    @Resource
    private AuditBiz auditBiz;
    @Resource
    private ServiceFeeEffectiveMapper serviceFeeEffectiveMapper;

    @Test
    public void applyOne() {
        //-------　报名申请　21690003344394
        ApplyServiceFeeRequest request = new ApplyServiceFeeRequest();
        request.setMerchantSn("21690003185820");
        request.setSn("21590000001144809");
        request.setServiceFeeId(51103L);
        request.setScenesCode("34592");
        request.setRemark("开通门店按使用收费");
        request.setPlatform("SP");
        request.setProfitShareRatio("0");
//        request.setMinCharge(7L);
        request.setCommission(5L);
        request.setOperatorId("8ebaf1d0-6a1a-4550-b7de-ea54aceed166");
        request.setOperatorName("tywei测试账号");

        System.out.println(JsonUtil.encode(request));
        System.exit(1);
        ApplyServiceFeeResponse applyResponse = serviceFeeEffectiveService.applyOne(request);
        Assert.notNull(applyResponse, "申请收费规则失败");
    }
    @Test
    public void cancelV2() {
        List<ServiceFeeEffectiveEntity> list = serviceFeeEffectiveMapper.queryByCondition(ServiceFeeEffectiveQueryParam.builder()
                .tradeAppId(4L)
                .merchantSn("21690003185820")
                .sn("21590000001144809")
                .level("STORE")
                .statusList(ImmutableList.of(ServiceFeeEffectiveStatusEnum.ACTIVE.getStatusValue()))
                .build());
        Assert.notEmpty(list);
        CancelRequestV2 request = new CancelRequestV2();
        request.setMerchantSn("21690003185820");
        request.setSn("21590000001144809");
        request.setLevel("STORE");
        request.setAppId("TEST");
        request.setTradeAppId(4L);
        request.setOperatorId("test");
        request.setOperatorName("test");
        request.setPlatform("SPA");
        serviceFeeEffectiveService.cancelV2(request);

        List<ServiceFeeEffectiveEntity> list2 = serviceFeeEffectiveMapper.queryByCondition(ServiceFeeEffectiveQueryParam.builder()
                .tradeAppId(4L)
                .merchantSn("21690003185820")
                .sn("21590000001144809")
                .level("STORE")
                .statusList(ImmutableList.of(ServiceFeeEffectiveStatusEnum.ACTIVE.getStatusValue()))
                .build());
        Assert.isTrue(list.size() == list2.size() + 1);
    }

    @Test
    public void enabledPeriod() {
        //{"status": 1, "bizType": "SAAS", "bizSn": "29", "merchantSn": "21690003344394", "storeSn": "", "level": "MERCHANT", "chargeCycle": "YEAR", "beginDate": 1687773614000, "endDate": 1719417600000, "lifeTime": 0, "payAmount": 1000}
        EnabledServiceFeePeriodRequest request = new EnabledServiceFeePeriodRequest();
        request.setServiceFeeId(30L);
        request.setMerchantSn("21690003344394");
        request.setStoreSn("");
        request.setLevel(ComboConfigLevelEnum.MERCHANT);
        request.setChargeCycle(ChargeCycleEnum.YEAR);
        request.setBeginDate(DateTimeUtils.convertDate(1687773614000L));
        request.setEndDate(DateTimeUtils.convertDate(1719417600000L));
        EnabledServiceFeeRequest feeRequest = new EnabledServiceFeeRequest();
        feeRequest.setChargeMethodEnum(ChargeMethodEnum.DIRECT);
        feeRequest.setPeriodRequest(request);
        serviceFeeEffectiveService.enabled(feeRequest);
    }

    @Test
    public void enabledPeriod2() {
        String str = "{\"dimensionNo\":\"c5115382231b34f5ad56875135e5b94a\",\"serviceFeeId\":44,\"merchantSn\":\"21690003291354\",\"storeSn\":\"21590000000917141\",\"level\":\"STORE\",\"chargeCycle\":\"LIFETIME\",\"beginDate\":\"2023-08-03 21:30:12\",\"endDate\":\"1970-01-01 08:00:00\",\"operatorId\":\"system\",\"operatorName\":\"system\",\"operatorType\":\"SUBSCRIBE\",\"effectiveTimeReached\":true}";
        EnabledServiceFeePeriodRequest event = JsonUtil.decode(str, EnabledServiceFeePeriodRequest.class);
        EnabledServiceFeeRequest feeRequest = new EnabledServiceFeeRequest();
        feeRequest.setChargeMethodEnum(ChargeMethodEnum.DIRECT);
        feeRequest.setPeriodRequest(event);
        serviceFeeEffectiveService.enabled(feeRequest);
    }

    @Test
    public void saveBusinessLog() {
        //21690003215621
        ServiceFeeEffectiveEntity effectiveDo = serviceFeeEffectiveMapper.selectByPrimaryKey(88L);
        serviceFeeEffectiveService.saveBusinessLog("b29f93e0-87eb-408d-8735-0c2b61ad53d2", null, effectiveDo);

    }

    @Test
    public void adjustPeriod() {
        AdjustServiceFeeEffectiveRequest request = new AdjustServiceFeeEffectiveRequest();
        request.setAuditSn("测试审批sn");
        request.setAuditId("123");
        request.setAuditTemplateId("123");
        request.setMerchantSn("21690003344394");
        request.setSn("21690003344394");
        request.setScenesCode("34592");
        request.setServiceFeeId(31L);
        request.setChargeAmount(1000L);
        request.setRemark("调整test");
        request.setOperatorId("8ebaf1d0-6a1a-4550-b7de-ea54aceed166");
        request.setOperatorName("tywei测试账号");

        serviceFeeEffectiveService.adjust(request);
    }

    @Test
    public void adjustProfit() {
        AdjustServiceFeeEffectiveRequest request = new AdjustServiceFeeEffectiveRequest();
        request.setAuditSn("测试审批sn");
        request.setMerchantSn("21690003344394");
        request.setSn("21690003344394");
        request.setScenesCode("34592");
        request.setServiceFeeId(1L);
        request.setMinCharge(1000L);
        request.setProfitShareRatio("0.38");
        request.setRemark("调整test");
        request.setOperatorId("8ebaf1d0-6a1a-4550-b7de-ea54aceed166");
        request.setOperatorName("tywei测试账号");
        serviceFeeEffectiveService.adjust(request);

    }

    @Test
    public void applyOneStr() {
        String json = "{\"merchantSn\":\"21690003215621\",\"sn\":\"21590000000852445\",\"serviceFeeId\":36,\"profitShareRatio\":\"8.4\",\"minCharge\":15,\"chargeAmount\":null,\"scenesCode\":null,\"beginTime\":null,\"remark\":\"我来测试下\",\"platform\":\"CRM\",\"operatorId\":\"867bcbe2-c78a-4a9e-bd7f-a506e9213c2f\",\"operatorName\":\"姚秀梅\",\"invoker\":null,\"auditSn\":null}";
        ApplyServiceFeeRequest request = JsonUtil.decode(json, ApplyServiceFeeRequest.class);
        serviceFeeEffectiveService.applyOne(request);
    }

    @Test
    public void adjustProfit2() {
        String s = "{\"auditId\":\"225030\",\"auditSn\":\"SP359720230908000014\",\"formId\":\"2530465\",\"finishTime\":1694166584565,\"operatorPlatform\":\"SP\",\"module\":\"CRM\",\"auditTemplateId\":\"78720\",\"eventType\":\"AUDIT_APPROVE\",\"templateId\":\"179698\",\"operatorName\":\"郑岩\",\"objectType\":\"AUDIT\",\"auditCommentProperty\":{\"id\":\"64935094\",\"operatorName\":\"郑岩\",\"operator\":\"05464253261172984\",\"platform\":\"SP\"},\"bizKey\":\"SP_6d3a99d9-d7a1-4719-85f2-ca599402c5f8\",\"templateEvent\":\"adjust_service_fee\",\"ctime\":1694166571000,\"auditCallBackProperty\":{\"resultFail\":1},\"businessMap\":{\"serviceFeeId\":\"185\",\"merchant\":{\"merch_info.sn\":{\"keyName\":\"商户号\",\"value\":\"21690003215621\"},\"merch_info.name\":{\"keyName\":\"商户名称\",\"value\":\"李婉玉测试2\"}},\"commentdetails\":{\"id\":\"64935094\",\"operatorName\":\"郑岩\",\"operator\":\"05464253261172984\",\"platform\":\"SP\"},\"chargeAmount\":0.01,\"remark\":\"速度发\",\"sn\":{\"store_sn\":{\"keyName\":\"门店号\",\"value\":\"21590000000852445\"},\"store_name\":{\"keyName\":\"门店名称\",\"value\":\"李婉玉测试2\"}},\"audit_info\":{\"id\":\"225030\",\"sn\":\"SP359720230908000014\",\"operator\":\"05464253261172984\",\"platform\":\"SP\",\"status\":\"2\",\"time\":1694166585000,\"template_id\":78720,\"template_name\":\"服务费收费规则调整\"}},\"operatorId\":\"05464253261172984\",\"seq\":0,\"timestamp\":1694166584596}";
        AuditInstanceApproveEvent event = JsonUtil.decode(s, AuditInstanceApproveEvent.class);
        auditBiz.adjustServiceFee(event);
    }

    @Test
    public void conditionQuery() {
        String str = "{\"merchantSn\":\"21690003625046\",\"sn\":\"21690003625046\",\"status\":2,\"page\":1,\"pageSize\":10}";
        ServiceFeeEffectiveConditionQueryRequest request = JsonUtil.decode(str, ServiceFeeEffectiveConditionQueryRequest.class);
        ListResult<ServiceFeeEffectiveConditionQueryResponse> list = serviceFeeEffectiveService.conditionQuery(request);
        System.out.println(list);
    }

    @Test
    public void getMerchantServiceAgreementFeeRates() {
        ServiceAgreementRequest request = new ServiceAgreementRequest();
        request.setMerchantSn("mch-1680001624761");
        List<StoreBizServiceAgreement> list = serviceFeeEffectiveService.getMerchantServiceAgreementFeeRates(request);
        log.info("list:{}", JsonUtil.encode(list));
    }

    @Test
    public void upsert() {
        String str = "{\n" +
                "            \"appId\": \"CAMPUS\",\n" +
                "            \"auditSn\": \"CAMPUS\",\n" +
                "            \"merchantSn\": \"21690003185820\",\n" +
                "            \"sn\": \"21590000001144809\",\n" +
                "            \"scenesCode\": \"34576\",\n" +
                "            \"serviceFeeId\": 373,\n" +
                "            \"profitShareRatio\": \"8.000\",\n" +
                "            \"commission\": 400,\n" +
                "            \"minCharge\": 10\n" +
                "        }";
        UpsertServiceFeeRecordRequest request = JsonUtil.decode(str, UpsertServiceFeeRecordRequest.class);
        serviceFeeEffectiveService.upsert(request);
    }

    @Test
    public void enabled() {
        String str = "{\"chargeMethodEnum\":\"PROFIT\",\"effectiveId\":2953}";
        EnabledServiceFeeRequest request = JsonUtil.decode(str, EnabledServiceFeeRequest.class);
        serviceFeeEffectiveService.enabled(request);
    }

    @Test
    public void migratePeriodApplyOne() {
        MigrateServiceFeeRequest request = new MigrateServiceFeeRequest();
        request.setMerchantSn("21690003344394");
        request.setSn("21690003344394");
        request.setServiceFeeId(372L);
        //applyServiceFeeRequest.setScenesCode("34592");
        request.setRemark("开通按时段收费");
        request.setPlatform("SP");
        request.setChargeAmount(1L);
        request.setOperatorId("4fa00de6-afb9-4236-93ee-b86b93ded7cc");
        request.setOperatorName("tywei测试账号");


        request.setOrderSn("test-111111");
        request.setEndDate(DateTimeUtils.parseDate("2024-01-01 00:00:00", DatePattern.NORM_DATETIME_PATTERN));
        serviceFeeEffectiveService.migratePeriodApplyOne(request);
    }

    @Test
    public void firstApplyOne() {
        String reqStr = "{\"merchantSn\":\"21690003215621\",\"sn\":\"21590000000852445\",\"serviceFeeId\":6199,\"profitShareRatio\":null,\"minCharge\":null,\"commission\":null,\"chargeAmount\":1,\"scenesCode\":null,\"beginTime\":null,\"remark\":\"我来测试下\",\"platform\":\"APP\",\"operatorId\":\"867bcbe2-c78a-4a9e-bd7f-a506e9213c2f\",\"operatorName\":\"姚秀梅\",\"invoker\":null,\"auditSn\":null,\"endDate\":\"2024-01-11 23:59:59\",\"orderSn\":\"o123456789\"}";
        MigrateServiceFeeRequest request = JsonUtil.decode(reqStr, MigrateServiceFeeRequest.class);
        serviceFeeEffectiveService.firstPeriodApplyOne(request);
    }
}
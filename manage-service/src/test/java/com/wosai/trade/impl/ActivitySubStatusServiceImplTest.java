package com.wosai.trade.impl;

import com.google.common.collect.ImmutableList;
import com.wosai.trade.BaseTest;
import com.wosai.trade.impl.inner.ActivitySubStatusService;
import com.wosai.trade.model.enums.ActivitySubStatusTagEnum;
import com.wosai.trade.repository.dao.ActivityDOMapper;
import com.wosai.trade.repository.dao.ActivitySubStatusDetailMapper;
import com.wosai.trade.repository.dao.TradeComboDao;
import com.wosai.trade.repository.dao.TradeComboDetailDao;
import com.wosai.trade.repository.dao.entity.ActivityEntity;
import com.wosai.trade.repository.dao.entity.ActivitySubStatusDetailEntity;
import com.wosai.trade.repository.dao.entity.TradeComboEntity;
import com.wosai.trade.service.activity.request.ActivityRule;
import com.wosai.trade.service.activity.request.ActivitySubStatusBatchRequest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class ActivitySubStatusServiceImplTest extends BaseTest {

    @Resource
    private ActivitySubStatusService activitySubStatusService;

    @Resource
    private ActivityDOMapper activityDOMapper;
    @Resource
    private ActivitySubStatusDetailMapper activitySubStatusDetailMapper;
    @Resource
    private TradeComboDao tradeComboDao;
    @Resource
    private TradeComboDetailDao comboDetailDao;


    @Test
    public void testBatchCreate() {
        ActivitySubStatusBatchRequest request = ActivitySubStatusBatchRequest.builder()
                .activityId(3216L)
                .subStatusList(ImmutableList.of(
                        ActivitySubStatusBatchRequest.SubStatus.builder()
                                .name("报名成功")
                                .tag(ActivitySubStatusTagEnum.WECHAT_APPLY_SUCCESS.name())
                                .defaultComboId(5961L)
                                .comboIds(ImmutableList.of(5961L, 5962L))
                                .build(),
                        ActivitySubStatusBatchRequest.SubStatus.builder()
                                .name("返佣成功")
                                .tag(ActivitySubStatusTagEnum.WECHAT_REBATE_SUCCESS.name())
                                .defaultComboId(5961L)
                                .comboIds(ImmutableList.of(5961L, 5962L))
                                .build(),
                        ActivitySubStatusBatchRequest.SubStatus.builder()
                                .name("返佣失败")
                                .tag(ActivitySubStatusTagEnum.WECHAT_REBATE_FAILED.name())
                                .defaultComboId(5962L)
                                .comboIds(ImmutableList.of(5962L))
                                .build()
                ))
                .build();
        String response = activitySubStatusService.batchCreate(request);
        log.info("response:{}", response);
        Assert.assertNotNull(response);
    }

    @Test
    public void rebindCombo() {
        Long activityId = 121L;
        Long subId = 6189L;
        List<Long> comboIds = ImmutableList.of(16657L, 16658L, 19412L);
        activitySubStatusService.rebindCombo(activityId, subId, comboIds);
        ActivitySubStatusDetailEntity sub = activitySubStatusDetailMapper.selectByPrimaryKey(subId);
        Assert.assertEquals("子状态套餐列表设置失败", comboIds, sub.buildEffectiveRule().getComboIdList());

        List<TradeComboEntity> comboList = tradeComboDao.selectByActivityId(activityId);
        List<Long> allComboIds = comboList.stream().map(TradeComboEntity::getId).collect(Collectors.toList());
        Assert.assertTrue("套餐内设置activityId失败", allComboIds.containsAll(comboIds));

        ActivityEntity activity = activityDOMapper.selectByPrimaryKey(activityId);
        List<ActivityRule.TradeCombo> combo = activity.buildChangeCombo();
        Assert.assertEquals("套餐内设置交换套餐失败", allComboIds, combo.stream().map(ActivityRule.TradeCombo::getId).collect(Collectors.toList()));
    }

    @Test
    public void reloadComboToActivityChangeCombo() {
        activitySubStatusService.reloadComboToActivityChangeCombo(10287L);
    }
}
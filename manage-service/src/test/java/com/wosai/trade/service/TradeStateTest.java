package com.wosai.trade.service;

import com.google.common.collect.Lists;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.trade.repository.dao.SharingAuditTaskDao;
import com.wosai.trade.repository.dao.entity.SharingAuditTaskEntity;
import com.wosai.trade.service.constant.SharingAuditConstant;
import com.wosai.trade.service.result.TradeStateResult;
import com.wosai.upay.wallet.model.EventLog;
import com.wosai.upay.wallet.model.MerchantLimitAuthorization;
import com.wosai.upay.wallet.service.MerchantLimitAuthorizationService;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.assertTrue;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

@RunWith(SpringRunner.class)
@SpringBootTest
public class TradeStateTest {


    @Autowired
    TradeStateService tradeStateService;
    @Autowired
    MerchantLimitAuthorizationService merchantLimitAuthorizationService;
    @Autowired
    SharingAuditTaskDao sharingAuditTaskDao;

    @Test
    public void testDic() {
        tradeStateService.dic();
    }

    @Test
    public void testQueryStateByBizIdAndMerchantId() {
        TradeStateResult result = tradeStateService.queryStateByBizIdAndMerchantId("00008bc9172d-e3ca-3064-7578-80cde633", 9992);
        Assert.assertTrue(result.getState());

        List<TradeStateResult.SubState> subStateList = result.getSubStateList();
        Assert.assertEquals(63, subStateList.size());
        TradeStateResult.SubState state1 = subStateList.get(0);
        Assert.assertEquals("原因1", state1
                .getDesc());
        Assert.assertEquals(1, state1.getType().intValue());
        Assert.assertTrue(state1.getValue());

        TradeStateResult.SubState state2 = subStateList.get(1);
        Assert.assertEquals("原因2", state2
                .getDesc());
        Assert.assertEquals(2, state2.getType().intValue());
        Assert.assertTrue(state2.getValue());
    }

    @Test
    public void testUpdateStateByBizAndTypeAndMerchantId() {
        String merchantId = "00008bc9172d-e3ca-3064-7578-80cde633";
        Integer biz = 9992;

        //关掉1
        tradeStateService.updateStateByBizAndTypeAndMerchantId(merchantId, biz, 1, false, "");
        TradeStateResult query = tradeStateService.queryStateByBizIdAndMerchantId(merchantId, biz);
        Assert.assertFalse(query.getState());
        Assert.assertFalse(query.getSubStateList().stream().filter(t -> t.getType().equals(1)).findFirst().get().getValue());
        Assert.assertTrue(query.getSubStateList().stream().filter(t -> t.getType().equals(2)).findFirst().get().getValue());

        //打开1
        tradeStateService.updateStateByBizAndTypeAndMerchantId(merchantId, biz, 1, true, "");
        query = tradeStateService.queryStateByBizIdAndMerchantId(merchantId, biz);
        Assert.assertTrue(query.getState());
        Assert.assertTrue(query.getSubStateList().stream().filter(t -> t.getType().equals(1)).findFirst().get().getValue());
        Assert.assertTrue(query.getSubStateList().stream().filter(t -> t.getType().equals(2)).findFirst().get().getValue());

        //再次关掉1
        tradeStateService.updateStateByBizAndTypeAndMerchantId(merchantId, biz, 1, false, "");
        query = tradeStateService.queryStateByBizIdAndMerchantId(merchantId, biz);
        Assert.assertFalse(query.getState());
        Assert.assertFalse(query.getSubStateList().stream().filter(t -> t.getType().equals(1)).findFirst().get().getValue());
        Assert.assertTrue(query.getSubStateList().stream().filter(t -> t.getType().equals(2)).findFirst().get().getValue());


        //关掉2
        tradeStateService.updateStateByBizAndTypeAndMerchantId(merchantId, biz, 2, false, "");
        query = tradeStateService.queryStateByBizIdAndMerchantId(merchantId, biz);
        Assert.assertFalse(query.getState());
        Assert.assertFalse(query.getSubStateList().stream().filter(t -> t.getType().equals(1)).findFirst().get().getValue());
        Assert.assertFalse(query.getSubStateList().stream().filter(t -> t.getType().equals(2)).findFirst().get().getValue());

        //打开2
        tradeStateService.updateStateByBizAndTypeAndMerchantId(merchantId, biz, 2, true, "");
        query = tradeStateService.queryStateByBizIdAndMerchantId(merchantId, biz);
        Assert.assertFalse(query.getState());
        Assert.assertFalse(query.getSubStateList().stream().filter(t -> t.getType().equals(1)).findFirst().get().getValue());
        Assert.assertTrue(query.getSubStateList().stream().filter(t -> t.getType().equals(2)).findFirst().get().getValue());

        //打开1
        tradeStateService.updateStateByBizAndTypeAndMerchantId(merchantId, biz, 1, true, "");
        query = tradeStateService.queryStateByBizIdAndMerchantId(merchantId, biz);
        Assert.assertTrue(query.getState());
        Assert.assertTrue(query.getSubStateList().stream().filter(t -> t.getType().equals(1)).findFirst().get().getValue());
        Assert.assertTrue(query.getSubStateList().stream().filter(t -> t.getType().equals(2)).findFirst().get().getValue());

        //关闭63
        tradeStateService.updateStateByBizAndTypeAndMerchantId(merchantId, biz, 63, false, "");
        query = tradeStateService.queryStateByBizIdAndMerchantId(merchantId, biz);
        Assert.assertFalse(query.getState());
        Assert.assertTrue(query.getSubStateList().stream().filter(t -> t.getType().equals(1)).findFirst().get().getValue());
        Assert.assertTrue(query.getSubStateList().stream().filter(t -> t.getType().equals(2)).findFirst().get().getValue());
        Assert.assertFalse(query.getSubStateList().stream().filter(t -> t.getType().equals(63)).findFirst().get().getValue());

        //开启63
        tradeStateService.updateStateByBizAndTypeAndMerchantId(merchantId, biz, 63, true, "");
        query = tradeStateService.queryStateByBizIdAndMerchantId(merchantId, biz);
        Assert.assertTrue(query.getState());
        Assert.assertTrue(query.getSubStateList().stream().filter(t -> t.getType().equals(1)).findFirst().get().getValue());
        Assert.assertTrue(query.getSubStateList().stream().filter(t -> t.getType().equals(2)).findFirst().get().getValue());
        Assert.assertTrue(query.getSubStateList().stream().filter(t -> t.getType().equals(63)).findFirst().get().getValue());
    }

    @Test
    public void listStateByBizIdAndMerchantIds() {
        int biz = 9992;
        String m1 = "m00001";
        String m2 = "m00002";

        tradeStateService.updateStateByBizAndTypeAndMerchantId(m1, biz, 63, true, "");
        tradeStateService.updateStateByBizAndTypeAndMerchantId(m2, biz, 63, false, "");

        List<String> merchantIds = Lists.newArrayList(m1, m2);
        List<TradeStateResult> tradeStateResults = tradeStateService.listStateByBizIdAndMerchantIds(merchantIds, biz);
        Assert.assertNotNull("单元测试失败", tradeStateResults);

        Map<String, TradeStateResult> resultMap = tradeStateResults
                .stream()
                .collect(Collectors.toMap(TradeStateResult::getMerchantId, Function.identity()));

        Assert.assertTrue("单元测试失败", resultMap.get(m1).getState());
        Assert.assertFalse("单元测试失败", resultMap.get(m2).getState());

        // 余额禁用测试
        biz = 2;
        // 开启余额转出
        tradeStateService.updateStateByBizAndTypeAndMerchantId(m1, biz, 1, true, "");
        Map<String, Object> authorizations = merchantLimitAuthorizationService.getMerchantAllLimitAuthorization(m1);
        String limitTypes = MapUtil.getString(authorizations, MerchantLimitAuthorization.LIMIT_TYPES);
        List<Integer> iLimitTypes = Collections.emptyList();
        if (StringUtil.isNotBlank(limitTypes)) {
            iLimitTypes = Arrays.asList(limitTypes.split(",")).stream().map(Integer::valueOf).collect(Collectors.toList());
        }
        assertTrue(!iLimitTypes.contains(EventLog.TYPE_DRAW)  && !iLimitTypes.contains(EventLog.TYPE_BUY_FINANCIAL_PRODUCT) && !iLimitTypes.contains(EventLog.TYPE_FINANCIAL_MANUAL_BUY));

        // 关闭余额转出
        tradeStateService.updateStateByBizAndTypeAndMerchantId(m1, biz, 1, false, "");
        authorizations = merchantLimitAuthorizationService.getMerchantAllLimitAuthorization(m1);
        limitTypes = MapUtil.getString(authorizations, MerchantLimitAuthorization.LIMIT_TYPES);
        iLimitTypes = Collections.emptyList();
        if (StringUtil.isNotBlank(limitTypes)) {
            iLimitTypes = Arrays.asList(limitTypes.split(",")).stream().map(Integer::valueOf).collect(Collectors.toList());
        }
        assertTrue(iLimitTypes.contains(EventLog.TYPE_DRAW) && iLimitTypes.contains(EventLog.TYPE_BUY_FINANCIAL_PRODUCT) && iLimitTypes.contains(EventLog.TYPE_FINANCIAL_MANUAL_BUY));
    }

    @Test
    public void testDao(){
        SharingAuditTaskEntity sharingAuditTaskEntity = new SharingAuditTaskEntity();
        sharingAuditTaskEntity.setStatus(0);
        sharingAuditTaskEntity.setAuditId(1L);
        sharingAuditTaskEntity.setAuditType(SharingAuditConstant.AUDIT_TYPE_OPEN_HAIKE_SHARING);
        SharingAuditTaskEntity.BizParams bizParams = new SharingAuditTaskEntity.BizParams();
        bizParams.setSubmitHaikeOpenBizTaskMap(MapUtil.hashMap("test","test1"));
        sharingAuditTaskEntity.setBizParams(bizParams.toMap());
        int insert = sharingAuditTaskDao.insert(sharingAuditTaskEntity);
        System.out.println(insert);
    }
}

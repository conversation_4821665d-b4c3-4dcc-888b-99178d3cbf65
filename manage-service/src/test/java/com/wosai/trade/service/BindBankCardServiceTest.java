package com.wosai.trade.service;

import com.wosai.aop.gateway.service.ClientSideNoticeService;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.trade.BaseTest;
import com.wosai.trade.config.apollo.CommonApolloConfig;
import com.wosai.trade.impl.MicroPuHuiService;
import com.wosai.trade.impl.TodayDateCheckService;
import com.wosai.trade.impl.TradeCommonService;
import com.wosai.trade.model.enums.PayWayEnum;
import com.wosai.trade.repository.dao.entity.MerchantFeeRateEntity;
import com.wosai.trade.repository.dao.entity.TradeActivityEntity;
import com.wosai.trade.service.request.CancelFeeRateRequest;
import com.wosai.trade.service.result.TradeActivityResult;
import com.wosai.trade.task.BindBankcardActivityScheduler;
import com.wosai.trade.util.DateTimeUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.Date;
import java.util.List;

/**
 * Created by wujianwei on 2022/3/21.
 */
public class BindBankCardServiceTest extends BaseTest {
    public static final String MERCHANT_SN = "mch-*********";
    public static final String MERCHANT_ID = "000011dd27c4-9599-1174-4184-e1cfd033";

    @SpyBean
    private TradeCommonService tradeCommonService;
    @SpyBean
    private MicroPuHuiService microPuHuiService;
    @Autowired
    private CommonApolloConfig commonApolloConfig;
    @Autowired
    private FeeRateService feeRateService;
    @Autowired
    private TradeActivityService tradeActivityService;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @SpyBean
    private BindBankcardActivityScheduler bankcardActivityScheduler;
    @SpyBean
    private TodayDateCheckService todayDateCheckService;

    @Before
    public void setUp() throws Exception {
        //组织mock返回直营
        PowerMockito.doReturn("00003").when(tradeCommonService).getMerchantOrganizationPathFromCrm(Mockito.anyString());
        //普惠活动城市永远满足
        PowerMockito.doReturn(true).when(microPuHuiService).isCityMatchWhenMerchantCreate(Mockito.anyString());
        jdbcTemplate.execute(String.format("delete from trade_activity where merchant_sn = '%s'", MERCHANT_SN));
        jdbcTemplate.execute(String.format("delete from merchant_fee_rate where merchant_sn = '%s'", MERCHANT_SN));
        jdbcTemplate.execute(String.format("delete from merchant_trade_amount where merchant_sn = '%s'", MERCHANT_SN));
    }

    /**
     * mock 相关时间
     * @param realNow
     * @param addMonth
     */
    private void mockBindBankcardActivitySchedulerDate(Date realNow, int addMonth){
        Date mockNow = DateTimeUtils.addMonth(realNow, addMonth);
        PowerMockito.doReturn(mockNow).when(bankcardActivityScheduler).now();
        PowerMockito.doReturn(mockNow).when(todayDateCheckService).findLastExistDataDay(Mockito.anyString());
    }

    /**
     * mock商户每月交易统计
     * @param merchantSn
     * @param mockAmount
     */
    private void mockGetLastMonthTradeAmountWithDefaultZero(String merchantSn, long mockAmount){
        PowerMockito.doReturn(mockAmount).when(microPuHuiService).getLastMonthTradeAmountWithDefaultZero(merchantSn);
    }



    @Test
    /**
     * 生效绑卡套餐
     */
    public void testEffectBindBankcardCombo(){
        long comboId = commonApolloConfig.getBindBankcardComboId();
        feeRateService.applyFeeRate(MERCHANT_SN, comboId);
        TradeActivityResult result = tradeActivityService.getEnableActivityByMerchantSnAndType(MERCHANT_SN, TradeActivityEntity.ACTIVITY_BIND_BANKCARD);
        Assert.assertNotNull(result);
        Assert.assertEquals("缺少套餐id", comboId, MapUtil.getLongValue(result.getExtra(), TradeActivityEntity.EXTRA_COMBO_ID));
        List<MerchantFeeRateEntity> effectMerchantFeeRate = tradeCommonService.getEffectMerchantFeeRate(comboId, MERCHANT_SN);
        Assert.assertTrue("商户已生效绑卡套餐费率记录不能为空", effectMerchantFeeRate != null && effectMerchantFeeRate.size() > 0 );
    }

    @Test
    /**
     * 取消绑卡套餐
     */
    public void testCancelBindBankcardCombo(){
        long comboId = commonApolloConfig.getBindBankcardComboId();
        testEffectBindBankcardCombo();
        CancelFeeRateRequest request = new CancelFeeRateRequest();
        request.setMerchantSn(MERCHANT_SN);
        request.setAuditSn("system");
        request.setTradeComboId(comboId);
        feeRateService.cancelFeeRate(request);
        checkBindBankcardComboIsOver();
    }

    @Test
    /**
     * 结束绑卡套餐
     */
    public void testEndBindBankcardCombo(){
        long comboId = commonApolloConfig.getBindBankcardComboId();
        testEffectBindBankcardCombo();
        List<MerchantFeeRateEntity> list = tradeCommonService.getEffectMerchantFeeRate(comboId, MERCHANT_SN);
        Assert.assertTrue("费率配置成功",  list.size() >= 2);
        MerchantFeeRateEntity weixin = list.stream().filter(m -> m.getPayWay() == PayWayEnum.WEIXIN.getCode()).findAny().orElse(null);
        MerchantFeeRateEntity alipay = list.stream().filter(m -> m.getPayWay() == PayWayEnum.ZHIFUBAO.getCode()).findAny().orElse(null);
        feeRateService.endMerchantFeeRate(weixin.getId());
        //还有支付宝未结束，不能参与普惠
        TradeActivityResult result = tradeActivityService.getEnableActivityByMerchantSnAndType(MERCHANT_SN, TradeActivityEntity.ACTIVITY_MICRO_PUHUI_RATE);
        Assert.assertNull(result);
        feeRateService.endMerchantFeeRate(alipay.getId());
        checkBindBankcardComboIsOver();
    }

    @Test
    /**
     * 绑卡套餐被其他套餐替换
     */
    public void testReplaceByOtherCombo(){
        //生效绑卡套餐
        testEffectBindBankcardCombo();
        //再生效基础套餐
        feeRateService.applyFeeRate(MERCHANT_SN, Long.parseLong(commonApolloConfig.getBaseTradeCombos().get(0)));
        TradeActivityResult result = tradeActivityService.getEnableActivityByMerchantSnAndType(MERCHANT_SN, TradeActivityEntity.ACTIVITY_BIND_BANKCARD);
        Assert.assertNull("不应该存在绑卡活动名单", result);
        result = tradeActivityService.getEnableActivityByMerchantSnAndType(MERCHANT_SN, TradeActivityEntity.ACTIVITY_MICRO_PUHUI_RATE);
        Assert.assertNull("不应该存在普惠活动名单", result);
        List<MerchantFeeRateEntity> merchantFeeRate = tradeCommonService.getEffectMerchantFeeRate(commonApolloConfig.getMicroPuhuiOld025ComboId(), MERCHANT_SN);
        Assert.assertTrue("不应该存在普惠费率", merchantFeeRate.isEmpty());
    }

    @Test
    /**
     * 测试每月绑卡切换
     */
    public void testMonthTask(){
        long comboId = commonApolloConfig.getBindBankcardComboId();
        Date realNow = new Date();
        //1: 生效
        testEffectBindBankcardCombo();

        //2: 生效下月 第2月切换任务
        mockBindBankcardActivitySchedulerDate(realNow, 1);
        //2.1 第2月不满足月交易额
        mockGetLastMonthTradeAmountWithDefaultZero(MERCHANT_SN, 100l);
        //2.2 每月切换
        bankcardActivityScheduler.task();
        //2.3 活动以及费率套餐保持不变
        TradeActivityResult result = tradeActivityService.getEnableActivityByMerchantSnAndType(MERCHANT_SN, TradeActivityEntity.ACTIVITY_BIND_BANKCARD);
        Assert.assertNotNull(result);
        List<MerchantFeeRateEntity> merchantFeeRates = tradeCommonService.getEffectMerchantFeeRate(comboId, MERCHANT_SN);
        Assert.assertEquals(merchantFeeRates.size(), 2);

        //3: 第3月切换任务
        mockBindBankcardActivitySchedulerDate(realNow, 2);
        //3.1 第3月不满足交易额
        mockGetLastMonthTradeAmountWithDefaultZero(MERCHANT_SN, 102l);
        //3.2 每月切换
        bankcardActivityScheduler.task();
        //3.3 活动保持，没有费率套餐
        result = tradeActivityService.getEnableActivityByMerchantSnAndType(MERCHANT_SN, TradeActivityEntity.ACTIVITY_BIND_BANKCARD);
        Assert.assertNotNull(result);
        merchantFeeRates = tradeCommonService.getEffectMerchantFeeRate(comboId, MERCHANT_SN);
        Assert.assertEquals(merchantFeeRates.size(), 0);

        //4: 第4月切换任务
        mockBindBankcardActivitySchedulerDate(realNow, 3);
        //4.1 第4月满足交易额
        mockGetLastMonthTradeAmountWithDefaultZero(MERCHANT_SN, 40000 * 100l);
        //3.2 每月切换
        bankcardActivityScheduler.task();
        //3.3 活动保持，有费率套餐
        result = tradeActivityService.getEnableActivityByMerchantSnAndType(MERCHANT_SN, TradeActivityEntity.ACTIVITY_BIND_BANKCARD);
        Assert.assertNotNull(result);
        merchantFeeRates = tradeCommonService.getEffectMerchantFeeRate(comboId, MERCHANT_SN);
        Assert.assertEquals(merchantFeeRates.size(), 2);

        //4: 第7月切换任务
        mockBindBankcardActivitySchedulerDate(realNow, 6);
        //4.1 第7月满足交易额
        mockGetLastMonthTradeAmountWithDefaultZero(MERCHANT_SN, 40000 * 100l);
        //4.2 每月切换
        bankcardActivityScheduler.task();
        //4.3 活动保持，有费率套餐
        result = tradeActivityService.getEnableActivityByMerchantSnAndType(MERCHANT_SN, TradeActivityEntity.ACTIVITY_BIND_BANKCARD);
        Assert.assertNotNull(result);
        merchantFeeRates = tradeCommonService.getEffectMerchantFeeRate(comboId, MERCHANT_SN);
        Assert.assertEquals(merchantFeeRates.size(), 2);

        //5: 第8月切换任务
        mockBindBankcardActivitySchedulerDate(realNow, 7);
        //5.1 第8月满足交易额
        mockGetLastMonthTradeAmountWithDefaultZero(MERCHANT_SN, 100l);
        //5.2 每月切换
        bankcardActivityScheduler.task();
        //5.3 活动结束 生效普惠
        result = tradeActivityService.getEnableActivityByMerchantSnAndType(MERCHANT_SN, TradeActivityEntity.ACTIVITY_BIND_BANKCARD);
        Assert.assertNull(result);
        merchantFeeRates = tradeCommonService.getEffectMerchantFeeRate(comboId, MERCHANT_SN);
        Assert.assertEquals(merchantFeeRates.size(), 0);
        merchantFeeRates = tradeCommonService.getEffectMerchantFeeRate(commonApolloConfig.getMicroPuhuiOld025ComboId(), MERCHANT_SN);
        Assert.assertEquals(merchantFeeRates.size(), 2);
    }

    /**
     * 检查绑卡名单是否已结束(被终止、取消、结束)
     */
    private void checkBindBankcardComboIsOver(){
        //活动名单已被去除
        TradeActivityResult result = tradeActivityService.getEnableActivityByMerchantSnAndType(MERCHANT_SN, TradeActivityEntity.ACTIVITY_BIND_BANKCARD);
        Assert.assertTrue("活动已结束", result == null);
        //已参加了普惠活动
        //  检查普惠活动名单
        result = tradeActivityService.getEnableActivityByMerchantSnAndType(MERCHANT_SN, TradeActivityEntity.ACTIVITY_MICRO_PUHUI_RATE);
        Assert.assertTrue("已加入到普惠活动名单", result != null);
        Assert.assertEquals("普惠参与类型正确", MapUtil.getIntValue(result.getExtra(), TradeActivityEntity.EXTRA_INSERT_MODE), TradeActivityEntity.INSERT_MODE_MICRO_PUHUI_OTHER_ACTIVITY_IN);
        Assert.assertEquals("普惠其他活动转入的活动类型记录正确", MapUtil.getIntValue(result.getExtra(), TradeActivityEntity.FROM_ACTIVITY_TYPE), TradeActivityEntity.ACTIVITY_BIND_BANKCARD);
        //  检查普惠费率
        List<MerchantFeeRateEntity> merchantFeeRates = tradeCommonService.getEffectMerchantFeeRate(commonApolloConfig.getMicroPuhuiOld025ComboId(), MERCHANT_SN);
        Assert.assertNotNull(merchantFeeRates);
        Assert.assertEquals(merchantFeeRates.size(), 2);
    }
}

package com.wosai.trade.service;/**
 * @description:
 * @author: lll
 * @date: 2022-01-17 10:42 下午
 */

import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest
public class CollectionToolServiceTest {

    public static final Logger logger = LoggerFactory.getLogger(CollectionToolServiceTest.class);
    @Autowired
    CollectionToolProductService collectionToolProductService;
    @Test
    public void test() {
        List<Map> allCollctionTools = collectionToolProductService.getAllCollctionTools();
        for(Map c : allCollctionTools){
            logger.info(String.valueOf(c));
        }
    }
}

package com.wosai.trade.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.wosai.trade.repository.dao.entity.TradeActivityEntity;
import com.wosai.trade.service.request.TradeActivityAddRequest;
import com.wosai.trade.service.result.TradeActivityResult;
import com.wosai.upay.core.model.MerchantConfig;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@RunWith(SpringRunner.class)
@SpringBootTest
public class TradeActivityTest {

    @Autowired
    TradeActivityService tradeActivityService;


    @Test
    public void testAddAndQueryActivity() {
        LocalDate now = LocalDate.now();
        String merchantSn = UUID.randomUUID().toString();
        Map<String, Object> audit = ImmutableMap.of("test", "test");
        Map<String, Object> extra = ImmutableMap.of(MerchantConfig.PAYWAY, JSON.toJSONString(Lists.newArrayList("1")));
        TradeActivityAddRequest request
                = new TradeActivityAddRequest();
        request.setMerchantSn(merchantSn);
        request.setType(TradeActivityEntity.ACTIVITY_LADDER);
        request.setBeginDate(now.toString());
        request.setEndDate(now.plusDays(61).toString());
        request.setAuditInfo(audit);
        request.setExtra(extra);
        int i = tradeActivityService.addActivity(request);
        Assert.assertEquals(1, i);
        TradeActivityResult result = tradeActivityService.getEnableActivityByMerchantSnAndType(merchantSn, TradeActivityEntity.ACTIVITY_LADDER);
        Assert.assertNotNull(result);
        Assert.assertEquals(merchantSn, result.getMerchantSn());
        Assert.assertEquals(TradeActivityEntity.ACTIVITY_LADDER, result.getType());
        Assert.assertEquals(1, result.getStatus());
        Assert.assertEquals(now.toString(), result.getBeginDate());
        Assert.assertEquals(now.plusDays(61).toString(), result.getEndDate());
        Assert.assertEquals(audit, result.getAuditInfo());
        Assert.assertEquals(extra, result.getExtra());
    }

    @Test
    public void testallExpireMerchant() {
        List<TradeActivityResult> result = tradeActivityService.allExpireMerchant("1990-01-01", 5000);
        Assert.assertEquals(0, result.size());
    }

    @Test
    public void testUpdateActivityStatusByIdAndPreStatus() {
        int result = tradeActivityService.updateActivityStatusByIdAndPreStatus
                ((long) 1, 999, 1);
        Assert.assertEquals(0, result);
    }


}

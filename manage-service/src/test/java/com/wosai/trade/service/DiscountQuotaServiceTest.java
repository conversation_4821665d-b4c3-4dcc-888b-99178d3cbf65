package com.wosai.trade.service;

import com.alibaba.fastjson.JSONObject;
import com.wosai.trade.BaseTest;
import com.wosai.trade.impl.CacheService;
import com.wosai.trade.service.activity.response.DiscountQuotaResponse;
import com.wosai.trade.service.request.PreDeductDiscountQuotaRequest;
import com.wosai.trade.util.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

public class DiscountQuotaServiceTest extends BaseTest {
    @Autowired
    private DiscountQuotaService discountQuotaService;
    @Autowired
    private CacheService cacheService;

    @Test
    public void testGetDiscountQuota() {


        PreDeductDiscountQuotaRequest request = new PreDeductDiscountQuotaRequest();
        request.setComboId(2752L);
        request.setPayWay(2);
        request.setMerchantId("1ec6c52d-30f9-4802-a8e1-bcfbd6821c39");
        request.setOriginalAmount(25000);
        request.setTradeApp("1");
        request.setTid("t2001259240148565");
        System.out.println(discountQuotaService.preDeductDiscountQuota(request));
    }

    @Test
    public void testGetDiscountQuota001() {
        PreDeductDiscountQuotaRequest request1 = JSONObject.parseObject(s, PreDeductDiscountQuotaRequest.class);
        System.out.println(discountQuotaService.preDeductDiscountQuota(request1));
    }

    String v = "{\"merchantId\":\"3498bc4b-1181-4a49-961e-5bd2102d5906\",\"tradeApp\":\"1\",\"tid\":\"t2003259248696848\",\"originalAmount\":51000,\"payWay\":2,\"comboId\":30,\"feeRate\":\"0.38\",\"provider\":1033,\"formal\":false}";
    @Test
    public void preDeductDiscountQuota() {
        PreDeductDiscountQuotaRequest request = JsonUtil.decode("{\"tradeApp\":\"1\",\"tid\":\"t2004259248071555\",\"merchantId\":\"4b831922-9a8f-4786-8208-c597d1f4e471\",\"originalAmount\":2000,\"feeRate\":\"0.38\",\"formal\":false,\"type\":\"NFC_POS\"}", PreDeductDiscountQuotaRequest.class);
        System.out.println(discountQuotaService.preDeductDiscountQuota(request));
    }

    @Test
    public void updateDiscountQuotaByTransaction() {
        String str = "{\"subject\":\"测试\",\"received_amount\":213,\"buyer_login\":\"***********\",\"merchant_id\":\"4b831922-9a8f-4786-8208-c597d1f4e471\",\"mtime\":*************,\"type\":30,\"extended_params\":{},\"tsn\":\"****************\",\"product_flag\":\"a1\",\"extra_out_fields\":{\"async_wallet_log\":true,\"payments\":[{\"type\":\"HONGBAO_CHANNEL_MCH\",\"origin_type\":\"MCOUPON\",\"amount\":2667},{\"type\":\"HONGBAO_CHANNEL\",\"origin_type\":\"COUPON\",\"amount\":80},{\"type\":\"ALIPAY_HUABEI\",\"origin_type\":\"PCREDIT\",\"amount\":133}],\"wallet_account_type\":1,\"is_default_poi\":true,\"quota_fee_rate\":\"0.2\",\"quota_fee_rate_tag\":57992},\"provider\":1037,\"original_amount\":3000,\"ctime\":*************,\"id\":\"t****************\",\"terminal_id\":\"122d9ca0-6867-4b81-9642-d493cd2bc318\",\"client_tsn\":\"*******************\",\"store_id\":\"6e992202-c893-450c-a7ee-0194e8c5986f\",\"provider_error_info\":{\"pay\":{\"msg\":\"处理成功\",\"code\":\"10000\"}},\"extra_params\":{\"barcode\":\"284151128093862120\",\"sqb_product_flag\":\"a1\"},\"payway\":2,\"version\":0,\"finish_time\":*************,\"sub_payway\":1,\"config_snapshot\":{\"clearance_provider\":7,\"latitude\":\"25.116106\",\"merchant_id\":\"4b831922-9a8f-4786-8208-c597d1f4e471\",\"merchant_sn\":\"*************\",\"provider\":1037,\"store_name\":\"CS银商小微0826101闫梦杰\",\"currency\":\"CNY\",\"terminal_id\":\"122d9ca0-6867-4b81-9642-d493cd2bc318\",\"terminal_sn\":\"**************\",\"longitude\":\"102.751907\",\"store_id\":\"6e992202-c893-450c-a7ee-0194e8c5986f\",\"store_sn\":\"1580000004686951\",\"channel_name\":\"上海收钱吧互联网科技股份有限公司\",\"store_city\":\"昆明市\",\"trade_app\":\"1\",\"across_store_refund_switch\":1,\"terminal_vendor_app_appid\":\"2019053000001597\",\"terminal_category\":108,\"merchant_name\":\"唐多令演示店-海科\",\"term_info\":{\"term_id\":\"TM000Bny\"},\"term_id\":\"TM000Bny\",\"haike_up_trade_params\":{\"is_affiliated\":false,\"fee\":1,\"alipay_sub_mch_id\":\"2088720484516973\",\"cert_id\":\"**********\",\"active\":true,\"agent_no\":\"ISV002073\",\"fee_rate\":\"0.38\",\"sys_pid\":\"2088531471794160\",\"liquidation_next_day\":true,\"original_provider_mch_id\":\"\",\"access_key\":\"z<[zhz}'byzn@b<@1}b88'`bZ`f8}zZffz\\\"h\",\"app_id\":\"1266000048330003\",\"provider_mch_id\":\"833F732354990001\"},\"terminal_name\":\"测试海科222\",\"district_code\":\"530103\",\"vendor_id\":\"859d9f5f-af99-11e5-9ec3-00163e00625b\",\"common_switch\":\"01000010100200012222222222222222\",\"sharing_switch\":1,\"merchant_country\":\"CHN\"},\"deleted\":false,\"effective_amount\":2880,\"paid_amount\":133,\"trade_no\":\"161725845317555688394\",\"channel_finish_time\":1725955979000,\"items\":{\"payments\":[{\"type\":\"DISCOUNT_WOSAI_MCH\",\"amount\":120,\"source\":\"**********\",\"origin_type\":\"jjz_price_discount\",\"origin_name\":\"全场活动(满3减1.20)\"}]},\"order_id\":\"o****************\",\"buyer_uid\":\"69314659863\",\"order_sn\":\"****************\",\"status\":2000}";
        discountQuotaService.updateDiscountQuotaByTransaction(JsonUtil.decode(str, Map.class));
    }

    @Test
    public void delCache() {
        Map<String, Object> info = cacheService.getQuotaInfo(57469L);
        System.out.println(JsonUtil.encode(info));

        //cacheService.delQuotaResponse();

    }

    @Test
    public void testQuota() {

        PreDeductDiscountQuotaRequest request = new PreDeductDiscountQuotaRequest();
        request.setComboId(2752L);
        request.setPayWay(2);
        request.setMerchantId("6526bbc2-2c0c-426c-a51e-26893c10665a");
        request.setOriginalAmount(25000);
        request.setTradeApp("1");
        request.setTid("t2001259240148565");

        DiscountQuotaResponse response = discountQuotaService.preDeductDiscountQuota(request);
        System.out.println(JSONObject.toJSONString(response));
    }


    @Test
    public void testQuota001() {

        PreDeductDiscountQuotaRequest request = new PreDeductDiscountQuotaRequest();
        request.setComboId(2752L);
        request.setPayWay(3);
        request.setMerchantId("6526bbc2-2c0c-426c-a51e-26893c10665a");
        request.setOriginalAmount(25000);
        request.setTradeApp("1");
        request.setTid("t2001259240148565");

        DiscountQuotaResponse response = discountQuotaService.preDeductDiscountQuota(request);
        System.out.println(JSONObject.toJSONString(response));
    }





    String s = "{\"merchantId\":\"34e86a08-8f10-4826-a1e3-84fbeecb3c7b\",\"tradeApp\":\"1\",\"tid\":\"t2003259248034977\",\"originalAmount\":100000,\"payWay\":2,\"comboId\":null,\"feeRate\":\"0.38\",\"provider\":1037,\"formal\":false}";
}
package com.wosai.trade.impl;

import com.wosai.data.bean.BeanUtil;
import com.wosai.trade.biz.businesslog.BusinessOpLogBiz;
import com.wosai.trade.biz.quota.FixedQuotaAddBiz;
import com.wosai.trade.repository.impl.TradeBizConfigServiceImpl;
import com.wosai.trade.service.activity.request.CancelByActivityIdRequest;
import com.wosai.trade.service.activity.request.RestoreByActivityIdRequest;
import com.wosai.trade.service.request.*;
import com.wosai.trade.service.result.QuotaQueryResult;
import com.wosai.upay.core.service.BusinssCommonService;
import com.wosai.upay.core.service.TradeConfigService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest
public class TransactionQuotaServiceImplTest {

    @Autowired
    private TransactionQuotaServiceImpl transactionQuotaService;

    @Autowired
    private FixedQuotaAddBiz fixedQuotaAddBiz;

    @Autowired
    private BusinssCommonService businssCommonService;

    @Autowired
    private BusinessOpLogBiz businessOpLogBiz;


    @Autowired
    private TradeBizConfigServiceImpl tradeBizConfigService;

    @Autowired
    private TradeConfigService tradeConfigService;


    @Autowired
    private ApplyActivityServiceImpl applyActivityService;


    @Autowired
    private FeeRateServiceImpl feeRateService;


    @Autowired
    private TradeStateServiceImpl tradeStateService;


    //添加风控单日信用额度

    @Test
    public void addFixedQuotaAndLog_Type5BizType1_AddsRiskDailyCreditTransactionLimit() {
        FixedQuotaAddRequest request = new FixedQuotaAddRequest();
        request.setBizType((byte) 1);
        request.setType((byte) 5);
        request.setFixedQuota(1000L);
        request.setMerchantSn("*************");

        OpLogCreateRequest opLogCreateRequest = new OpLogCreateRequest();
        opLogCreateRequest.setOuterSceneTraceId("traceId");
        opLogCreateRequest.setPlatformCode("platformCode");
        opLogCreateRequest.setOpUserId("opUserId");
        opLogCreateRequest.setOpUserName("opUserName");


        transactionQuotaService.addFixedQuotaAndLog(request, opLogCreateRequest);

    }

    // 添加银行卡单笔额度
    @Test
    public void addFixedQuotaAndLog_Type6BizType3_AddsSingleTransactionBankCardLimit() {
        FixedQuotaAddRequest request = new FixedQuotaAddRequest();
        request.setBizType((byte) 3);
        request.setType((byte) 6);
        request.setFixedQuota(2000L);
        request.setMerchantSn("*************");

        OpLogCreateRequest opLogCreateRequest = new OpLogCreateRequest();
        opLogCreateRequest.setOuterSceneTraceId("traceId");
        opLogCreateRequest.setPlatformCode("platformCode");
        opLogCreateRequest.setOpUserId("opUserId");
        opLogCreateRequest.setOpUserName("opUserName");


        transactionQuotaService.addFixedQuotaAndLog(request, opLogCreateRequest);


    }

    //添加风控单笔额度
    @Test
    public void addFixedQuotaAndLog_Type2BizType0_AddsRiskQuota() {
        FixedQuotaAddRequest request = new FixedQuotaAddRequest();
        request.setBizType((byte) 0);
        request.setType((byte) 2);
        request.setFixedQuota(3000L);
        request.setMerchantSn("*************");

        OpLogCreateRequest opLogCreateRequest = new OpLogCreateRequest();
        opLogCreateRequest.setOuterSceneTraceId("traceId");
        opLogCreateRequest.setPlatformCode("platformCode");
        opLogCreateRequest.setOpUserId("opUserId");
        opLogCreateRequest.setOpUserName("opUserName");

        transactionQuotaService.addFixedQuotaAndLog(request, opLogCreateRequest);

    }


    //添加特殊固定额度
    @Test
    public void addFixedQuotaAndLog_Type3BizType0_AddsSpecialFixedQuota() {
        FixedQuotaAddRequest request = new FixedQuotaAddRequest();
        request.setBizType((byte) 0);
        request.setType((byte) 3);
        request.setFixedQuota(4000L);
        request.setMerchantSn("*************");

        OpLogCreateRequest opLogCreateRequest = new OpLogCreateRequest();
        opLogCreateRequest.setOuterSceneTraceId("traceId");
        opLogCreateRequest.setPlatformCode("platformCode");
        opLogCreateRequest.setOpUserId("opUserId");
        opLogCreateRequest.setOpUserName("opUserName");

        transactionQuotaService.addFixedQuotaAndLog(request, opLogCreateRequest);

    }


    // 添加风控一般单月信用额度
    @Test
    public void addFixedQuotaAndLog_Type4BizType2_AddsRiskMonthlyCreditTransactionLimit() {
        FixedQuotaAddRequest request = new FixedQuotaAddRequest();
        request.setBizType((byte) 2);
        request.setType((byte) 4);
        request.setFixedQuota(5000L);
        request.setMerchantSn("*************");

        OpLogCreateRequest opLogCreateRequest = new OpLogCreateRequest();
        opLogCreateRequest.setOuterSceneTraceId("traceId");
        opLogCreateRequest.setPlatformCode("platformCode");
        opLogCreateRequest.setOpUserId("opUserId");
        opLogCreateRequest.setOpUserName("opUserName");
        transactionQuotaService.addFixedQuotaAndLog(request, opLogCreateRequest);

    }


    // 添加风控特殊单月信用额度
    @Test
    public void addFixedQuotaAndLog_Type5BizType2_AddsRiskMonthlySpecialCreditTransactionLimit() {
        FixedQuotaAddRequest request = new FixedQuotaAddRequest();
        request.setBizType((byte) 2);
        request.setType((byte) 5);
        request.setFixedQuota(6000L);
        request.setMerchantSn("*************");

        OpLogCreateRequest opLogCreateRequest = new OpLogCreateRequest();
        opLogCreateRequest.setOuterSceneTraceId("traceId");
        opLogCreateRequest.setPlatformCode("platformCode");
        opLogCreateRequest.setOpUserId("opUserId");
        opLogCreateRequest.setOpUserName("opUserName");
        transactionQuotaService.addFixedQuotaAndLog(request, opLogCreateRequest);

    }


    //修改风控单日信用额度

    @Test
    public void modifyFixedQuotaAndLog_Type5BizType1_AddsRiskDailyCreditTransactionLimit() {
        FixedQuotaModifyRequest request = new FixedQuotaModifyRequest();
        request.setBizType((byte) 1);
        request.setType((byte) 5);
        request.setFixedQuota(2000L);
        request.setMerchantSn("*************");

        OpLogCreateRequest opLogCreateRequest = new OpLogCreateRequest();
        opLogCreateRequest.setOuterSceneTraceId("traceId");
        opLogCreateRequest.setPlatformCode("platformCode");
        opLogCreateRequest.setOpUserId("opUserId");
        opLogCreateRequest.setOpUserName("opUserName");


        transactionQuotaService.modifyFixedQuotaAndLog(request, opLogCreateRequest);

    }

    // 修改银行卡单笔额度
    @Test
    public void modifyFixedQuotaAndLog_Type6BizType3_AddsSingleTransactionBankCardLimit() {
        FixedQuotaModifyRequest request = new FixedQuotaModifyRequest();
        request.setBizType((byte) 3);
        request.setType((byte) 6);
        request.setFixedQuota(3000L);
        request.setMerchantSn("*************");

        OpLogCreateRequest opLogCreateRequest = new OpLogCreateRequest();
        opLogCreateRequest.setOuterSceneTraceId("traceId");
        opLogCreateRequest.setPlatformCode("platformCode");
        opLogCreateRequest.setOpUserId("opUserId");
        opLogCreateRequest.setOpUserName("opUserName");


        transactionQuotaService.modifyFixedQuotaAndLog(request, opLogCreateRequest);


    }

    //修改风控单笔额度
    @Test
    public void modifyFixedQuotaAndLog_Type2BizType0_AddsRiskQuota() {
        FixedQuotaModifyRequest request = new FixedQuotaModifyRequest();
        request.setBizType((byte) 0);
        request.setType((byte) 2);
        request.setFixedQuota(4000L);
        request.setMerchantSn("*************");

        OpLogCreateRequest opLogCreateRequest = new OpLogCreateRequest();
        opLogCreateRequest.setOuterSceneTraceId("traceId");
        opLogCreateRequest.setPlatformCode("platformCode");
        opLogCreateRequest.setOpUserId("opUserId");
        opLogCreateRequest.setOpUserName("opUserName");

        transactionQuotaService.modifyFixedQuotaAndLog(request, opLogCreateRequest);

    }


    //修改特殊固定额度
    @Test
    public void modifyFixedQuotaAndLog_Type3BizType0_AddsSpecialFixedQuota() {
        FixedQuotaModifyRequest request = new FixedQuotaModifyRequest();
        request.setBizType((byte) 0);
        request.setType((byte) 3);
        request.setFixedQuota(4000L);
        request.setMerchantSn("*************");

        OpLogCreateRequest opLogCreateRequest = new OpLogCreateRequest();
        opLogCreateRequest.setOuterSceneTraceId("traceId");
        opLogCreateRequest.setPlatformCode("platformCode");
        opLogCreateRequest.setOpUserId("opUserId");
        opLogCreateRequest.setOpUserName("opUserName");

        transactionQuotaService.modifyFixedQuotaAndLog(request, opLogCreateRequest);

    }


    // 修改风控一般单月信用额度
    @Test
    public void modifyFixedQuotaAndLog_Type4BizType2_AddsRiskMonthlyCreditTransactionLimit() {
        FixedQuotaModifyRequest request = new FixedQuotaModifyRequest();
        request.setBizType((byte) 2);
        request.setType((byte) 4);
        request.setFixedQuota(6000L);
        request.setMerchantSn("*************");

        OpLogCreateRequest opLogCreateRequest = new OpLogCreateRequest();
        opLogCreateRequest.setOuterSceneTraceId("traceId");
        opLogCreateRequest.setPlatformCode("platformCode");
        opLogCreateRequest.setOpUserId("opUserId");
        opLogCreateRequest.setOpUserName("opUserName");
        transactionQuotaService.modifyFixedQuotaAndLog(request, opLogCreateRequest);

    }


    // 修改风控特殊单月信用额度
    @Test
    public void modifyFixedQuotaAndLog_Type5BizType2_AddsRiskMonthlySpecialCreditTransactionLimit() {
        FixedQuotaModifyRequest request = new FixedQuotaModifyRequest();
        request.setBizType((byte) 2);
        request.setType((byte) 5);
        request.setFixedQuota(7000L);
        request.setMerchantSn("*************");
        OpLogCreateRequest opLogCreateRequest = new OpLogCreateRequest();
        opLogCreateRequest.setOuterSceneTraceId("traceId");
        opLogCreateRequest.setPlatformCode("platformCode");
        opLogCreateRequest.setOpUserId("opUserId");
        opLogCreateRequest.setOpUserName("opUserName");
        transactionQuotaService.modifyFixedQuotaAndLog(request, opLogCreateRequest);

    }


    // 添加临时额度并记录

    @Test
    public void testAddTempQuotaAndLog_SuccessfulScenario() {
        // Arrange
        TempQuotaAddRequest tempQuotaAddRequest = new TempQuotaAddRequest();
        tempQuotaAddRequest.setBeginDate("2018-01-01");
        tempQuotaAddRequest.setEndDate("2025-01-02");
        tempQuotaAddRequest.setMerchantSn("*************");
        tempQuotaAddRequest.setQuota(6000L);
        tempQuotaAddRequest.setType((byte) 4);

        OpLogCreateRequest opLogCreateRequest = new OpLogCreateRequest();
        opLogCreateRequest.setOuterSceneTraceId("traceId");
        opLogCreateRequest.setPlatformCode("platform");
        opLogCreateRequest.setOpUserId("opUserId");
        opLogCreateRequest.setOpUserName("opUserName");
        opLogCreateRequest.setRemark("remark");



        // Act
        transactionQuotaService.addTempQuotaAndLog(tempQuotaAddRequest, opLogCreateRequest);


    }


    // 单笔限额修改
    @Test
    public void testUpdateCategoryMerchantSingleMaxAndLog_SuccessfulUpdate_LogSent() {
        // Arrange
        String merchantSn = "*************";
        Long quota = 1000L;
        String category = "106";
        OpLogCreateRequest opLogCreateRequest = new OpLogCreateRequest();
        opLogCreateRequest.setOpUserId("opUserId");
        opLogCreateRequest.setOpUserName("opUserName");
        opLogCreateRequest.setOuterSceneTraceId("traceId");
        opLogCreateRequest.setPlatformCode("platformCode");
        opLogCreateRequest.setRemark("remark");


        // Act
        Map<String, Object> result = tradeBizConfigService.updateCategoryMerchantSingleMaxAndLog(merchantSn, quota, category, opLogCreateRequest);


    }

    // 测试活动取消
    @Test
    public void testCancelByActivityId_Success() throws Exception {
        // Arrange
        CancelByActivityIdRequest cancelByActivityIdRequest = new CancelByActivityIdRequest();
        CancelByActivityIdRequest.CancelEntity cancelEntity = new CancelByActivityIdRequest.CancelEntity();
        cancelEntity.setMerchantSn("*************");
        cancelEntity.setRiskActivityProcessTypeEnum(com.wosai.trade.service.enums.RiskActivityProcessTypeEnum.WECHAT_UNIVERSITY);
        cancelByActivityIdRequest.setCancelEntity(cancelEntity);
        CancelByActivityIdRequest.CancelDescribe cancelDescribe = new CancelByActivityIdRequest.CancelDescribe();
        cancelDescribe.setOperator("operator");
        cancelDescribe.setOperatorName("operatorName");
        cancelDescribe.setReason("reason");
        cancelByActivityIdRequest.setDescribe(cancelDescribe);
        OpLogCreateRequest opLogCreateRequest = new OpLogCreateRequest();
        opLogCreateRequest.setOuterSceneTraceId("traceId");
        opLogCreateRequest.setPlatformCode("platformCode");
        opLogCreateRequest.setOpUserId("opUserId");
        opLogCreateRequest.setOpUserName("opUserName");
        opLogCreateRequest.setRemark("remark");
        // Act
        applyActivityService.cancelByActivityIdAndLog(cancelByActivityIdRequest, opLogCreateRequest);

        // Assert
        // Verify interactions and no exception thrown
    }


    // 测试活动恢复
    @Test
    public void testRestoreActivityId_Success() throws Exception {
        // Arrange
        RestoreByActivityIdRequest restoreByActivityIdRequest = new RestoreByActivityIdRequest();
        RestoreByActivityIdRequest.RestoreEntity restoreEntity = new RestoreByActivityIdRequest.RestoreEntity();
        restoreEntity.setMerchantSn("*************");
        restoreEntity.setRiskActivityProcessTypeEnum(com.wosai.trade.service.enums.RiskActivityProcessTypeEnum.WECHAT_UNIVERSITY);
        restoreByActivityIdRequest.setRestoreEntity(restoreEntity);
        RestoreByActivityIdRequest.RestoreDescribe restoreDescribe = new RestoreByActivityIdRequest.RestoreDescribe();
        restoreDescribe.setOperator("operator");
        restoreDescribe.setOperatorName("operatorName");
        restoreDescribe.setReason("reason");
        restoreByActivityIdRequest.setDescribe(restoreDescribe);
        OpLogCreateRequest opLogCreateRequest = new OpLogCreateRequest();
        opLogCreateRequest.setOuterSceneTraceId("traceId");
        opLogCreateRequest.setPlatformCode("platformCode");
        opLogCreateRequest.setOpUserId("opUserId");
        opLogCreateRequest.setOpUserName("opUserName");
        opLogCreateRequest.setRemark("remark");
        // Act
        applyActivityService.restoreByActivityIdAndLog(restoreByActivityIdRequest, opLogCreateRequest);

        // Assert
        // Verify interactions and no exception thrown
    }


    // 测试教培活动
    @Test
    public void testApplyTradeCombo078FeeRate() throws Exception {

        OpLogCreateRequest opLogCreateRequest = new OpLogCreateRequest();
        opLogCreateRequest.setOuterSceneTraceId("traceId");
        opLogCreateRequest.setPlatformCode("platformCode");
        opLogCreateRequest.setOpUserId("opUserId");
        opLogCreateRequest.setOpUserName("opUserName");
        opLogCreateRequest.setRemark("remark");
        feeRateService.cancelTradeCombo078FeeRateAndLog("*************", opLogCreateRequest);

    }


    // 测试门店间连状态

    @Test
    public void testUpdateStateByBizAndTypeAndStoreIdAndLog() throws Exception {

        OpLogCreateRequest opLogCreateRequest = new OpLogCreateRequest();
        opLogCreateRequest.setOuterSceneTraceId("traceId");
        opLogCreateRequest.setPlatformCode("platformCode");
        opLogCreateRequest.setOpUserId("opUserId");
        opLogCreateRequest.setOpUserName("opUserName");
        opLogCreateRequest.setRemark("remark");
        tradeStateService.updateStateByBizAndTypeAndStoreIdAndLog("ddeb0550-c58f-4a64-bc67-dff8b1e542f2", 1, 1, false, "remark",  opLogCreateRequest);

    }

    // 测试商户间连状态

    @Test
    public void testUpdateStateByBizAndTypeAndMerchantIdAndLog() throws Exception {

        OpLogCreateRequest opLogCreateRequest = new OpLogCreateRequest();
        opLogCreateRequest.setOuterSceneTraceId("traceId");
        opLogCreateRequest.setPlatformCode("platformCode");
        opLogCreateRequest.setOpUserId("opUserId");
        opLogCreateRequest.setOpUserName("opUserName");
        opLogCreateRequest.setRemark("remark测试");
        tradeStateService.updateStateByBizAndTypeAndMerchantIdAndLog("06e44600-b889-4828-987b-7f67e04bd49f", 1, 1, false, "remark",  opLogCreateRequest);

    }

    // 取消额度信息
    @Test
    public void testCancelFixedQuotaAndLog() throws Exception {
        OpLogCreateRequest opLogCreateRequest = new OpLogCreateRequest();
        opLogCreateRequest.setOuterSceneTraceId("traceId");
        opLogCreateRequest.setPlatformCode("platformCode");
        opLogCreateRequest.setOpUserId("opUserId");
        opLogCreateRequest.setOpUserName("opUserName");
        opLogCreateRequest.setRemark("remark");
        QuotaCancelRequest quotaCancelRequest = new QuotaCancelRequest();
        quotaCancelRequest.setBizType((byte) 0);
        quotaCancelRequest.setType((byte) 1);
        quotaCancelRequest.setSubType((byte) 4);
        quotaCancelRequest.setMerchantSn("*************");
        transactionQuotaService.cancelQuotaAndLog(quotaCancelRequest, opLogCreateRequest);
    }





}


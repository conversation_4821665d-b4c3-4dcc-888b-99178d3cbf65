package com.wosai.trade.consumer;

import com.alibaba.fastjson.JSON;
import com.wosai.databus.LogEntry;
import com.wosai.databus.event.pay.trade.state.TradeStateChangeEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.generic.GenericRecord;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.test.context.junit4.SpringRunner;

import java.nio.ByteBuffer;

/**
 * TradeStateChangeConsumerTest
 *
 * <AUTHOR>
 */
//@RunWith(SpringRunner.class)
//@SpringBootTest
//@Component
@Slf4j
public class TradeStateChangeConsumerTest {

    @KafkaListener(topics = "databus.event.pay.trade.state.allin")
    public void consume(ConsumerRecord<String, GenericRecord> record) {
        GenericRecord value = record.value();
        try {
            ByteBuffer buffer = (ByteBuffer) value.get(LogEntry.EVENT);
            TradeStateChangeEvent tradeStateChangeEvent = JSON.parseObject(buffer.array(), TradeStateChangeEvent.class);

            tradeStateChangeEvent.setSeq((Long) value.get(LogEntry.SEQ));
            tradeStateChangeEvent.setTimestamp((Long) value.get(LogEntry.TIMESTAMP));

        } catch (Throwable e) {
            log.error("", e);
        }
    }
}

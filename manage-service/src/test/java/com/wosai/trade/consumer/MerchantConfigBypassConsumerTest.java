package com.wosai.trade.consumer;

import java.util.Map;

import org.apache.avro.Schema;
import org.apache.avro.generic.GenericData;
import org.apache.avro.generic.GenericRecord;
import org.apache.commons.io.IOUtils;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.serialization.StringSerializer;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.stereotype.Component;
import org.springframework.test.context.junit4.SpringRunner;

import com.wosai.trade.service.MerchantConfigBypassService;

import io.confluent.kafka.serializers.KafkaAvroSerializer;
import io.confluent.kafka.serializers.KafkaAvroSerializerConfig;
import lombok.SneakyThrows;

@RunWith(SpringRunner.class)
@SpringBootTest
@Component
public class MerchantConfigBypassConsumerTest {
    @Autowired
    KafkaProperties kafkaProperties;
    @Value("${spring.kafka.bootstrap-servers}")
    private String bootstrap;
    @Value("${spring.kafka.properties.schema.registry.url}")
    private String registryUrl;
    @Autowired
    MerchantConfigBypassService merchantConfigBypassService;

    @Test
    @SneakyThrows
    public void testUpdateBankAccountConsumer() {
        Map<String, Object> props = kafkaProperties.buildProducerProperties();
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, KafkaAvroSerializer.class);
        props.put(KafkaAvroSerializerConfig.SCHEMA_REGISTRY_URL_CONFIG, registryUrl);
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrap);
        KafkaProducer kafkaProducer = new KafkaProducer(props);
        // 银行卡变更状态为可用
        Schema schema = new Schema.Parser().parse(IOUtils.toInputStream("\n"
                + "{\n"
                + "  \"namespace\" : \"com.wosai.upay.job.avro\",\n"
                + "  \"type\" : \"record\",\n"
                + "  \"name\" : \"UpdateBankAccount\",\n"
                + "  \"fields\" : [\n"
                + "    {\"name\":\"merchant_sn\",\"type\":\"string\", \"meta\":\"商户号\"},\n"
                + "    {\"name\":\"merchant_id\",\"type\":\"string\", \"meta\":\"商户id\"},\n"
                + "    {\"name\":\"acquirer\",\"type\":\"string\", \"meta\":\"收单机构\"},\n"
                + "    {\"name\":\"match\",\"type\":\"int\", \"meta\":\"收钱吧和收单机构银行卡是否匹配 0不匹配 1匹配\"}\n"
                + "  ]\n"
                + "}"));
        GenericRecord gr = new GenericData.Record(schema);
        gr.put("acquirer", "lkl");
        gr.put("merchant_id", "0a20ca4c-2d79-43b3-a679-ba391069fd1e");
        gr.put("merchant_sn", "cc");
        gr.put("match", 1);
        kafkaProducer.send(new ProducerRecord("events.merchant-contract-job.update-bank-account", gr));
        kafkaProducer.flush();
        Thread.sleep(1000);
        assert null != merchantConfigBypassService.getByMerchantIdAndPayway("0a20ca4c-2d79-43b3-a679-ba391069fd1e", 2);

        // 银行卡变更状态为不可用
        gr.put("match", 0);
        kafkaProducer.send(new ProducerRecord("events.merchant-contract-job.update-bank-account", gr));
        kafkaProducer.flush();
        Thread.sleep(1000);
        assert null == merchantConfigBypassService.getByMerchantIdAndPayway("0a20ca4c-2d79-43b3-a679-ba391069fd1e", 2);
        kafkaProducer.close();
    }

    @Test
    @SneakyThrows
    public void testMerchantContractSuccess() {
        Map<String, Object> props = kafkaProperties.buildProducerProperties();
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, KafkaAvroSerializer.class);
        props.put(KafkaAvroSerializerConfig.SCHEMA_REGISTRY_URL_CONFIG, registryUrl);
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrap);
        KafkaProducer kafkaProducer = new KafkaProducer(props);
        // 商户入网
        Schema schema = new Schema.Parser().parse(IOUtils.toInputStream("{\n"
                + "  \"namespace\" : \"com.wosai.upay.job.avro\",\n"
                + "  \"type\" : \"record\",\n"
                + "  \"name\" : \"MerchantContractSuccess\",\n"
                + "  \"fields\" : [\n"
                + "    {\"name\":\"merchant_sn\",\"type\":\"string\", \"meta\":\"商户号\"},\n"
                + "    {\"name\":\"merchant_id\",\"type\":\"string\", \"meta\":\"商户id\"},\n"
                + "    {\"name\":\"acquirer\",\"type\":\"string\", \"meta\":\"收单机构\"},\n"
                + "    {\"name\":\"bank_channel_flag\",\"type\":[\"null\",\"string\"],\"default\":null,\"meta\":\"通道是否为银行通道0:非银行通道 1:银行通道\"}\n"
                + "  ]\n"
                + "}"));
        GenericRecord gr = new GenericData.Record(schema);
        gr.put("acquirer", "lkl");
        gr.put("merchant_id", "0a20ca4c-2d79-43b3-a679-ba391069fd1e");
        gr.put("merchant_sn", "**************");
        gr.put("bank_channel_flag", "1");
        kafkaProducer.send(new ProducerRecord("events.merchant-contract-job.contract-success", gr));
        kafkaProducer.flush();
        Thread.sleep(1000);
        kafkaProducer.close();
    }
}

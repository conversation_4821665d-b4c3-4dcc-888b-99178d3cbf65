package com.wosai.trade;

import com.alibaba.fastjson.JSON;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.wosai.business.log.model.BusinessLog;
import com.wosai.business.log.model.BusinessLogType;
import com.wosai.data.bean.BeanUtil;
import com.wosai.databus.event.audit.AuditInstanceApproveEvent;
import com.wosai.databus.event.audit.AuditInstanceEvent;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.trade.biz.activity.QuotaApplyActivityBiz;
import com.wosai.trade.client.DingTalkClient;
import com.wosai.trade.config.apollo.CommonApolloConfig;
import com.wosai.trade.consumer.AuditInstanceEventConsumer;
import com.wosai.trade.impl.*;
import com.wosai.trade.model.biz.ApplyComboDetailParam;
import com.wosai.trade.model.biz.PuHuiLadderBoundary;
import com.wosai.trade.model.dal.MerchantFeeRateCountDalParam;
import com.wosai.trade.repository.dao.MerchantFeeRateDao;
import com.wosai.trade.repository.dao.StoreFeeRateDao;
import com.wosai.trade.repository.dao.entity.TradeActivityEntity;
import com.wosai.trade.service.AppService;
import com.wosai.trade.service.TradeActivityService;
import com.wosai.trade.service.request.ActivityBaseRequest;
import com.wosai.trade.service.result.StoreBizServiceAgreement;
import com.wosai.trade.service.result.TradeActivityResult;
import com.wosai.trade.task.MicroPuHuiActivityScheduler;
//import com.wosai.upay.core.bean.response.GetAllMetaResponse;
import com.wosai.trade.task.TradeActivityScheduler;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.service.BusinssCommonService;
import com.wosai.upay.user.api.service.UserService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.PrintStream;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Created by wujianwei on 2021/7/9.
 */

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class WjwTest {


    @Autowired
    private MicroPuHuiActivityScheduler microPuHuiActivityScheduler;
    @Autowired
    private AppService appService;
    @Autowired
    private TradeCommonService tradeCommonService;
    @Autowired
    private TodayDateCheckService todayDateCheckService;
    @Autowired
    private FeeRateCommonService feeRateCommonService;
    @Autowired
    private DingTalkClient client;
    @Autowired
    private BusinssCommonService businssCommonService;
    @Autowired
    private StoreFeeRateDao storeFeeRateDao;
    @Autowired
    private MerchantFeeRateDao merchantFeeRateDao;
    @Autowired
    private UserService userService;
    @Autowired
    private ServiceAgreementFacade serviceAgreementFacade;
    @Autowired
    private MicroPuHuiService microPuHuiService;
    @Autowired
    private TradeActivityService tradeActivityService;
    @Autowired
    private CommonApolloConfig commonApolloConfig;
    @Autowired
    private QuotaApplyActivityBiz quotaApplyActivityBiz;



//    @Autowired
    private AuditInstanceEventConsumer consumer;
    @Test
    public void test(){
//        String str = "{\"auditId\":\"4949420\",\"auditSn\":\"SP3520220602000003\",\"formId\":\"1880429\",\"finishTime\":1654099402722,\"operatorPlatform\":\"CRM\",\"module\":\"CRM\",\"auditTemplateId\":\"2057\",\"operatorOrgNamePath\":\"服务商/总部/总部/测试/演示商户\",\"eventType\":\"AUDIT_APPROVE\",\"templateId\":\"11069\",\"operatorName\":\"边路遥\",\"objectType\":\"AUDIT\",\"auditCommentProperty\":{\"id\":\"70741128\",\"operatorName\":\"边路遥\",\"operator\":\"a929886a-48f5-4931-8764-450e6de98b3f\",\"platform\":\"CRM\"},\"operatorOrgCode\":\"09011\",\"bizKey\":\"CRM_ec696866-3ec5-4eb8-8242-3927a2a32928\",\"templateEvent\":\"fee_rate_combo_approval\",\"ctime\":1654099399000,\"auditCallBackProperty\":{\"resultFail\":1},\"businessMap\":{\"trade_combo\":\"普惠政策:88\",\"action\":\"申请普惠政策\",\"merchant\":{\"merch_info.ctime\":{\"keyName\":\"入网时间\",\"value\":\"2022-06-01 22:58\"},\"merch_info.sn\":{\"keyName\":\"商户号\",\"value\":\"1680005461421\"},\"merch_info.name\":{\"keyName\":\"商户名称\",\"value\":\"服务商费率测试商户\"},\"merch_info.province_city_district\":{\"keyName\":\"省市区\",\"value\":\"广东省/深圳市/罗湖区\"}},\"commentdetails\":{\"id\":\"70741128\",\"operatorName\":\"边路遥\",\"operator\":\"a929886a-48f5-4931-8764-450e6de98b3f\",\"platform\":\"CRM\"},\"audit_info\":{\"id\":\"4949420\",\"sn\":\"SP3520220602000003\",\"operator\":\"a929886a-48f5-4931-8764-450e6de98b3f\",\"platform\":\"CRM\",\"status\":\"2\",\"time\":1654099403000,\"template_id\":2057,\"template_name\":\"特殊地区商户普惠政策申请\"}},\"operatorId\":\"a929886a-48f5-4931-8764-450e6de98b3f\",\"seq\":3621010,\"timestamp\":1654099404747}";
//        AuditInstanceEvent event = JSON.parseObject(str, AuditInstanceApproveEvent.class);
//        try {
//            consumer.handleEvent(event);
//
//        } catch (Throwable e) {
//            consumer.handleEventFail(event, e);
//        }
//
        System.out.println(commonApolloConfig.getFeeRateAuditFileLineLimit());

    }

    @Autowired
    StringRedisTemplate redisTemplate;

    @SneakyThrows
    @Test
    public void redisTest(){
        ExecutorService executorService = Executors.newFixedThreadPool(10);
        for (int i = 0; i < 1000; i++) {
            int finalI1 = i;
            executorService.submit(() -> {
                String finalI = "" + finalI1;
                Boolean aBoolean = redisTemplate.hasKey(finalI);
                System.out.println(finalI + " " + aBoolean);
                try {
                    Thread.sleep(1000l);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }

            });
        }
        Thread.sleep(1000000l);
    }

    private static Cache<String, String> puHuiPictureCache = CacheBuilder.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .maximumSize(100000)
            .build();

    @SneakyThrows
    @Test
    public void daotest(){
//        MerchantFeeRateCountDalParam param = MerchantFeeRateCountDalParam.builder().merchantSn("21690003301376")
//                 w       .payWays(Arrays.asList(2, 3)).minCreateAt(new Date()).build();
//        System.out.println(merchantFeeRateDao.count(param));;
//        tradeCommonService.getMerchantOrganizationPathFromCrm("7c145a93-f541-40a7-9111-fb5b2cb64f58");

//        feeRateCommonService.getMerchantDefaultComboConfig("21690002997765");
        PrintStream out = new PrintStream(new File("invalid.txt"));
        Scanner scanner = new Scanner(new File("/Users/<USER>/work/wosai/documents/temp1"));
        while (scanner.hasNextLine()){
            String line = scanner.nextLine();
            if(!line.contains("jsonrpc")){
                continue;
            }
            Map map = JsonUtil.jsonStringToObject(line.split(" :  ")[1].trim(), Map.class);
            List<Map> list = (List<Map>) map.get("result");
            if(list == null || list.isEmpty()){
                out.println(line);
                log.warn("不合法 {}", line);
                continue;
            }
            Map origin = list.get(0);
            Map<Integer, ApplyComboDetailParam> config = feeRateCommonService.buildComboConfig(list);
            List<Map<String,Object>> records = (List<Map<String, Object>>) origin.get("records");
            log.info("{}", JsonUtil.toJsonStr(config));
            List<Integer> payways = records.stream().map(r -> BeanUtil.getPropInt(r, "payway")).collect(Collectors.toList());
            if(payways.size() != config.size()){
                out.println(line);
                log.warn("不合法 {}", line);
            }
        }
        out.flush();


    }

    @SneakyThrows
    public static void main(String[] args) {
//        String merchantId = "7c145a93-f541-40a7-9111-fb5b2cb64f58";
//        String merchantSn = "21690003225341";
//        BusinessLog build = BusinessLog.builder()
//                .objectId(merchantId)
//                .platform("trade-manager")
//                .userId("system")
//                .userName("system")
//                .logType(BusinessLogType.builder().functionCode("***********").tableName("merchant").objectCode("merchant")
//                        .remark("商户结束普惠: " + merchantSn).opType(3)
//                        .build())
//                .before(null)
//                .includeKey(null)
//                .after(null)
//                .build();
//        System.out.println(JsonUtil.objectToJsonString(build));


    }
}

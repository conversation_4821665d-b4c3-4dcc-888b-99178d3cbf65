package com.wosai.trade;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import java.util.concurrent.ThreadLocalRandom;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.wosai.trade.model.enums.StatusEnum;
import com.wosai.trade.repository.dao.TradeAppDao;
import com.wosai.trade.repository.dao.TradeComboDao;
import com.wosai.trade.service.TradeAppService;
import com.wosai.trade.service.exception.TradeManageBizException;
import com.wosai.trade.service.exception.enums.TradeManageRespCodeEnum;
import com.wosai.trade.service.request.TradeAppAddRequest;
import com.wosai.trade.service.request.TradeAppModifyRequest;
import com.wosai.trade.service.request.TradeAppQueryRequest;
import com.wosai.trade.service.result.ListResult;
import com.wosai.trade.service.result.TradeAppResult;

@RunWith(SpringRunner.class)
@SpringBootTest
public class TradeAppTest {
    @Autowired
    TradeAppService tradeAppService;
    
    @Autowired
    TradeAppDao tradeAppDao;

    private static final String NEXT_NAME = System.currentTimeMillis() + "";

    /**
     * 接口非空校验
     */
    @Test
    public void testInvalidParams() {
        try {
            tradeAppService.addTradeApp(null);
        }catch (Exception e) {
            assertTrue("必填参数为空校验", e instanceof TradeManageBizException);
        }
        
        TradeAppAddRequest request = new TradeAppAddRequest();
        request.setName(NEXT_NAME);
        
        try {
            tradeAppService.addTradeApp(request);
        }catch (Exception e) {
            assertTrue("必填参数为空校验", ((TradeManageBizException)e).getMessage().indexOf("业务方简要描述不能为空")>= 0);
        }
    }

    /**
     * 添加业务方
     */
    @Test
    public void testAdd() {
        TradeAppAddRequest request = new TradeAppAddRequest();
        request.setName(NEXT_NAME);
        request.setCreator("wosai-test");
        request.setDescription("wosai-description");
        request.setStatus(StatusEnum.ENABLE.getCode());
        request.setPayApp(true);
        tradeAppService.addTradeApp(request);
        assertTrue("添加业务方", Boolean.TRUE);

        // 重复添加业务方
        try {
            tradeAppService.addTradeApp(request);
        }catch (TradeManageBizException e) {
            assertEquals("添加业务方", TradeManageRespCodeEnum.TRADE_APP_EXISTS.getCode(), e.getCode());
        }
        
        TradeAppQueryRequest queryRequest = new TradeAppQueryRequest();
        queryRequest.setName(NEXT_NAME);
        ListResult<TradeAppResult> result  = tradeAppService.queryTradeApps(queryRequest);
        assertTrue("添加业务方", result.getTotal() == 1L);
        assertTrue("添加业务方", result.getRecords().get(0).getPayApp());
        tradeAppDao.delete(((TradeAppResult)result.getRecords().get(0)).getId());
        
        
    }
 
    /**
     * 修改业务方
     */
    @Test
    public void testModify() {
        TradeAppAddRequest request = new TradeAppAddRequest();
        request.setName(NEXT_NAME);
        request.setCreator("wosai-test");
        request.setDescription("wosai-description");
        request.setStatus(StatusEnum.ENABLE.getCode());
        tradeAppService.addTradeApp(request);
        assertTrue("修改业务方", Boolean.TRUE);

        TradeAppQueryRequest queryRequest = new TradeAppQueryRequest();
        queryRequest.setName(NEXT_NAME);
        ListResult<TradeAppResult> result  = tradeAppService.queryTradeApps(queryRequest);
        assertTrue("修改业务方", result.getTotal() == 1L);
        long id = ((TradeAppResult)result.getRecords().get(0)).getId();

        TradeAppModifyRequest modifyRequest = new TradeAppModifyRequest();
        modifyRequest.setName(NEXT_NAME);
        modifyRequest.setCreator("wosai-test-new");
        modifyRequest.setDescription("wosai-description-new");
        modifyRequest.setStatus(StatusEnum.ENABLE.getCode());
        modifyRequest.setId(id);
        tradeAppService.modifyTradeApp(modifyRequest);
        assertTrue("修改业务方", Boolean.TRUE);

        result  = tradeAppService.queryTradeApps(queryRequest);
        assertTrue("修改业务方", result.getTotal() == 1L);
        
        TradeAppResult entity = (TradeAppResult)result.getRecords().get(0);
        assertEquals("修改业务方 creator", modifyRequest.getCreator(), entity.getCreator());
        assertEquals("修改业务方 description", modifyRequest.getDescription(), entity.getDescription());
        assertEquals("修改业务方 status", modifyRequest.getStatus().intValue(), entity.getStatus());
        
        tradeAppDao.delete(id);
    }

    /**
     * 关闭和开启业务方
     */
    @Test
    public void testChangeStatus() {
        TradeAppAddRequest request = new TradeAppAddRequest();
        request.setName(NEXT_NAME);
        request.setCreator("wosai-test");
        request.setDescription("wosai-description");
        request.setStatus(StatusEnum.ENABLE.getCode());
        tradeAppService.addTradeApp(request);
        assertTrue("关闭和开启业务方", Boolean.TRUE);
        
        TradeAppQueryRequest queryRequest = new TradeAppQueryRequest();
        queryRequest.setName(NEXT_NAME);
        ListResult<TradeAppResult> result  = tradeAppService.queryTradeApps(queryRequest);
        assertTrue("关闭和开启业务方", result.getTotal() == 1L);
        
        long id = ((TradeAppResult)result.getRecords().get(0)).getId();
        tradeAppService.disableTradeApp(id);
        
        result  = tradeAppService.queryTradeApps(queryRequest);
        assertTrue("关闭和开启业务方", result.getTotal() == 1L);
        
        TradeAppResult entity = (TradeAppResult)result.getRecords().get(0);
        assertEquals("关闭和开启业务方", StatusEnum.DISABLE.getCode(), entity.getStatus());
        
        tradeAppService.enableTradeApp(id);
        
        result  = tradeAppService.queryTradeApps(queryRequest);
        assertTrue("关闭和开启业务方", result.getTotal() == 1L);
        
        entity = (TradeAppResult)result.getRecords().get(0);
        assertEquals("关闭和开启业务方", StatusEnum.ENABLE.getCode(), entity.getStatus());

        tradeAppDao.delete(id);
    }


}

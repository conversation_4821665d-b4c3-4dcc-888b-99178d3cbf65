package com.wosai.trade.biz.servicefee;

import com.wosai.profit.sharing.model.request.BusinessOpenRequest;
import com.wosai.profit.sharing.service.BusinessOpenService;
import com.wosai.trade.BaseTest;
import com.wosai.trade.repository.dao.ServiceFeeEffectiveMapper;
import com.wosai.trade.repository.dao.ServiceFeeMapper;
import com.wosai.trade.repository.dao.entity.ServiceFeeEffectiveEntity;
import com.wosai.trade.repository.dao.entity.ServiceFeeEntity;
import com.wosai.trade.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;


@Slf4j
public class OpenProfitSharingBizTest extends BaseTest {

    @Resource
    private OpenProfitSharingBiz openProfitSharingBiz;
    @Resource
    private ServiceFeeEffectiveMapper serviceFeeEffectiveMapper;
    @Resource
    private ServiceFeeMapper serviceFeeMapper;


    @Test
    public void changeBusinessRule() {
        ServiceFeeEntity feeDo = serviceFeeMapper.selectByPrimaryKey(51090L);
        openProfitSharingBiz.changeBusinessRule(feeDo);
    }

    @Test
    public void execute() {
        ServiceFeeEntity feeDo = serviceFeeMapper.selectByPrimaryKey(121L);
        ServiceFeeEffectiveEntity effectiveDo = serviceFeeEffectiveMapper.selectByPrimaryKey(160L);
        openProfitSharingBiz.openExecute(effectiveDo, feeDo, null);

    }

    @Resource
    private BusinessOpenService businessOpenService;

    @Test
    public void test() {
        try {
            String json = "{\"trade_app_id\":\"7\",\"merchant_id\":\"ecc255d4-8e99-4014-a9fa-c72172049cbb\",\"store_id\":\"455bc657-4702-4a2e-9dd3-2a99501d828d\",\"service_fees\":[{\"ratio\":\"0.22%\",\"service_fee_id\":\"29\",\"base_metrics\":\"settlement_amount\"}]}";
            BusinessOpenRequest request = JsonUtil.decode(json, BusinessOpenRequest.class);
            request.setTradeAppId("5555555");
            businessOpenService.open(request);
        } catch (Exception e) {
            log.error("", e);
        }
    }
}
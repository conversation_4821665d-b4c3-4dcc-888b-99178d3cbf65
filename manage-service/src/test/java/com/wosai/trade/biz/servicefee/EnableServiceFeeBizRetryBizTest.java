package com.wosai.trade.biz.servicefee;

import com.wosai.trade.BaseTest;
import com.wosai.trade.repository.dao.ServiceFeeEffectiveMapper;
import com.wosai.trade.repository.dao.entity.ServiceFeeEffectiveEntity;
import org.junit.Test;

import javax.annotation.Resource;

public class EnableServiceFeeBizRetryBizTest extends BaseTest {

    @Resource
    private EnableServiceFeeRetryBiz retryBiz;
    @Resource
    private ServiceFeeEffectiveMapper effectiveMapper;

    @Test
    public void execute0() {
        retryBiz.execute0();
    }

    @Test
    public void retry() {
        ServiceFeeEffectiveEntity entityDo = effectiveMapper.selectByPrimaryKey(20828L);
        retryBiz.retry(entityDo);
    }
}
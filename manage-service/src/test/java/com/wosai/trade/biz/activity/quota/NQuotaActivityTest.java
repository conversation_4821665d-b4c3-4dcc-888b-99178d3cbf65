package com.wosai.trade.biz.activity.quota;

import com.google.common.collect.Maps;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.trade.BaseTest;
import com.wosai.trade.biz.activity.model.DiscountQuotaInfo;
import com.wosai.trade.impl.CacheService;
import com.wosai.trade.service.DiscountQuotaService;
import com.wosai.trade.service.activity.constant.ActivityConstants;
import com.wosai.trade.service.activity.constant.QuotaActivityConstants;
import com.wosai.trade.service.activity.request.QuotaActivityApplyRequest;
import com.wosai.trade.service.activity.response.DiscountQuotaResponse;
import com.wosai.trade.service.enums.QuotaActivityScenesTypeEnum;
import com.wosai.trade.service.request.PreDeductDiscountQuotaRequest;
import com.wosai.trade.util.LocalDateTimeUtil;
import com.wosai.upay.model.dao.Transaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/27
 **/
@Slf4j
public class NQuotaActivityTest extends BaseTest {
    @Autowired
    private DiscountQuotaCacheBiz discountQuotaCacheBiz;
    @Autowired
    private DiscountQuotaService discountQuotaService;
    @Autowired
    private CacheService cacheService;

    @Test
    public void applyRollingTime() {
        testRolling1();
        testRolling2();
        testRolling3();
    }

    @Test
    public void prepareDiscountQuotaInfo() {
        String tid = "t78952test1111113";
//        String merchantId = "024d6097-dfde-4ca4-91c4-dc4d73cada0b";
        String merchantId = "541d5351-977c-42b6-b8ff-f5b5e86514fd";
        log.info("test 删除info缓存");
        discountQuotaCacheBiz.delDiscountQuotaCacheInfoList(merchantId);
        List<DiscountQuotaInfo> preList = discountQuotaCacheBiz.getDiscountQuotaInfoCacheList(merchantId);
        Map<Long, Long> preMap = getUsable(preList);
        PreDeductDiscountQuotaRequest request = new PreDeductDiscountQuotaRequest();
        request.setProvider(1033);
        request.setPayWay(2);
        request.setFeeRate("0.8");
        request.setOriginalAmount(1000L);
        request.setTid(tid);
        request.setMerchantId(merchantId);
        request.setSubPayWay(4);
        request.setScenesType(QuotaActivityScenesTypeEnum.N7);
        request.setVendorAppAppId("2019071800001840");
        request.setScenesCode("2101018400290772325");
        DiscountQuotaResponse response = discountQuotaService.preDeductDiscountQuota(request);
        if (!response.isUsable()) {
            log.info("没有可以使用的额度包活动");
            return;
        }
        Long quotaId = response.getQuotaRecordId();
        Long beforeUsable = preMap.get(quotaId);
        Map<String, Object> afterInfo = cacheService.getQuotaInfo(quotaId);
        Long afterUsable = MapUtils.getLong(afterInfo, ActivityConstants.REDIS_USABLE);
        Long decr = MapUtils.getLong(afterInfo, tid);
        log.info("额度包扣减. quotaId={},beforeUsable={},decr={},afterUsable={}", quotaId, beforeUsable, decr, afterUsable);
        Assert.assertEquals((beforeUsable - decr), (long) afterUsable);
        updateUsableQuota0(tid, merchantId, quotaId);
    }

    private Map<Long, Long> getUsable(List<DiscountQuotaInfo> preList) {
        Map<Long, Long> result = Maps.newHashMap();
        for (DiscountQuotaInfo info : preList) {
            cacheService.initPreUsable(info.getQuotaId());
            Map<String, Object> use = cacheService.getQuotaInfo(info.getQuotaId());
            result.put(info.getQuotaId(), MapUtils.getLong(use, ActivityConstants.REDIS_USABLE));
        }
        return result;
    }

    public void updateUsableQuota0(String tid, String merchantId, Long quotaId) {
        Map<String, Object> transaction = Maps.newHashMap();
        transaction.put("id", tid);
        transaction.put(Transaction.TSN, "78952test1111111");
        transaction.put(Transaction.MERCHANT_ID, merchantId);
        transaction.put(Transaction.STATUS, 2000);
        transaction.put(Transaction.TYPE, Transaction.TYPE_PAYMENT);
        transaction.put(Transaction.ORIGINAL_AMOUNT, 10000L);
        transaction.put(Transaction.EXTRA_OUT_FIELDS, MapUtil.hashMap(
                "wallet_account_type", 1,
                "combo_id", "4",
                "quota_fee_rate", "0.5",
                "quota_fee_rate_tag", quotaId
        ));
        discountQuotaService.updateDiscountQuotaByTransaction(transaction);
    }

    /**
     * 3. 当日生效、滚动周期、是否自然月滚动
     * <p>
     * 2. 例1：当日生效（当前时间为2025-03-27）、2个月滚动周期、不是自然月滚动
     * 1. 2025-03-27　～　2025-04-26
     * 2. 2025-04-27　～　2025-05-26
     */
    @Test
    public void testRolling0() {
        int rolling = 8;
        QuotaActivityApplyRequest request = new QuotaActivityApplyRequest();
        request.setApplyType(QuotaActivityConstants.ROLL_TIME);
        request.setIsNaturalMonthRolling(false);
        //request.setEffectiveRule(QuotaActivityConstants.EFFECTIVE_RULE_NEXT_DAY);
        request.setRolling(rolling);


        String startTime = LocalDate.now()
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String endTime = LocalDate.now()
                .plusMonths(rolling)
                .minusDays(NumberUtils.LONG_ONE)
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        Assert.assertEquals(startTime, request.fetchStartTime());
        Assert.assertEquals(endTime, request.fetchEndTime());
        printRollingDateList(request);
    }

    /**
     * 2. 次月生效、滚动周期、是否自然月滚动
     * 1. 例1：次月生效（当前时间为2025-03-27）、2个月滚动周期、是自然月滚动(次月生效一定是自然月)
     * 1. 2025-04-1　～　2025-04-30
     * 2. 2025-05-1　～　2025-05-31
     */
    @Test
    public void testRolling1() {
        int rolling = 2;
        QuotaActivityApplyRequest request = new QuotaActivityApplyRequest();
        request.setApplyType(QuotaActivityConstants.ROLL_TIME);
        request.setEffectiveRule(QuotaActivityConstants.EFFECTIVE_RULE_NEXT_MONTH);
        request.setRolling(rolling);


        String startTime = LocalDate.now()
                .with(TemporalAdjusters.firstDayOfMonth())
                .plusMonths(1)
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String endTime = LocalDate.now()
                .plusMonths(rolling)
                .with(TemporalAdjusters.lastDayOfMonth())
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        Assert.assertEquals(startTime, request.fetchStartTime());
        Assert.assertEquals(endTime, request.fetchEndTime());
        printRollingDateList(request);
    }

    /**
     * 3. 次日生效、滚动周期、是否自然月滚动
     * 1. 例1：次日生效（当前时间为2025-03-27）、2个月滚动周期、是自然月滚动
     * 1. 2025-03-28　～　2025-03-31
     * 2. 2025-04-1　～　2025-04-30　（第2个月结束）
     */
    @Test
    public void testRolling2() {
        int rolling = 5;
        QuotaActivityApplyRequest request = new QuotaActivityApplyRequest();
        request.setApplyType(QuotaActivityConstants.ROLL_TIME);
        request.setIsNaturalMonthRolling(true);
        request.setEffectiveRule(QuotaActivityConstants.EFFECTIVE_RULE_NEXT_DAY);
        request.setRolling(rolling);
        String startTime = LocalDate.now().plusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String endTime = LocalDate.now()
                .with(TemporalAdjusters.lastDayOfMonth())
                .plusMonths(rolling - 1)
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        Assert.assertEquals("开始时间", startTime, request.fetchStartTime());
        Assert.assertEquals("结束时间", endTime, request.fetchEndTime());
        printRollingDateList(request);
    }

    /**
     * 3. 次日生效、滚动周期、是否自然月滚动
     * <p>
     * 2. 例1：次日生效（当前时间为2025-03-27）、2个月滚动周期、不是自然月滚动
     * 1. 2025-03-28　～　2025-04-27
     * 2. 2025-04-28　～　2025-05-27
     */
    @Test
    public void testRolling3() {
        int rolling = 8;
        QuotaActivityApplyRequest request = new QuotaActivityApplyRequest();
        request.setApplyType(QuotaActivityConstants.ROLL_TIME);
        request.setIsNaturalMonthRolling(false);
        request.setEffectiveRule(QuotaActivityConstants.EFFECTIVE_RULE_NEXT_DAY);
        request.setRolling(rolling);


        String startTime = LocalDate.now()
                .plusDays(1) //次日生效
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String endTime = LocalDate.now()
                .plusMonths(rolling)
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        Assert.assertEquals(startTime, request.fetchStartTime());
        Assert.assertEquals(endTime, request.fetchEndTime());
        printRollingDateList(request);
    }

    private void printRollingDateList(QuotaActivityApplyRequest request) {
        request.fetchRollingDateList().forEach(pair -> {
            System.out.println(LocalDateTimeUtil.getFormatDateTime(pair.getLeft().atStartOfDay())
                    + "~" + LocalDateTimeUtil.getFormatDateTime(LocalDateTimeUtil.getEndOfDay(pair.getRight().atStartOfDay())));
        });
    }
}

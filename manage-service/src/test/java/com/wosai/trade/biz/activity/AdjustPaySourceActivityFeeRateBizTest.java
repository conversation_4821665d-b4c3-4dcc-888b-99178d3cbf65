package com.wosai.trade.biz.activity;

import com.wosai.databus.event.audit.AuditInstanceApproveEvent;
import com.wosai.trade.BaseTest;
import com.wosai.trade.service.ApplyActivityService;
import com.wosai.trade.service.activity.request.ApplyActivityRequest;
import com.wosai.trade.service.activity.response.ApplyActivityResponse;
import com.wosai.trade.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class AdjustPaySourceActivityFeeRateBizTest extends BaseTest {

    @Resource
    private AdjustPaySourceActivityFeeRateBiz adjustPaySourceActivityFeeRateBiz;
    @Resource
    private ApplyActivityService applyActivityService;


    /**
     * 接口申请
     */
    @Test
    public void testApply001() {
        String merchantSn = "21690003218289";
        ApplyActivityRequest applyRequest = new ApplyActivityRequest();
        applyRequest.setMerchantSn(merchantSn);
        applyRequest.setActivityId(121L);
        applyRequest.setOperator("支付源活动报名");
        applyRequest.setSubMerchantSn("20712112424");
        applyRequest.setSubStatusDetailId(6188L);
        ApplyActivityResponse apply = applyActivityService.apply(applyRequest);
        log.info("富友-报名 end");
        Assert.assertTrue("活动申请失败", Objects.nonNull(apply) && apply.isSuccess());
    }

    @Test
    public void takeEffect() {
        applyActivityService.takeEffect(98159414847L);
    }

    @Test
    public void verifyAuditParam() {
        Map event = JsonUtil.decode(eventStr, Map.class);
        adjustPaySourceActivityFeeRateBiz.verifyAuditParam(MapUtils.getMap(event, "businessMap"));
    }

    @Test
    public void execute() {
        AuditInstanceApproveEvent event = JsonUtil.decode(eventStr, AuditInstanceApproveEvent.class);
        adjustPaySourceActivityFeeRateBiz.execute(event);
    }

    final String eventStr = "{\n" +
            "    \"auditId\": \"498407\",\n" +
            "    \"auditSn\": \"SP42420241128000001\",\n" +
            "    \"formId\": \"2803808\",\n" +
            "    \"finishTime\": 1732784935050,\n" +
            "    \"operatorPlatform\": \"CRM\",\n" +
            "    \"module\": \"CRM\",\n" +
            "    \"auditTemplateId\": \"208349\",\n" +
            "    \"operatorOrgNamePath\": \"直营\",\n" +
            "    \"eventType\": \"AUDIT_APPROVE\",\n" +
            "    \"templateId\": \"340565\",\n" +
            "    \"operatorName\": \"核心业务CRM账号\",\n" +
            "    \"objectType\": \"AUDIT\",\n" +
            "    \"auditCommentProperty\": {\n" +
            "        \"comment\": \"zzz\",\n" +
            "        \"id\": \"65561667\",\n" +
            "        \"operatorName\": \"核心业务CRM账号\",\n" +
            "        \"operator\": \"3685adda-0c78-4236-b6f0-cbc2477b6789\",\n" +
            "        \"platform\": \"CRM\"\n" +
            "    },\n" +
            "    \"operatorOrgCode\": \"00003\",\n" +
            "    \"bizKey\": \"CRM_94cf6b59-42a5-4539-8295-ad5718a072a4\",\n" +
            "    \"templateEvent\": \"modify_pay_source_fee_rate\",\n" +
            "    \"ctime\": 1732784772000,\n" +
            "    \"auditCallBackProperty\": {\n" +
            "        \"resultFail\": 0\n" +
            "    },\n" +
            "    \"businessMap\": {\n" +
            "        \"combo_info\": [\n" +
            "            {\n" +
            "                \"ladder:0-30\": 0.3,\n" +
            "                \"combo_id\": \"16658\",\n" +
            "                \"feeRate\": \"0.3\",\n" +
            "                \"ladder:30-2147483647\": 0.26\n" +
            "            }\n" +
            "        ],\n" +
            "        \"activity_info\": \"121\",\n" +
            "        \"merchant\": {\n" +
            "            \"merch_info.sn\": {\n" +
            "                \"keyName\": \"商户号\",\n" +
            "                \"value\": \"21690003218289\"\n" +
            "            },\n" +
            "            \"extra.weixin_school_sn\": {\n" +
            "                \"keyName\": \"微信校园商户号\"\n" +
            "            },\n" +
            "            \"merch_info.name\": {\n" +
            "                \"keyName\": \"商户名称\",\n" +
            "                \"value\": \"人脸识别小店\"\n" +
            "            }\n" +
            "        },\n" +
            "        \"commentdetails\": {\n" +
            "            \"comment\": \"zzz\",\n" +
            "            \"id\": \"65561667\",\n" +
            "            \"operatorName\": \"核心业务CRM账号\",\n" +
            "            \"operator\": \"3685adda-0c78-4236-b6f0-cbc2477b6789\",\n" +
            "            \"platform\": \"CRM\"\n" +
            "        },\n" +
            "        \"audit_info\": {\n" +
            "            \"id\": \"498407\",\n" +
            "            \"sn\": \"SP42420241128000001\",\n" +
            "            \"operator\": \"3685adda-0c78-4236-b6f0-cbc2477b6789\",\n" +
            "            \"platform\": \"CRM\",\n" +
            "            \"comment\": \"zzz\",\n" +
            "            \"status\": \"2\",\n" +
            "            \"time\": 1732784935000,\n" +
            "            \"template_id\": 208349,\n" +
            "            \"template_name\": \"微信间连高校活动优惠费率申请\"\n" +
            "        }\n" +
            "    },\n" +
            "    \"operatorId\": \"3685adda-0c78-4236-b6f0-cbc2477b6789\",\n" +
            "    \"seq\": 0,\n" +
            "    \"timestamp\": 1732784935086\n" +
            "}";
}
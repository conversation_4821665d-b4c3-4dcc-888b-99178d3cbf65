package com.wosai.trade.biz.activity;

import com.wosai.pantheon.util.MapUtil;
import com.wosai.trade.BaseTest;
import com.wosai.trade.biz.activity.quota.DiscountQuotaBiz;
import com.wosai.trade.service.SwitchService;
import com.wosai.upay.core.constant.CoreCommonConstants;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.TransactionParam;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

public class DiscountQuotaBizTest extends BaseTest {
    @Autowired
    private DiscountQuotaBiz discountQuotaBiz;
    @Autowired
    private SwitchService switchService;

    @Test
    public void testUsableQuotaLEZero() {
        discountQuotaBiz.usableQuotaLEZero(10206L);
    }


    @Test
    public void testUsableQuotaLEZero001() {
        discountQuotaBiz.usableQuotaLEZero(10531L);


    }

    @Test
    public void testSwitch(){
        //活动生效
        Map<String, Object> request = MapUtil.hashMap(MerchantConfig.MERCHANT_ID, "6526bbc2-2c0c-426c-a51e-26893c10665a",
                CoreCommonConstants.KEY_COMMON_SWITCH_TYPE, TransactionParam.TYPE_COMMON_SWITCH_QUOTA_APPLY_ACTIVITY);
        switchService.openCommonSwitchRemoveCache(request);
    }
}
package com.wosai.trade.biz.businesslog;

import com.fasterxml.jackson.core.type.TypeReference;
import com.wosai.business.log.model.BizOpLog;
import com.wosai.data.util.CollectionUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.shouqianba.withdrawservice.model.MerchantWithdrawConfig;
import com.wosai.trade.BaseTest;
import com.wosai.trade.biz.audit.AuditBiz;
import com.wosai.trade.impl.SendMerchantInfoService;
import com.wosai.trade.model.biz.ServiceFeeEffectiveBusinessParam;
import com.wosai.trade.util.ConstantUtil;
import com.wosai.trade.util.JsonUtil;
import com.wosai.upay.core.service.BusinssCommonService;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Before;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Map;

import static com.wosai.trade.impl.SendMerchantInfoService.*;

public class BusinessOpLogConstantBizTest extends BaseTest {

    @Resource
    private BusinessOpLogBiz businessOpLogBiz;
    @Resource
    private BusinssCommonService businssCommonService;

    public static final String MERCHANT_SN = "21690003215621";

    private String merchantId;

    @Before
    public void before() {
        merchantId = businssCommonService.getMerchantIdBySn(MERCHANT_SN);
    }

    @Test
    public void sendFeeRateMerchantConfigBusinessLog() {
        System.out.println(merchantId);

        Map<String, Object> before = MapUtil.hashMap(
                "b2c_fee_rate", "0.1",
                "mini_fee_rate", "0.1",
                "payway", "2"
        );

        Map<String, Object> after = MapUtil.hashMap(
                "b2c_fee_rate", "0.1",
                "mini_fee_rate", "0.1",
                "payway", "2",
                "b2c_status", "1",
                "c2b_status", "0",
                "mini_status", "0",
                "wap_status", "0",
                "c2b_fee_rate", "0",
                "wap_fee_rate", "0",
                "params", MapUtil.hashMap(
                        "alipay_v2_trade_params", MapUtil.hashMap(
                                "auth_app_id", "2018070760511637",
                                "mch_id", "2088821610439660",
                                "app_auth_token", "201903BB473e028a8b6b4239901fcc858fb32X66"
                        ),
                        "deposit", MapUtil.hashMap("alipay", "1", "weixin", "1"),
                        "ladder_fee_rates", Arrays.asList(
                                MapUtil.hashMap("b2c_fee_rate", "0.25",
                                        "c2b_fee_rate", "0.25",
                                        "max", 300.0,
                                        "min", 0.0,
                                        "mini_fee_rate", "0.25",
                                        "wap_fee_rate", "0.25"
                                ),
                                MapUtil.hashMap("b2c_fee_rate", "0.38",
                                        "c2b_fee_rate", "0.38",
                                        "max", null,
                                        "min", 300.0,
                                        "mini_fee_rate", "0.38",
                                        "wap_fee_rate", "0.38"
                                )
                        ),
                        "bankcard_fee", JsonUtil.decode("{\"credit\":{\"fee\":\"0.37\"},\"others\":{\"fee\":\"0.25\"}}", Map.class),
                        "ladder_status", "1",
                        "history_trade_refund_flag", 1
                )
        );
        businessOpLogBiz.sendFeeRateMerchantConfigBusinessLog(merchantId, "SPA", merchantId, "system", "test", before, after);
    }

    @Test
    public void sendExchangeBusinessLog() {
        Map<String, Object> before = MapUtil.hashMap("payway", "支付宝",
                "b2c_fee_rate", "0.2",
                "mini_fee_rate", "0.3",
                "c2b_fee_rate", "0.3",
                "wap_fee_rate", "0.3"
        );
        Map<String, Object> after = MapUtil.hashMap("payway", "支付宝",
                "b2c_fee_rate", "0.21",
                "mini_fee_rate", "0.1",
                "c2b_fee_rate", "0.35",
                "wap_fee_rate", "0.32"
        );
        businessOpLogBiz.sendExchangeBusinessLog(merchantId, "SPA", merchantId, "SYSTEM", before, after);
    }

    @Test
    public void sendSettingsUnionLadderFeeRatesBusinessLog() {
        Map before = CollectionUtil.hashMap("union_pay_ladder_apply", "-");
        Map after = CollectionUtil.hashMap("union_pay_ladder_apply", "通过");
        businessOpLogBiz.sendSettingsUnionLadderFeeRatesBusinessLog(merchantId, merchantId,"交易管理系统", "云闪付单笔1000元以上交易权限关闭", before, after);
    }

    @Test
    public void sendD0WithdrawHandleBusinessLog() {
        Map before = CollectionUtil.hashMap("status", AuditBiz.OPEN);
        Map after = CollectionUtil.hashMap("status", AuditBiz.CLOSE);
        businessOpLogBiz.sendD0WithdrawHandleBusinessLog(merchantId, merchantId, "SYSTEM", before, after);
    }

    @Test
    public void sendSaasBusinessLog() {
        ServiceFeeEffectiveBusinessParam before = ServiceFeeEffectiveBusinessParam.builder()
                .feeId(StringUtils.EMPTY)
                .chargeAmount(StringUtils.EMPTY)
                .profitShareRatio(StringUtils.EMPTY)
                .commission(StringUtils.EMPTY)
                .minChargeAmount(StringUtils.EMPTY)
                .tradeAppName(StringUtils.EMPTY)
                .build();
        ServiceFeeEffectiveBusinessParam after = ServiceFeeEffectiveBusinessParam.builder()
                .feeId("123")
                .chargeAmount("11.22")
                .profitShareRatio("11.2")
                .commission("21")
                .minChargeAmount("222")
                .tradeAppName("22")
                .build();
        //日志中心，remark最大长度为200
        String remark = "saas测试";
        businessOpLogBiz.sendSaasBusinessLog(merchantId, "SPA", merchantId, "SYSTEM", remark,
                JsonUtil.decode(JsonUtil.encode(before), new TypeReference<Map<String, Object>>() {
                }),
                JsonUtil.decode(JsonUtil.encode(after), new TypeReference<Map<String, Object>>() {
                })
        );
    }

    @Test
    public void sendByTemplateCodeBusinessLog() {
        /*// 提现留存金额设置
        WITHDRAW_RETAINED_AMOUNT_WITHDRAW("4FNNGCS75WWT", "merchant_withdraw_config", Collections.singletonList("retained_amount")),
                // 移出普惠考核普惠结束
                CLOSE_PUHUI_AUDIT("EDIL6SGDQSGU", "merchant", Collections.singletonList("content")),
                // 关闭信用卡/花呗支付方式
                CLOSE_CREDIT_LIMIT_PAY("V0TAUPUE2L4W", "credit_limit", Collections.singletonList("credit_pay_status")),
                // 关闭微信信用卡支付方式
                CLOSE_CREDIT_LIMIT_WECHAT("8R1M1CPKWEBG", "credit_limit", Collections.singletonList("wechat")),
                // 关闭支付宝信用卡/花呗支付方式
                CLOSE_CREDIT_LIMIT_ALIPAY("H2UTT81726UO", "credit_limit", Collections.singletonList("alipay")),*/

        Map before = CollectionUtil.hashMap(MerchantWithdrawConfig.RETAINED_AMOUNT, 2002);
        Map after = CollectionUtil.hashMap(MerchantWithdrawConfig.RETAINED_AMOUNT, 2003);
        businessOpLogBiz.sendByTemplateCodeBusinessLog(TemplateCodeEnum.WITHDRAW_RETAINED_AMOUNT_WITHDRAW, merchantId, ConstantUtil.SYSTEM_NAME, ConstantUtil.SYSTEM_NAME, "商户自动结算留存金额配置，单位元", before, after);

        businessOpLogBiz.sendByTemplateCodeBusinessLog(TemplateCodeEnum.CLOSE_PUHUI_AUDIT, merchantId, ConstantUtil.SYSTEM_NAME, ConstantUtil.SYSTEM_NAME,
                "商户结束普惠: " + merchantId, CollectionUtil.hashMap("content", ""), CollectionUtil.hashMap("content", "无"));

        String remark = "信用卡/花呗交易已达额度上限，完成营业执照认证可提升信用额度";
        businessOpLogBiz.sendByTemplateCodeBusinessLog(TemplateCodeEnum.CLOSE_CREDIT_LIMIT_PAY, merchantId, ConstantUtil.SYSTEM_NAME, ConstantUtil.SYSTEM_NAME, remark, SendMerchantInfoService.CREDIT_PAY_STATUS_BEFORE, SendMerchantInfoService.CREDIT_PAY_STATUS_AFTER);

        businessOpLogBiz.sendByTemplateCodeBusinessLog(TemplateCodeEnum.CLOSE_CREDIT_LIMIT_WECHAT, merchantId, ConstantUtil.SYSTEM_NAME, ConstantUtil.SYSTEM_NAME, "微信信用卡单日交易已达额度上限，引导使用其他支付方式", WECHANT_CREDIT_PAY_STATUS_BEFORE, WECHANT_CREDIT_PAY_STATUS_AFTER);

        businessOpLogBiz.sendByTemplateCodeBusinessLog(TemplateCodeEnum.CLOSE_CREDIT_LIMIT_ALIPAY, merchantId, ConstantUtil.SYSTEM_NAME, ConstantUtil.SYSTEM_NAME, "支付宝信用卡/花呗单日交易已达额度上限，引导使用其他支付方式", ALIPAY_CREDIT_PAY_STATUS_BEFORE, ALIPAY_CREDIT_PAY_STATUS_AFTER);
    }
    @Test
    public void sendMerchantBusinessLogTest() {
        Map MERCHANT_PAY_STATUS_BEFORE = CollectionUtil.hashMap(TemplateCodeEnum.OPEN_MERCHANT_PAY.getFiledNameList().get(0), "关闭");
        Map MERCHANT_PAY_STATUS_AFTER = CollectionUtil.hashMap(TemplateCodeEnum.OPEN_MERCHANT_PAY.getFiledNameList().get(0), "开启");
        businessOpLogBiz.sendOpenMerchantPayBusinessLog("1e12d53ac54c-8389-46f4-feac-ad8480af", "SPA", ConstantUtil.SYSTEM_NAME, ConstantUtil.SYSTEM_NAME, "审批编号: " + " ",
                MERCHANT_PAY_STATUS_BEFORE, MERCHANT_PAY_STATUS_AFTER);
    }
    @Test
    public void sendByPassSummaryQuerryTest(){
        Map<String, Object> before = CollectionUtil.hashMap(TemplateCodeEnum.BYPASS_SUMMARY_QUERY.getFiledNameList().get(0), "拉卡拉");
        Map<String, Object> after = CollectionUtil.hashMap(TemplateCodeEnum.BYPASS_SUMMARY_QUERY.getFiledNameList().get(0), "通联");
        businessOpLogBiz.sendMerchantConfigBypassSummaryQueryLog("1e12d53ac54c-8389-46f4-feac-ad8480af", "SPA", ConstantUtil.SYSTEM_NAME, ConstantUtil.SYSTEM_NAME, "通道变更: " + " ",
                before, after);
    }

    @Test
    public void sendBankCooperationD0BusinessLogTest(){
        Map<String, Object> before = CollectionUtil.hashMap(TemplateCodeEnum.D0_CONFIIG.getFiledNameList().get(0), "");
        Map<String, Object> after = CollectionUtil.hashMap(TemplateCodeEnum.D0_CONFIIG.getFiledNameList().get(0), "open".equals("open") ? "开启" : "关闭");
        businessOpLogBiz.sendBankCooperationD0BusinessLog("1e12d53ac54c-8389-46f4-feac-ad8480af", "SPA", "system",
                "system", "迁移阶段测试", before, after);
    }
}
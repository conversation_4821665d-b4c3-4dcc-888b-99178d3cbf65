package com.wosai.trade.biz.fee;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.wosai.trade.biz.activity.ModifyApplyActivityBiz;
import com.wosai.trade.biz.fee.model.ProviderFeeRateSyncResponse;
import com.wosai.trade.client.LarkClient;
import com.wosai.trade.config.apollo.CommonApolloConfig;
import com.wosai.trade.model.dal.ApplyExtraParam;
import com.wosai.trade.model.enums.PayWayEnum;
import com.wosai.trade.repository.ActivityApplyRepository;
import com.wosai.trade.repository.dao.TradeAppDao;
import com.wosai.trade.repository.dao.entity.ActivityApplyEntity;
import com.wosai.trade.repository.dao.entity.ActivityEntity;
import com.wosai.trade.repository.dao.entity.TradeAppEntity;
import com.wosai.trade.repository.dao.entity.ext.ActivityApplyProviderSyncExtra;
import com.wosai.trade.service.CrmService;
import com.wosai.trade.service.activity.request.ApplyActivityRequest;
import com.wosai.trade.service.enums.FeeRateTypeEnum;
import com.wosai.trade.util.JsonUtil;
import com.wosai.trade.util.SpringBeanUtil;
import com.wosai.upay.core.bean.response.AllMerchantConfigResponse;
import com.wosai.upay.core.meta.Provider;
import com.wosai.upay.core.model.MerchantAppConfig;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.ProviderAbility;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.service.AcquirerService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.api.support.membermodification.MemberModifier;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.*;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @description
 * @date 2024-04-15
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest(SpringBeanUtil.class)
public class ProviderFeeRateSyncBizTest {
    @InjectMocks
    private ProviderFeeRateSyncBiz providerFeeRateSyncBiz;

    @Mock
    private CommonApolloConfig commonApolloConfig;

    @Mock
    private TradeConfigService tradeConfigService;
    @Mock
    private LarkClient larkClient;

    @Mock
    private AcquirerService acquirerService;
    @Mock
    private TradeAppDao tradeAppDao;
    @Mock
    private ActivityApplyRepository activityApplyRepository;
    @Mock
    private ModifyApplyActivityBiz modifyApplyActivityBiz;

    @Mock
    private CrmService crmService;

    //实时生效的审批模板id
    private static final String IMMEDIATE_TEMPLATE_ID = "111";
    //延迟生效的审批模板id
    private static final String DELAY_TEMPLATE_ID = "46301";

    public static final String MERCHANT_SN = "test_merchant_sn";

    //基础业务
    private static final long BASE_APP = 1;
    //非基础业务，5是会员储值
    private static final long NON_BASE_APP = 5;

    @Before
    public void setUp() {
        PowerMockito.mockStatic(SpringBeanUtil.class);
    }

    @Test
    public void setDelaySyncFeeRateToProvider() throws Exception {
        when(commonApolloConfig.getDelayTakeAffectAndSendMessageTemplateIds()).thenReturn(Collections.singletonList(DELAY_TEMPLATE_ID));
        ActivityEntity activityEntity = new ActivityEntity();

        /**
         * 基础业务场景，商户报名了基础业务活动
         * 活动支持的收单机构中，不包含需要实时同步费率的收单机构
         * =============================================
         */
        activityEntity.setBiz_id(BASE_APP);
        activityEntity.setAcquirer(JsonUtil.encode(Collections.singletonList(AcquirerTypeEnum.LKL.getValue())));
        //实时生效模板
        ApplyActivityRequest applyActivityRequest = buildActivityRequest();
        applyActivityRequest.setAuditTemplateId(IMMEDIATE_TEMPLATE_ID);
        providerFeeRateSyncBiz.setDelaySyncFeeRateToProvider(activityEntity, applyActivityRequest);
        assertFalse(providerFeeRateSyncBiz.isDelaySyncFeeRateToProvider(IMMEDIATE_TEMPLATE_ID));

        //延迟生效模板
        applyActivityRequest.setAuditTemplateId(DELAY_TEMPLATE_ID);
        providerFeeRateSyncBiz.setDelaySyncFeeRateToProvider(activityEntity, applyActivityRequest);
        assertTrue(providerFeeRateSyncBiz.isDelaySyncFeeRateToProvider(DELAY_TEMPLATE_ID));

        /**
         * 活动支持的收单机构中，包含需要实时同步费率的收单机构
         * case 1: 基础业务的清算通道不需要实时同步费率
         * ===========================================
         */
        activityEntity.setAcquirer(JsonUtil.encode(Collections.singletonList(AcquirerTypeEnum.FU_YOU.getValue())));
        //实时生效模板，基础业务的清算通道不需要实时同步费率
        applyActivityRequest.setAuditTemplateId(IMMEDIATE_TEMPLATE_ID);
        when(tradeConfigService.queryAllMerchantConfigs(anyString())).thenReturn(buildNoMatchedBaseMerchantConfig());
        providerFeeRateSyncBiz.setDelaySyncFeeRateToProvider(activityEntity, applyActivityRequest);
        assertFalse(providerFeeRateSyncBiz.isDelaySyncFeeRateToProvider(IMMEDIATE_TEMPLATE_ID));

        //延迟生效模板，基础业务的清算通道不需要实时同步费率
        applyActivityRequest.setAuditTemplateId(DELAY_TEMPLATE_ID);
        when(tradeConfigService.queryAllMerchantConfigs(anyString())).thenReturn(buildNoMatchedBaseMerchantConfig());
        providerFeeRateSyncBiz.setDelaySyncFeeRateToProvider(activityEntity, applyActivityRequest);
        assertTrue(providerFeeRateSyncBiz.isDelaySyncFeeRateToProvider(DELAY_TEMPLATE_ID));

        /**
         * 活动支持的收单机构中，包含需要实时同步费率的收单机构
         * case 2: 基础业务的清算通道需要实时同步费率
         * ===========================================
         */
        //实时生效模板，基础业务的清算通道需要实时同步费率
        applyActivityRequest.setAuditTemplateId(IMMEDIATE_TEMPLATE_ID);
        when(tradeConfigService.queryAllMerchantConfigs(anyString())).thenReturn(buildMatchedBaseMerchantConfig());
        providerFeeRateSyncBiz.setDelaySyncFeeRateToProvider(activityEntity, applyActivityRequest);
        assertFalse(providerFeeRateSyncBiz.isDelaySyncFeeRateToProvider(IMMEDIATE_TEMPLATE_ID));

        //延迟生效模板，基础业务的清算通道需要实时同步费率
        applyActivityRequest.setAuditTemplateId(DELAY_TEMPLATE_ID);
        when(tradeConfigService.queryAllMerchantConfigs(anyString())).thenReturn(buildMatchedBaseMerchantConfig());
        providerFeeRateSyncBiz.setDelaySyncFeeRateToProvider(activityEntity, applyActivityRequest);
        assertFalse(providerFeeRateSyncBiz.isDelaySyncFeeRateToProvider(DELAY_TEMPLATE_ID));

        //延迟生效模板，基础业务的清算通道需要实时同步费率(含多业务配置)
        applyActivityRequest.setAuditTemplateId(DELAY_TEMPLATE_ID);
        when(tradeConfigService.queryAllMerchantConfigs(anyString())).thenReturn(buildMatchedBaseMerchantConfigAndAppMerchantConfig());
        providerFeeRateSyncBiz.setDelaySyncFeeRateToProvider(activityEntity, applyActivityRequest);
        assertFalse(providerFeeRateSyncBiz.isDelaySyncFeeRateToProvider(DELAY_TEMPLATE_ID));

        /**
         * 多业务场景, 商户报名了多业务活动
         */
        activityEntity.setBiz_id(NON_BASE_APP);
        //延迟生效模板，多业务的清算通道不需要实时同步费率
        applyActivityRequest.setAuditTemplateId(DELAY_TEMPLATE_ID);
        when(tradeConfigService.queryAllMerchantConfigs(anyString())).thenReturn(buildMatchedBaseMerchantConfigAndNotMatchAppMerchantConfig());
        providerFeeRateSyncBiz.setDelaySyncFeeRateToProvider(activityEntity, applyActivityRequest);
        assertTrue(providerFeeRateSyncBiz.isDelaySyncFeeRateToProvider(DELAY_TEMPLATE_ID));

        //延迟生效模板，多业务的清算通道需要实时同步费率
        applyActivityRequest.setAuditTemplateId(DELAY_TEMPLATE_ID);
        when(tradeConfigService.queryAllMerchantConfigs(anyString())).thenReturn(buildMatchedBaseMerchantConfigAndAppMerchantConfig());
        providerFeeRateSyncBiz.setDelaySyncFeeRateToProvider(activityEntity, applyActivityRequest);
        assertFalse(providerFeeRateSyncBiz.isDelaySyncFeeRateToProvider(DELAY_TEMPLATE_ID));

        //延迟生效模板，商户含有多个b2c费率
        PowerMockito.doNothing().when(larkClient).sendMsg(anyString(), anyString(), anyString());
        when(tradeConfigService.queryAllMerchantConfigs(anyString())).thenReturn(buildMultiFeeRate());
        when(SpringBeanUtil.getEnvName()).thenReturn("测试环境");
        //给私有变量赋默认值
        MemberModifier.field(ProviderFeeRateSyncBiz.class, "feeRateSyncDingTalkUrl").set(providerFeeRateSyncBiz, "https://oapi.dingtalk.com/robot/send?access_token=26fa9cf50f");
        providerFeeRateSyncBiz.setDelaySyncFeeRateToProvider(activityEntity, applyActivityRequest);
        assertFalse(providerFeeRateSyncBiz.isDelaySyncFeeRateToProvider(DELAY_TEMPLATE_ID));
        //校验larkClient的sendMsg方法是否被调用过一次
        PowerMockito.verifyPrivate(larkClient, Mockito.times(1)).invoke("sendMsg", anyString(), anyString(), anyString());
    }


    @Test
    public void setDelaySyncFeeRateToProviderFuyou() {
        when(commonApolloConfig.getDelayTakeAffectAndSendMessageTemplateIds()).thenReturn(Collections.singletonList(DELAY_TEMPLATE_ID));
        when(acquirerService.getMerchantAcquirer(anyString())).thenReturn(AcquirerTypeEnum.TONG_LIAN_V2.getValue());
        when(tradeAppDao.queryTradeAppById(anyLong())).thenReturn(buildTradeAppEntity(1L, false));
        when(tradeConfigService.queryAllMerchantConfigs(anyString())).thenReturn(buildNoMatchedBaseMerchantConfig(TransactionParam.CLEARANCE_PROVIDER_SYB, Provider.TL_SYB.getCode()));
        doNothing().when(crmService).notifyFeeRateChange(anyLong());

        ActivityEntity activityEntity = new ActivityEntity();
        activityEntity.setBiz_id(BASE_APP);
        activityEntity.setAcquirer(JsonUtil.encode(Collections.singletonList(AcquirerTypeEnum.FU_YOU.getValue())));
//        /**
//         * 收银宝设置标识符
//         */
//        //实时生效模板
//        ApplyActivityRequest applyActivityRequest = buildActivityRequest();
//        applyActivityRequest.setAuditTemplateId(IMMEDIATE_TEMPLATE_ID);
//        providerFeeRateSyncBiz.setDelaySyncFeeRateToProvider(activityEntity, applyActivityRequest);
//        assertTrue(providerFeeRateSyncBiz.feeRateSyncFinishNotifyMerchantFeeRateChange());
//
//        /**
//         * 固定费率同步完成处理
//         */
//        applyActivityRequest = buildActivityRequest();
//        applyActivityRequest.setAuditTemplateId(IMMEDIATE_TEMPLATE_ID);
//        providerFeeRateSyncBiz.setDelaySyncFeeRateToProvider(activityEntity, applyActivityRequest);
//        assertTrue(providerFeeRateSyncBiz.feeRateSyncFinishNotifyMerchantFeeRateChange());
//        when(activityApplyRepository.getEffectIngApplyBySn(anyString())).thenReturn(buildFixedActivityApplyEntityList());
//        providerFeeRateSyncBiz.providerSyncFinish(buildFixedProviderFeeRateSyncResponse());
//        //给私有变量赋默认值
//        MemberModifier.field(ProviderFeeRateSyncBiz.class, "feeRateSyncDingTalkUrl").set(providerFeeRateSyncBiz, "https://oapi.dingtalk.com/robot/send?access_token=26fa9cf50f");
//        //校验dingTalkClient的sendMsg方法是否被调用过一次
//        //PowerMockito.verifyPrivate(dingTalkClient, Mockito.times(1)).invoke("sendMsg", anyString(), anyString(), anyString());

        /**
         * 阶梯费率同步完成处理
         */
        ApplyActivityRequest applyActivityRequest = buildActivityRequest();
        applyActivityRequest.setAuditTemplateId(IMMEDIATE_TEMPLATE_ID);
        providerFeeRateSyncBiz.setDelaySyncFeeRateToProvider(activityEntity, applyActivityRequest);
        assertTrue(providerFeeRateSyncBiz.feeRateSyncFinishNotifyMerchantFeeRateChange());



        when(activityApplyRepository.getEffectIngApplyBySn(anyString())).thenReturn(buildLadderActivityApplyEntityList());
        providerFeeRateSyncBiz.providerSyncFinish(buildLadderProviderFeeRateSyncResponse());

        //PowerMockito.verifyPrivate()



    }


    private List<ActivityApplyEntity> buildFixedActivityApplyEntityList() {
        List<ActivityApplyEntity> activityApplyEntityList = new ArrayList<>();
        ActivityApplyProviderSyncExtra activityApplyProviderSyncExtra = new ActivityApplyProviderSyncExtra();
        activityApplyProviderSyncExtra.setNotifyCompleted(false);
        activityApplyProviderSyncExtra.setCrmFeeRateChangeId(1L);
        activityApplyProviderSyncExtra.setFeeRateSyncFinishNotify(true);
        ApplyExtraParam applyExtraParam = new ApplyExtraParam();
        applyExtraParam.setProviderSyncExtra(activityApplyProviderSyncExtra);

        ActivityApplyEntity activityApplyEntity = new ActivityApplyEntity();
        activityApplyEntity.setBiz_id(BASE_APP);
        activityApplyEntity.setMerchant_sn(MERCHANT_SN);
        activityApplyEntity.setStatus(4);
        activityApplyEntity.setFee_rate(JsonUtil.encode(ImmutableMap.of("3", "0.2")));
        activityApplyEntity.setExtra(JsonUtil.encode(applyExtraParam));
        activityApplyEntityList.add(activityApplyEntity);
        return activityApplyEntityList;
    }

    private ProviderFeeRateSyncResponse buildFixedProviderFeeRateSyncResponse() {
        ProviderFeeRateSyncResponse response = new ProviderFeeRateSyncResponse();
        response.setProvider(Provider.FUYOU.getCode());
        response.setResult(true);
        response.setMerchantSn(MERCHANT_SN);
        ProviderFeeRateSyncResponse.FeeRateResult feeRateResult = new ProviderFeeRateSyncResponse.FeeRateResult();
        feeRateResult.setB2cFeeRate("0.1");
        feeRateResult.setFeeRateType(FeeRateTypeEnum.FIXED.name());
        feeRateResult.setPayway(PayWayEnum.WEIXIN.getCode());
        response.setFeeRateList(ImmutableList.of(feeRateResult));
        return response;
    }

    private List<ActivityApplyEntity> buildLadderActivityApplyEntityList() {
        List<ActivityApplyEntity> activityApplyEntityList = new ArrayList<>();
        ActivityApplyProviderSyncExtra activityApplyProviderSyncExtra = new ActivityApplyProviderSyncExtra();
        activityApplyProviderSyncExtra.setNotifyCompleted(false);
        activityApplyProviderSyncExtra.setCrmFeeRateChangeId(1L);
        activityApplyProviderSyncExtra.setFeeRateSyncFinishNotify(true);
        ApplyExtraParam applyExtraParam = new ApplyExtraParam();
        applyExtraParam.setProviderSyncExtra(activityApplyProviderSyncExtra);

        ActivityApplyEntity activityApplyEntity = new ActivityApplyEntity();
        activityApplyEntity.setId(111L);
        activityApplyEntity.setBiz_id(BASE_APP);
        activityApplyEntity.setMerchant_sn(MERCHANT_SN);
        activityApplyEntity.setStatus(4);
        activityApplyEntity.setFee_rate(JsonUtil.encode(ImmutableMap.of("3", "{\"value\":[{\"min\":0.0,\"max\":30.0,\"fee_rate\":\"0.2\"},{\"min\":30.0,\"fee_rate\":\"0.38\"}],\"fee_rate_type\":\"ladder\"}")));
        activityApplyEntity.setExtra(JsonUtil.encode(applyExtraParam));
        activityApplyEntityList.add(activityApplyEntity);
        return activityApplyEntityList;
    }

    private ProviderFeeRateSyncResponse buildLadderProviderFeeRateSyncResponse() {
        ProviderFeeRateSyncResponse response = new ProviderFeeRateSyncResponse();
        response.setProvider(Provider.FUYOU.getCode());
        response.setResult(true);
        response.setMerchantSn(MERCHANT_SN);
        ProviderFeeRateSyncResponse.FeeRateResult feeRateResult = new ProviderFeeRateSyncResponse.FeeRateResult();
        feeRateResult.setFeeRateType(FeeRateTypeEnum.LADDER.name());
        feeRateResult.setPayway(PayWayEnum.WEIXIN.getCode());

        List<ProviderFeeRateSyncResponse.FeeRateResult.LadderFeeRatesDTO> ladderFeeRates = new ArrayList<>();
        feeRateResult.setLadderFeeRates(ladderFeeRates);
        ProviderFeeRateSyncResponse.FeeRateResult.LadderFeeRatesDTO ladderFeeRate = new ProviderFeeRateSyncResponse.FeeRateResult.LadderFeeRatesDTO();
        ladderFeeRate.setMin(0.0);
        ladderFeeRate.setMax(30.0);
        ladderFeeRate.setB2cFeeRate("0.2");
        ladderFeeRates.add(ladderFeeRate);
        ProviderFeeRateSyncResponse.FeeRateResult.LadderFeeRatesDTO ladderFeeRate2 = new ProviderFeeRateSyncResponse.FeeRateResult.LadderFeeRatesDTO();
        ladderFeeRate2.setMin(30.0);
        ladderFeeRate2.setMax(null);
        ladderFeeRate2.setB2cFeeRate("0.38");
        ladderFeeRates.add(ladderFeeRate2);
        response.setFeeRateList(ImmutableList.of(feeRateResult));
        return response;
    }

    public static void main(String[] args) {
        String feeRateStr = "{\"provider\": \"1032\", \"result\": true, \"merchantSn\": \"21690003254043\", \"feeRate\": \"[{\\\"c2b_fee_rate\\\":\\\"0.38\\\",\\\"payway\\\":2,\\\"b2c_fee_rate\\\":\\\"0.38\\\",\\\"wap_fee_rate\\\":\\\"0.38\\\",\\\"fee_rate_type\\\":\\\"fixed\\\"},{\\\"c2b_fee_rate\\\":\\\"0.38\\\",\\\"payway\\\":3,\\\"b2c_fee_rate\\\":\\\"0.38\\\",\\\"wap_fee_rate\\\":\\\"0.38\\\",\\\"fee_rate_type\\\":\\\"fixed\\\"},{\\\"ladder_fee_rates\\\":[{\\\"c2b_fee_rate\\\":\\\"0.25\\\",\\\"min\\\":0.0,\\\"max\\\":1000.0,\\\"mini_fee_rate\\\":\\\"0.25\\\",\\\"b2c_fee_rate\\\":\\\"0.25\\\",\\\"wap_fee_rate\\\":\\\"0.25\\\"},{\\\"c2b_fee_rate\\\":\\\"0.38\\\",\\\"min\\\":1000.0,\\\"mini_fee_rate\\\":\\\"0.38\\\",\\\"b2c_fee_rate\\\":\\\"0.38\\\",\\\"wap_fee_rate\\\":\\\"0.38\\\"}],\\\"c2b_fee_rate\\\":\\\"0.3\\\",\\\"payway\\\":17,\\\"b2c_fee_rate\\\":\\\"0.3\\\",\\\"wap_fee_rate\\\":\\\"0.3\\\",\\\"fee_rate_type\\\":\\\"ladder\\\"},{\\\"ladder_status\\\":2,\\\"c2b_fee_rate\\\":\\\"0.3\\\",\\\"payway\\\":18,\\\"b2c_fee_rate\\\":\\\"0.3\\\",\\\"wap_fee_rate\\\":\\\"0.3\\\"}]\"}";
        ProviderFeeRateSyncResponse response = JsonUtil.decode(feeRateStr, ProviderFeeRateSyncResponse.class);
        List<ProviderFeeRateSyncResponse.FeeRateResult> list = JsonUtil.decode(response.getFeeRate(), new TypeReference<List<ProviderFeeRateSyncResponse.FeeRateResult>>() {
        });
        response.setFeeRateList(list);
        System.out.println(response);
    }

    private TradeAppEntity buildTradeAppEntity(Long id, boolean payApp) {
        TradeAppEntity tradeAppEntity = new TradeAppEntity();
        tradeAppEntity.setPayApp(payApp);
        tradeAppEntity.setId(id);
        return tradeAppEntity;
    }

    private ApplyActivityRequest buildActivityRequest() {
        ApplyActivityRequest request = new ApplyActivityRequest();
        request.setMerchantSn("21690003629637");

        List<ApplyActivityRequest.ApplyPayFeeRate> applyPayFeeRates = new ArrayList<>();
        applyPayFeeRates.add(ApplyActivityRequest.ApplyPayFeeRate.builder()
                .payWay(PayWayEnum.WEIXIN.getCode())
                .build());
        applyPayFeeRates.add(ApplyActivityRequest.ApplyPayFeeRate.builder()
                .payWay(PayWayEnum.ZHIFUBAO.getCode())
                .build());
        request.setApplyPayFeeRates(applyPayFeeRates);
        return request;
    }

    /**
     * 构建未匹配的基础业务配置
     *
     * @return
     */
    private AllMerchantConfigResponse buildNoMatchedBaseMerchantConfig() {
        return buildNoMatchedBaseMerchantConfig(TransactionParam.CLEARANCE_PROVIDER_LKL, Provider.LAKALA.getCode());
    }


    /**
     * 构建未匹配的基础业务配置
     *
     * @return
     */
    private AllMerchantConfigResponse buildNoMatchedBaseMerchantConfig(int clearanceProvider, int provider) {
        List<Map> baseMerchantConfigs = new ArrayList<>();
        Map merchantConfig = new HashMap(6);
        merchantConfig.put(MerchantConfig.PAYWAY, PayWayEnum.WEIXIN.getCode());
        merchantConfig.put(ProviderAbility.CLEARANCE_PROVIDER, clearanceProvider);
        merchantConfig.put(MerchantConfig.PROVIDER, provider);
        baseMerchantConfigs.add(merchantConfig);

        AllMerchantConfigResponse response = new AllMerchantConfigResponse();
        response.setBaseMerchantConfigs(baseMerchantConfigs);
        return response;
    }

    /**
     * 构建匹配的基础业务配置
     *
     * @return
     */
    private AllMerchantConfigResponse buildMatchedBaseMerchantConfig() {
        List<Map> baseMerchantConfigs = new ArrayList<>();
        Map merchantConfig = new HashMap(6);
        merchantConfig.put(MerchantConfig.PAYWAY, PayWayEnum.WEIXIN.getCode());
        merchantConfig.put(ProviderAbility.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_FUYOU);
        baseMerchantConfigs.add(merchantConfig);

        AllMerchantConfigResponse response = new AllMerchantConfigResponse();
        response.setBaseMerchantConfigs(baseMerchantConfigs);
        return response;
    }

    /**
     * 构建匹配的基础业务配置和匹配的App业务配置
     *
     * @return
     */
    private AllMerchantConfigResponse buildMatchedBaseMerchantConfigAndAppMerchantConfig() {
        List<Map> baseMerchantConfigs = new ArrayList<>();
        Map merchantConfig = new HashMap(6);
        merchantConfig.put(MerchantConfig.PAYWAY, PayWayEnum.WEIXIN.getCode());
        merchantConfig.put(ProviderAbility.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_FUYOU);
        baseMerchantConfigs.add(merchantConfig);

        List<Map> appMerchantConfigs = new ArrayList<>();
        Map fuyouAppMerchantConfig = new HashMap(6);
        fuyouAppMerchantConfig.put(MerchantAppConfig.APP_ID, NON_BASE_APP);
        fuyouAppMerchantConfig.put(MerchantConfig.PAYWAY, PayWayEnum.WEIXIN.getCode());
        fuyouAppMerchantConfig.put(ProviderAbility.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_FUYOU);
        appMerchantConfigs.add(fuyouAppMerchantConfig);

        AllMerchantConfigResponse response = new AllMerchantConfigResponse();
        response.setBaseMerchantConfigs(baseMerchantConfigs);
        response.setAppMerchantConfigs(appMerchantConfigs);
        return response;
    }

    /**
     * 构建匹配的基础业务配置和不匹配的App业务配置
     *
     * @return
     */
    private AllMerchantConfigResponse buildMatchedBaseMerchantConfigAndNotMatchAppMerchantConfig() {
        List<Map> baseMerchantConfigs = new ArrayList<>();
        Map merchantConfig = new HashMap(6);
        merchantConfig.put(MerchantConfig.PAYWAY, PayWayEnum.WEIXIN.getCode());
        merchantConfig.put(ProviderAbility.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_FUYOU);
        baseMerchantConfigs.add(merchantConfig);

        List<Map> appMerchantConfigs = new ArrayList<>();
        Map lklAppMerchantConfig = new HashMap(6);
        lklAppMerchantConfig.put(MerchantAppConfig.APP_ID, NON_BASE_APP);
        lklAppMerchantConfig.put(MerchantConfig.PAYWAY, PayWayEnum.ZHIFUBAO.getCode());
        lklAppMerchantConfig.put(ProviderAbility.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL);
        appMerchantConfigs.add(lklAppMerchantConfig);

        Map haikeAppMerchantConfig = new HashMap(6);
        haikeAppMerchantConfig.put(MerchantAppConfig.APP_ID, 100);
        haikeAppMerchantConfig.put(MerchantConfig.PAYWAY, PayWayEnum.ZHIFUBAO.getCode());
        haikeAppMerchantConfig.put(ProviderAbility.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_HAIKE);
        appMerchantConfigs.add(haikeAppMerchantConfig);

        AllMerchantConfigResponse response = new AllMerchantConfigResponse();
        response.setBaseMerchantConfigs(baseMerchantConfigs);
        response.setAppMerchantConfigs(appMerchantConfigs);
        return response;
    }


    /**
     * 构建多费率
     *
     * @return
     */
    private AllMerchantConfigResponse buildMultiFeeRate() {
        List<Map> baseMerchantConfigs = new ArrayList<>();
        Map fuyouMerchantConfig = new HashMap(6);
        fuyouMerchantConfig.put(MerchantConfig.PAYWAY, PayWayEnum.WEIXIN.getCode());
        fuyouMerchantConfig.put(ProviderAbility.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_FUYOU);
        fuyouMerchantConfig.put(MerchantConfig.B2C_FEE_RATE, "0.24");
        baseMerchantConfigs.add(fuyouMerchantConfig);

        List<Map> appMerchantConfigs = new ArrayList<>();
        Map fuyouAppMerchantConfig = new HashMap(6);
        fuyouAppMerchantConfig.put(MerchantAppConfig.APP_ID, NON_BASE_APP);
        fuyouAppMerchantConfig.put(MerchantConfig.PAYWAY, PayWayEnum.ZHIFUBAO.getCode());
        fuyouAppMerchantConfig.put(ProviderAbility.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_FUYOU);
        fuyouAppMerchantConfig.put(MerchantConfig.B2C_FEE_RATE, "0.3");
        appMerchantConfigs.add(fuyouAppMerchantConfig);

        AllMerchantConfigResponse response = new AllMerchantConfigResponse();
        response.setBaseMerchantConfigs(baseMerchantConfigs);
        response.setAppMerchantConfigs(appMerchantConfigs);
        return response;
    }
}
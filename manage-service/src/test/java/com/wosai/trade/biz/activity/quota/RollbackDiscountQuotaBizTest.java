package com.wosai.trade.biz.activity.quota;

import com.wosai.pantheon.util.MapUtil;
import com.wosai.trade.BaseTest;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.transaction.service.GatewaySupportService;
import org.junit.Test;

import javax.annotation.Resource;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static org.junit.Assert.*;

public class RollbackDiscountQuotaBizTest extends BaseTest {

    @Resource
    private RollbackDiscountQuotaBiz rollbackDiscountQuotaBiz;

    @Resource
    private GatewaySupportService gatewaySupportService;

    @Test
    public void rollbackDiscountQuotaByTransaction() {
        String merchantId = "3498bc4b-1181-4a49-961e-5bd2102d5906";
        String orderSn = "2001259237548567";
        long ctime = 1729819005899L;
        List<Map<String, Object>> trans = gatewaySupportService.getRefundSuccessTransactionList(merchantId, orderSn, ctime);
        Map<String, Object> refund = trans.stream().filter(t -> {
            String tsn = MapUtil.getString(t, Transaction.TSN);
            return Objects.equals(tsn, "2004259248289571");
        }).findAny().orElse(null);
        rollbackDiscountQuotaBiz.rollbackDiscountQuotaByTransaction(refund);
    }
}
package com.wosai.trade.biz.activity;

import com.wosai.databus.event.audit.AuditInstanceApproveEvent;
import com.wosai.trade.BaseTest;
import com.wosai.trade.util.JsonUtil;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Map;

public class ModifyActivityComboFeeRateBizTest extends BaseTest {

    @Resource
    private ModifyActivityComboFeeRateBiz modifyActivityComboFeeRateBiz;

    @Test
    public void execute() {
        ModifyActivityComboFeeRateBiz.ChangeActivityComboParam param = new ModifyActivityComboFeeRateBiz.ChangeActivityComboParam();
        param.setMerchantSn("21690003516615");
        param.setSubStatusId(562L);
        param.setComboId(5962L);
        param.setFeeRate("0.3");
        modifyActivityComboFeeRateBiz.execute(param);
    }

    @Test
    public void execute0() {
        String str = " {\"auditId\":\"159886\",\"auditSn\":\"SP723820230404000022\",\"formId\":\"2465322\",\"finishTime\":1680599602954,\"operatorPlatform\":\"SP\",\"module\":\"CRM\",\"auditTemplateId\":\"68762\",\"eventType\":\"AUDIT_APPROVE\",\"templateId\":\"114183\",\"operatorName\":\"邵一帆测试\",\"objectType\":\"AUDIT\",\"auditCommentProperty\":{\"comment\":\"111\",\"id\":\"64791782\",\"operatorName\":\"邵一帆测试\",\"operator\":\"c0a893bf-7b39-143a-817b-4879951e0e3c\",\"platform\":\"SP\"},\"bizKey\":\"SP_7a3ed572-d448-4462-89f5-70b18f375db6\",\"templateEvent\":\"modify_activity_fee_rate\",\"ctime\":1680599592000,\"auditCallBackProperty\":{\"resultFail\":0},\"businessMap\":{\"ladder_list\":[{\"ladder:0-50\":0.27,\"ladder:50-2147483647\":0.35}],\"combo_info\":\"6609\",\"merchant\":{\"merch_info.sn\":{\"keyName\":\"商户号\",\"value\":\"21690003482586\"},\"merch_info.name\":{\"keyName\":\"商户名称\",\"value\":\"测试啊啊啊\"}},\"commentdetails\":{\"comment\":\"111\",\"id\":\"64791782\",\"operatorName\":\"邵一帆测试\",\"operator\":\"c0a893bf-7b39-143a-817b-4879951e0e3c\",\"platform\":\"SP\"},\"remark\":\"syf\",\"audit_info\":{\"id\":\"159886\",\"sn\":\"SP723820230404000022\",\"operator\":\"c0a893bf-7b39-143a-817b-4879951e0e3c\",\"platform\":\"SP\",\"comment\":\"111\",\"status\":\"2\",\"time\":1680599603000,\"template_id\":68762,\"template_name\":\"活动套餐切换通用申请\"}},\"operatorId\":\"c0a893bf-7b39-143a-817b-4879951e0e3c\",\"seq\":0,\"timestamp\":1680599602998}";
        AuditInstanceApproveEvent event = JsonUtil.decode(str, AuditInstanceApproveEvent.class);
        ModifyActivityComboFeeRateBiz.ChangeActivityComboParam param = ModifyActivityComboFeeRateBiz.ChangeActivityComboParam.build(event);
        modifyActivityComboFeeRateBiz.execute(param);
    }

    @Test
    public void verify() {
        String str = "{\n" +
                "    \"ladder_list\": [\n" +
                "        {\n" +
                "            \"ladder:0-50\": 0.25,\n" +
                "            \"ladder:50-2147483647\": 0.38\n" +
                "        }\n" +
                "    ],\n" +
                "    \"audit.operator_id\": \"c0a893bf-7b39-143a-817b-4879951e0e3c\",\n" +
                "    \"combo_info\": \"6608\",\n" +
                "    \"audit.audit_template_id\": 68762,\n" +
                "    \"merchant\": {\n" +
                "        \"merch_info.sn\": {\n" +
                "            \"keyName\": \"商户号\",\n" +
                "            \"value\": \"21690003216146\"\n" +
                "        },\n" +
                "        \"merch_info.name\": {\n" +
                "            \"keyName\": \"商户名称\",\n" +
                "            \"value\": \"拉卡拉syf测试店\"\n" +
                "        }\n" +
                "    },\n" +
                "    \"remark\": \"22\",\n" +
                "    \"fee_rate\": null\n" +
                "}";
        Map request = JsonUtil.decode(str, Map.class);
        ModifyActivityComboFeeRateBiz.ChangeActivityComboParam param = ModifyActivityComboFeeRateBiz.ChangeActivityComboParam.build("verify", request);
        modifyActivityComboFeeRateBiz.verify(param);
    }
}
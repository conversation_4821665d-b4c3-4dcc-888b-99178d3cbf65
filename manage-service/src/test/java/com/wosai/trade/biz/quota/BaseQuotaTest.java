package com.wosai.trade.biz.quota;

import com.wosai.trade.model.dal.TransactionQuotaDetailDalDO;
import com.wosai.trade.model.dal.TransactionQuotaSummaryDalDO;
import com.wosai.trade.model.enums.FixedQuotaTypeEnum;
import com.wosai.trade.model.enums.QuotaDetailTypeEnum;
import com.wosai.trade.service.enums.QuotaDetailStatusEnum;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Date: 2020/6/9 Time: 9:36 上午
 */
public class BaseQuotaTest {

    protected List<TransactionQuotaDetailDalDO> buildTransactionQuotaDetailDalDOList() {
        TransactionQuotaDetailDalDO detailDalDO1 = TransactionQuotaDetailDalDO.builder()
                .id(1L)
                .merchantSn("mch-123")
                .quota(9000L)
                .type(QuotaDetailTypeEnum.FIXED_QUOTA)
                .subType(Byte.parseByte("1"))
                .beginDate(null)
                .endDate(null)
                .status(QuotaDetailStatusEnum.EFFECTIVE)
                .ext("")
                .ctime(LocalDateTime.now())
                .mtime(LocalDateTime.now())
                .build();
        TransactionQuotaDetailDalDO detailDalDO2 = TransactionQuotaDetailDalDO.builder()
                .id(2L)
                .merchantSn("mch-123")
                .quota(800L)
                .type(QuotaDetailTypeEnum.TEMPORARY_QUOTA)
                .subType(Byte.parseByte("1"))
                .beginDate(LocalDate.now())
                .endDate(LocalDate.now().plusDays(1))
                .status(QuotaDetailStatusEnum.NOT_EFFECTIVE)
                .ext("")
                .ctime(LocalDateTime.now())
                .mtime(LocalDateTime.now())
                .build();
        TransactionQuotaDetailDalDO detailDalDO3 = TransactionQuotaDetailDalDO.builder()
                .id(3L)
                .merchantSn("mch-123")
                .quota(2000L)
                .type(QuotaDetailTypeEnum.TEMPORARY_QUOTA)
                .subType(Byte.parseByte("3"))
                .beginDate(null)
                .endDate(null)
                .status(QuotaDetailStatusEnum.NOT_EFFECTIVE)
                .ext("")
                .ctime(LocalDateTime.now())
                .mtime(LocalDateTime.now())
                .build();
        TransactionQuotaDetailDalDO detailDalDO4 = TransactionQuotaDetailDalDO.builder()
                .id(4L)
                .merchantSn("mch-123")
                .quota(800L)
                .type(QuotaDetailTypeEnum.TEMPORARY_QUOTA)
                .subType(Byte.parseByte("5"))
                .beginDate(LocalDate.now().minusDays(2))
                .endDate(LocalDate.now().minusDays(1))
                .status(QuotaDetailStatusEnum.EFFECTIVE)
                .ext("")
                .ctime(LocalDateTime.now())
                .mtime(LocalDateTime.now())
                .build();

        List<TransactionQuotaDetailDalDO> detailDalDOS = new ArrayList<>();
        detailDalDOS.add(detailDalDO1);
        detailDalDOS.add(detailDalDO2);
        detailDalDOS.add(detailDalDO3);
        detailDalDOS.add(detailDalDO4);

        return detailDalDOS;
    }

    protected TransactionQuotaSummaryDalDO buildTransactionQuotaSummaryDalDO() {
        return TransactionQuotaSummaryDalDO.builder()
                .id(1L)
                .merchantSn("mch-123")
                .merchantId("123")
                .fixedQuota(9000L)
                .temporaryQuota(800L)
                .totalQuota(9800L)
                .nextComputeDate(null)
                .ext("")
                .ctime(LocalDateTime.now())
                .mtime(LocalDateTime.now())
                .version(1)
                .build();
    }

    protected TransactionQuotaDetailDalDO buildTransactionQuotaDetailDalDO() {
        return TransactionQuotaDetailDalDO.builder()
                .id(1L)
                .merchantSn("mch-123")
                .quota(9000L)
                .type(QuotaDetailTypeEnum.FIXED_QUOTA)
                .subType(Byte.parseByte("1"))
                .beginDate(null)
                .endDate(null)
                .status(QuotaDetailStatusEnum.EFFECTIVE)
                .ext("")
                .ctime(LocalDateTime.now())
                .mtime(LocalDateTime.now())
                .build();
    }

    protected List<TransactionQuotaDetailDalDO> buildStandardFixedQuotaDetailList() {
        TransactionQuotaDetailDalDO detailDalDO = TransactionQuotaDetailDalDO.builder()
                .id(1L)
                .merchantSn("mch-123")
                .quota(9000L)
                .type(QuotaDetailTypeEnum.FIXED_QUOTA)
                .subType(FixedQuotaTypeEnum.STANDARD_FIXED_QUOTA.getCode())
                .beginDate(null)
                .endDate(null)
                .status(QuotaDetailStatusEnum.EFFECTIVE)
                .ext("")
                .ctime(LocalDateTime.now())
                .mtime(LocalDateTime.now())
                .build();

        List<TransactionQuotaDetailDalDO> detailDalDOS = new ArrayList<>();
        detailDalDOS.add(detailDalDO);

        return detailDalDOS;
    }

    protected List<TransactionQuotaDetailDalDO> buildRiskControlFixedQuotaDetailList() {
        TransactionQuotaDetailDalDO detailDalDO1 = TransactionQuotaDetailDalDO.builder()
                .id(1L)
                .merchantSn("mch-123")
                .quota(9000L)
                .type(QuotaDetailTypeEnum.FIXED_QUOTA)
                .subType(FixedQuotaTypeEnum.RISK_CONTROL_SPECIAL_FIXED_QUOTA.getCode())
                .beginDate(null)
                .endDate(null)
                .status(QuotaDetailStatusEnum.EFFECTIVE)
                .ext("")
                .ctime(LocalDateTime.now())
                .mtime(LocalDateTime.now())
                .build();

        TransactionQuotaDetailDalDO detailDalDO2 = TransactionQuotaDetailDalDO.builder()
                .id(1L)
                .merchantSn("mch-123")
                .quota(9000L)
                .type(QuotaDetailTypeEnum.FIXED_QUOTA)
                .subType(FixedQuotaTypeEnum.STANDARD_FIXED_QUOTA.getCode())
                .beginDate(null)
                .endDate(null)
                .status(QuotaDetailStatusEnum.NOT_EFFECTIVE)
                .ext("")
                .ctime(LocalDateTime.now())
                .mtime(LocalDateTime.now())
                .build();

        List<TransactionQuotaDetailDalDO> detailDalDOS = new ArrayList<>();
        detailDalDOS.add(detailDalDO1);
        detailDalDOS.add(detailDalDO2);

        return detailDalDOS;
    }
}

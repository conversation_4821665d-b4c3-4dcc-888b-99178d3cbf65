package com.wosai.trade.biz.activity.converter;

import com.wosai.trade.BaseTest;
import com.wosai.trade.repository.dao.entity.ActivityEntity;
import com.wosai.trade.repository.dao.ActivityDOMapper;
import com.wosai.trade.service.activity.request.ActivityRule;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class ActivityBuildParamsTest extends BaseTest {

    @Autowired
    private ActivityBuildParams activityBuildParams;
    @Autowired
    private ActivityDOMapper activityDOMapper;


    @Test
    public void testBuildAddTradeComboRequest() {

        ActivityEntity activityEntity = activityDOMapper.selectByPrimaryKey(1271L);
        List<ActivityRule.FeeRateDetails> feeRateDetails = activityEntity.buildDiscountFeeRate();

        activityBuildParams.buildAddTradeComboRequest(activityEntity, feeRateDetails);
    }
}
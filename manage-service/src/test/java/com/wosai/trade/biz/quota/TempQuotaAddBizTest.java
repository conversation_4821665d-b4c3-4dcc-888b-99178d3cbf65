package com.wosai.trade.biz.quota;

import com.wosai.trade.client.CoreBusinessClient;
import com.wosai.trade.config.apollo.QuotaTypeConfig;
import com.wosai.trade.model.biz.TempQuotaAddBizParam;
import com.wosai.trade.model.client.coreb.GetMerchantResult;
import com.wosai.trade.model.dal.TransactionQuotaDetailDalDO;
import com.wosai.trade.model.dal.TransactionQuotaSummaryDalDO;
import com.wosai.trade.model.enums.QuotaDetailTypeEnum;
import com.wosai.trade.repository.TransactionQuotaDetailRepository;
import com.wosai.trade.repository.TransactionQuotaSummaryRepository;
import com.wosai.trade.service.exception.TradeManageBizException;
import com.wosai.trade.service.request.TempQuotaAddRequest;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionTemplate;


import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import static com.wosai.trade.service.exception.enums.TradeManageRespCodeEnum.*;

/**
 * <AUTHOR> Date: 2020/7/6 Time: 3:30 下午
 */
@RunWith(PowerMockRunner.class)
public class TempQuotaAddBizTest extends BaseQuotaTest {

    @InjectMocks
    private TempQuotaAddBiz tempQuotaAddBiz;
    @Mock
    private TransactionQuotaSummaryRepository summaryRepository;
    @Mock
    private TransactionQuotaDetailRepository detailRepository;
    @Mock
    private QuotaTypeConfig quotaTypeConfig;
    @Mock
    protected CoreBusinessClient coreBusinessClient;

    @Before
    public void setUp() throws Exception {
        Class<TempQuotaAddBiz> tempQuotaAddBizClass = (Class<TempQuotaAddBiz>) tempQuotaAddBiz.getClass();
        Field field = tempQuotaAddBizClass.getDeclaredField("transactionTemplate");
        field.setAccessible(true);
        field.set(tempQuotaAddBiz, new TransactionTemplate(){{
            setTransactionManager(new PlatformTransactionManager() {
                @Override
                public TransactionStatus getTransaction(TransactionDefinition definition) throws TransactionException {
                    return null;
                }

                @Override
                public void commit(TransactionStatus status) throws TransactionException {

                }

                @Override
                public void rollback(TransactionStatus status) throws TransactionException {

                }
            });
        }});

        Class<AbstractQuotaBiz> abstractQuotaBizClass = (Class<AbstractQuotaBiz>) tempQuotaAddBiz.getClass().getSuperclass();
        Field abstractQuotaBizField = abstractQuotaBizClass.getDeclaredField("transactionTemplate");
        abstractQuotaBizField.setAccessible(true);
        abstractQuotaBizField.set(tempQuotaAddBiz, new TransactionTemplate(){{
            setTransactionManager(new PlatformTransactionManager() {
                @Override
                public TransactionStatus getTransaction(TransactionDefinition definition) throws TransactionException {
                    return null;
                }

                @Override
                public void commit(TransactionStatus status) throws TransactionException {

                }

                @Override
                public void rollback(TransactionStatus status) throws TransactionException {

                }
            });
        }});
    }

    @Test
    public void before() {
        TempQuotaAddRequest quotaAddRequest = new TempQuotaAddRequest();
        try {
            tempQuotaAddBiz.before(quotaAddRequest);
            Assert.assertNotNull("入参校验失败", null);
        } catch (TradeManageBizException e) {
            Assert.assertTrue("额度类型校验失败", StringUtils.equals(e.getCode(), ILLEGAL_ARGUMENT.getCode()));
        }

        quotaAddRequest.setMerchantSn("mch-123");
        quotaAddRequest.setQuota(6000L);
        quotaAddRequest.setType(Byte.valueOf("1"));
        quotaAddRequest.setBeginDate("2020-07-08");
        quotaAddRequest.setEndDate("2022-08-07");
        try {
            PowerMockito.when(quotaTypeConfig.isLegalType(Mockito.anyByte(), Mockito.anyByte()))
                    .thenReturn(false);
            tempQuotaAddBiz.before(quotaAddRequest);
            Assert.assertNotNull("额度类型校验失败", null);
        } catch (TradeManageBizException e) {
            Assert.assertTrue("额度类型校验失败", StringUtils.equals(e.getCode(), ILLEGAL_QUOTA_TYPE_OR_SUBTYPE.getCode()));
        }

        PowerMockito.when(quotaTypeConfig.isLegalType(Mockito.anyByte(), Mockito.anyByte()))
                .thenReturn(true);
        PowerMockito.when(detailRepository.query(Mockito.any()))
                .thenReturn(TransactionQuotaDetailDalDO.builder().build());
        try {
            tempQuotaAddBiz.before(quotaAddRequest);
            Assert.assertNotNull("风控场景校验失败", null);
        } catch (TradeManageBizException e) {
            Assert.assertTrue("风控场景校验失败", StringUtils.equals(e.getCode()
                    , RISK_CONTROL_NOT_ALLOW_OPERATION.getCode()));
        }

        PowerMockito.when(detailRepository.query(Mockito.any()))
                .thenReturn(null);
        quotaAddRequest.setBeginDate("2030-08-07");
        quotaAddRequest.setEndDate("2030-07-08");
        try {
            tempQuotaAddBiz.before(quotaAddRequest);
            Assert.assertNotNull("生效时效日期校验失败", null);
        } catch (TradeManageBizException e) {
            Assert.assertTrue("生效时效日期校验失败", StringUtils.equals(e.getCode()
                    , BEGIN_DATE_NOT_ALLOWED_GREATER_THAN_END_DATE.getCode()));
        }

        quotaAddRequest.setBeginDate("2020-06-07");
        quotaAddRequest.setEndDate("2020-06-08");
        try {
            tempQuotaAddBiz.before(quotaAddRequest);
            Assert.assertNotNull("生效时效日期校验失败", null);
        } catch (TradeManageBizException e) {
            Assert.assertTrue("生效时效日期校验失败", StringUtils.equals(e.getCode()
                    , END_DATE_NOT_ALLOWED_LESS_THAN_CURRENT_DATE.getCode()));
        }

    }

    @Test
    public void buildParam() throws Throwable {
        String merchantSn = "mch-123";
        long quota = 8000L;
        byte type = Byte.parseByte("1");
        String beginDate = "2020-06-08";
        String endDate = "2020-06-10";
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        TempQuotaAddRequest quotaAddRequest = new TempQuotaAddRequest();
        quotaAddRequest.setMerchantSn(merchantSn);
        quotaAddRequest.setQuota(quota);
        quotaAddRequest.setType(type);
        quotaAddRequest.setBeginDate(beginDate);
        quotaAddRequest.setEndDate(endDate);
        TempQuotaAddBizParam tempQuotaAddBizParam = tempQuotaAddBiz.buildParam(quotaAddRequest);
        Assert.assertNotNull("参数转换失败", tempQuotaAddBizParam);
        Assert.assertEquals("参数转换失败", merchantSn, tempQuotaAddBizParam.getMerchantSn());
        Assert.assertEquals("参数转换失败", quota, tempQuotaAddBizParam.getQuota());
        Assert.assertEquals("参数转换失败", type, tempQuotaAddBizParam.getSubType());
        Assert.assertEquals("参数转换失败", beginDate, tempQuotaAddBizParam.getBeginDate().format(dateTimeFormatter));
        Assert.assertEquals("参数转换失败", endDate, tempQuotaAddBizParam.getEndDate().format(dateTimeFormatter));

    }

    @Test
    public void execute() throws Throwable {
        TempQuotaAddBizParam tempQuotaAddBizParam = TempQuotaAddBizParam.builder()
                .merchantSn("mch-123").quota(6000L)
                .type(QuotaDetailTypeEnum.TEMPORARY_QUOTA.getCode()).subType(Byte.parseByte("1"))
                .beginDate(LocalDate.now())
                .endDate(LocalDate.now())
                .currentDateTime(LocalDateTime.now())
                .build();
        PowerMockito.when(summaryRepository.query(Mockito.any()))
                .thenReturn(null);
        PowerMockito.when(coreBusinessClient.getMerchantByMerchantSn(Mockito.any()))
                .thenReturn(new GetMerchantResult(){{
                    setMerchantId("123");
                    setMerchantSn("mch-123");
                }});
        PowerMockito.when(summaryRepository.queryForUpdate(Mockito.any()))
                .thenReturn(buildTransactionQuotaSummaryDalDO());
        tempQuotaAddBiz.execute(tempQuotaAddBizParam);

    }

    @Test
    public void buildSummarySaveDalParam() {
        TempQuotaAddBizParam tempQuotaAddBizParam = TempQuotaAddBizParam.builder().build();
        TransactionQuotaSummaryDalDO summaryDalDO = TransactionQuotaSummaryDalDO.builder().build();
        try {
            tempQuotaAddBiz.buildSummarySaveDalParam(tempQuotaAddBizParam, summaryDalDO);
            Assert.assertNotNull("商户存在性校验失败", null);
        } catch (TradeManageBizException e) {
            Assert.assertTrue("商户存在性校验失败", StringUtils.equals(e.getCode()
                    , MERCHANT_NOT_FOUND.getCode()));
        }

        tempQuotaAddBizParam = TempQuotaAddBizParam.builder()
                .merchantSn("mch-123").quota(6000L)
                .type(QuotaDetailTypeEnum.TEMPORARY_QUOTA.getCode()).subType(Byte.parseByte("1"))
                .beginDate(LocalDate.now()).endDate(LocalDate.now())
                .currentDateTime(LocalDateTime.now())
                .build();
        PowerMockito.when(coreBusinessClient.getMerchantByMerchantSn(Mockito.any()))
                .thenReturn(new GetMerchantResult(){{
                    setMerchantSn("mch-123");
                    setMerchantId("123");
                }});
        tempQuotaAddBiz.buildSummarySaveDalParam(tempQuotaAddBizParam, null);

    }
}
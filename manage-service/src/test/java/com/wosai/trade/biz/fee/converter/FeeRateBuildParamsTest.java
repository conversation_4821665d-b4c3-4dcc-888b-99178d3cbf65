package com.wosai.trade.biz.fee.converter;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableMap;
import com.wosai.trade.BaseTest;
import com.wosai.trade.biz.audit.model.ApplyLadderFeeRate;
import com.wosai.trade.repository.dao.TradeComboDetailDao;
import com.wosai.trade.repository.dao.entity.TradeComboDetailEntity;
import com.wosai.trade.service.enums.FeeRateTypeEnum;
import com.wosai.trade.util.JsonUtil;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

public class FeeRateBuildParamsTest extends BaseTest {

    @Resource
    private FeeRateBuildParams feeRateBuildParams;
    @Resource
    private TradeComboDetailDao tradeComboDetailDao;


    @Test
    public void getEffectFeeRate() {

        TradeComboDetailEntity detail = tradeComboDetailDao.selectById(12762L);

        String ladderStr = "[{\"min\":0.0,\"max\":100.0,\"fee_rate\":\"0.26\"},{\"min\":100.0,\"fee_rate\":\"0.39\"}]";

        ApplyLadderFeeRate applyLadderFeeRate = new ApplyLadderFeeRate();
        applyLadderFeeRate.setFeeRateType(FeeRateTypeEnum.LADDER.name().toLowerCase());
        applyLadderFeeRate.setValue(JsonUtil.decode(ladderStr, new TypeReference<List<ApplyLadderFeeRate.LadderFeeRate>>() {
        }));
        String feeRateStr = JsonUtil.encode(applyLadderFeeRate);
        String feeRate = feeRateBuildParams.getEffectFeeRate(detail, ImmutableMap.of("3", feeRateStr));
        System.out.println(feeRate);
    }
}
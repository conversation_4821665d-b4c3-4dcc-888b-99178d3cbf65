package com.wosai.trade.biz.audit;

import com.google.common.collect.Maps;
import com.wosai.databus.event.audit.AuditInstanceApproveEvent;
import com.wosai.databus.jackson.EventAwareJackson2PersistenceHelper;
import com.wosai.trade.BaseTest;
import com.wosai.trade.repository.dao.entity.TradeActivityEntity;
import com.wosai.trade.util.AttachmentFileUtil;
import com.wosai.trade.util.JsonUtil;
import org.apache.commons.lang.StringUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class WithdrawRemarkBizTest extends BaseTest {
    @Autowired
    private WithdrawRemarkBiz withdrawRemarkBiz;
    protected EventAwareJackson2PersistenceHelper persistenceHelper = new EventAwareJackson2PersistenceHelper();
    @Test
    public void execute() {
        AuditInstanceApproveEvent event = JsonUtil.decode(str, AuditInstanceApproveEvent.class);
        withdrawRemarkBiz.execute(event);

    }

    @Test
    public void readFile() {
        List<Map> recordList = AttachmentFileUtil.getInstance().readFile(Arrays.asList("http://wosai-sales.oss-cn-hangzhou.aliyuncs.com/workflow/%E5%95%86%E6%88%B7%E5%AF%B9%E8%B4%A6%E5%8D%95%E6%89%93%E6%AC%BE%E5%A4%87%E6%B3%A8%E7%94%B3%E8%AF%B716336656330441717653256203.xlsx?type=file"), (object) -> {
            String customizeRemark = StringUtils.trim(object.get(2));
            Map<String, Object> record = Maps.newHashMap();
            record.put("merchant_sn", object.get(0));
            record.put("customize_remark", customizeRemark);
            return record;
        });

        System.out.println(recordList);
    }
    @Test
    public void readFile2() {
        List<String> fileUrls = Arrays.asList("http://wosai-sales.oss-cn-hangzhou.aliyuncs.com/workflow/%E5%95%86%E6%88%B7%E5%AF%B9%E8%B4%A6%E5%8D%95%E6%89%93%E6%AC%BE%E5%A4%87%E6%B3%A8%E7%94%B3%E8%AF%B716336656330441717653256203.xlsx?type=file");
        List<Map> list = AttachmentFileUtil.getInstance().read(TradeActivityEntity.TYPE_COMMON_RATE, fileUrls, null);
    }



    String str = "{\"auditId\":\"389084\",\"auditSn\":\"SP764320240606000001\",\"formId\":\"2694485\",\"finishTime\":1717647326593,\"operatorPlatform\":\"SP\",\"module\":\"CRM\",\"auditTemplateId\":\"23127\",\"eventType\":\"AUDIT_APPROVE\",\"templateId\":\"281942\",\"operatorName\":\"公用测试账号\",\"objectType\":\"AUDIT\",\"auditCommentProperty\":{\"id\":\"65327309\",\"operatorName\":\"公用测试账号\",\"operator\":\"0af4121d-6cf5-19cc-816c-f66ffddc0000\",\"platform\":\"SP\"},\"bizKey\":\"SP_cd6ec99b-1e2b-4b9d-aea6-d7bfbf0cc3ee\",\"templateEvent\":\"customize_withdraw_remark_switch\",\"ctime\":1717647324000,\"auditCallBackProperty\":{\"resultFail\":0},\"businessMap\":{\"reason\":\"自定义备注test2222\",\"customize_remark\":\"自定义备注test\",\"commentdetails\":{\"id\":\"65327309\",\"operatorName\":\"公用测试账号\",\"operator\":\"0af4121d-6cf5-19cc-816c-f66ffddc0000\",\"platform\":\"SP\"},\"merchant_sn\":{\"merch_info.sn\":{\"keyName\":\"商户号\",\"value\":\"21690003359311\"},\"acquirer\":{\"keyName\":\"收单机构\",\"value\":\"lkl\"},\"merch_info.name\":{\"keyName\":\"商户名称\",\"value\":\"朱秋云818银行卡进件\"}},\"remark_type\":\"customize\",\"audit_info\":{\"id\":\"389084\",\"sn\":\"SP764320240606000001\",\"operator\":\"0af4121d-6cf5-19cc-816c-f66ffddc0000\",\"platform\":\"SP\",\"status\":\"2\",\"time\":1717647327000,\"template_id\":23127,\"template_name\":\"商户对账单打款备注申请\"},\"merchant_type\":\"single\",\"switch\":\"open\"},\"operatorId\":\"0af4121d-6cf5-19cc-816c-f66ffddc0000\",\"seq\":0,\"timestamp\":1717647326622}";
}
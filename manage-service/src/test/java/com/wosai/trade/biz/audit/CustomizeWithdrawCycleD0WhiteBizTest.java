package com.wosai.trade.biz.audit;

import com.wosai.databus.event.audit.AuditInstanceApproveEvent;
import com.wosai.trade.BaseTest;
import com.wosai.trade.util.JsonUtil;
import org.junit.Test;

import javax.annotation.Resource;

import static org.junit.Assert.*;

public class CustomizeWithdrawCycleD0WhiteBizTest extends BaseTest {

    @Resource
    private CustomizeWithdrawCycleD0WhiteBiz customizeWithdrawCycleD0WhiteBiz;

    @Test
    public void execute() {
        AuditInstanceApproveEvent approveEvent = JsonUtil.decode(jsonStr, AuditInstanceApproveEvent.class);
        customizeWithdrawCycleD0WhiteBiz.execute(approveEvent);
    }

    String jsonStr = "{\n" +
            "    \"auditId\":\"6712928\",\n" +
            "    \"auditSn\":\"******************\",\n" +
            "    \"formId\":\"3634250\",\n" +
            "    \"finishTime\":1697556639026,\n" +
            "    \"operatorPlatform\":\"CRM\",\n" +
            "    \"module\":\"CRM\",\n" +
            "    \"auditTemplateId\":\"462\",\n" +
            "    \"operatorOrgNamePath\":\"服务商/华中/江西/赣州君赋信息技术有限公司\",\n" +
            "    \"eventType\":\"AUDIT_APPROVE\",\n" +
            "    \"templateId\":\"21264\",\n" +
            "    \"operatorName\":\"钟君发\",\n" +
            "    \"objectType\":\"AUDIT\",\n" +
            "    \"auditCommentProperty\":{\n" +
            "        \"id\":\"76515577\",\n" +
            "        \"operatorName\":\"钟君发\",\n" +
            "        \"operator\":\"37c943bb-0cb3-4ed0-bf0f-17eb86c7451d\",\n" +
            "        \"platform\":\"CRM\"\n" +
            "    },\n" +
            "    \"operatorOrgCode\":\"102041\",\n" +
            "    \"bizKey\":\"CRM_581b348d-df84-43ae-b84b-b27fc6b3cfc7\",\n" +
            "    \"templateEvent\":\"customize_offset_hour_merchant_d0\",\n" +
            "    \"ctime\":1697556636000,\n" +
            "    \"auditCallBackProperty\":{\n" +
            "        \"resultFail\":1\n" +
            "    },\n" +
            "    \"businessMap\":{\n" +
            "        \"event_remark\":\"\",\n" +
            "        \"actionType\":\"APPLY\",\n" +
            "        \"commentdetails\":{\n" +
            "            \"id\":\"76515577\",\n" +
            "            \"operatorName\":\"钟君发\",\n" +
            "            \"operator\":\"37c943bb-0cb3-4ed0-bf0f-17eb86c7451d\",\n" +
            "            \"platform\":\"CRM\"\n" +
            "        },\n" +
            "        \"merchant_sn\":{\n" +
            "            \"merch_info.sn\":{\n" +
            "                \"keyName\":\"商户号\",\n" +
            "                \"value\":\"**************\"\n" +
            "            },\n" +
            "            \"bank_account.number\":{\n" +
            "                \"keyName\":\"银行账户号码\",\n" +
            "                \"value\":\"6221884280020800574\"\n" +
            "            },\n" +
            "            \"merch_info.name\":{\n" +
            "                \"keyName\":\"商户名称\",\n" +
            "                \"value\":\"蓝水湾\"\n" +
            "            },\n" +
            "            \"extra.industry_name\":{\n" +
            "                \"keyName\":\"所属行业\",\n" +
            "                \"value\":\"休闲娱乐养生会所\"\n" +
            "            },\n" +
            "            \"bank_account.bank_name\":{\n" +
            "                \"keyName\":\"银行账户开户行\",\n" +
            "                \"value\":\"中国邮政储蓄银行\"\n" +
            "            }\n" +
            "        },\n" +
            "        \"audit_info\":{\n" +
            "            \"id\":\"6712928\",\n" +
            "            \"sn\":\"******************\",\n" +
            "            \"operator\":\"37c943bb-0cb3-4ed0-bf0f-17eb86c7451d\",\n" +
            "            \"platform\":\"CRM\",\n" +
            "            \"status\":\"2\",\n" +
            "            \"time\":*************,\n" +
            "            \"template_id\":462,\n" +
            "            \"template_name\":\"商户自定义提现周期快速到账申请\\t\"\n" +
            "        }\n" +
            "    },\n" +
            "    \"operatorId\":\"37c943bb-0cb3-4ed0-bf0f-17eb86c7451d\",\n" +
            "    \"seq\":6066872,\n" +
            "    \"timestamp\":*************\n" +
            "}";
}
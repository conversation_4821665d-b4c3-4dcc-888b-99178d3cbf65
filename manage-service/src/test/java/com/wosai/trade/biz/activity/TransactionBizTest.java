package com.wosai.trade.biz.activity;

import com.alibaba.fastjson.JSONObject;
import com.wosai.trade.BaseTest;
import org.junit.Test;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.Duration;
import java.util.Map;

public class TransactionBizTest extends BaseTest {
    @Autowired
    private TransactionBiz transactionBiz;


    @Test
    public void testPaySuccess() {
        Map<String, Object> transactionMap = JSONObject.parseObject(s3, Map.class);
        transactionBiz.handleBiz(transactionMap);
    }

    public static RedissonClient redissonClient() {
        Config config = new Config();
        String host = "r-8vbkddg0ez3eak2rzq.redis.zhangbei.rds.aliyuncs.com";
        String password = "roFXzHwXPY3RnI%5";
        int port = 6379;
        int database = 0;
        config.useSingleServer().setAddress("redis://" + host + ":" + port).setDatabase(database).setPassword(password);
        return Redisson.create(config);
    }

    public static void main(String[] args) {
        String key = "transaction_update_quota_:****************:11:2000";
        RedissonClient redissonClient = redissonClient();
        boolean value = redissonClient.getBucket(key).setIfAbsent("1", Duration.ofMinutes(30L));
        System.out.println(value);
    }



    String s3="{\"subject\":\"test\",\"received_amount\":9,\"buyer_login\":\"6224****0052\",\"merchant_id\":\"8270ef40-d47c-4e7b-abbe-5a4d2cde4168\",\"mtime\":*************,\"type\":11,\"tsn\":\"****************\",\"operator\":\"tywei\",\"product_flag\":\"ar\",\"extra_out_fields\":{\"wild_card_type\":\"dcc\",\"quota_fee_rate_tag\":60071,\"payments\":[{\"type\":\"BANKCARD_CREDIT\",\"origin_type\":\"BS1_OUT_CARD\",\"amount\":9}],\"wallet_account_type\":2,\"combo_id\":\"15720\",\"order_info\":{\"subject\":\"test\",\"original_total\":10000,\"buyer_login\":\"6224****0052\",\"merchant_id\":\"8270ef40-d47c-4e7b-abbe-5a4d2cde4168\",\"mtime\":*************,\"client_sn\":\"tyw-mock20241015083001\",\"operator\":\"tyw\",\"provider\":1034,\"ctime\":*************,\"id\":\"o****************\",\"sn\":\"****************\",\"net_original\":1,\"terminal_id\":\"8b442d01-30f9-498b-a074-9be7fb332a32\",\"store_id\":\"f6ae3663-19de-4ecb-8a60-f0478864c1ac\",\"tcp_modified\":false,\"payway\":21,\"version\":6,\"sub_payway\":2,\"deleted\":false,\"trade_no\":\"**************\",\"effective_total\":10000,\"items\":{\"channel_payments\":[{\"amount_total\":200,\"net_amount\":-9799,\"type\":\"BANKCARD_CREDIT\"}]},\"net_effective\":1,\"buyer_uid\":\"6224****0052\",\"status\":2210}},\"provider\":1034,\"original_amount\":9,\"ctime\":*************,\"id\":\"t****************\",\"terminal_id\":\"8b442d01-30f9-498b-a074-9be7fb332a32\",\"client_tsn\":\"tyw-mock20241015083001-refund-************\",\"store_id\":\"f6ae3663-19de-4ecb-8a60-f0478864c1ac\",\"provider_error_info\":{\"refund\":{\"msg\":\"执行成功\",\"code\":\"000000\"}},\"extra_params\":{},\"payway\":21,\"version\":0,\"finish_time\":*************,\"sub_payway\":2,\"config_snapshot\":{\"clearance_provider\":2,\"lakala_open_trade_params\":{\"fee_rate_original\":\"1.49\",\"merc_id\":\"928326056152DNJ\",\"fee_rate_tag\":{\"2\":\"15720:\"},\"fee\":0,\"active\":true,\"private_key\":\"eyZ@}1<ebn`'@b<@efbzZ18bfy}8fnzhe8`n\",\"bankcard_fee\":{\"dcc\":{\"fee\":\"1.49\"},\"edc\":{\"fee\":\"3.09\"},\"credit\":{\"fee\":\"0.55\"},\"debit\":{\"max\":2000,\"fee\":\"0.55\"}},\"term_id\":\"*************\",\"version\":\"v3\",\"fee_rate\":\"1.29\",\"liquidation_next_day\":true,\"term_no\":\"F9338807\",\"seria_no\":\"00dfba8194c41b84cf\",\"app_id\":\"OP00000003\",\"channel_id\":\"28\"},\"latitude\":\"31.309188\",\"merchant_id\":\"8270ef40-d47c-4e7b-abbe-5a4d2cde4168\",\"merchant_sn\":\"**************\",\"merchant_monthly_max_credit_limit_trans\":401,\"pay_status\":1,\"merchant_daily_max_credit_limit_trans\":301,\"provider\":1034,\"union_over_seas_wallet_day_tran_limit\":507,\"store_name\":\"商户门店名称1014cejA\",\"currency\":\"CNY\",\"terminal_id\":\"8b442d01-30f9-498b-a074-9be7fb332a32\",\"terminal_sn\":\"2101048790290737057\",\"longitude\":\"120.776461\",\"store_id\":\"f6ae3663-19de-4ecb-8a60-f0478864c1ac\",\"store_sn\":\"*****************\",\"channel_name\":\"上海收钱吧互联网科技股份有限公司\",\"store_city\":\"苏州市\",\"trade_app\":\"8221\",\"terminal_vendor_app_appid\":\"2022071100004879\",\"terminal_category\":101,\"union_over_seas_wallet_single_tran_limit\":506,\"merchant_name\":\"商户1014wUlg\",\"term_info\":{\"term_id\":\"F0254975\",\"term_type\":\"04\",\"serial_num\":\"c9bde37e-89fa-11ef-bba3-8e2756728eef\"},\"term_id\":\"F0254975\",\"terminal_name\":\"New Term (activateV2) 1728889569287\",\"district_code\":\"320506\",\"vendor_id\":\"859d9f5f-af99-11e5-9ec3-00163e00625b\",\"common_switch\":\"00000010000200002222222222222222\",\"merchant_country\":\"CHN\"},\"deleted\":false,\"effective_amount\":9,\"paid_amount\":9,\"trade_no\":\"99613443602557\",\"channel_finish_time\":1728959495000,\"order_id\":\"o****************\",\"buyer_uid\":\"6224****0052\",\"order_sn\":\"****************\",\"status\":2000}";

    String s1="{\n" +
            "    \"subject\": \"费率测试\",\n" +
            "    \"buyer_login\": \"wx1234567890\",\n" +
            "    \"received_amount\": 10000,\n" +
            "    \"merchant_id\": \"4b6769b2-f3b1-4f6c-a98b-52210b055748\",\n" +
            "    \"type\": 30,\n" +
            "    \"body\": null,\n" +
            "    \"mtime\": *************,\n" +
            "    \"extended_params\": {\n" +
            "        \n" +
            "    },\n" +
            "    \"tsn\": \"****************\",\n" +
            "    \"operator\": null,\n" +
            "    \"product_flag\": null,\n" +
            "    \"extra_out_fields\": {\n" +
            "        \"payments\": [\n" +
            "            {\n" +
            "                \"type\": \"BANKCARD_CREDIT\",\n" +
            "                \"origin_type\": \"ICBC_CREDIT\",\n" +
            "                \"amount\": 9900\n" +
            "            },\n" +
            "            {\n" +
            "                \"type\": \"DISCOUNT_CHANNEL\",\n" +
            "                \"origin_type\": \"COUPON\",\n" +
            "                \"amount\": 100,\n" +
            "                \"source\": \"74738091935000728537420788701024\"\n" +
            "            }\n" +
            "        ],\n" +
            "        \"combo_id\": \"2663\",\n" +
            "        \"weixin_appid\": \"wx72534f3638c59073\"\n" +
            "    },\n" +
            "    \"reflect\": null,\n" +
            "    \"provider\": 1033,\n" +
            "    \"original_amount\": 10000,\n" +
            "    \"ctime\": *************,\n" +
            "    \"id\": \"t****************\",\n" +
            "    \"terminal_id\": \"f2642d05-6cde-4b11-97e6-3f2e6091391a\",\n" +
            "    \"client_tsn\": \"1618822718111892201119774\",\n" +
            "    \"store_id\": \"8a3b3c52-3d40-4e43-baef-23e4bb6bc340\",\n" +
            "    \"provider_error_info\": {\n" +
            "        \"pay\": {\n" +
            "            \"return_msg\": \"处理成功\",\n" +
            "            \"result_code\": \"SUCCESS\",\n" +
            "            \"return_code\": \"SUCCESS\"\n" +
            "        }\n" +
            "    },\n" +
            "    \"extra_params\": {\n" +
            "        \"barcode\": \"13332183970404012\"\n" +
            "    },\n" +
            "    \"payway\": 3,\n" +
            "    \"finish_time\": 1657674299551,\n" +
            "    \"sub_payway\": 1,\n" +
            "    \"config_snapshot\": {\n" +
            "        \"clearance_provider\": 2,\n" +
            "        \"latitude\": \"31.309188\",\n" +
            "        \"store_client_sn\": null,\n" +
            "        \"merchant_id\": \"4b6769b2-f3b1-4f6c-a98b-52210b055748\",\n" +
            "        \"merchant_sn\": \"21690003346880\",\n" +
            "        \"pay_status\": 1,\n" +
            "        \"store_name\": \"业务开通门店名称07121456\",\n" +
            "        \"currency\": \"CNY\",\n" +
            "        \"terminal_id\": \"f2642d05-6cde-4b11-97e6-3f2e6091391a\",\n" +
            "        \"terminal_sn\": \"2101000010090417986\",\n" +
            "        \"longitude\": \"120.776461\",\n" +
            "        \"lkl_up_trade_params\": {\n" +
            "            \"fee_rate_tag\": {\n" +
            "                \"1\": \"2663: \"\n" +
            "            },\n" +
            "            \"fee\": 30,\n" +
            "            \"cert_id\": \"**********\",\n" +
            "            \"active\": true,\n" +
            "            \"weixin_appid\": \"wxd23604aba7ed0487\",\n" +
            "            \"weixin_mini_sub_appid\": \"wxccbcac9a3ece5112\",\n" +
            "            \"fee_rate\": \"0.3\",\n" +
            "            \"weixin_sub_mch_id\": \"1657609016008\",\n" +
            "            \"liquidation_next_day\": true,\n" +
            "            \"weixin_sub_appid\": \"wx72534f3638c59073\",\n" +
            "            \"channel_id\": \"32631798\",\n" +
            "            \"provider_mch_id\": \"cup_no1657609010846\"\n" +
            "        },\n" +
            "        \"store_id\": \"8a3b3c52-3d40-4e43-baef-23e4bb6bc340\",\n" +
            "        \"store_sn\": \"21590000000963042\",\n" +
            "        \"channel_name\": \"上海收钱吧互联网科技股份有限公司\",\n" +
            "        \"store_city\": \"苏州市\",\n" +
            "        \"trade_app\": \"2\",\n" +
            "        \"terminal_vendor_app_appid\": \"2020091800000001\",\n" +
            "        \"terminal_category\": null,\n" +
            "        \"merchant_name\": \"业务开通07121456\",\n" +
            "        \"terminal_name\": \"test-1118\",\n" +
            "        \"district_code\": \"320506\",\n" +
            "        \"vendor_id\": \"859d9f5f-af99-11e5-9ec3-00163e00625b\",\n" +
            "        \"common_switch\": \"00000011222222222222222222222222\",\n" +
            "        \"merchant_country\": \"CHN\",\n" +
            "        \"is_need_refund_fee_flag\": null,\n" +
            "        \"hit_payway\": null\n" +
            "    },\n" +
            "    \"effective_amount\": 10000,\n" +
            "    \"paid_amount\": 9900,\n" +
            "    \"trade_no\": \"95452782424742400153120769931139\",\n" +
            "    \"channel_finish_time\": 1657674299000,\n" +
            "    \"order_id\": \"o****************\",\n" +
            "    \"order_sn\": \"****************\",\n" +
            "    \"buyer_uid\": \"1234567890987654321\",\n" +
            "    \"status\": 2000\n" +
            "}";

    String s = "{\n" +
            "    \"subject\": \"费率测试\",\n" +
            "    \"buyer_login\": \"15988885620\",\n" +
            "    \"received_amount\": 468,\n" +
            "    \"merchant_id\": \"4b6769b2-f3b1-4f6c-a98b-52210b055748\",\n" +
            "    \"type\": 30,\n" +
            "    \"body\": null,\n" +
            "    \"mtime\": 1657674232569,\n" +
            "    \"extended_params\": {\n" +
            "        \n" +
            "    },\n" +
            "    \"tsn\": \"2003259240758127\",\n" +
            "    \"operator\": null,\n" +
            "    \"product_flag\": null,\n" +
            "    \"extra_out_fields\": {\n" +
            "        \"payments\": [\n" +
            "            {\n" +
            "                \"type\": \"DISCOUNT_CHANNEL_MCH\",\n" +
            "                \"origin_type\": \"MDISCOUNT\",\n" +
            "                \"amount\": 9532\n" +
            "            },\n" +
            "            {\n" +
            "                \"type\": \"DISCOUNT_CHANNEL\",\n" +
            "                \"origin_type\": \"DISCOUNT\",\n" +
            "                \"amount\": 444\n" +
            "            },\n" +
            "            {\n" +
            "                \"type\": \"ALIPAY_HUABEI\",\n" +
            "                \"origin_type\": \"PCREDIT\",\n" +
            "                \"amount\": 24\n" +
            "            }\n" +
            "        ],\n" +
            "        \"combo_id\": \"2663\"\n" +
            "    },\n" +
            "    \"reflect\": null,\n" +
            "    \"provider\": 1033,\n" +
            "    \"original_amount\": 10000,\n" +
            "    \"ctime\": 1657674232542,\n" +
            "    \"id\": \"t2003259240758127\",\n" +
            "    \"terminal_id\": \"f2642d05-6cde-4b11-97e6-3f2e6091391a\",\n" +
            "    \"client_tsn\": \"161882271111892201119774\",\n" +
            "    \"store_id\": \"8a3b3c52-3d40-4e43-baef-23e4bb6bc340\",\n" +
            "    \"provider_error_info\": {\n" +
            "        \"pay\": {\n" +
            "            \"msg\": \"处理成功\",\n" +
            "            \"code\": \"10000\"\n" +
            "        }\n" +
            "    },\n" +
            "    \"extra_params\": {\n" +
            "        \"barcode\": \"2813332183970404012\"\n" +
            "    },\n" +
            "    \"payway\": 2,\n" +
            "    \"finish_time\": 1657674232603,\n" +
            "    \"sub_payway\": 1,\n" +
            "    \"config_snapshot\": {\n" +
            "        \"clearance_provider\": 2,\n" +
            "        \"latitude\": \"31.309188\",\n" +
            "        \"store_client_sn\": null,\n" +
            "        \"merchant_id\": \"4b6769b2-f3b1-4f6c-a98b-52210b055748\",\n" +
            "        \"merchant_sn\": \"21690003346880\",\n" +
            "        \"pay_status\": 1,\n" +
            "        \"store_name\": \"业务开通门店名称07121456\",\n" +
            "        \"currency\": \"CNY\",\n" +
            "        \"terminal_id\": \"f2642d05-6cde-4b11-97e6-3f2e6091391a\",\n" +
            "        \"terminal_sn\": \"2101000010090417986\",\n" +
            "        \"longitude\": \"120.776461\",\n" +
            "        \"lkl_up_trade_params\": {\n" +
            "            \"provider_mch_id\": \"\",\n" +
            "            \"fee_rate_tag\": {\n" +
            "                \"1\": \"2663: \"\n" +
            "            },\n" +
            "            \"fee\": 0,\n" +
            "            \"alipay_sub_mch_id\": \"2081657609015955\",\n" +
            "            \"cert_id\": \"**********\",\n" +
            "            \"alipay_store_id\": null,\n" +
            "            \"active\": true,\n" +
            "            \"fee_rate\": \"0.1\",\n" +
            "            \"liquidation_next_day\": true,\n" +
            "            \"sys_pid\": \"2088421809493486\",\n" +
            "            \"service_id\": null,\n" +
            "            \"category\": null,\n" +
            "            \"app_id\": \"1266000048220000\"\n" +
            "        },\n" +
            "        \"store_id\": \"8a3b3c52-3d40-4e43-baef-23e4bb6bc340\",\n" +
            "        \"store_sn\": \"21590000000963042\",\n" +
            "        \"channel_name\": \"上海收钱吧互联网科技股份有限公司\",\n" +
            "        \"store_city\": \"苏州市\",\n" +
            "        \"trade_app\": \"2\",\n" +
            "        \"terminal_vendor_app_appid\": \"2020091800000001\",\n" +
            "        \"terminal_category\": null,\n" +
            "        \"merchant_name\": \"业务开通07121456\",\n" +
            "        \"terminal_name\": \"test-1118\",\n" +
            "        \"district_code\": \"320506\",\n" +
            "        \"vendor_id\": \"859d9f5f-af99-11e5-9ec3-00163e00625b\",\n" +
            "        \"common_switch\": \"00000011222222222222222222222222\",\n" +
            "        \"merchant_country\": \"CHN\",\n" +
            "        \"is_need_refund_fee_flag\": null,\n" +
            "        \"hit_payway\": null\n" +
            "    },\n" +
            "    \"effective_amount\": 10000,\n" +
            "    \"paid_amount\": 24,\n" +
            "    \"trade_no\": \"161657594809167245555\",\n" +
            "    \"channel_finish_time\": 1657674232000,\n" +
            "    \"order_id\": \"o2003259240758127\",\n" +
            "    \"order_sn\": \"2003259240758127\",\n" +
            "    \"buyer_uid\": \"2088102122524333\",\n" +
            "    \"status\": 2000\n" +
            "}";

    @Test
    public void handleBiz() {
    }
}
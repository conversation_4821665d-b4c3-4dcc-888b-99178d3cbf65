package com.wosai.trade.biz.activity;

import com.alibaba.fastjson.JSONObject;
import com.wosai.databus.event.audit.AuditInstanceApproveEvent;
import com.wosai.databus.event.audit.AuditInstanceCreateEvent;
import com.wosai.trade.BaseTest;
import com.wosai.trade.biz.activity.converter.ApplyActivityBuildParams;
import com.wosai.trade.service.ApplyActivityService;
import com.wosai.trade.util.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

public class AuditHandleBizTest extends BaseTest {
    @Autowired
    private AuditHandleBiz auditHandleBiz;
    @Autowired
    private ModifyActivityPaySourceFeeRateBiz modifyActivityPaySourceFeeRateBiz;
    @Autowired
    private ApplyActivityBuildParams applyActivityBuildParams;
    @Autowired
    private ApplyActivityService applyActivityService;


    @Test
    public void test(){

        AuditInstanceApproveEvent event = JSONObject.parseObject(s3, AuditInstanceApproveEvent.class);

        auditHandleBiz.auditApprove(event);

    }


    String s3="{\n" +
            "    \"auditId\": \"528911\",\n" +
            "    \"auditSn\": \"SP723820250114000025\",\n" +
            "    \"formId\": \"2834312\",\n" +
            "    \"finishTime\": 1736825533355,\n" +
            "    \"operatorPlatform\": \"SP\",\n" +
            "    \"module\": \"CRM\",\n" +
            "    \"auditTemplateId\": \"46301\",\n" +
            "    \"eventType\": \"AUDIT_APPROVE\",\n" +
            "    \"templateId\": \"355327\",\n" +
            "    \"operatorName\": \"公用测试账号\",\n" +
            "    \"objectType\": \"AUDIT\",\n" +
            "    \"auditCommentProperty\": {\n" +
            "        \"id\": \"65628094\",\n" +
            "        \"operatorName\": \"公用测试账号\",\n" +
            "        \"operator\": \"0af4121d-6cf5-19cc-816c-f66ffddc0000\",\n" +
            "        \"platform\": \"SP\"\n" +
            "    },\n" +
            "    \"bizKey\": \"SP_a6524e78-bfd9-44f2-bc3d-d307f6c330a3\",\n" +
            "    \"templateEvent\": \"fix_or_range_fee_rate_activity_apply\",\n" +
            "    \"ctime\": 1736825530000,\n" +
            "    \"auditCallBackProperty\": {\n" +
            "        \"resultFail\": 1\n" +
            "    },\n" +
            "    \"businessMap\": {\n" +
            "        \"apply_merchant\": \"multiple\",\n" +
            "        \"activity_id\": \"10112\",\n" +
            "        \"fileurl\": [\n" +
            "            \"http://wosai-sales.oss-cn-hangzhou.aliyuncs.com/workflow/mch*************%E8%B4%B9%E7%8E%87%E8%B0%83%E6%95%B41736825526630.xlsx?type=file\"\n" +
            "        ],\n" +
            "        \"action\": \"apply\",\n" +
            "        \"commentdetails\": {\n" +
            "            \"id\": \"65628094\",\n" +
            "            \"operatorName\": \"公用测试账号\",\n" +
            "            \"operator\": \"0af4121d-6cf5-19cc-816c-f66ffddc0000\",\n" +
            "            \"platform\": \"SP\"\n" +
            "        },\n" +
            "        \"remark\": \"test....\",\n" +
            "        \"audit_info\": {\n" +
            "            \"id\": \"528911\",\n" +
            "            \"sn\": \"SP723820250114000025\",\n" +
            "            \"operator\": \"0af4121d-6cf5-19cc-816c-f66ffddc0000\",\n" +
            "            \"platform\": \"SP\",\n" +
            "            \"status\": \"2\",\n" +
            "            \"time\": 1736825533000,\n" +
            "            \"template_id\": 46301,\n" +
            "            \"template_name\": \"费率活动通用申请\\t\"\n" +
            "        }\n" +
            "    },\n" +
            "    \"operatorId\": \"0af4121d-6cf5-19cc-816c-f66ffddc0000\",\n" +
            "    \"seq\": 0,\n" +
            "    \"timestamp\": 1736825533386\n" +
            "}";

    String s1="{\n" +
            "    \"auditId\": \"106411\",\n" +
            "    \"auditSn\": \"SP723820220706000010\",\n" +
            "    \"formId\": \"66357\",\n" +
            "    \"finishTime\": 1657100693707,\n" +
            "    \"operatorPlatform\": \"SP\",\n" +
            "    \"module\": \"CRM\",\n" +
            "    \"auditTemplateId\": \"46301\",\n" +
            "    \"eventType\": \"AUDIT_APPROVE\",\n" +
            "    \"templateId\": \"72529\",\n" +
            "    \"operatorName\": \"公用测试账号\",\n" +
            "    \"objectType\": \"AUDIT\",\n" +
            "    \"auditCommentProperty\": {\n" +
            "        \"comment\": \"VS\",\n" +
            "        \"id\": \"64674770\",\n" +
            "        \"operatorName\": \"公用测试账号\",\n" +
            "        \"operator\": \"0af4121d-6cf5-19cc-816c-f66ffddc0000\",\n" +
            "        \"platform\": \"SP\"\n" +
            "    },\n" +
            "    \"bizKey\": \"SP_7010d0b7-4840-49e9-acf9-ad813b0eabb5\",\n" +
            "    \"templateEvent\": \"fix_or_range_fee_rate_activity_apply\",\n" +
            "    \"ctime\": 1657100673000,\n" +
            "    \"auditCallBackProperty\": {\n" +
            "        \"resultFail\": 1\n" +
            "    },\n" +
            "    \"businessMap\": {\n" +
            "        \"apply_merchant\": \"single\",\n" +
            "        \"activity_id\": \"1166\",\n" +
            "        \"action\": \"apply\",\n" +
            "        \"merchant\": {\n" +
            "            \"merch_info.sn\": {\n" +
            "                \"keyName\": \"商户号\",\n" +
            "                \"value\": \"21690003254048\"\n" +
            "            },\n" +
            "            \"merch_info.name\": {\n" +
            "                \"keyName\": \"商户名称\",\n" +
            "                \"value\": \"苏州存量商户\"\n" +
            "            }\n" +
            "        },\n" +
            "        \"commentdetails\": {\n" +
            "            \"comment\": \"VS\",\n" +
            "            \"id\": \"64674770\",\n" +
            "            \"operatorName\": \"公用测试账号\",\n" +
            "            \"operator\": \"0af4121d-6cf5-19cc-816c-f66ffddc0000\",\n" +
            "            \"platform\": \"SP\"\n" +
            "        },\n" +
            "        \"remark\": \"测试\",\n" +
            "        \"pay_way_list\": [\n" +
            "            \"微信\",\n" +
            "            \"支付宝\"\n" +
            "        ],\n" +
            "        \"audit_info\": {\n" +
            "            \"id\": \"106411\",\n" +
            "            \"sn\": \"SP723820220706000010\",\n" +
            "            \"operator\": \"0af4121d-6cf5-19cc-816c-f66ffddc0000\",\n" +
            "            \"platform\": \"SP\",\n" +
            "            \"comment\": \"VS\",\n" +
            "            \"status\": \"2\",\n" +
            "            \"time\": 1657100694000,\n" +
            "            \"template_id\": 46301,\n" +
            "            \"template_name\": \"费率活动通用申请\\t\"\n" +
            "        }\n" +
            "    },\n" +
            "    \"operatorId\": \"0af4121d-6cf5-19cc-816c-f66ffddc0000\",\n" +
            "    \"seq\": 978802,\n" +
            "    \"timestamp\": 1657100695735\n" +
            "}";

    @Test
    public void testPaySourceAuditApprove() {

        AuditInstanceCreateEvent event = JSONObject.parseObject(s, AuditInstanceCreateEvent.class);


        auditHandleBiz.paySourceAuditCreate(event);
    }

    String s2="{\"auditId\":\"677978\",\"auditSn\":\"SP1953420250827000007\",\"formId\":\"2983209\",\"operatorPlatform\":\"SP\",\"module\":\"CRM\",\"auditTemplateId\":\"295785\",\"eventType\":\"AUDIT_CREATE\",\"templateId\":\"415994\",\"operatorName\":\"公用测试账号\",\"objectType\":\"AUDIT\",\"bizKey\":\"SP_a904777e-ec95-41f6-99bc-49cce5acaa7d\",\"templateEvent\":\"wechat_university_activity_rate_platform_23_V2\",\"ctime\":1756277186000,\"auditCallBackProperty\":{\"resultFail\":1},\"businessMap\":{\"parent_merchant\":{\"business_license.license_name\":{\"keyName\":\"营业执照名称\",\"value\":\"商户个体工商户营业执照\"},\"merch_info.sn\":{\"keyName\":\"商户号\",\"value\":\"21690004168547\"},\"business_license.license_number\":{\"keyName\":\"营业执照号码\",\"value\":\"92912786739159511Q\"}},\"merchant_info\":{\"merch_info.sn\":{\"keyName\":\"商户号\",\"value\":\"21690004182756\"}},\"audit_info\":{\"id\":\"677978\",\"sn\":\"SP1953420250827000007\",\"operator\":\"0af4121d-6cf5-19cc-816c-f66ffddc0000\",\"platform\":\"SP\",\"comment\":\"\",\"status\":\"1\",\"time\":1756277185000,\"template_id\":295785,\"template_name\":\"微信间连高校食堂报名（主体一致）\"},\"apply_type\":\"inherited\"},\"operatorId\":\"0af4121d-6cf5-19cc-816c-f66ffddc0000\",\"seq\":0,\"timestamp\":1756277186000}";


    String s="{\n" +
            "    \"auditId\": \"677980\",\n" +
            "    \"auditSn\": \"SP1953420250827000008\",\n" +
            "    \"formId\": \"2983211\",\n" +
            "    \"operatorPlatform\": \"SP\",\n" +
            "    \"module\": \"CRM\",\n" +
            "    \"auditTemplateId\": \"298699\",\n" +
            "    \"eventType\": \"AUDIT_CREATE\",\n" +
            "    \"templateId\": \"419899\",\n" +
            "    \"operatorName\": \"公用测试账号\",\n" +
            "    \"objectType\": \"AUDIT\",\n" +
            "    \"bizKey\": \"SP_fbcde3e6-c941-4957-9a5a-6250ef76a446\",\n" +
            "    \"templateEvent\": \"alipay_campus_policy_h2_rate_platform_V2\",\n" +
            "    \"ctime\": 1756279714000,\n" +
            "    \"auditCallBackProperty\": {\n" +
            "        \"resultFail\": 1\n" +
            "    },\n" +
            "    \"businessMap\": {\n" +
            "        \"activity_info\": \"1302\",\n" +
            "        \"school_type\": \"private_school\",\n" +
            "        \"schoolAddress\": [\n" +
            "            {\n" +
            "                \"province\": \"江苏省\",\n" +
            "                \"city\": \"苏州市\",\n" +
            "                \"district\": \"吴中区\"\n" +
            "            }\n" +
            "        ],\n" +
            "        \"merchant_info\": {\n" +
            "            \"business_license.license_name\": {\n" +
            "                \"keyName\": \"营业执照名称\",\n" +
            "                \"value\": \"商户个体工商户营业执照\"\n" +
            "            },\n" +
            "            \"merch_info.sn\": {\n" +
            "                \"keyName\": \"商户号\",\n" +
            "                \"value\": \"21690004168547\"\n" +
            "            },\n" +
            "            \"acquirer\": {\n" +
            "                \"keyName\": \"收单机构\",\n" +
            "                \"value\": \"拉卡拉\"\n" +
            "            },\n" +
            "            \"merch_info.name\": {\n" +
            "                \"keyName\": \"商户名称\",\n" +
            "                \"value\": \"商户0814rhUT\"\n" +
            "            },\n" +
            "            \"extra.industry_name\": {\n" +
            "                \"keyName\": \"所属行业\",\n" +
            "                \"value\": \"餐饮快餐\"\n" +
            "            },\n" +
            "            \"audit.status\": {\n" +
            "                \"keyName\": \"商户认证状态\",\n" +
            "                \"value\": \"认证通过\"\n" +
            "            },\n" +
            "            \"business_license.license_number\": {\n" +
            "                \"keyName\": \"营业执照号码\",\n" +
            "                \"value\": \"92912786739159511Q\"\n" +
            "            }\n" +
            "        },\n" +
            "        \"audit_info\": {\n" +
            "            \"id\": \"677980\",\n" +
            "            \"sn\": \"SP1953420250827000008\",\n" +
            "            \"operator\": \"0af4121d-6cf5-19cc-816c-f66ffddc0000\",\n" +
            "            \"platform\": \"SP\",\n" +
            "            \"comment\": \"\",\n" +
            "            \"status\": \"1\",\n" +
            "            \"time\": 1756279714000,\n" +
            "            \"template_id\": 298699,\n" +
            "            \"template_name\": \"支付宝间连校园食堂活动（主体一致）\\t\"\n" +
            "        },\n" +
            "        \"outdoor\": [\n" +
            "            \"http://wosai-sales.oss-cn-hangzhou.aliyuncs.com/workflow/202501021751571756279656136-2713b8ef3ff2b11dabe488590aab6189.jpeg?type=file\"\n" +
            "        ],\n" +
            "        \"instStdCode\": \"328323232323\",\n" +
            "        \"qualification\": [\n" +
            "            \"http://wosai-sales.oss-cn-hangzhou.aliyuncs.com/workflow/202501021751571756279638292-2713b8ef3ff2b11dabe488590aab6189.jpeg?type=file\"\n" +
            "        ],\n" +
            "        \"learningStage\": [\n" +
            "            \"高职高专\"\n" +
            "        ],\n" +
            "        \"financePic\": [\n" +
            "            \"http://wosai-sales.oss-cn-hangzhou.aliyuncs.com/workflow/202501021751571756279674963-2713b8ef3ff2b11dabe488590aab6189.jpeg?type=file\"\n" +
            "        ],\n" +
            "        \"indoor\": [\n" +
            "            \"http://wosai-sales.oss-cn-hangzhou.aliyuncs.com/workflow/202501021751571756279666306-2713b8ef3ff2b11dabe488590aab6189.jpeg?type=file\"\n" +
            "        ],\n" +
            "        \"action\": \"apply\",\n" +
            "        \"schoolName\": \"金额就觉得大学\",\n" +
            "        \"merchant_type\": \"group_meal\",\n" +
            "        \"apply_type\": \"same_entity\"\n" +
            "    },\n" +
            "    \"operatorId\": \"0af4121d-6cf5-19cc-816c-f66ffddc0000\",\n" +
            "    \"seq\": 0,\n" +
            "    \"timestamp\": 1756279714452\n" +
            "}";


    @Test
    public void modifyPaySourceFeeRate() {
        AuditInstanceApproveEvent event = JsonUtil.decode(moStr, AuditInstanceApproveEvent.class);
        auditHandleBiz.modifyPaySourceFeeRate(event);
    }

    @Test
    public void modifyPaySourceLadderFeeRate() {
        AuditInstanceApproveEvent event = JsonUtil.decode(moStr2, AuditInstanceApproveEvent.class);
        auditHandleBiz.modifyPaySourceLadderFeeRate(event);
    }

    @Test
    public void verifyModifyPaySourceFeeRate() {
        Map<String, Object> req = JsonUtil.decode(moStr, Map.class);
        modifyActivityPaySourceFeeRateBiz.verifyModifyPaySourceFeeRate(req);
    }

    public static final String moStr = "{\"auditId\":\"158731\",\"auditSn\":\"SP1953420230331000006\",\"formId\":\"2464167\",\"finishTime\":1680249714594,\"operatorPlatform\":\"SP\",\"module\":\"CRM\",\"auditTemplateId\":\"71119\",\"eventType\":\"AUDIT_APPROVE\",\"templateId\":\"104972\",\"operatorName\":\"邵一帆测试\",\"objectType\":\"AUDIT\",\"auditCommentProperty\":{\"comment\":\"11\",\"id\":\"64789245\",\"operatorName\":\"邵一帆测试\",\"operator\":\"c0a893bf-7b39-143a-817b-4879951e0e3c\",\"platform\":\"SP\"},\"bizKey\":\"SP_8369bfef-5b82-4989-8fee-f64c657e81c8\",\"templateEvent\":\"modify_pay_source_fee_rate\",\"ctime\":1680249703000,\"auditCallBackProperty\":{\"resultFail\":0},\"businessMap\":{\"activity_info\":\"3612\",\"merchant\":{\"merch_info.sn\":{\"keyName\":\"商户号\",\"value\":\"21690003482151\"},\"merch_info.name\":{\"keyName\":\"商户名称\",\"value\":\"业务开通1208Nnxp\"},\"extra.industry_name\":{\"keyName\":\"所属行业\",\"value\":\"餐饮日韩料理\"}},\"commentdetails\":{\"comment\":\"11\",\"id\":\"64789245\",\"operatorName\":\"邵一帆测试\",\"operator\":\"c0a893bf-7b39-143a-817b-4879951e0e3c\",\"platform\":\"SP\"},\"remark\":\"qaz\",\"feeRate\":0.27,\"feeRateType\":\"FIXED\",\"audit_info\":{\"id\":\"158731\",\"sn\":\"SP1953420230331000006\",\"operator\":\"c0a893bf-7b39-143a-817b-4879951e0e3c\",\"platform\":\"SP\",\"comment\":\"11\",\"status\":\"2\",\"time\":1680249715000,\"template_id\":71119,\"template_name\":\"微信行业活动费率申请\"}},\"operatorId\":\"c0a893bf-7b39-143a-817b-4879951e0e3c\",\"seq\":0,\"timestamp\":1680249714631}";
    public static final String moStr2 = "{\"module\":\"CRM\",\"objectType\":\"AUDIT\",\"eventType\":\"AUDIT_CREATE\",\"operatorId\":\"3685adda-0c78-4236-b6f0-cbc2477b6789\",\"operatorPlatform\":\"CRM\",\"auditId\":\"435115\",\"auditSn\":\"SP23015720240814000002\",\"auditTemplateId\":\"213449\",\"templateId\":\"305625\",\"formId\":\"2740516\",\"operatorName\":\"核心业务CRM账号\",\"operatorOrgCode\":\"00003\",\"operatorOrgNamePath\":\"直营\",\"ctime\":1723601567000,\"templateEvent\":\"modify_pay_source_ladder_fee_rate\",\"bizKey\":\"CRM_c1b0929c-81b9-4f4a-a6b5-1d08f88ec813\",\"businessMap\":{\"ladder_list\":[{\"ladder:0-30\":\"0.21\",\"ladder:30-**********\":\"0.31\",\"pay_way_list\":[\"支付宝\"]}],\"activity_info\":\"9254:6450\",\"merchant\":{\"business_license.license_name\":{\"keyName\":\"营业执照名称\"},\"merch_info.sn\":{\"keyName\":\"商户号\",\"value\":\"21690003623851\"},\"acquirer\":{\"keyName\":\"收单机构\",\"value\":\"lkl\"},\"merch_info.name\":{\"keyName\":\"商户名称\",\"value\":\"业务开通1007LXwi\"}},\"action\":\"apply\",\"audit_info\":{\"id\":\"435115\",\"sn\":\"SP23015720240814000002\",\"operator\":\"3685adda-0c78-4236-b6f0-cbc2477b6789\",\"platform\":\"CRM\",\"comment\":\"\",\"status\":\"1\",\"time\":1723601567000,\"template_id\":213449,\"template_name\":\"支付宝高校食堂调价审批\"}},\"auditCallBackProperty\":{\"resultFail\":0}}";

    @Test
    public void doApplyAuditApprove() {
        AuditInstanceApproveEvent event = JsonUtil.decode(cancelEventStr, AuditInstanceApproveEvent.class);
        auditHandleBiz.auditApprove(event);
    }

    @Test
    public void takeEffect() {
        applyActivityService.takeEffect(98159415367L);
    }

    @Test
    public void testChannelFeeRate() {
        String arguments = "{\"debit_max\":23,\"audit.operator_id\":\"3685adda-0c78-4236-b6f0-cbc2477b6789\",\"activity_info\":\"10287\",\"audit.audit_template_id\":244889,\"merchant\":{\"merch_info.sn\":{\"keyName\":\"商户号\",\"value\":\"mch-*************\"},\"merch_info.name\":{\"keyName\":\"商户名称\",\"value\":\"上海特产专卖店\"}},\"remark\":\"ss\",\"debit_fee_rate\":0.5,\"credit_fee_rate\":0.5}";
        Map map = JsonUtil.decode(arguments, Map.class);
        applyActivityBuildParams.buildApplyActivityByChannelFeeRate(map);

    }

    String eventStr = "{\"auditId\":\"509438\",\"auditSn\":\"SP359720241217000003\",\"formId\":\"2814839\",\"finishTime\":1734420660022,\"operatorPlatform\":\"CRM\",\"module\":\"CRM\",\"auditTemplateId\":\"244889\",\"operatorOrgNamePath\":\"直营\",\"eventType\":\"AUDIT_APPROVE\",\"templateId\":\"347114\",\"operatorName\":\"核心业务CRM账号\",\"objectType\":\"AUDIT\",\"auditCommentProperty\":{\"id\":\"********\",\"operatorName\":\"核心业务CRM账号\",\"operator\":\"3685adda-0c78-4236-b6f0-cbc2477b6789\",\"platform\":\"CRM\"},\"operatorOrgCode\":\"00003\",\"bizKey\":\"CRM_87ab15ab-de77-44b5-9ee1-10efa5de161d\",\"templateEvent\":\"bankcard_channel_activity_apply\",\"ctime\":*************,\"auditCallBackProperty\":{\"resultFail\":0},\"businessMap\":{\"debit_max\":25,\"apply_merchant\":\"single\",\"activity_info\":\"10287\",\"action\":\"apply\",\"merchant\":{\"merch_info.sn\":{\"keyName\":\"商户号\",\"value\":\"mch-*************\"},\"merch_info.name\":{\"keyName\":\"商户名称\",\"value\":\"上海特产专卖店\"}},\"commentdetails\":{\"id\":\"********\",\"operatorName\":\"核心业务CRM账号\",\"operator\":\"3685adda-0c78-4236-b6f0-cbc2477b6789\",\"platform\":\"CRM\"},\"debit_fee_rate\":0.6,\"remark\":\"ss\",\"credit_fee_rate\":0.55,\"audit_info\":{\"id\":\"509438\",\"sn\":\"SP359720241217000003\",\"operator\":\"3685adda-0c78-4236-b6f0-cbc2477b6789\",\"platform\":\"CRM\",\"status\":\"2\",\"time\":*************,\"template_id\":244889,\"template_name\":\"POS-T9商户优惠费率\"}},\"operatorId\":\"3685adda-0c78-4236-b6f0-cbc2477b6789\",\"seq\":1187976,\"timestamp\":*************}";


    String cancelEventStr = "{\"auditId\":\"510530\",\"auditSn\":\"SP359720241219000001\",\"formId\":\"2815931\",\"finishTime\":1734580176287,\"operatorPlatform\":\"CRM\",\"module\":\"CRM\",\"auditTemplateId\":\"244889\",\"operatorOrgNamePath\":\"直营\",\"eventType\":\"AUDIT_APPROVE\",\"templateId\":\"347114\",\"operatorName\":\"核心业务CRM账号\",\"objectType\":\"AUDIT\",\"auditCommentProperty\":{\"id\":\"********\",\"operatorName\":\"核心业务CRM账号\",\"operator\":\"3685adda-0c78-4236-b6f0-cbc2477b6789\",\"platform\":\"CRM\"},\"operatorOrgCode\":\"00003\",\"bizKey\":\"CRM_72e4b8dd-0982-4a7b-8138-31eb05932bde\",\"templateEvent\":\"bankcard_channel_activity_apply\",\"ctime\":*************,\"auditCallBackProperty\":{\"resultFail\":0},\"businessMap\":{\"debit_max\":22,\"apply_merchant\":\"single\",\"activity_info\":\"10287\",\"action\":\"cancel\",\"merchant\":{\"merch_info.sn\":{\"keyName\":\"商户号\",\"value\":\"mch-*************\"},\"merch_info.name\":{\"keyName\":\"商户名称\",\"value\":\"上海特产专卖店\"}},\"commentdetails\":{\"id\":\"********\",\"operatorName\":\"核心业务CRM账号\",\"operator\":\"3685adda-0c78-4236-b6f0-cbc2477b6789\",\"platform\":\"CRM\"},\"debit_fee_rate\":0.4,\"remark\":\"222\",\"credit_fee_rate\":0.4,\"audit_info\":{\"id\":\"510530\",\"sn\":\"SP359720241219000001\",\"operator\":\"3685adda-0c78-4236-b6f0-cbc2477b6789\",\"platform\":\"CRM\",\"status\":\"2\",\"time\":*************,\"template_id\":244889,\"template_name\":\"POS-T9商户优惠费率\"}},\"operatorId\":\"3685adda-0c78-4236-b6f0-cbc2477b6789\",\"seq\":1188703,\"timestamp\":*************}";
}
package com.wosai.trade.biz.activity;

import com.wosai.trade.BaseTest;

import com.wosai.trade.repository.dao.QuotaActivityApplyEntityMapper;
import com.wosai.trade.repository.dao.entity.QuotaActivityApplyEntity;
import com.wosai.trade.repository.dao.entity.QuotaActivityEntity;
import com.wosai.trade.service.FeeRateService;
import com.wosai.trade.service.request.ApplyFeeRateRequest;
import com.wosai.trade.service.request.CancelFeeRateRequest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class QuotaApplyActivityCheckBizTest  extends BaseTest {

    @Autowired
    private QuotaApplyActivityCheckBiz  checkBiz;
    @Autowired
    QuotaActivityApplyEntityMapper applyMapper;
    @Autowired
    private FeeRateService feeRateService;

    @Test
    public void testCheckEffectiveBizRule() {

        QuotaActivityApplyEntity apply = applyMapper.selectByPrimaryKey(12L);

        QuotaActivityEntity entity = apply.buildActivityInfo();

        checkBiz.checkEffectiveBizRule(apply.getMerchant_sn(), apply.getMerchant_id(),entity);

    }



    @Test
    public void cancelCombo(){

        CancelFeeRateRequest cancel=new CancelFeeRateRequest();
        cancel.setMerchantSn("21690003434106");
        cancel.setTradeComboId(3334L);
        cancel.setAuditSn("yes");

        feeRateService.cancelFeeRate(cancel);

    }

    @Test
    public void effectCombo(){

        ApplyFeeRateRequest request = new ApplyFeeRateRequest();
        request.setMerchantSn("21690003434106");
        request.setTradeComboId(3270L);
        request.setAuditSn("yes");

        feeRateService.applyFeeRateOne(request);

    }

    @Test
    public void checkCloseActivityRuleSwitch() {
        boolean result = checkBiz.checkCloseActivityRuleSwitch("21690003254043", 5348L, 1);
        System.out.println(result);
    }
}
package com.wosai.trade.biz.activity;

import com.wosai.trade.BaseTest;
import com.wosai.trade.repository.dao.QuotaActivityApplyEntityMapper;
import com.wosai.trade.repository.dao.entity.QuotaActivityApplyEntity;
import org.junit.Test;

import javax.annotation.Resource;


public class QuotaActivityBusinessOpLogBizTestConstant extends BaseTest {

    @Resource
    private QuotaActivityApplyEntityMapper quotaActivityApplyEntityMapper;
    @Resource
    private QuotaActivityBusinessLogBiz quotaActivityBusinessLogBiz;

    @Test
    public void effectActivityApply() {
        QuotaActivityApplyEntity applyDO = quotaActivityApplyEntityMapper.selectByPrimaryKey(57772L);
        quotaActivityBusinessLogBiz.effectActivityApply(applyDO);
    }

    @Test
    public void cancelActivityApply() {
        QuotaActivityApplyEntity applyDO = quotaActivityApplyEntityMapper.selectByPrimaryKey(55979L);
        quotaActivityBusinessLogBiz.cancelActivityApply(applyDO, "system", "", "");
    }
}
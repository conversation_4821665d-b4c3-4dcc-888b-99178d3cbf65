package com.wosai.trade.biz.activity;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.wosai.sales.core.service.OrganizationService;
import com.wosai.trade.BaseTest;
import com.wosai.trade.repository.dao.ActivityApplyDOMapper;
import com.wosai.trade.repository.dao.ActivityDOMapper;
import com.wosai.trade.repository.dao.entity.ActivityApplyEntity;
import com.wosai.trade.repository.dao.entity.ActivityEntity;
import com.wosai.trade.service.activity.request.ActivityRule;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.job.model.dto.AcquirerDto;
import com.wosai.upay.job.service.ContractManagerService;
import com.wosai.web.api.ListResult;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public class ActivityBizTest extends BaseTest {
    @Autowired
    private ActivityBiz activityBiz;
    @Autowired
    private ActivityDOMapper activityDOMapper;
    @Autowired
    private ActivityApplyDOMapper applyDOMapper;

    @Test
    public void auditCreateActivity() {
        ActivityEntity activityEntity = activityDOMapper.selectByPrimaryKey(2660L);
        System.out.println(JSONObject.toJSONString(activityBiz.auditCreateActivity(activityEntity, "")));
    }

    @Test
    public void auditUpdateActivity() {
        ActivityEntity activityEntity = activityDOMapper.selectByPrimaryKey(2660L);
        Map<String, String> auditResult = activityBiz.auditUpdateActivity(activityEntity, activityEntity, "");
        System.out.println(auditResult);
    }

    @Test
    public void testD() {
        ActivityEntity activityEntity = activityDOMapper.selectByPrimaryKey(1309L);
        System.out.println(JSONObject.toJSONString(activityBiz.auditCreateActivity(activityEntity, "0af41a29-7327-1663-8173-28034f720000")));
    }

    @Test
    public void testD001() {
        ActivityApplyEntity effectIngApply = applyDOMapper.getEffectIngApply(1201L, "d09b026a-d3d0-4055-a855-eab295c17dfb");
        System.out.println("====");
          ActivityApplyEntity effectIngApply1 = applyDOMapper.getEffectIngApplyByMerchantSn(1201L, "21690003335697");
          System.out.println(JSONObject.toJSONString(effectIngApply));
          System.out.println(JSONObject.toJSONString(effectIngApply1));
    }


    @Test
    public void buildIndustryRule() {
        ArrayList<String> denyList = Lists.newArrayList(
                "7198ee72-7371-422e-833c-eb6ddd9be5b1",
                "68464d77-611f-4635-b085-a25453a598a4",
                "747f3e28-312d-11e6-aebb-ecf4bbdee2f0",
                "29a5b208-c27c-4bac-9688-4d4375bb9f0c",
                "749ccf48-312d-11e6-aebb-ecf4bbdee2f0",
                "c1504cd2-84cd-4e23-b622-db3c008f37cc",
                "74b7d131-312d-11e6-aebb-ecf4bbdee2f0",
                "74c5a7b5-312d-11e6-aebb-ecf4bbdee2f0",
                "1cf81fcb-51e3-40de-bc84-aa7eacf46b7d",
                "def0642b-7914-4a55-be04-599ba233036c",
                "748ecbb6-312d-11e6-aebb-ecf4bbdee2f0",
                "27dc0e3d-834d-455b-b9b6-bfcd5ecffcbd",
                "74aa94ca-312d-11e6-aebb-ecf4bbdee2f0",
                "cbd824f3-c294-435d-b5db-385ce3fa654d",
                "db7794bc-aabc-4eaa-9ed5-09d1fc0e5513",
                "cfe3f32f-960f-44dd-8302-ee0937b9a27a",
                "05048c2c-0a49-4719-b8e2-1ffc77971e3b", "abcde3");
        String val = activityBiz.buildIndustryRule(new ActivityRule.IndustryRule().setDenyList(denyList));
        System.out.println(val);
    }
    @Resource
    private OrganizationService organizationService;

    @Autowired
    private ContractManagerService contractManagerService;
    @Test
    public void testOrg() {
        /*Map<String, Map> list = organizationService.getOrganizationByCode(ImmutableList.of("85000"));
        System.out.println(list);*/

        List<String> names = ImmutableList.of("icbc", "hxb").stream().map(code -> {
            PageInfo pageInfo = new PageInfo();
            pageInfo.setPage(1);
            pageInfo.setPageSize(1);
            AcquirerDto acquirerDto = new AcquirerDto();
            acquirerDto.setAcquirer(code);
            ListResult<AcquirerDto> acquirerList = contractManagerService.findAcquirerList(pageInfo, acquirerDto);
            if (Objects.isNull(acquirerList) || CollectionUtils.isEmpty(acquirerList.getRecords())) {
                return null;
            }
            return acquirerList.getRecords().get(0).getName();
        }).filter(Objects::nonNull).collect(Collectors.toList());
        System.out.println(names);
    }
}
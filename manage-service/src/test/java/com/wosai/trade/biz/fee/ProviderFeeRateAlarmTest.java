package com.wosai.trade.biz.fee;


import com.fasterxml.jackson.core.type.TypeReference;
import com.wosai.trade.BaseTest;
import com.wosai.trade.biz.fee.model.ProviderFeeRateSyncResponse;
import com.wosai.trade.client.LarkClient;
import com.wosai.trade.util.JsonUtil;
import com.wosai.trade.util.SpringBeanUtil;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.avro.FeeRateResult;
import org.apache.commons.lang3.math.NumberUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest(SpringBeanUtil.class)
public class ProviderFeeRateAlarmTest extends BaseTest {

    @Resource
    private ProviderFeeRateSyncBiz providerFeeRateSyncBiz;

    @Mock
    private TradeConfigService tradeConfigService;
    @Mock
    private LarkClient larkClient;


    @Resource
    private ProviderDiffFeeTransactionBiz providerDiffFeeTransactionBiz;

    @Test
    public void providerDiffFeeTransaction() {
        providerDiffFeeTransactionBiz.diffAlarm();
    }

    @Test
    public void test() {
        String str = "{\"provider\": \"1038\", \"result\": true, \"merchantSn\": \"21690003629637\", \"feeRate\": \"[{\\\"c2b_fee_rate\\\":\\\"0.38\\\",\\\"payway\\\":2,\\\"b2c_fee_rate\\\":\\\"0.38\\\",\\\"wap_fee_rate\\\":\\\"0.38\\\",\\\"fee_rate_type\\\":\\\"fixed\\\"},{\\\"c2b_fee_rate\\\":\\\"0.43\\\",\\\"payway\\\":3,\\\"b2c_fee_rate\\\":\\\"0.43\\\",\\\"wap_fee_rate\\\":\\\"0.43\\\",\\\"fee_rate_type\\\":\\\"fixed\\\"},{\\\"ladder_status\\\":2,\\\"c2b_fee_rate\\\":\\\"0.4\\\",\\\"payway\\\":17,\\\"b2c_fee_rate\\\":\\\"0.4\\\",\\\"wap_fee_rate\\\":\\\"0.4\\\"},{\\\"ladder_status\\\":2,\\\"c2b_fee_rate\\\":\\\"0.4\\\",\\\"payway\\\":18,\\\"b2c_fee_rate\\\":\\\"0.4\\\",\\\"wap_fee_rate\\\":\\\"0.4\\\"},{\\\"c2b_fee_rate\\\":\\\"0.38\\\",\\\"payway\\\":5,\\\"b2c_fee_rate\\\":\\\"0.38\\\",\\\"wap_fee_rate\\\":\\\"0.38\\\",\\\"fee_rate_type\\\":\\\"fixed\\\"}]\"}";
        ProviderFeeRateSyncResponse response = buildProviderFeeRateSyncResponse(JsonUtil.decode(str, FeeRateResult.class));
        providerFeeRateSyncBiz.providerSyncFinish(response);
    }


    private ProviderFeeRateSyncResponse buildProviderFeeRateSyncResponse(FeeRateResult feeRateResult) {
        String feeRateStr = feeRateResult.getFeeRate() + "";
        List<ProviderFeeRateSyncResponse.FeeRateResult> feeRateResultList = JsonUtil.decode(feeRateStr, new TypeReference<List<ProviderFeeRateSyncResponse.FeeRateResult>>() {
        });
        ProviderFeeRateSyncResponse response = new ProviderFeeRateSyncResponse();
        response.setProvider(Objects.isNull(feeRateResult.getProvider()) ? null : NumberUtils.toInt(feeRateResult.getProvider() + ""));
        response.setResult(feeRateResult.getResult());
        response.setMerchantSn(feeRateResult.getMerchantSn() + "");
        response.setFeeRateList(feeRateResultList);
        return response;
    }
}

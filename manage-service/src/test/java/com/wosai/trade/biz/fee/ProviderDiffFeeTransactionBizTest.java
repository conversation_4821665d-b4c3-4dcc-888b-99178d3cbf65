package com.wosai.trade.biz.fee;

import com.wosai.trade.BaseTest;
import com.wosai.trade.biz.fee.model.SyncForProviderResponse;
import com.wosai.trade.util.JsonUtil;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

public class ProviderDiffFeeTransactionBizTest extends BaseTest {

    @Resource
    private ProviderDiffFeeTransactionBiz providerDiffFeeTransactionBiz;

    @Test
    public void fullSyncForProvider() {
        List<SyncForProviderResponse> result = providerDiffFeeTransactionBiz.fullSyncForProvider("21690003629637", 1038);
        System.out.println(JsonUtil.encode(result));
    }
}
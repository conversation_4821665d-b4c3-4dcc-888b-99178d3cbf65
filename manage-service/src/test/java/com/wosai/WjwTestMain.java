package com.wosai;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.MapUtils;
import com.wosai.pantheon.util.MapUtil;
import lombok.SneakyThrows;

import java.io.File;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by w<PERSON><PERSON><PERSON><PERSON> on 2023/5/31.
 */
public class WjwTestMain {

    @SneakyThrows
    public static void main(String[] args) {
        List<Map<String,Object>> list = new ObjectMapper().readValue(new File("/Users/<USER>/work/wosai/documents/20230830活动误取消/apply_fee_rate_right.json"), List.class);
        for (Map<String, Object> map : list) {
//            System.out.println(map);
            String expiration_time = MapUtil.getString(map, "expiration_time");
            String payways = MapUtil.getString(map, "payway");
            String fee_rate_original = MapUtil.getString(map, "fee_rate");
            String fee_rate = fee_rate_original.substring(1, fee_rate_original.length() -1);
//            System.out.println(fee_rate);

            long id = MapUtil.getLong(map, "id");
            int status = MapUtil.getIntValue(map, "status");

            String[] split = fee_rate.split("\\|,\\|");
            Map<String,Object> values = new HashMap<>();
            for (String s : split) {
                String[] config = s.split("\\*");
                int payway = Integer.parseInt(config[0]);
                String type = config[1];
                String value = config[2];
                if(type.equals("fixed")){
                    values.put(payway+"", value);
                }else{
                    Map<String,Object> ladder = new HashMap<>();
                    ladder.put("fee_type", "ladder");
                    ladder.put("value", JsonUtil.jsonStringToObject(value, List.class));
                    values.put(payway+"", JsonUtil.objectToJsonString(ladder).replace("\"", "\\\""));
                }
            }
            String feeRate = JsonUtil.objectToJsonString(values);
            String sql = "update activity_apply set status = 2 , effective_time = now(), expiration_time = '%s', fee_rate = '%s' where id = %d and status = 6;" ;
            System.out.println(String.format(sql, expiration_time, feeRate, id, feeRate));
        }

    }
}

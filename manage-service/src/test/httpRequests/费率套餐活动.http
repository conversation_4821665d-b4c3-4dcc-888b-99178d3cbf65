
###　费率套餐活动创建

POST {{host}}/rpc/activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 1461

{
  "method": "create",
  "params": [
    {
      "info": {
        "name": "测试-250619002",
        "type": 1,
        "desc": "测试-250619002",
        "tradeAppId": 1,
        "tradeAppName": null,
        "status": null,
        "acquirers": [
          "haike"
        ],
        "operator": "公用测试账号",
        "operatorId": "0af4121d-6cf5-19cc-816c-f66ffddc0000",
        "tags": null,
        "merchantNotice": null,
        "saleNotice": null
      },
      "rule": {
        "configLevel": "merchant",
        "effectiveRules": {
          "byTimeRule": {
            "fixedTime": 1748707200365
          }
        },
        "expirationRules": {
          "byTimeRule": {
            "fixedTime": 1751212800038,
            "rollMonth": null
          }
        },
        "mutexActivityIds": null,
        "lowestFeeRate": null,
        "subStatusDetails": [],
        "tradeCombos": [
          {
            "id": null,
            "name": "测试-250619002",
            "take_effect_notice": {
              "merchant": null,
              "sale": null
            },
            "trade_combo_details": [
              {
                "payway": 2,
                "fee_rate_min": "0.2",
                "fee_rate_max": "0.24",
                "ladder_fee_rates": null,
                "channel_fee_rates": null,
                "channel_ladder_fee_rates": null,
                "b2c_status": 1,
                "c2b_status": 1,
                "wap_status": 1,
                "mini_status": 1,
                "h5_status": 0,
                "app_status": 0
              }
            ]
          }
        ],
        "applyTime": {
          "begin": "2026-06-01 00:00:00",
          "end": "2026-06-01 23:59:59"
        },
        "cities": [
          "上海市",
          "北京市"
        ],
        "organizationList": [
          "60292"
        ],
        "industryRule": {
          "allowList": null,
          "denyList": null
        }
      }
    }
  ],
  "id": "1",
  "jsonrpc": "2.0"
}

### 可用
POST {{host}}/rpc/activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 1461

{
  "method": "usableById",
  "params": [
    13320
  ],
  "id": "1",
  "jsonrpc": "2.0"
}


### 主体商户报名
POST {{host}}/rpc/applyActivity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 1461

{
  "method": "apply",
  "params": [
    {
      "activityId": 121,
      "merchantSn": "21690004082343",
      "merchantId": "c4197360-199e-4c6a-aa95-7fd3eca4dbab",
      "auditId": 9367727,
      "auditSn": null,
      "applyPayFeeRates": null,
      "subMerchantSn": "802964563",
      "lowestFeeRate": null,
      "sn": "21690004082343",
      "operator": "涂玉卫",
      "remark": "主商户报名",
      "sourceApplyId": null,
      "subStatusDetailId": 6188,
      "comboId": 16655,
      "auditTemplateId": null,
      "platform": null
    }
  ],
  "id": "1",
  "jsonrpc": "2.0"
}


### 子商户报名
POST {{host}}/rpc/applyActivity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 1461

{
  "method": "apply",
  "params": [
    {
      "activityId": 121,
      "merchantSn": "21690004084479",
      "merchantId": "35ae8564-b7fe-4dee-9f2e-e61feb55b4cc",
      "auditId": 9367727,
      "auditSn": null,
      "applyPayFeeRates": null,
      "subMerchantSn": "802964563",
      "lowestFeeRate": null,
      "sn": "21690004084479",
      "operator": "涂玉卫",
      "remark": "主商户报名",
      "sourceApplyId": null,
      "auditTemplateId": null,
      "masterInfo": {
        "merchantSn": "21690004082343"
      },
      "platform": null
    }
  ],
  "id": "1",
  "jsonrpc": "2.0"
}

### 子商户报名
POST {{host}}/rpc/applyActivity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 1461

{
  "method": "takeEffect",
  "params": [
    98159424692
  ],
  "id": "1",
  "jsonrpc": "2.0"
}


### 切换子状态

POST {{host}}/rpc/applyActivity
Content-Type: application/json

{
  "jsonrpc": "2.0",
  "method": "changeByActivityCombo",
  "params": [{
    "combo": {
      "auditSn": "111",
      "merchantSn": "21690004082343",
      "activityId": 121,
      "subStatusId": 6189,
      "comboId": 16658,
      "applyFeeRateMap": {
        "3": "0.3"
      }
    },
    "describe": {
      "operator": "涂玉卫",
      "operatorName": "涂玉卫",
      "reason": "返佣成功套餐切换"
    }
  }],
  "id": 1
}
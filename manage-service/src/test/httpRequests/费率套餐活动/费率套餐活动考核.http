
###创建活动
POST {{host}}/rpc/activity
Content-Type: application/json

{
  "method": "create",
  "params": [
    {
      "info": {
        "name": "费率套餐活动滚动考核测试-tyw滚动周期冒烟测试验证",
        "type": 1,
        "desc": "费率套餐活动滚动考核测试-tyw滚动周期冒烟测试验证",
        "tradeAppId": 1,
        "acquirers": [
          "lkl",
          "tonglian",
          "fuyou",
          "haike"
        ],
        "operator": "system",
        "operatorId": "0af4121d-6cf5-19cc-816c-f66ffddc0000",
        "saleNotice": {
          "applySuccess": "销售通知活动报名成功",
          "applyCancel": "销售通知活动取消成功",
          "assessmentPass": "销售通知考核达标",
          "assessmentFail": "销售通知考核不达标"
        },
        "merchantNotice": {
          "applySuccess": "商户通知活动报名成功",
          "applyCancel": "商户通知活动取消成功",
          "assessmentPass": "商户通知考核达标",
          "assessmentFail": "商户通知考核不达标"
        }
      },
      "rule": {
        "configLevel": "merchant",
        "effectiveRules": {
          "byTimeRule": {
            "fixedTime": 1735660800000
          }
        },
        "expirationRules": {
          "byTimeRule": {
            "rollMonth": 2
          }
        },
        "tradeAssessmentRule": {
          "cycle": 1,
          "tradeAssessmentType": "ROLLING",
          "byQuotaLimitRule": {
            "limit": 10000
          },
          "byQuantityLimitRule": {
            "limit": 1000000,
            "min": 0,
            "max": 100000
          }
        },
        "industryRule": {
        },
        "tradeCombos": [
          {
            "name": "考核套餐A",
            "take_effect_notice": {
              "merchant": "商户套餐生效通知",
              "sale": "销售套餐生效通知"
            },
            "trade_combo_details": [
              {
                "payway": 2,
                "fee_rate_min": "0",
                "fee_rate_max": "0.6"
              },
              {
                "payway": 3,
                "fee_rate_min": "0",
                "fee_rate_max": "0.6"
              },
              {
                "payway": 17,
                "fee_rate_min": "0",
                "fee_rate_max": "0.6"
              }
            ]
          }
        ],
        "applyTime": {
          "begin": "2025-01-01 00:00:00",
          "end": "2025-12-31 23:59:59"
        }
      }
    }
  ],
  "id": "1",
  "jsonrpc": "2.0"
}


### 活动查询
POST {{host}}/rpc/applyActivity
Accept: */*
Accept-Language: zh-CN,zh;q=0.9
Content-Type: application/json
x-env-flag: pay12696

{
  "method": "applyInfo",
  "params": [
    98159420846
  ],
  "id": "1",
  "jsonrpc": "2.0"
}


### [普通]活动报名
POST {{host}}/activity/upsert
Accept: */*
Accept-Language: zh-CN,zh;q=0.9
Content-Type: application/json

{
  "activityId": 13182,
  "applyPayFeeRates": [
    {
      "feeRate": "0.24",
      "payWay": 2
    }
  ],
  "comboId": 24307,
  "merchantId": "35ae8564-b7fe-4dee-9f2e-e61feb55b4cc",
  "merchantSn": "**************",
  "operator": "system",
  "remark": "test 考核",
  "sn": "**************",
  "subStatusDetailId": 10272
}


### 考核活动报名upsert
POST {{host}}/activity/upsert
Accept: */*
Accept-Language: zh-CN,zh;q=0.9
Content-Type: application/json

{
  "activityId": 13178,
  "merchantSn": "21690004082343",
  "merchantId": "c4197360-199e-4c6a-aa95-7fd3eca4dbab",
  "operator": "system",
  "remark": "滚动考核冒烟验证",
  "platform": 1,
  "applyPayFeeRates": [
    {
      "payWay": 2,
      "feeRate": "0.5"
    }
  ]
}

### 延迟生效
POST {{host}}/rpc/crm
Content-Type: application/json

{
  "method": "delayEffectApplyActivityFeeRate",
  "params": [
    [
      1
    ]
  ],
  "id": "1",
  "jsonrpc": "2.0"
}



### 报名生效
POST {{host}}/rpc/applyActivity
Accept: */*
Accept-Language: zh-CN,zh;q=0.9
Content-Type: application/json
x-env-flag: pay12696

{
  "method": "takeEffect",
  "params": [
    98159421890
  ],
  "id": "1",
  "jsonrpc": "2.0"
}

### 报名失效
POST {{host}}/rpc/applyActivity
Accept: */*
Accept-Language: zh-CN,zh;q=0.9
Content-Type: application/json
x-env-flag: pay12696

{
  "method": "invalid",
  "params": [
    98159421213
  ],
  "id": "1",
  "jsonrpc": "2.0"
}

### 取消报名
POST {{host}}/rpc/applyActivity
Accept: */*
Accept-Language: zh-CN,zh;q=0.9
Content-Type: application/json

{
  "method": "cancel",
  "params": [
    {
      "cancelDescribe": {
        "operator": "0af4121d-6cf5-19cc-816c-f66ffddc0000",
        "operatorName": "公用测试账号",
        "reason": "不要了",
        "operatorType": null
      },
      "cancelEntity": {
        "occurSource": null,
        "cancelByActivityApply": {
          "activityApplyId": 98159416640,
          "payways": null
        },
        "cancelByFile": null
      },
      "extra": null
    }
  ],
  "id": "1",
  "jsonrpc": "2.0"
}


### 查询可用活动
POST {{host}}/rpc/activity
Content-Type: application/json
x-env-flag: pay12696

{
  "method": "queryUsableActivity",
  "params": [
    "考核"
  ],
  "id": "1",
  "jsonrpc": "2.0"
}


### 交易考核活动定时任务
POST {{host}}/activity/xxlJob/tradeAssessmentActivity
Content-Type: application/json

{
  "assessmentTime": "********"
}


### 获取费率快照
POST {{host}}/rpc/bankFeeRate
Content-Type: application/json
x-env-flag: pay12696

{
  "method": "getFeeRateSnapshot",
  "params": [
    {
      "merchantSn": "**************"
    }
  ],
  "id": "1",
  "jsonrpc": "2.0"
}


### 费率快照恢复
POST {{host}}/rpc/bankFeeRate
Content-Type: application/json
x-env-flag: pay12696

{
  "method": "restoreFeeRateSnapshot",
  "params": [
    {
      "merchantSn": "**************",
      "feeRateSnapshotList": [
        {
          "activityId": 13178,
          "feeRateId": **********,
          "level": "MERCHANT",
          "comboId": 24303,
          "payWay": 2
        }
      ]
    }
  ],
  "id": "1",
  "jsonrpc": "2.0"
}



### 获取交易考核活动信息
POST {{host}}/rpc/app
Content-Type: application/json
x-env-flag: pay12696

{
  "method": "getTradeAssessmentApplyInfo",
  "params": [
    {
      "merchant_id": "54adfb2a-4168-4194-a898-e424492d4606"
    }
  ],
  "id": "1",
  "jsonrpc": "2.0"
}


### 生效费率
POST {{host}}/rpc/mchFeeRate
Content-Type: application/json

{
  "method": "applyFeeRateOne",
  "params": [
    {
      "merchantSn": "**************",
      "storeSn": null,
      "terminalSn": null,
      "auditSn": "银行合作测试",
      "auditTemplateId": null,
      "subStatusId": null,
      "tradeComboId": 180,
      "applyPartialPayway": true,
      "applyFeeRateMap": {
        "2": "0.3"
      },
      "platform": null,
      "operator": "system",
      "operatorName": "system",
      "applyTimeMillis": null,
      "check": true,
      "remark": null
    }
  ],
  "id": "1",
  "jsonrpc": "2.0"
}

### 新增考核记录
POST {{host}}/tradeAssessmentSummary/tradeAssessmentSummary/insert
Content-Type: application/json

{
  "merchantSn": "test2021",
  "merchantId": "ctest2021",
  "originalAmount": 100000,
  "receivedAmount": 99000,
  "tradeCount": 10,
  "tradeMonth": "202509",
  "applyId": 98159421213,
  "activityId": 13178
}


### 更新考核记录
POST {{host}}/tradeAssessmentSummary/tradeAssessmentSummary/update
Content-Type: application/json

{
  "id": 1233,
  "merchantSn": "test2021",
  "originalAmount": 3
}

### taskFlag操作
POST {{host}}/rpc/taskFlag
Content-Type: application/json

{
  "method": "findLastExistDataDay",
  "params": [
    "assessment_activity_transaction_summary"
  ],
  "id": "1",
  "jsonrpc": "2.0"
}

### taskFlag操作
POST {{host}}/rpc/taskFlag
Content-Type: application/json

{
  "method": "update",
  "params": [
    7,
    "assessment_activity_transaction_summary",
    "2025-11-25"
  ],
  "id": "1",
  "jsonrpc": "2.0"
}
#POST http://trade-manage-service.beta.iwosai.com/rpc/discountQuota
POST http://localhost:8081/rpc/discountQuota
x-env-flag: pay10929
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 1461

{
    "method": "preDeductDiscountQuota",
    "params": [
      {
        "merchantId": "541d5351-977c-42b6-b8ff-f5b5e86514fd",
        "tradeApp": "1",
        "tid": "t7895352022078667",
        "originalAmount": 100000,
        "payWay": 3,
        "comboId": 5,
        "subPayWay": 4,
        "feeRate": "0.25",
        "provider": 1033,
        "formal": false,
        "type": "BASE"
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}



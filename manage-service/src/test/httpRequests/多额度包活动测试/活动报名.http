#POST http://trade-manage-service.beta.iwosai.com/rpc/apply_quota_activity
POST http://localhost:8081/rpc/apply_quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 1461

{
    "method": "apply",
    "params": [
      {
        "activityId": 2445,
        "merchantSn": "21690003987725",
        "merchantId": "541d5351-977c-42b6-b8ff-f5b5e86514fd",
        "sn": "21690003987725",
        "auditId": null,
        "auditSn": null,
        "applyType": 1,
        "startTime": "2025-04-08",
        "endTime": "2025-04-30",
        "rolling": 2,
        "quota": 100,
        "feeRate": "0.5",
        "quotaRecordId": null,
        "quotaRecordIdList": null,
        "msg": "验证展示行",
        "operator": "涂玉卫",
        "externalActivityId": "11122",
        "openAutoD1Withdraw": null
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}
POST http://localhost:8081/rpc/apply_quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 649

{
    "method": "apply",
    "params": [
      {
        "activityId": 2442,
        "merchantSn": "**************",
        "merchantId": "a9355edb-7f73-4cae-a8ff-f84653315ca4",
        "sn": "**************",
        "auditId": null,
        "auditSn": null,
        "applyType": 1,
        "startTime": "2025-03-01",
        "endTime": "2025-04-01",
        "rolling": 0,
        "quota": 200,
        "feeRate": "0.4",
        "quotaRecordId": null,
        "quotaRecordIdList": null,
        "msg": "多活动共存自测",
        "operator": "涂玉卫",
        "openAutoD1Withdraw": null
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-03-04T120219.200.json-rpc

###

POST http://localhost:8081/rpc/apply_quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 649

{
    "method": "apply",
    "params": [
      {
        "activityId": 2442,
        "merchantSn": "**************",
        "merchantId": "a9355edb-7f73-4cae-a8ff-f84653315ca4",
        "sn": "**************",
        "auditId": null,
        "auditSn": null,
        "applyType": 1,
        "startTime": "2025-03-01",
        "endTime": "2025-04-01",
        "rolling": 0,
        "quota": 200,
        "feeRate": "0.4",
        "quotaRecordId": null,
        "quotaRecordIdList": null,
        "msg": "多活动共存自测",
        "operator": "涂玉卫",
        "openAutoD1Withdraw": null
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-03-04T120039.200.json-rpc

###

POST http://localhost:8081/rpc/apply_quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 649

{
    "method": "apply",
    "params": [
      {
        "activityId": 2444,
        "merchantSn": "**************",
        "merchantId": "541d5351-977c-42b6-b8ff-f5b5e86514fd",
        "sn": "**************",
        "auditId": null,
        "auditSn": null,
        "applyType": 1,
        "startTime": "2025-05-02",
        "endTime": "2025-06-01",
        "rolling": 0,
        "quota": 200,
        "feeRate": "0.4",
        "quotaRecordId": null,
        "quotaRecordIdList": null,
        "msg": "多活动共存自测",
        "operator": "涂玉卫",
        "openAutoD1Withdraw": null
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-03-04T112527.200.json-rpc

###

POST http://localhost:8081/rpc/apply_quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 649

{
    "method": "apply",
    "params": [
      {
        "activityId": 2444,
        "merchantSn": "**************",
        "merchantId": "541d5351-977c-42b6-b8ff-f5b5e86514fd",
        "sn": "**************",
        "auditId": null,
        "auditSn": null,
        "applyType": 1,
        "startTime": "2025-05-01",
        "endTime": "2025-06-01",
        "rolling": 0,
        "quota": 200,
        "feeRate": "0.4",
        "quotaRecordId": null,
        "quotaRecordIdList": null,
        "msg": "多活动共存自测",
        "operator": "涂玉卫",
        "openAutoD1Withdraw": null
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-03-04T112505.200.json-rpc

###

POST http://localhost:8081/rpc/apply_quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 649

{
    "method": "apply",
    "params": [
      {
        "activityId": 2444,
        "merchantSn": "**************",
        "merchantId": "541d5351-977c-42b6-b8ff-f5b5e86514fd",
        "sn": "**************",
        "auditId": null,
        "auditSn": null,
        "applyType": 1,
        "startTime": "2025-05-01",
        "endTime": "2025-06-01",
        "rolling": 0,
        "quota": 200,
        "feeRate": "0.4",
        "quotaRecordId": null,
        "quotaRecordIdList": null,
        "msg": "多活动共存自测",
        "operator": "涂玉卫",
        "openAutoD1Withdraw": null
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-03-04T112415.200.json-rpc

###

POST http://localhost:8081/rpc/apply_quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 649

{
    "method": "apply",
    "params": [
      {
        "activityId": 2444,
        "merchantSn": "**************",
        "merchantId": "541d5351-977c-42b6-b8ff-f5b5e86514fd",
        "sn": "**************",
        "auditId": null,
        "auditSn": null,
        "applyType": 1,
        "startTime": "2025-03-01",
        "endTime": "2025-05-01",
        "rolling": 0,
        "quota": 200,
        "feeRate": "0.4",
        "quotaRecordId": null,
        "quotaRecordIdList": null,
        "msg": "多活动共存自测",
        "operator": "涂玉卫",
        "openAutoD1Withdraw": null
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-03-04T112356.200.json-rpc

###

POST http://localhost:8081/rpc/apply_quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 658

{
    "method": "apply",
    "params": [
      {
        "activityId": 2443,
        "merchantSn": "**************",
        "merchantId": "a9355edb-7f73-4cae-a8ff-f84653315ca4",
        "sn": "**************",
        "auditId": null,
        "auditSn": null,
        "applyType": 1,
        "startTime": "2025-03-01",
        "endTime": "2025-05-01",
        "rolling": 0,
        "quota": 200,
        "feeRate": "0.6",
        "quotaRecordId": null,
        "quotaRecordIdList": null,
        "msg": "银行卡额度包活动申请",
        "operator": "涂玉卫",
        "openAutoD1Withdraw": null
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-03-04T110728.200.json-rpc

###

POST http://localhost:8081/rpc/apply_quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 658

{
    "method": "apply",
    "params": [
      {
        "activityId": 2443,
        "merchantSn": "**************",
        "merchantId": "a9355edb-7f73-4cae-a8ff-f84653315ca4",
        "sn": "**************",
        "auditId": null,
        "auditSn": null,
        "applyType": 1,
        "startTime": "2025-03-01",
        "endTime": "2025-05-01",
        "rolling": 0,
        "quota": 200,
        "feeRate": "0.6",
        "quotaRecordId": null,
        "quotaRecordIdList": null,
        "msg": "银行卡额度包活动申请",
        "operator": "涂玉卫",
        "openAutoD1Withdraw": null
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-03-04T110630.200.json-rpc

###

POST http://localhost:8081/rpc/apply_quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 658

{
    "method": "apply",
    "params": [
      {
        "activityId": 2443,
        "merchantSn": "**************",
        "merchantId": "a9355edb-7f73-4cae-a8ff-f84653315ca4",
        "sn": "**************",
        "auditId": null,
        "auditSn": null,
        "applyType": 1,
        "startTime": "2025-03-01",
        "endTime": "2025-05-01",
        "rolling": 0,
        "quota": 200,
        "feeRate": "0.6",
        "quotaRecordId": null,
        "quotaRecordIdList": null,
        "msg": "银行卡额度包活动申请",
        "operator": "涂玉卫",
        "openAutoD1Withdraw": null
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-03-04T110419.200.json-rpc

###

POST http://localhost:8081/rpc/apply_quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 658

{
    "method": "apply",
    "params": [
      {
        "activityId": 2443,
        "merchantSn": "**************",
        "merchantId": "a9355edb-7f73-4cae-a8ff-f84653315ca4",
        "sn": "**************",
        "auditId": null,
        "auditSn": null,
        "applyType": 1,
        "startTime": "2025-03-01",
        "endTime": "2025-05-01",
        "rolling": 0,
        "quota": 200,
        "feeRate": "0.6",
        "quotaRecordId": null,
        "quotaRecordIdList": null,
        "msg": "银行卡额度包活动申请",
        "operator": "涂玉卫",
        "openAutoD1Withdraw": null
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-03-04T110155.200.json-rpc

###

POST http://localhost:8081/rpc/apply_quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 658

{
    "method": "apply",
    "params": [
      {
        "activityId": 2443,
        "merchantSn": "**************",
        "merchantId": "a9355edb-7f73-4cae-a8ff-f84653315ca4",
        "sn": "**************",
        "auditId": null,
        "auditSn": null,
        "applyType": 1,
        "startTime": "2025-02-01",
        "endTime": "2025-05-01",
        "rolling": 0,
        "quota": 200,
        "feeRate": "0.6",
        "quotaRecordId": null,
        "quotaRecordIdList": null,
        "msg": "银行卡额度包活动申请",
        "operator": "涂玉卫",
        "openAutoD1Withdraw": null
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-03-04T110013.200.json-rpc

###

POST http://localhost:8081/rpc/apply_quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 649

{
    "method": "apply",
    "params": [
      {
        "activityId": 2444,
        "merchantSn": "**************",
        "merchantId": "541d5351-977c-42b6-b8ff-f5b5e86514fd",
        "sn": "**************",
        "auditId": null,
        "auditSn": null,
        "applyType": 1,
        "startTime": "2025-03-01",
        "endTime": "2025-05-01",
        "rolling": 0,
        "quota": 100,
        "feeRate": "0.5",
        "quotaRecordId": null,
        "quotaRecordIdList": null,
        "msg": "多活动共存自测",
        "operator": "涂玉卫",
        "openAutoD1Withdraw": null
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-03-03T143600.200.json-rpc

###

POST http://localhost:8081/rpc/apply_quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 649

{
    "method": "apply",
    "params": [
      {
        "activityId": 2444,
        "merchantSn": "**************",
        "merchantId": "541d5351-977c-42b6-b8ff-f5b5e86514fd",
        "sn": "**************",
        "auditId": null,
        "auditSn": null,
        "applyType": 1,
        "startTime": "2025-03-01",
        "endTime": "2025-05-01",
        "rolling": 0,
        "quota": 100,
        "feeRate": "0.5",
        "quotaRecordId": null,
        "quotaRecordIdList": null,
        "msg": "多活动共存自测",
        "operator": "涂玉卫",
        "openAutoD1Withdraw": null
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-03-03T143522.200.json-rpc

###

POST http://localhost:8081/rpc/apply_quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 649

{
    "method": "apply",
    "params": [
      {
        "activityId": 2442,
        "merchantSn": "**************",
        "merchantId": "541d5351-977c-42b6-b8ff-f5b5e86514fd",
        "sn": "**************",
        "auditId": null,
        "auditSn": null,
        "applyType": 1,
        "startTime": "2025-03-01",
        "endTime": "2025-05-01",
        "rolling": 0,
        "quota": 100,
        "feeRate": "0.5",
        "quotaRecordId": null,
        "quotaRecordIdList": null,
        "msg": "多活动共存自测",
        "operator": "涂玉卫",
        "openAutoD1Withdraw": null
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-03-03T143341.200.json-rpc

###

POST http://localhost:8081/rpc/apply_quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 649

{
    "method": "apply",
    "params": [
      {
        "activityId": 2442,
        "merchantSn": "**************",
        "merchantId": "541d5351-977c-42b6-b8ff-f5b5e86514fd",
        "sn": "**************",
        "auditId": null,
        "auditSn": null,
        "applyType": 1,
        "startTime": "2025-03-01",
        "endTime": "2025-05-01",
        "rolling": 0,
        "quota": 100,
        "feeRate": "0.5",
        "quotaRecordId": null,
        "quotaRecordIdList": null,
        "msg": "多活动共存自测",
        "operator": "涂玉卫",
        "openAutoD1Withdraw": null
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-03-03T143246.200.json-rpc

###

POST http://localhost:8081/rpc/apply_quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 649

{
    "method": "apply",
    "params": [
      {
        "activityId": 2442,
        "merchantSn": "**************",
        "merchantId": "541d5351-977c-42b6-b8ff-f5b5e86514fd",
        "sn": "**************",
        "auditId": null,
        "auditSn": null,
        "applyType": 1,
        "startTime": "2025-03-01",
        "endTime": "2025-05-01",
        "rolling": 0,
        "quota": 100,
        "feeRate": "0.5",
        "quotaRecordId": null,
        "quotaRecordIdList": null,
        "msg": "多活动共存自测",
        "operator": "涂玉卫",
        "openAutoD1Withdraw": null
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-03-03T143113.200.json-rpc

###

POST http://localhost:8081/rpc/quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 1437

{
    "method": "create",
    "params": [
      {
        "info": {
          "name": "多活动创建tyw01-总行-移动支付",
          "desc": "多活动创建tyw01",
          "type":"CAMPUS",
          "tradeAppId": 1,
          "tradeAppName": null,
          "configLevel": "merchant",
          "status": null,
          "operator": "system",
          "operatorId": null,
          "tags": null,
          "merchantNotice": {
            "applySuccess": "报名成功通知",
            "manualCancel": "手动取消通知",
            "activityExpired": "活动到期失效通知",
            "bankcardInvalid": "解绑银行卡失效通知",
            "discountQuotaUseUp": "额度包用完失效通知"
          },
          "saleNotice": null
        },
        "rule": {
          "startTime": *************,
          "endTime": *************,
          "cancelReApply": 0,
          "bandComboIds": null,
          "paywayCategory":"ALL",
          "acquirers": [
            "lkl",
            "fuyou",
            "haike"
          ],
          "bankName": "华夏银行",
          "cities": null,
          "discountQuota": {
            "type": "fix",
            "quota": {
              "max": "1000",
              "min": "0"
            },
            "feeRate": {
              "max": "1",
              "min": "0"
            }
          }
        }
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-03-03T142153.200.json-rpc

###

POST http://localhost:8081/rpc/quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 1480

{
    "method": "create",
    "params": [
      {
        "info": {
          "name": "多活动创建tyw01-总行-移动支付",
          "desc": "多活动创建tyw01",
          "type":"BRANCH",
          "tradeAppId": 1,
          "tradeAppName": null,
          "configLevel": "merchant",
          "status": null,
          "operator": "system",
          "operatorId": null,
          "tags": null,
          "merchantNotice": {
            "applySuccess": "报名成功通知",
            "manualCancel": "手动取消通知",
            "activityExpired": "活动到期失效通知",
            "bankcardInvalid": "解绑银行卡失效通知",
            "discountQuotaUseUp": "额度包用完失效通知"
          },
          "saleNotice": null
        },
        "rule": {
          "startTime": *************,
          "endTime": *************,
          "cancelReApply": 0,
          "bandComboIds": null,
          "paywayCategory":"BANK_CARD",
          "mobileBankProvider":1022,
          "acquirers": [
            "lkl",
            "fuyou",
            "haike"
          ],
          "bankName": "华夏银行",
          "cities": null,
          "discountQuota": {
            "type": "fix",
            "quota": {
              "max": "1000",
              "min": "0"
            },
            "feeRate": {
              "max": "1",
              "min": "0"
            }
          }
        }
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-03-03T141720.200.json-rpc

###

POST http://localhost:8081/rpc/quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 1443

{
    "method": "create",
    "params": [
      {
        "info": {
          "name": "多活动创建tyw01-总行-移动支付",
          "desc": "多活动创建tyw01",
          "type":"BRANCH",
          "tradeAppId": 1,
          "tradeAppName": null,
          "configLevel": "merchant",
          "status": null,
          "operator": "system",
          "operatorId": null,
          "tags": null,
          "merchantNotice": {
            "applySuccess": "报名成功通知",
            "manualCancel": "手动取消通知",
            "activityExpired": "活动到期失效通知",
            "bankcardInvalid": "解绑银行卡失效通知",
            "discountQuotaUseUp": "额度包用完失效通知"
          },
          "saleNotice": null
        },
        "rule": {
          "startTime": *************,
          "endTime": *************,
          "cancelReApply": 0,
          "bandComboIds": null,
          "paywayCategory":"BANK_CARD",
          "acquirers": [
            "lkl",
            "fuyou",
            "haike"
          ],
          "bankName": "华夏银行",
          "cities": null,
          "discountQuota": {
            "type": "fix",
            "quota": {
              "max": "1000",
              "min": "0"
            },
            "feeRate": {
              "max": "1",
              "min": "0"
            }
          }
        }
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-03-03T141626.200.json-rpc

###

POST http://localhost:8081/rpc/quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 1446

{
    "method": "create",
    "params": [
      {
        "info": {
          "name": "多活动创建tyw01-总行-移动支付",
          "desc": "多活动创建tyw01",
          "type":"TOTAL_BRANCH",
          "tradeAppId": 1,
          "tradeAppName": null,
          "configLevel": "merchant",
          "status": null,
          "operator": "system",
          "operatorId": null,
          "tags": null,
          "merchantNotice": {
            "applySuccess": "报名成功通知",
            "manualCancel": "手动取消通知",
            "activityExpired": "活动到期失效通知",
            "bankcardInvalid": "解绑银行卡失效通知",
            "discountQuotaUseUp": "额度包用完失效通知"
          },
          "saleNotice": null
        },
        "rule": {
          "startTime": *************,
          "endTime": *************,
          "cancelReApply": 0,
          "bandComboIds": null,
          "paywayCategory":"MOBILE",
          "acquirers": [
            "lkl",
            "fuyou",
            "haike"
          ],
          "bankName": "华夏银行",
          "cities": null,
          "discountQuota": {
            "type": "fix",
            "quota": {
              "max": "1000",
              "min": "0"
            },
            "feeRate": {
              "max": "1",
              "min": "0"
            }
          }
        }
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-03-03T141232.200.json-rpc

###

POST http://localhost:8081/rpc/quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 1483

{
    "method": "create",
    "params": [
      {
        "info": {
          "name": "多活动创建tyw01-总行-移动支付",
          "desc": "多活动创建tyw01",
          "type":"TOTAL_BRANCH",
          "tradeAppId": 1,
          "tradeAppName": null,
          "configLevel": "merchant",
          "status": null,
          "operator": "system",
          "operatorId": null,
          "tags": null,
          "merchantNotice": {
            "applySuccess": "报名成功通知",
            "manualCancel": "手动取消通知",
            "activityExpired": "活动到期失效通知",
            "bankcardInvalid": "解绑银行卡失效通知",
            "discountQuotaUseUp": "额度包用完失效通知"
          },
          "saleNotice": null
        },
        "rule": {
          "startTime": *************,
          "endTime": *************,
          "cancelReApply": 0,
          "bandComboIds": null,
          "paywayCategory":"MOBILE",
          "mobileBankProvider":1028,
          "acquirers": [
            "lkl",
            "fuyou",
            "haike"
          ],
          "bankName": "华夏银行",
          "cities": null,
          "discountQuota": {
            "type": "fix",
            "quota": {
              "max": "1000",
              "min": "0"
            },
            "feeRate": {
              "max": "1",
              "min": "0"
            }
          }
        }
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-03-03T141212.200.json-rpc

###

POST http://localhost:8081/rpc/quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 1429

{
    "method": "create",
    "params": [
      {
        "info": {
          "name": "多活动创建tyw01-NPOS",
          "desc": "多活动创建tyw01-NPOS",
          "type":"NFC_POS",
          "tradeAppId": 1,
          "tradeAppName": null,
          "configLevel": "merchant",
          "status": null,
          "operator": "system",
          "operatorId": null,
          "tags": null,
          "merchantNotice": {
            "applySuccess": "报名成功通知",
            "manualCancel": "手动取消通知",
            "activityExpired": "活动到期失效通知",
            "bankcardInvalid": "解绑银行卡失效通知",
            "discountQuotaUseUp": "额度包用完失效通知"
          },
          "saleNotice": null
        },
        "rule": {
          "paywayCategory": "ALL",
          "startTime": *************,
          "endTime": *************,
          "cancelReApply": 0,
          "bandComboIds": null,
          "acquirers": [
            "lkl",
            "fuyou",
            "haike"
          ],
          "bankName": "华夏银行",
          "cities": null,
          "discountQuota": {
            "type": "fix",
            "quota": {
              "max": "1000",
              "min": "0"
            },
            "feeRate": {
              "max": "1",
              "min": "0"
            }
          }
        }
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-03-03T140543.200.json-rpc

###

POST http://localhost:8081/rpc/quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 1394

{
    "method": "create",
    "params": [
      {
        "info": {
          "name": "多活动创建tyw01-NPOS",
          "desc": "多活动创建tyw01-NPOS",
          "type":"NFC_POS",
          "tradeAppId": 1,
          "tradeAppName": null,
          "configLevel": "merchant",
          "status": null,
          "operator": "system",
          "operatorId": null,
          "tags": null,
          "merchantNotice": {
            "applySuccess": "报名成功通知",
            "manualCancel": "手动取消通知",
            "activityExpired": "活动到期失效通知",
            "bankcardInvalid": "解绑银行卡失效通知",
            "discountQuotaUseUp": "额度包用完失效通知"
          },
          "saleNotice": null
        },
        "rule": {
          "startTime": *************,
          "endTime": *************,
          "cancelReApply": 0,
          "bandComboIds": null,
          "acquirers": [
            "lkl",
            "fuyou",
            "haike"
          ],
          "bankName": "华夏银行",
          "cities": null,
          "discountQuota": {
            "type": "fix",
            "quota": {
              "max": "1000",
              "min": "0"
            },
            "feeRate": {
              "max": "1",
              "min": "0"
            }
          }
        }
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-03-03T134232.200.json-rpc

###

POST http://localhost:8081/rpc/apply_quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 658

{
    "method": "apply",
    "params": [
      {
        "activityId": 2426,
        "merchantSn": "**************",
        "merchantId": "a9355edb-7f73-4cae-a8ff-f84653315ca4",
        "sn": "**************",
        "auditId": null,
        "auditSn": null,
        "applyType": 1,
        "startTime": "2025-02-01",
        "endTime": "2025-05-01",
        "rolling": 0,
        "quota": 500,
        "feeRate": "0.5",
        "quotaRecordId": null,
        "quotaRecordIdList": null,
        "msg": "银行卡额度包活动申请",
        "operator": "涂玉卫",
        "openAutoD1Withdraw": null
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-02-25T191532.200.json-rpc

###

POST http://localhost:8081/rpc/apply_quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 658

{
    "method": "apply",
    "params": [
      {
        "activityId": 2426,
        "merchantSn": "**************",
        "merchantId": "a9355edb-7f73-4cae-a8ff-f84653315ca4",
        "sn": "**************",
        "auditId": null,
        "auditSn": null,
        "applyType": 1,
        "startTime": "2025-02-01",
        "endTime": "2025-05-01",
        "rolling": 0,
        "quota": 500,
        "feeRate": "0.5",
        "quotaRecordId": null,
        "quotaRecordIdList": null,
        "msg": "银行卡额度包活动申请",
        "operator": "涂玉卫",
        "openAutoD1Withdraw": null
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-02-25T184128.200.json-rpc

###

POST http://localhost:8081/rpc/apply_quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 658

{
    "method": "apply",
    "params": [
      {
        "activityId": 2426,
        "merchantSn": "**************",
        "merchantId": "a9355edb-7f73-4cae-a8ff-f84653315ca4",
        "sn": "**************",
        "auditId": null,
        "auditSn": null,
        "applyType": 1,
        "startTime": "2025-02-01",
        "endTime": "2025-05-01",
        "rolling": 0,
        "quota": 500,
        "feeRate": "0.5",
        "quotaRecordId": null,
        "quotaRecordIdList": null,
        "msg": "银行卡额度包活动申请",
        "operator": "涂玉卫",
        "openAutoD1Withdraw": null
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-02-25T184037.200.json-rpc

###

POST http://localhost:8081/rpc/apply_quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 658

{
    "method": "apply",
    "params": [
      {
        "activityId": 2426,
        "merchantSn": "**************",
        "merchantId": "a9355edb-7f73-4cae-a8ff-f84653315ca4",
        "sn": "**************",
        "auditId": null,
        "auditSn": null,
        "applyType": 1,
        "startTime": "2025-02-01",
        "endTime": "2025-05-01",
        "rolling": 0,
        "quota": 500,
        "feeRate": "0.5",
        "quotaRecordId": null,
        "quotaRecordIdList": null,
        "msg": "银行卡额度包活动申请",
        "operator": "涂玉卫",
        "openAutoD1Withdraw": null
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-02-25T184023.200.json-rpc

###

POST http://localhost:8081/rpc/apply_quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 649

{
    "method": "apply",
    "params": [
      {
        "activityId": 2408,
        "merchantSn": "21690003990547",
        "merchantId": "a0cb7770-701e-4b61-be3b-3a4633eff339",
        "sn": "21690003990547",
        "auditId": null,
        "auditSn": null,
        "applyType": 1,
        "startTime": "2025-02-01",
        "endTime": "2025-05-01",
        "rolling": 0,
        "quota": 500,
        "feeRate": "0.5",
        "quotaRecordId": null,
        "quotaRecordIdList": null,
        "msg": "多活动共存自测",
        "operator": "涂玉卫",
        "openAutoD1Withdraw": null
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-02-25T175407.200.json-rpc

###

POST http://localhost:8081/rpc/apply_quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 649

{
    "method": "apply",
    "params": [
      {
        "activityId": 2408,
        "merchantSn": "21690003990547",
        "merchantId": "a0cb7770-701e-4b61-be3b-3a4633eff339",
        "sn": "21690003990547",
        "auditId": null,
        "auditSn": null,
        "applyType": 1,
        "startTime": "2025-02-01",
        "endTime": "2025-05-01",
        "rolling": 0,
        "quota": 500,
        "feeRate": "0.5",
        "quotaRecordId": null,
        "quotaRecordIdList": null,
        "msg": "多活动共存自测",
        "operator": "涂玉卫",
        "openAutoD1Withdraw": null
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-02-25T174849.200.json-rpc

###

POST http://localhost:8081/rpc/apply_quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 685

{
    "method": "apply",
    "params": [
      {
        "activityId": 2408,
        "merchantSn": "**************",
        "merchantId": "541d5351-977c-42b6-b8ff-f5b5e86514fd",
        "sn": "**************",
        "auditId": null,
        "auditSn": null,
        "applyType": 1,
        "startTime": "2025-02-01",
        "endTime": "2025-05-01",
        "rolling": 0,
        "quota": 500,
        "feeRate": "0.5",
        "quotaRecordId": null,
        "quotaRecordIdList": null,
        "msg": "多活动共存自测",
        "operator": "涂玉卫",
        "openAutoD1Withdraw": null,
        "mobileBankProvider": 1022
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-02-25T173439.200.json-rpc

###

POST http://localhost:8081/rpc/apply_quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 649

{
    "method": "apply",
    "params": [
      {
        "activityId": 2408,
        "merchantSn": "**************",
        "merchantId": "541d5351-977c-42b6-b8ff-f5b5e86514fd",
        "sn": "**************",
        "auditId": null,
        "auditSn": null,
        "applyType": 1,
        "startTime": "2025-02-01",
        "endTime": "2025-05-01",
        "rolling": 0,
        "quota": 500,
        "feeRate": "0.5",
        "quotaRecordId": null,
        "quotaRecordIdList": null,
        "msg": "多活动共存自测",
        "operator": "涂玉卫",
        "openAutoD1Withdraw": null
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-02-25T173335.200.json-rpc

###

POST http://localhost:8081/rpc/apply_quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 649

{
    "method": "apply",
    "params": [
      {
        "activityId": 2420,
        "merchantSn": "**************",
        "merchantId": "541d5351-977c-42b6-b8ff-f5b5e86514fd",
        "sn": "**************",
        "auditId": null,
        "auditSn": null,
        "applyType": 1,
        "startTime": "2025-02-01",
        "endTime": "2025-05-01",
        "rolling": 0,
        "quota": 500,
        "feeRate": "0.5",
        "quotaRecordId": null,
        "quotaRecordIdList": null,
        "msg": "多活动共存自测",
        "operator": "涂玉卫",
        "openAutoD1Withdraw": null
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-02-24T160345.200.json-rpc

###

POST http://localhost:8081/rpc/apply_quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 649

{
    "method": "apply",
    "params": [
      {
        "activityId": 2420,
        "merchantSn": "**************",
        "merchantId": "541d5351-977c-42b6-b8ff-f5b5e86514fd",
        "sn": "**************",
        "auditId": null,
        "auditSn": null,
        "applyType": 1,
        "startTime": "2025-02-01",
        "endTime": "2025-05-01",
        "rolling": 0,
        "quota": 500,
        "feeRate": "0.5",
        "quotaRecordId": null,
        "quotaRecordIdList": null,
        "msg": "多活动共存自测",
        "operator": "涂玉卫",
        "openAutoD1Withdraw": null
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-02-24T160219.200.json-rpc

###

POST http://localhost:8081/rpc/apply_quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 649

{
    "method": "apply",
    "params": [
      {
        "activityId": 2420,
        "merchantSn": "**************",
        "merchantId": "541d5351-977c-42b6-b8ff-f5b5e86514fd",
        "sn": "**************",
        "auditId": null,
        "auditSn": null,
        "applyType": 1,
        "startTime": "2025-02-01",
        "endTime": "2025-05-01",
        "rolling": 0,
        "quota": 500,
        "feeRate": "0.5",
        "quotaRecordId": null,
        "quotaRecordIdList": null,
        "msg": "多活动共存自测",
        "operator": "涂玉卫",
        "openAutoD1Withdraw": null
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-02-24T160147.200.json-rpc

###

POST http://localhost:8081/rpc/apply_quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 649

{
    "method": "apply",
    "params": [
      {
        "activityId": 2420,
        "merchantSn": "**************",
        "merchantId": "541d5351-977c-42b6-b8ff-f5b5e86514fd",
        "sn": "**************",
        "auditId": null,
        "auditSn": null,
        "applyType": 1,
        "startTime": "2025-02-01",
        "endTime": "2025-05-01",
        "rolling": 0,
        "quota": 500,
        "feeRate": "0.5",
        "quotaRecordId": null,
        "quotaRecordIdList": null,
        "msg": "多活动共存自测",
        "operator": "涂玉卫",
        "openAutoD1Withdraw": null
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-02-24T160115.200.json-rpc

###

POST http://trade-manage-service-pay10929.beta.iwosai.com/rpc/quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 1446

{
    "method": "create",
    "params": [
      {
        "info": {
          "name": "多活动创建tyw01-总行-移动支付",
          "desc": "多活动创建tyw01",
          "type":"TOTAL_BRANCH",
          "tradeAppId": 1,
          "tradeAppName": null,
          "configLevel": "merchant",
          "status": null,
          "operator": "system",
          "operatorId": null,
          "tags": null,
          "merchantNotice": {
            "applySuccess": "报名成功通知",
            "manualCancel": "手动取消通知",
            "activityExpired": "活动到期失效通知",
            "bankcardInvalid": "解绑银行卡失效通知",
            "discountQuotaUseUp": "额度包用完失效通知"
          },
          "saleNotice": null
        },
        "rule": {
          "startTime": *************,
          "endTime": *************,
          "cancelReApply": 0,
          "bandComboIds": null,
          "paywayCategory":"MOBILE",
          "acquirers": [
            "lkl",
            "fuyou",
            "haike"
          ],
          "bankName": "华夏银行",
          "cities": null,
          "discountQuota": {
            "type": "fix",
            "quota": {
              "max": "1000",
              "min": "0"
            },
            "feeRate": {
              "max": "1",
              "min": "0"
            }
          }
        }
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-02-24T155815.200.json-rpc

###

POST http://trade-manage-service-pay10929.beta.iwosai.com/rpc/quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 1485

{
    "method": "create",
    "params": [
      {
        "info": {
          "name": "多活动创建tyw01-总行-移动支付",
          "desc": "多活动创建tyw01",
          "type":"TOTAL_BRANCH",
          "tradeAppId": 1,
          "tradeAppName": null,
          "configLevel": "merchant",
          "status": null,
          "operator": "system",
          "operatorId": null,
          "tags": null,
          "merchantNotice": {
            "applySuccess": "报名成功通知",
            "manualCancel": "手动取消通知",
            "activityExpired": "活动到期失效通知",
            "bankcardInvalid": "解绑银行卡失效通知",
            "discountQuotaUseUp": "额度包用完失效通知"
          },
          "saleNotice": null
        },
        "rule": {
          "startTime": *************,
          "endTime": *************,
          "cancelReApply": 0,
          "bandComboIds": null,
          "paywayCategory":"MOBILE",
//          "mobileBankProvider":1028,
          "acquirers": [
            "lkl",
            "fuyou",
            "haike"
          ],
          "bankName": "华夏银行",
          "cities": null,
          "discountQuota": {
            "type": "fix",
            "quota": {
              "max": "1000",
              "min": "0"
            },
            "feeRate": {
              "max": "1",
              "min": "0"
            }
          }
        }
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-02-24T155802.400.json-rpc

###

POST http://trade-manage-service-pay10929.beta.iwosai.com/rpc/quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 1483

{
    "method": "create",
    "params": [
      {
        "info": {
          "name": "多活动创建tyw01-总行-移动支付",
          "desc": "多活动创建tyw01",
          "type":"TOTAL_BRANCH",
          "tradeAppId": 1,
          "tradeAppName": null,
          "configLevel": "merchant",
          "status": null,
          "operator": "system",
          "operatorId": null,
          "tags": null,
          "merchantNotice": {
            "applySuccess": "报名成功通知",
            "manualCancel": "手动取消通知",
            "activityExpired": "活动到期失效通知",
            "bankcardInvalid": "解绑银行卡失效通知",
            "discountQuotaUseUp": "额度包用完失效通知"
          },
          "saleNotice": null
        },
        "rule": {
          "startTime": *************,
          "endTime": *************,
          "cancelReApply": 0,
          "bandComboIds": null,
          "paywayCategory":"MOBILE",
          "mobileBankProvider":1028,
          "acquirers": [
            "lkl",
            "fuyou",
            "haike"
          ],
          "bankName": "华夏银行",
          "cities": null,
          "discountQuota": {
            "type": "fix",
            "quota": {
              "max": "1000",
              "min": "0"
            },
            "feeRate": {
              "max": "1",
              "min": "0"
            }
          }
        }
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

<> 2025-02-24T155634.200.json-rpc

###

POST http://trade-manage-service.beta.iwosai.com/rpc/apply_quota_activity
Content-Type: application/json
Content-Length: 482
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*

{
"method": "apply",
"params": [
{
"activityId": 1922,
"merchantSn": "**************",
"merchantId": "541d5351-977c-42b6-b8ff-f5b5e86514fd",
"sn": "**************",
"auditId": null,
"auditSn": null,
"applyType": 1,
"startTime": "2025-02-01",
"endTime": "2025-05-01",
"rolling": 0,
"quota": 500,
"feeRate": "0.45",
"quotaRecordId": null,
"quotaRecordIdList": null,
"msg": "多活动共存自测",
"operator": "涂玉卫",
"openAutoD1Withdraw": null
}
],
"id": "1",
"jsonrpc": "2.0"
}

<> 2025-02-24T144925.200.json-rpc

###

POST http://trade-manage-servce.beta.iwosai.com/rpc/apply_quota_activity
Content-Type: application/json
Content-Length: 482
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*

{
"method": "apply",
"params": [
{
"activityId": 1922,
"merchantSn": "**************",
"merchantId": "541d5351-977c-42b6-b8ff-f5b5e86514fd",
"sn": "**************",
"auditId": null,
"auditSn": null,
"applyType": 1,
"startTime": "2025-02-01",
"endTime": "2025-05-01",
"rolling": 0,
"quota": 500,
"feeRate": "0.45",
"quotaRecordId": null,
"quotaRecordIdList": null,
"msg": "多活动共存自测",
"operator": "涂玉卫",
"openAutoD1Withdraw": null
}
],
"id": "1",
"jsonrpc": "2.0"
}

<> 2025-02-24T144820.403.html

###


POST http://localhost:8081/rpc/apply_quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 1461

{
    "method": "apply",
    "params": [
      {
        "activityId": 2443,
        "merchantSn": "21690003981585",
        "merchantId": "a9355edb-7f73-4cae-a8ff-f84653315ca4",
        "sn": "21690003981585",
        "auditId": null,
        "auditSn": null,
        "applyType": 1,
        "startTime": "2025-03-01",
        "endTime": "2025-05-01",
        "rolling": 0,
        "quota": 200,
        "feeRate": "0.6",
        "quotaRecordId": null,
        "quotaRecordIdList": null,
        "msg": "银行卡额度包活动申请",
        "operator": "涂玉卫",
        "openAutoD1Withdraw": null
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}
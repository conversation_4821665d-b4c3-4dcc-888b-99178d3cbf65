### 风控冻结余额
POST http://{{host}}/rpc/risk/manage
Content-Type: application/json

{
    "method": "freezeBalanceByRisk",
    "params": [
      {
        "merchantId": "06d93c19-4861-47de-be35-b58723572cb0",
        "clearanceProvider": 7,
        "walletAccountType": 1,
        "frozenType": 3,
        "freezeAmount": 20000,
        "actionId": "7e026d1f-2a9b-4656-89fe-a238368b976b",
        "reason": "商户涉及风险操作，需冻结金额",
        "remark": "冻结商户余额",
        "logRequest": {
          "platformCode": "SPA",
          "operatorUserId": "userId-ljl",
          "operatorUserName": "ljl",
          "remark": "风控冻结商户余额",
          "externalSceneTraceId": "trace-111",
          "sceneTemplateCode": "43M4G0AI03C8",
          "opObjectId": "06d93c19-4861-47de-be35-b58723572cb0"
        }
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

### 风控解冻余额
POST http://{{host}}/rpc/risk/manage
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*

{
    "method": "unfreezeBalanceByRisk",
    "params": [
      {
        "merchantId": "06d93c19-4861-47de-be35-b58723572cb0",
        "frozenType": 3,
        "clearanceProvider": 7,
        "walletAccountType": 1,
        "freezeAccountId": "7e026d1f-2a9b-4656-89fe-a238368b976b",
        "unfreezeAccountId": "1ffdeaba-1695-4268-90b1-40ef6aa1af90",
        "unfreezeAmount": 10000,
        "unfreezeRemaining": false,
        "logRequest": {
          "platformCode": "SPA",
          "operatorUserId": "userId-ljl",
          "operatorUserName": "ljl",
          "remark": "风控解冻商户余额",
          "externalSceneTraceId": "trace-111",
          "sceneTemplateCode": "4HTTG75CMIKD",
          "opObjectId": "06d93c19-4861-47de-be35-b58723572cb0"
        }
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}

### 查询冻结订单
#POST http://upay-wallet.beta.iwosai.com/rpc/freezeV3
POST http://localhost:9966/upay-wallet/rpc/freezeV3
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*

{
  "method": "findFrozenOrders",
  "params": [
    {
      "merchant_id": "218d3b3b-a4a7-45c8-9ff3-fe220ca2e914",
      "type": 3,
      "page_size": 1000
    },
    {
      "page": 1,
      "page_size": 1000
    }
  ],
  "id": "1",
  "jsonrpc": "2.0"
}

### 查询余额
POST http://upay-wallet.beta.iwosai.com/rpc/freezeV3
#POST http://localhost:9966/upay-wallet/rpc/walletV3
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*

{
  "method": "getBalances",
  "params": [
    {
      "merchant_id": "218d3b3b-a4a7-45c8-9ff3-fe220ca2e914",
      "clearance_provider": 2,
      "account_type": 1
    }
  ],
  "id": "1",
  "jsonrpc": "2.0"
}

### 风控延迟结算
POST http://{{host}}/rpc/risk/manage
Content-Type: application/json

{
  "method": "delayAllMerchantD1Withdraw",
  "params": [
    "06d93c19-4861-47de-be35-b58723572cb0",
    {
      "platformCode": "SPA",
      "operatorUserId": "userId-ljl",
      "operatorUserName": "ljl",
      "remark": "风控延迟结算",
      "externalSceneTraceId": "trace-111",
      "sceneTemplateCode": "GW4KM4LO5A0K",
      "opObjectId": "06d93c19-4861-47de-be35-b58723572cb0"
    }
  ],
  "id": "1",
  "jsonrpc": "2.0"
}

### 风控取消延迟结算
POST http://{{host}}/rpc/risk/manage
Content-Type: application/json

{
  "method": "cancelDelayAllMerchantD1Withdraw",
  "params": [
    "06d93c19-4861-47de-be35-b58723572cb0",
    {
      "platformCode": "SPA",
      "operatorUserId": "userId-ljl",
      "operatorUserName": "ljl",
      "remark": "风控取消延迟结算",
      "externalSceneTraceId": "trace-111",
      "sceneTemplateCode": "A1ULGKBW5EW3",
      "opObjectId": "06d93c19-4861-47de-be35-b58723572cb0"
    }
  ],
  "id": "1",
  "jsonrpc": "2.0"
}

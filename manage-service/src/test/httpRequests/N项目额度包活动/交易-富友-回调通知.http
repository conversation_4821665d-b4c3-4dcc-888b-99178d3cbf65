POST http://notify-pay.beta.iwosai.com/admin/saveEventDo
x-env-flag: pay13002
Content-Type: application/json

{
  "tsn": "****************",
  "orderSn": "****************",
  "merchantId": "8cf4fe32-ac20-439b-9d14-ecabd1541716",
  "type": 30,
  "provider": 1038,
  "notifyParams": "{\"channel_order_no\":\"****************\",\"fee_amt\":\"4\",\"ins_cd\":\"08M0031385\",\"kbps_trace_no\":\"************\",\"mchnt_cd\":\"0003050F6518836\",\"mchnt_order_no\":\"****************\",\"orig_mchnt_order_no\":\"\",\"reserved_bank_active_deduction_fee\":\"0\",\"reserved_discount_amt\":\"0\",\"reserved_fas_settle_dt\":\"********\",\"reserved_paidin_amt\":\"4\",\"reserved_receivable_amt\":\"0\",\"reserved_set_cd\":\"M0175\",\"sign\":\"P2KwDyZ4EU2Ks/2KAnrgkcyp5XDu Mx35CT7blQJ3zx0K6K/VhFwHImLWjt8wA4TpHuKobZN/vdFo1jDFHGw1v/Mp8zyWe5DjGxj4j4zBJubc0N6d1sWnqSg5hbfDGPJcIoIytUO0 knQVOwpu/39IPUs3rBMNqGmW5Qz5Oe5mc=\",\"txn_amt\":\"1000\",\"txn_fin_ts\":\"**************\"}"
}


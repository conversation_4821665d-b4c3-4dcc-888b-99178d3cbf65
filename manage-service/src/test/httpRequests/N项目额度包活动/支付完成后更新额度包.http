#POST http://trade-manage-service.beta.iwosai.com/rpc/discountQuota
POST http://localhost:8081/rpc/discountQuota
Content-Type: application/json
x-env-flag: pay13002

{
  "method": "updateDiscountQuotaByTransaction",
  "params": [
    {
      "subject": "自测",
      "received_amount": 20000,
      "buyer_login": "153nvkLEnU2",
      "merchant_id": "8cf4fe32-ac20-439b-9d14-ecabd1541716",
      "mtime": *************,
      "type": 30,
      "extended_params": {},
      "tsn": "****************",
      "operator": "tyw",
      "product_flag": "au",
      "extra_out_fields": {
        "trade_no": "8774590026697556561279802663",
        "wallet_account_type": 1,
        "combo_id": "4861",
        "is_default_poi": true,
        "quota_fee_rate": "0.4",
        "quota_fee_rate_tag": 73236,
        "quota_fee": 100
      },
      "reflect": "测试",
      "provider": 1038,
      "original_amount": 20000,
      "ctime": *************,
      "id": "t****************",
      "terminal_id": "63710c7e-5ead-4e73-8a57-3b828f313de3",
      "client_tsn": "tyw_2025_04_28_005",
      "store_id": "e57fe2b6-9f0d-4f9f-8138-077d002a5a0a",
      "provider_error_info": {
        "pay": {
          "msg": "SUCCESS",
          "code": "000000"
        }
      },
      "extra_params": {
        "device_id": "",
        "sqb_pay_source": "sqb_npos",
        "barcode": "287390615814730896"
      },
      "payway": 2,
      "version": 0,
      "finish_time": *************,
      "sub_payway": 1,
      "config_snapshot": {
        "clearance_provider": 8,
        "latitude": "31.309188",
        "merchant_id": "8cf4fe32-ac20-439b-9d14-ecabd1541716",
        "merchant_sn": "21690004016218",
        "merchant_monthly_max_credit_limit_trans": 401,
        "pay_status": 1,
        "merchant_daily_max_credit_limit_trans": 301,
        "provider": 1038,
        "union_over_seas_wallet_day_tran_limit": 507,
        "store_name": "商户门店名称0330WzKd",
        "currency": "CNY",
        "fuyou_trade_params": {
          "public_key": "8\"[hn[\"}b1f}fb<}Z<b8z1\"be[ey\"@18'}}Z",
          "liquidation_next_day": true,
          "sys_pid": "",
          "fee_rate_tag": {
            "1": "4861:"
          },
          "fee": 80,
          "alipay_sub_mch_id": "20881743319349250",
          "active": true,
          "agent_no": "08A9999999",
          "private_key": "[<1'}`z@b`nfhb<}y@b[Z'fbn`[z\"fZz}8}z",
          "provider_mch_id": "6961826FY783046",
          "fee_rate": "0.4"
        },
        "terminal_id": "63710c7e-5ead-4e73-8a57-3b828f313de3",
        "terminal_sn": "2101077140290782889",
        "longitude": "120.776461",
        "store_id": "e57fe2b6-9f0d-4f9f-8138-077d002a5a0a",
        "store_sn": "21590000001633159",
        "channel_name": "上海收钱吧互联网科技股份有限公司",
        "store_city": "苏州市",
        "trade_app": "1",
        "terminal_vendor_app_appid": "2024092000007714",
        "terminal_category": 116,
        "union_over_seas_wallet_single_tran_limit": 506,
        "merchant_name": "商户0330qiFF富友",
        "term_info": {
          "term_id": "SH7xtNZgDZLh"
        },
        "term_id": "SH7xtNZgDZLh",
        "terminal_name": "New Term (activateV2) 1743319680588",
        "district_code": "320506",
        "vendor_id": "859d9f5f-af99-11e5-9ec3-00163e00625b",
        "common_switch": "00000010000200000000002222222222",
        "merchant_country": "CHN"
      },
      "deleted": false,
      "effective_amount": 20000,
      "paid_amount": 20000,
      "channel_finish_time": 0,
      "order_id": "o****************",
      "buyer_uid": "mock2088L5T77p9J",
      "order_sn": "****************",
      "status": 2000
    }
  ],
  "id": "1",
  "jsonrpc": "2.0"
}



#POST localhost:8081/rpc/quota_activity
POST http://trade-manage-service.beta.iwosai.com/rpc/quota_activity
x-env-flag: pay13002
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 1461

{
  "method": "create",
  "params": [
    {
      "info": {
        "name": "N项目-N线圈额度包-冒烟",
        "desc": "N项目-N线圈额度包-冒烟",
        "type": "NFC_POS",
        "tradeAppId": 1,
        "tradeAppName": null,
        "configLevel": "merchant",
        "status": null,
        "operator": "system",
        "operatorId": null,
        "tags": null,
        "merchantNotice": {
          "applySuccess": "报名成功通知",
          "manualCancel": "手动取消通知",
          "activityExpired": "活动到期失效通知",
          "bankcardInvalid": "解绑银行卡失效通知",
          "discountQuotaUseUp": "额度包用完失效通知"
        },
        "saleNotice": null,
        "quoteType": "TERMINAL"
      },
      "rule": {
        "paywayCategory": "ALL",
        "startTime": *************,
        "endTime": *************,
        "cancelReApply": 0,
        "bandComboIds": null,
        "acquirers": [
          "lkl",
          "fuyou",
          "haike"
        ],
        "scenesTypeList": [
          "N_COIL"
        ],
        "singleOriginalAmount": {
          "min": 0,
          "max": 20000
        },
        "bankName": null,
        "cities": null,
        "discountQuota": {
          "type": "fix",
          "quota": {
            "max": "1000",
            "min": "0"
          },
          "feeRate": {
            "max": "1",
            "min": "0"
          }
        }
      }
    }
  ],
  "id": "1",
  "jsonrpc": "2.0"
}



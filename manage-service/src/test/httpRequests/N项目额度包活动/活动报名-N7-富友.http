#POST http://trade-manage-service.beta.iwosai.com/rpc/apply_quota_activity
POST http://localhost:8081/rpc/apply_quota_activity
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 1461

{
  "method": "apply",
  "params": [
    {
      "activityId": 2629,
      "externalActivityId": null,
      "merchantSn": "21690003254048",
      "merchantId": null,
      "sn": null,
      "auditId": null,
      "auditSn": null,
      "applyType": 2,
      "startTime": "",
      "endTime": "2024-06-19",
      "rolling": 1,
      "quota": 100.0,
      "feeRate": "0.1",
      "quotaRecordId": null,
      "quotaRecordIdList": null,
      "msg": null,
      "operator": null,
      "openAutoD1Withdraw": null,
      "singleOriginalAmountLimit": null,
      "isNaturalMonthRolling": null,
      "effectiveRule": null,
      "scenesCodes": null,
      "scenesCode": null
    }
  ],
  "id": "1",
  "jsonrpc": "2.0"
}
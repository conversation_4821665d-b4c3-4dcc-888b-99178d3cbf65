#POST http://localhost:8081/rpc/apply_quota_activity
POST http://trade-manage-service.beta.iwosai.com/rpc/apply_quota_activity
x-env-flag: pay13002
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Content-Length: 1461

{
    "method": "apply",
    "params": [
      {
        "activityId": 2629,
        "merchantSn": "21690003987725",
        "merchantId": "541d5351-977c-42b6-b8ff-f5b5e86514fd",
        "sn": "21690003987725",
        "auditId": null,
        "auditSn": null,
        "applyType": 1,
        "startTime": "2025-03-31",
        "endTime": "2025-04-04",
        "rolling": null,
        "quota": 200,
        "feeRate": "0.5",
        "quotaRecordId": null,
        "quotaRecordIdList": null,
        "singleOriginalAmountLimit": 2000,
        "msg": "自测-报名全选-冒烟",
        "operator": "涂玉卫"
      }
    ],
    "id": "1",
    "jsonrpc": "2.0"
}
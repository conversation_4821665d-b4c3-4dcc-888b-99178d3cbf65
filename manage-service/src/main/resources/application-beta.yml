spring:
  application:
    name : manage-service
  server:
    core-business: http://core-business/
    remit: http://remit-gateway/
    aop: http://aop-gateway/
    user-service: http://user-service/
    merchant-user-service: http://merchant-user-service/
    terminal-sale-service: http://shouqianba-terminal-service/
    sales-system-service: http://sales-system-service/
    signature-proxy: http://signature-proxy/
    credit-pay-backend-service: http://credit-pay-backend/
    upay-transaction-service: http://upay-transaction/
    upay-wallet: http://upay-wallet/
    upay-grayscale: http://upay-grayscale/
    mail-gateway: http://mail-gateway/mail/send
    enterprise: http://enterprise/
    shouqianba-risk-service: http://shouqianba-risk-service/
    merchant-contract-activity: http://merchant-contract-activity/
    merchant-contract-job: http://merchant-contract-job/
    merchant-contract-access: http://merchant-contract-access/
    merchant-business-open: http://merchant-business-open/
    data-cooperation: http://data-cooperation/
    scene-manage-service: http://scene-manage-service/
    clearance-service: http://clearance-service/
    profit-sharing: http://profit-sharing/
    uc-user-service: http://uc-user-service/
  redis:
    host: redis-beta


flags:
  consumer:
    run: false

#port
server:
  port : 8080

trade:
  activity:
    bindBankCardMessage:
      templateCode: YHBKHDSQTZMB
      devCode: NZT0IL3UJXQY
  saas:
    #saas服费app展示
    appServiceAgreementFeeRates:
      #校园外卖
      xywm: 4
      #扫码点单
      smdd: 6
  #CRM费率调整通知
  crmFeeRateAdjustNotify:
    latelyDay: 3
    #延迟时间，单位：分钟
    delayMinute: 30
    #商户配置
    merchant:
      templateCode: HMF2AG6DGEZZ
      devCode: I0UDXL7CVVCT
    #销售配置
    sale:
      templateCode: VEGQRLVTB8J2
      devCode: I0UDXL7CVVCT
  # 新希望集团Id
  xinxiwangGroupId: dcb8b829-cd1b-4a99-b8bf-39f5733f5eeb
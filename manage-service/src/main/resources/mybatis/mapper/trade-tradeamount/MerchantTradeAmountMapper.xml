<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.trade.repository.dao.MerchantTradeAmountDao">

    <select id="getAllOldMchTradeAmountPage" resultType="com.wosai.trade.repository.dao.entity.MerchantTradeAmountEntity">
        SELECT id,
        merchant_sn,
        mch_ctime,
        received_amount,
        trade_amount,
        month
        FROM merchant_trade_amount
        <where>
            <if test="month != null">
                month = #{month}
            </if>
        </where>
        limit #{startIndex},#{pageSize}
        order by id asc
    </select>

    <select id="queryMchTradeAmountByMchSnAndMonth" resultType="com.wosai.trade.repository.dao.entity.MerchantTradeAmountEntity">
        select * from merchant_trade_amount where merchant_sn = #{merchantSn} and month = #{month} limit 1
    </select>

    <select id="getTradeAmountCountByMonth" resultType="java.lang.Integer">
        select count(*) from merchant_trade_amount where month = #{month}
    </select>

</mapper>
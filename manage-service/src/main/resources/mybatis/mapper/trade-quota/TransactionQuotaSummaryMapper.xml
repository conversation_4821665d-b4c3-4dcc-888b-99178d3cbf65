<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.trade.repository.dao.TransactionQuotaSummaryDao">

    <resultMap id="QuotaSummaryResultMap" type="com.wosai.trade.repository.dao.entity.TransactionQuotaSummaryEntity">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="merchantSn" column="merchant_sn" jdbcType="VARCHAR"/>
        <result property="merchantId" column="merchant_id" jdbcType="VARCHAR"/>
        <result property="fixedQuota" column="fixed_quota" jdbcType="BIGINT"/>
        <result property="temporaryQuota" column="temporary_quota" jdbcType="BIGINT"/>
        <result property="totalQuota" column="total_quota" jdbcType="BIGINT"/>
        <result property="bizType" column="biz_type" jdbcType="TINYINT"/>
        <result property="nextComputeDate" column="next_compute_date" jdbcType="OTHER" typeHandler="org.apache.ibatis.type.LocalDateTypeHandler"/>
        <result property="ext" column="ext" jdbcType="VARCHAR"/>
        <result property="ctime" column="ctime" jdbcType="OTHER" typeHandler="org.apache.ibatis.type.LocalDateTimeTypeHandler"/>
        <result property="mtime" column="mtime" jdbcType="OTHER" typeHandler="org.apache.ibatis.type.LocalDateTimeTypeHandler"/>
        <result property="version" column="version" jdbcType="BIGINT"/>
    </resultMap>


    <sql id="table">
        transaction_quota_summary
    </sql>

    <sql id="allColumn">
        id,
        merchant_sn,
        merchant_id,
        fixed_quota,
        temporary_quota,
        total_quota,
        next_compute_date,
        ext,
        biz_type,
        ctime,
        mtime,
        version
    </sql>

    <sql id="insertColumn">
        merchant_sn,
        merchant_id,
        fixed_quota,
        temporary_quota,
        total_quota,
        next_compute_date,
        biz_type,
        ext,
        ctime,
        mtime
    </sql>

    <sql id="insertColumnValue">
        #{merchantSn},
        #{merchantId},
        #{fixedQuota},
        #{temporaryQuota},
        #{totalQuota},
        #{nextComputeDate},
        #{bizType},
        #{ext},
        #{ctime},
        #{mtime}
    </sql>


    <insert id="insert">
        insert into
        <include refid="table"/>
        (
        <include refid="insertColumn"/>
        )
        values
        (
        <include refid="insertColumnValue"/>
        )
    </insert>

    <insert id="insertOnDuplicateUpdate" parameterType="TransactionQuotaSummaryUpsertDalParam">
        insert into
        <include refid="table"/>
        (
        <include refid="insertColumn"/>
        )
        values
        (
        <include refid="insertColumnValue"/>
        )
        on duplicate key update
            fixed_quota = values(fixed_quota), temporary_quota = values(temporary_quota)
            , total_quota = values(total_quota), next_compute_date = values(next_compute_date)
            , mtime = values(mtime), version = version + 1
    </insert>

    <insert id="batchInsertOnDuplicateUpdate" parameterType="TransactionQuotaSummaryBatchUpsertDalParam">
        insert into
        <include refid="table"/>
        (
        <include refid="insertColumn"/>
        )
        values
        <foreach collection="summaryUpsertDalParams" item="item" separator=",">
            (#{item.merchantSn}, #{item.merchantId}, #{item.fixedQuota}, #{item.temporaryQuota}
            , #{item.totalQuota}, #{item.nextComputeDate}, #{item.bizType}, #{item.ext}, #{item.ctime}, #{item.mtime})
        </foreach>
        on duplicate key update
            next_compute_date = values(next_compute_date)
            , mtime = values(mtime), version = version + 1
    </insert>

    <insert id="insertIgnoreOnDuplicate" parameterType="TransactionQuotaSummaryUpsertDalParam">
        insert ignore into
        <include refid="table"/>
        (
        <include refid="insertColumn"/>
        )
        values
        (
        <include refid="insertColumnValue"/>
        )
    </insert>

    <select id="query" parameterType="TransactionQuotaSummaryQueryDalParam" resultMap="QuotaSummaryResultMap">
        select
        <include refid="allColumn"/>
        from
        <include refid="table"/>
        <where>
            <choose>
                <when test="merchantSn != null">
                    merchant_sn = #{merchantSn}
                </when>
                <otherwise>
                    false
                </otherwise>
            </choose>
        </where>
        <if test="bizType != null">
        and biz_type = #{bizType}
        </if>
        limit 1
    </select>

    <select id="queryForUpdate" parameterType="TransactionQuotaSummaryQueryDalParam" resultMap="QuotaSummaryResultMap">
        select
        <include refid="allColumn"/>
        from
        <include refid="table"/>
        <where>
            <choose>
                <when test="merchantSn != null">
                    merchant_sn = #{merchantSn}
                </when>
                <otherwise>
                    false
                </otherwise>
            </choose>
        </where>
        <if test="bizType != null">
            and biz_type = #{bizType}
        </if>
        limit 1
        for update
    </select>

    <select id="list" parameterType="TransactionQuotaSummaryQueryDalParam" resultMap="QuotaSummaryResultMap">
        select
        <include refid="allColumn"/>
        from
        <include refid="table"/>
        <where>
            <choose>
                <when test="merchantSnList != null and merchantSnList.size() > 0">
                    merchant_sn in
                    <foreach collection="merchantSnList" item="merSn" open="(" close=")" separator=",">
                        #{merSn}
                    </foreach>
                </when>
                <when test="currentDate != null">
                    next_compute_date <![CDATA[<=]]> #{currentDate}
                </when>
                <otherwise>
                    false
                </otherwise>
            </choose>
        </where>
        <if test="bizType != null">
            and biz_type = #{bizType}
        </if>
        order by id asc
        <if test="count != null">
            limit ${count}
        </if>
    </select>

    <select id="listForUpdate" parameterType="TransactionQuotaSummaryQueryDalParam" resultMap="QuotaSummaryResultMap">
        select
        <include refid="allColumn"/>
        from
        <include refid="table"/>
        <where>
            <choose>
                <when test="merchantSnList != null and merchantSnList.size() > 0">
                    merchant_sn in
                    <foreach collection="merchantSnList" item="merSn" open="(" close=")" separator=",">
                        #{merSn}
                    </foreach>
                </when>
                <otherwise>
                    false
                </otherwise>
            </choose>
        </where>
        for update
    </select>

    <update id="update" parameterType="TransactionQuotaSummaryUpsertDalParam">
        update
        <include refid="table"/>
        <set>
            <if test="fixedQuota != null">
                fixed_quota = #{fixedQuota},
            </if>
            <if test="temporaryQuota != null">
                temporary_quota = #{temporaryQuota},
            </if>
            <if test="totalQuota != null">
                total_quota = #{totalQuota},
            </if>
            <if test="isNeedModifyNextComputeDate != null and isNeedModifyNextComputeDate">
                next_compute_date = #{nextComputeDate},
            </if>
            mtime = #{mtime}, version = version + 1
        </set>
        where merchant_sn = #{merchantSn} and biz_type = #{bizType}
    </update>

    <update id="updateWithOptimisticLock" parameterType="TransactionQuotaSummaryUpsertDalParam">
        update
        <include refid="table"/>
        set
            temporary_quota = #{temporaryQuota},
            total_quota = #{totalQuota},
            next_compute_date = #{nextComputeDate},
            mtime = #{mtime}, version = version + 1
        where merchant_sn = #{merchantSn} and biz_type = #{bizType} and version = #{version}

    </update>


</mapper>
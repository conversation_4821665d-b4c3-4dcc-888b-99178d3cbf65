<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.trade.repository.dao.ApplyAuditRecordEntityMapper">
    <resultMap id="BaseResultMap" type="com.wosai.trade.repository.dao.entity.ApplyAuditRecordEntity">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="merchant_sn" property="merchantSn" jdbcType="VARCHAR"/>
        <result column="merchant_id" property="merchantId" jdbcType="VARCHAR"/>
        <result column="audit_sn" property="auditSn" jdbcType="VARCHAR"/>
        <result column="audit_template_id" property="auditTemplateId" jdbcType="VARCHAR"/>
        <result column="template_event" property="templateEvent" jdbcType="VARCHAR"/>
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR"/>
        <result column="proc_status" property="procStatus" jdbcType="INTEGER"/>
        <result column="sub_proc_status" property="subProcStatus" jdbcType="INTEGER"/>
        <result column="error_msg" property="errorMsg" jdbcType="VARCHAR"/>
        <result column="extra" property="extra" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
        <result column="ctime" property="ctime" jdbcType="TIMESTAMP"/>
        <result column="mtime" property="mtime" jdbcType="TIMESTAMP"/>
        <result column="deleted" property="deleted" jdbcType="TINYINT"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, merchant_sn, merchant_id, audit_sn, audit_template_id, template_event, operator_name,
        proc_status, sub_proc_status, error_msg, extra, remark, version, ctime, mtime, deleted
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from apply_audit_record
        where id = #{id,jdbcType=VARCHAR} and deleted = 0
    </select>

    <select id="queryMerchantApplyAuditRecord" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from apply_audit_record
        where merchant_sn =#{merchantSn,jdbcType=VARCHAR}
        and template_event = #{templateEvent,jdbcType=VARCHAR}
        and deleted = 0
        limit 100;
    </select>

    <update id="updateProcStatusByAuditSn">
        update apply_audit_record
        set mtime = now(),version=version+1,proc_status=#{status,jdbcType=INTEGER}
        where audit_sn =#{auditSn,jdbcType=VARCHAR}
        and deleted = 0
    </update>

    <select id="queryByTemplateEventAndProcStatusList"
            resultType="com.wosai.trade.repository.dao.entity.ApplyAuditRecordEntity">
        select
        <include refid="Base_Column_List"/>
        from apply_audit_record
        where
        template_event = #{templateEvent,jdbcType=VARCHAR}
        and ctime >= #{beginDate,jdbcType=TIMESTAMP}
        and deleted = 0
        and proc_status in
        <foreach collection="statusList" item="status" open="(" close=")" separator=",">
            #{status}
        </foreach>
    </select>
    <insert id="insert" parameterType="com.wosai.trade.repository.dao.entity.ApplyAuditRecordEntity">
        insert into apply_audit_record (id, merchant_sn, merchant_id,
        audit_sn, audit_template_id, template_event,
        operator_name, proc_status, sub_proc_status,
        error_msg, extra, remark,
        version, ctime, mtime,
        deleted)
        values (#{id,jdbcType=VARCHAR}, #{merchantSn,jdbcType=VARCHAR}, #{merchantId,jdbcType=VARCHAR},
        #{auditSn,jdbcType=VARCHAR}, #{auditTemplateId,jdbcType=VARCHAR}, #{templateEvent,jdbcType=VARCHAR},
        #{operatorName,jdbcType=VARCHAR}, #{procStatus,jdbcType=INTEGER}, #{subProcStatus,jdbcType=INTEGER},
        #{errorMsg,jdbcType=VARCHAR}, #{extra,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
        0, now(), now(), false)
    </insert>
    <insert id="insertSelective" parameterType="com.wosai.trade.repository.dao.entity.ApplyAuditRecordEntity">
        insert into apply_audit_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="merchantSn != null">
                merchant_sn,
            </if>
            <if test="merchantId != null">
                merchant_id,
            </if>
            <if test="auditSn != null">
                audit_sn,
            </if>
            <if test="auditTemplateId != null">
                audit_template_id,
            </if>
            <if test="templateEvent != null">
                template_event,
            </if>
            <if test="operatorName != null">
                operator_name,
            </if>
            <if test="procStatus != null">
                proc_status,
            </if>
            <if test="subProcStatus != null">
                sub_proc_status,
            </if>
            <if test="errorMsg != null">
                error_msg,
            </if>
            <if test="extra != null">
                extra,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="ctime != null">
                ctime,
            </if>
            <if test="mtime != null">
                mtime,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="merchantSn != null">
                #{merchantSn,jdbcType=VARCHAR},
            </if>
            <if test="merchantId != null">
                #{merchantId,jdbcType=VARCHAR},
            </if>
            <if test="auditSn != null">
                #{auditSn,jdbcType=VARCHAR},
            </if>
            <if test="auditTemplateId != null">
                #{auditTemplateId,jdbcType=VARCHAR},
            </if>
            <if test="templateEvent != null">
                #{templateEvent,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null">
                #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="procStatus != null">
                #{procStatus,jdbcType=INTEGER},
            </if>
            <if test="subProcStatus != null">
                #{subProcStatus,jdbcType=INTEGER},
            </if>
            <if test="errorMsg != null">
                #{errorMsg,jdbcType=VARCHAR},
            </if>
            <if test="extra != null">
                #{extra,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="version != null">
                #{version,jdbcType=INTEGER},
            </if>
            <if test="ctime != null">
                #{ctime,jdbcType=TIMESTAMP},
            </if>
            <if test="mtime != null">
                #{mtime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.wosai.trade.repository.dao.entity.ApplyAuditRecordEntity">
        update apply_audit_record
        <set>
            <if test="merchantSn != null">
                merchant_sn = #{merchantSn,jdbcType=VARCHAR},
            </if>
            <if test="merchantId != null">
                merchant_id = #{merchantId,jdbcType=VARCHAR},
            </if>
            <if test="auditSn != null">
                audit_sn = #{auditSn,jdbcType=VARCHAR},
            </if>
            <if test="auditTemplateId != null">
                audit_template_id = #{auditTemplateId,jdbcType=VARCHAR},
            </if>
            <if test="templateEvent != null">
                template_event = #{templateEvent,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null">
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="procStatus != null">
                proc_status = #{procStatus,jdbcType=INTEGER},
            </if>
            <if test="subProcStatus != null">
                sub_proc_status = #{subProcStatus,jdbcType=INTEGER},
            </if>
            <if test="errorMsg != null">
                error_msg = #{errorMsg,jdbcType=VARCHAR},
            </if>
            <if test="extra != null">
                extra = #{extra,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=TINYINT},
            </if>
            mtime = now(),
            version = version + 1
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from apply_audit_record
        where id = #{id,jdbcType=VARCHAR}
    </delete>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.trade.repository.dao.TradeActivityDao">

    <resultMap id="TradeActivityEntityMap" type="com.wosai.trade.repository.dao.entity.TradeActivityEntity">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="merchantSn" column="merchant_sn" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="beginDate" column="begin_date" jdbcType="OTHER"
                typeHandler="org.apache.ibatis.type.LocalDateTypeHandler"/>
        <result property="endDate" column="end_date" jdbcType="OTHER"
                typeHandler="org.apache.ibatis.type.LocalDateTypeHandler"/>
        <result property="auditInfo" column="audit_info"
                typeHandler="com.wosai.trade.repository.dao.JsonBlobTypeHandler"/>
        <result property="extra" column="extra" typeHandler="com.wosai.trade.repository.dao.JsonBlobTypeHandler"/>
        <result property="ctime" column="ctime" jdbcType="BIGINT"/>
        <result property="mtime" column="mtime" jdbcType="BIGINT"/>
    </resultMap>

    <insert id="insert" parameterType="com.wosai.trade.repository.dao.entity.TradeActivityEntity">
        insert into trade_activity
        (
            `merchant_sn`,  `type`, `status`, begin_date,end_date,audit_info,extra,`ctime`, `mtime`
        )
        values
        (
            #{merchantSn}, #{type}, #{status}, #{beginDate},#{endDate},
            #{auditInfo,jdbcType=OTHER,typeHandler=com.wosai.trade.repository.dao.JsonBlobTypeHandler},
            #{extra,jdbcType=OTHER,typeHandler=com.wosai.trade.repository.dao.JsonBlobTypeHandler}, unix_timestamp()*1000, unix_timestamp()*1000
        )
    </insert>

    <update id="updateActivityStatusByIdAndPreStatus">
        update trade_activity set status = #{afterStatus}, mtime=unix_timestamp(now())*1000 where id = #{id} and status = #{preStatus}
    </update>

    <update id="updateExtraById">
        UPDATE trade_activity
        SET extra = #{extra,jdbcType=OTHER,typeHandler=com.wosai.trade.repository.dao.JsonBlobTypeHandler}, mtime=unix_timestamp(now())*1000
        WHERE id = #{id}
    </update>

    <update id="batchUpdateStatus">
        UPDATE trade_activity SET status = #{status} , mtime=unix_timestamp(now())*1000 WHERE id IN (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
    </update>

    <select id="getEnableActivityByMerchantSnAndType" resultMap="TradeActivityEntityMap">
        select * from trade_activity where merchant_sn = #{merchantSn} and type = #{type} and status = 1 limit 1;
    </select>

    <select id="getEnableActivityListByMerchantSnAndType" resultMap="TradeActivityEntityMap">
        select * from trade_activity where merchant_sn = #{merchantSn} and type = #{type} and status = 1;
    </select>

    <select id="allExpireMerchant" resultMap="TradeActivityEntityMap">
        select * from trade_activity where end_date &lt;= #{endDate} and status = 1 limit #{limit}
    </select>

    <select id="selectInUsedList" resultMap="TradeActivityEntityMap">
        SELECT *
        FROM trade_activity
        WHERE type = #{type} AND end_date >= #{beginDate} AND end_date &lt;= #{endDate} AND status = 1
        ORDER BY id
        LIMIT #{limit}
    </select>

    <select id="queryTradeActivities" resultMap="TradeActivityEntityMap">
        SELECT id,
            merchant_sn,
            type,
            status,
            begin_date,
            end_date,
            audit_info,
            extra,
            ctime,
            mtime
        FROM trade_activity
        <where>
            AND type = #{type}
            AND status = 1
            AND id > #{id}
            AND ctime &lt; #{endTime}
        </where>
        order by id asc
        limit #{pageSize}
    </select>

    <select id="queryMerchantPendingApplyRecords" resultMap="TradeActivityEntityMap">
        SELECT id,
            merchant_sn,
            type,
            status,
            begin_date,
            end_date,
            audit_info,
            extra,
            ctime,
            mtime
        FROM trade_activity
        <where>
            AND merchant_sn = #{merchantSn}
            AND type = #{type}
            AND status = 1
            AND ctime between #{startTime} and #{endTime}
        </where>
    </select>
</mapper>
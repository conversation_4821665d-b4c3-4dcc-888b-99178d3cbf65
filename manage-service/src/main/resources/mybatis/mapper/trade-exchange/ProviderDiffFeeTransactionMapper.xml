<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.trade.repository.dao.ProviderDiffFeeTransactionMapper">

    <resultMap id="BaseResultMap" type="com.wosai.trade.repository.dao.entity.ProviderDiffFeeTransactionEntity">
            <id property="tsn" column="tsn" jdbcType="VARCHAR"/>
            <result property="merchantSn" column="merchant_sn" jdbcType="VARCHAR"/>
            <result property="merchantId" column="merchant_id" jdbcType="VARCHAR"/>
            <result property="merchantName" column="merchant_name" jdbcType="VARCHAR"/>
            <result property="provider" column="provider" jdbcType="TINYINT"/>
            <result property="payWay" column="pay_way" jdbcType="INTEGER"/>
            <result property="cusId" column="cus_id" jdbcType="VARCHAR"/>
            <result property="originalAmount" column="original_amount" jdbcType="BIGINT"/>
            <result property="recevieAmount" column="recevie_amount" jdbcType="BIGINT"/>
            <result property="fee" column="fee" jdbcType="BIGINT"/>
            <result property="realFee" column="real_fee" jdbcType="BIGINT"/>
            <result property="realFeeRate" column="real_fee_rate" jdbcType="VARCHAR"/>
            <result property="level1Name" column="level1_name" jdbcType="VARCHAR"/>
            <result property="level2Name" column="level2_name" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="ctime" column="ctime" jdbcType="BIGINT"/>
            <result property="mtime" column="mtime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="SummaryResultMap" type="com.wosai.trade.repository.dao.entity.ProviderDiffFeeTransactionSummaryEntity">
            <result property="merchantSn" column="merchant_sn" jdbcType="VARCHAR"/>
            <result property="provider" column="provider" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        tsn,merchant_sn,merchant_id,
        merchant_name,provider,pay_way,
        cus_id,original_amount,recevie_amount,
        fee,real_fee,real_fee_rate,
        level1_name,level2_name,remark,
        ctime,mtime
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from provider_diff_fee_transaction
        where  tsn = #{tsn,jdbcType=VARCHAR} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from provider_diff_fee_transaction
        where  tsn = #{tsn,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" keyColumn="tsn" keyProperty="tsn" parameterType="com.wosai.trade.repository.dao.entity.ProviderDiffFeeTransactionEntity" useGeneratedKeys="true">
        insert into provider_diff_fee_transaction
        ( tsn,merchant_sn,merchant_id
        ,merchant_name,provider,pay_way
        ,cus_id,original_amount,recevie_amount
        ,fee,real_fee,real_fee_rate
        ,level1_name,level2_name,remark
        ,ctime,mtime)
        values (#{tsn,jdbcType=VARCHAR},#{merchantSn,jdbcType=VARCHAR},#{merchantId,jdbcType=VARCHAR}
        ,#{merchantName,jdbcType=VARCHAR},#{provider,jdbcType=TINYINT},#{payWay,jdbcType=INTEGER}
        ,#{cusId,jdbcType=VARCHAR},#{originalAmount,jdbcType=BIGINT},#{recevieAmount,jdbcType=BIGINT}
        ,#{fee,jdbcType=BIGINT},#{realFee,jdbcType=BIGINT},#{realFeeRate,jdbcType=VARCHAR}
        ,#{level1Name,jdbcType=VARCHAR},#{level2Name,jdbcType=VARCHAR},#{remark,jdbcType=VARCHAR}
        ,#{ctime,jdbcType=BIGINT},#{mtime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="tsn" keyProperty="tsn" parameterType="com.wosai.trade.repository.dao.entity.ProviderDiffFeeTransactionEntity" useGeneratedKeys="true">
        insert into provider_diff_fee_transaction
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="tsn != null">tsn,</if>
                <if test="merchantSn != null">merchant_sn,</if>
                <if test="merchantId != null">merchant_id,</if>
                <if test="merchantName != null">merchant_name,</if>
                <if test="provider != null">provider,</if>
                <if test="payWay != null">pay_way,</if>
                <if test="cusId != null">cus_id,</if>
                <if test="originalAmount != null">original_amount,</if>
                <if test="recevieAmount != null">recevie_amount,</if>
                <if test="fee != null">fee,</if>
                <if test="realFee != null">real_fee,</if>
                <if test="realFeeRate != null">real_fee_rate,</if>
                <if test="level1Name != null">level1_name,</if>
                <if test="level2Name != null">level2_name,</if>
                <if test="remark != null">remark,</if>
                <if test="ctime != null">ctime,</if>
                <if test="mtime != null">mtime,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="tsn != null">#{tsn,jdbcType=VARCHAR},</if>
                <if test="merchantSn != null">#{merchantSn,jdbcType=VARCHAR},</if>
                <if test="merchantId != null">#{merchantId,jdbcType=VARCHAR},</if>
                <if test="merchantName != null">#{merchantName,jdbcType=VARCHAR},</if>
                <if test="provider != null">#{provider,jdbcType=TINYINT},</if>
                <if test="payWay != null">#{payWay,jdbcType=INTEGER},</if>
                <if test="cusId != null">#{cusId,jdbcType=VARCHAR},</if>
                <if test="originalAmount != null">#{originalAmount,jdbcType=BIGINT},</if>
                <if test="recevieAmount != null">#{recevieAmount,jdbcType=BIGINT},</if>
                <if test="fee != null">#{fee,jdbcType=BIGINT},</if>
                <if test="realFee != null">#{realFee,jdbcType=BIGINT},</if>
                <if test="realFeeRate != null">#{realFeeRate,jdbcType=VARCHAR},</if>
                <if test="level1Name != null">#{level1Name,jdbcType=VARCHAR},</if>
                <if test="level2Name != null">#{level2Name,jdbcType=VARCHAR},</if>
                <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
                <if test="ctime != null">#{ctime,jdbcType=BIGINT},</if>
                <if test="mtime != null">#{mtime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.wosai.trade.repository.dao.entity.ProviderDiffFeeTransactionEntity">
        update provider_diff_fee_transaction
        <set>
                <if test="merchantSn != null">
                    merchant_sn = #{merchantSn,jdbcType=VARCHAR},
                </if>
                <if test="merchantId != null">
                    merchant_id = #{merchantId,jdbcType=VARCHAR},
                </if>
                <if test="merchantName != null">
                    merchant_name = #{merchantName,jdbcType=VARCHAR},
                </if>
                <if test="provider != null">
                    provider = #{provider,jdbcType=TINYINT},
                </if>
                <if test="payWay != null">
                    pay_way = #{payWay,jdbcType=INTEGER},
                </if>
                <if test="cusId != null">
                    cus_id = #{cusId,jdbcType=VARCHAR},
                </if>
                <if test="originalAmount != null">
                    original_amount = #{originalAmount,jdbcType=BIGINT},
                </if>
                <if test="recevieAmount != null">
                    recevie_amount = #{recevieAmount,jdbcType=BIGINT},
                </if>
                <if test="fee != null">
                    fee = #{fee,jdbcType=BIGINT},
                </if>
                <if test="realFee != null">
                    real_fee = #{realFee,jdbcType=BIGINT},
                </if>
                <if test="realFeeRate != null">
                    real_fee_rate = #{realFeeRate,jdbcType=VARCHAR},
                </if>
                <if test="level1Name != null">
                    level1_name = #{level1Name,jdbcType=VARCHAR},
                </if>
                <if test="level2Name != null">
                    level2_name = #{level2Name,jdbcType=VARCHAR},
                </if>
                <if test="remark != null">
                    remark = #{remark,jdbcType=VARCHAR},
                </if>
                <if test="ctime != null">
                    ctime = #{ctime,jdbcType=BIGINT},
                </if>
                <if test="mtime != null">
                    mtime = #{mtime,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   tsn = #{tsn,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.wosai.trade.repository.dao.entity.ProviderDiffFeeTransactionEntity">
        update provider_diff_fee_transaction
        set
            merchant_sn =  #{merchantSn,jdbcType=VARCHAR},
            merchant_id =  #{merchantId,jdbcType=VARCHAR},
            merchant_name =  #{merchantName,jdbcType=VARCHAR},
            provider =  #{provider,jdbcType=TINYINT},
            pay_way =  #{payWay,jdbcType=INTEGER},
            cus_id =  #{cusId,jdbcType=VARCHAR},
            original_amount =  #{originalAmount,jdbcType=BIGINT},
            recevie_amount =  #{recevieAmount,jdbcType=BIGINT},
            fee =  #{fee,jdbcType=BIGINT},
            real_fee =  #{realFee,jdbcType=BIGINT},
            real_fee_rate =  #{realFeeRate,jdbcType=VARCHAR},
            level1_name =  #{level1Name,jdbcType=VARCHAR},
            level2_name =  #{level2Name,jdbcType=VARCHAR},
            remark =  #{remark,jdbcType=VARCHAR},
            ctime =  #{ctime,jdbcType=BIGINT},
            mtime =  #{mtime,jdbcType=TIMESTAMP}
        where   tsn = #{tsn,jdbcType=VARCHAR}
    </update>

    <select id="queryRangeCtimeList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        FROM provider_diff_fee_transaction
        where ctime <![CDATA[ >= ]]> #{begin}
        and ctime <![CDATA[ < ]]> #{end}
    </select>

    <select id="queryMerchantProviderSummary" resultMap="SummaryResultMap">
        SELECT
            `merchant_sn`,
            `provider`
        FROM `provider_diff_fee_transaction`
        WHERE mtime >= #{mtime}
        GROUP BY `merchant_sn`, `provider`
        ORDER BY `provider`, `merchant_sn` ASC
        LIMIT #{offset}, #{limit}
    </select>

</mapper>

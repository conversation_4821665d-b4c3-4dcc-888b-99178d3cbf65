<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.trade.repository.dao.CrmFeeRateChangeMapper">
    <resultMap id="BaseResultMap" type="com.wosai.trade.repository.dao.entity.CrmFeeRateChangeEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="merchant_sn" property="merchantSn" jdbcType="VARCHAR"/>
        <result column="pay_way" property="payWay" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="value" property="value" jdbcType="OTHER" typeHandler="com.wosai.trade.repository.dao.JsonBlobTypeHandler"/>
        <result column="ctime" property="ctime" jdbcType="TIMESTAMP"/>
        <result column="mtime" property="mtime" jdbcType="TIMESTAMP"/>
        <result column="deleted" property="deleted" jdbcType="TINYINT"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,type, merchant_sn, pay_way, status, `value`, ctime, mtime, deleted, version
    </sql>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from crm_fee_rate_change
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insertSelective" parameterType="com.wosai.trade.repository.dao.entity.CrmFeeRateChangeEntity"
            useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        insert into crm_fee_rate_change
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="merchantSn != null">
                merchant_sn,
            </if>
            <if test="payWay != null">
                pay_way,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="ctime != null">
                ctime,
            </if>
            <if test="mtime != null">
                mtime,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="value != null">
                value,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="merchantSn != null">
                REPLACE(REPLACE(#{merchantSn,jdbcType=VARCHAR}, '　',''),' ',''),
            </if>
            <if test="payWay != null">
                #{payWay,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                #{type,jdbcType = INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="ctime != null">
                #{ctime,jdbcType=TIMESTAMP},
            </if>
            <if test="mtime != null">
                #{mtime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=TINYINT},
            </if>
            <if test="version != null">
                #{version,jdbcType=INTEGER},
            </if>
            <if test="value != null">
                #{value,jdbcType=OTHER,typeHandler=com.wosai.trade.repository.dao.JsonBlobTypeHandler},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.wosai.trade.repository.dao.entity.CrmFeeRateChangeEntity">
        update crm_fee_rate_change
        <set>
            <if test="merchantSn != null">
                merchant_sn = #{merchantSn,jdbcType=VARCHAR},
            </if>
            <if test="payWay != null">
                pay_way = #{payWay,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="ctime != null">
                ctime = #{ctime,jdbcType=TIMESTAMP},
            </if>
            <if test="mtime != null">
                mtime = #{mtime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=TINYINT},
            </if>
            <if test="value != null">
                value = #{value,jdbcType=OTHER,typeHandler=com.wosai.trade.repository.dao.JsonBlobTypeHandler},
            </if>
            version = #{version,jdbcType=INTEGER} + 1
        </set>
        where id = #{id,jdbcType=BIGINT} and version=#{version}
    </update>
    <select id="findByPkId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crm_fee_rate_change where id=#{id}
    </select>
    <select id="findByStatusList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crm_fee_rate_change where 1=1
        <if test="merchantSn != null">
            and merchant_sn = #{merchantSn}
        </if>
        <if test="payWay != null">
            and pay_way = #{payWay}
        </if>

        <if test="types != null">
            and type in
            <foreach collection="types" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and status in
        <foreach collection="statusList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        limit 200
    </select>

    <select id="findLastUpdateInitMerchantList" resultMap="BaseResultMap">
        select merchant_sn, MAX(ctime) as ctime
        from crm_fee_rate_change
        where 1=1
            and ctime &gt;= #{beginTime}
            and status in
            <foreach collection="statusList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>

        <if test="types != null">
            and type in
            <foreach collection="types" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by merchant_sn
        having ctime &lt;= #{endTime}
        order by merchant_sn
        limit #{page.offset}, #{page.pageSize}
    </select>

    <select id="findMerchantSnByGroupList" resultType="java.lang.String">
        select merchant_sn
        from crm_fee_rate_change
        where 1=1
        and ctime &gt;= #{beginTime}
        and status in
        <foreach collection="statusList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

        <if test="type != null">
            and type = #{type}
        </if>
        group by merchant_sn
        order by merchant_sn
        limit #{page.offset}, #{page.pageSize}
    </select>

    <select id="queryDelayTakeAffectActivityApply" resultMap="BaseResultMap">
        select
        *
        from crm_fee_rate_change
        <where>
            and `type` in
            <foreach collection="typeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and status in
            <foreach collection="statusList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>

            <if test="pageable.lastId != null">
                and id > #{pageable.lastId}
            </if>
        </where>
        order by id
        limit #{pageable.limit}
    </select>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.trade.repository.dao.TradeExchangeRateDao">


    <insert id="insert"
            parameterType="com.wosai.trade.repository.dao.entity.TradeExchangeRateEntity">
        insert ignore into  trade_exchange_rate(`date`, currency,`update_time`, `value`)
            value (#{date}, #{currency},#{update_time}, #{value})
    </insert>


</mapper>
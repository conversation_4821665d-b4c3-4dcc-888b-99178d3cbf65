<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.trade.repository.dao.TradeComboDetailDao">

    <resultMap id="TradeComboDetailResultMap" type="com.wosai.trade.repository.dao.entity.TradeComboDetailEntity">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="comboId" column="combo_id" jdbcType="BIGINT"/>
        <result property="payway" column="payway" jdbcType="INTEGER"/>
        <result property="feeRate" column="fee_rate" jdbcType="VARCHAR"/>
        <result property="feeRateMin" column="fee_rate_min" jdbcType="VARCHAR"/>
        <result property="feeRateMax" column="fee_rate_max" jdbcType="VARCHAR"/>
        <result property="mutexTradeCombo" column="mutex_trade_combo" jdbcType="VARCHAR"/>
        <result property="extendInfo" column="extend_info"
                typeHandler="com.wosai.trade.repository.dao.JsonBlobTypeHandler"/>
        <result property="ctime" column="ctime" jdbcType="BIGINT"/>
        <result property="mtime" column="mtime" jdbcType="BIGINT"/>
        <result property="ladderFeeRates" column="ladder_fee_rates" jdbcType="VARCHAR"/>
        <result property="channelFeeRates" column="channel_fee_rates" jdbcType="VARCHAR"/>
    </resultMap>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from trade_combo_detail
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="batchInsert">
        INSERT INTO trade_combo_detail(combo_id, payway, fee_rate, sub_payway_status, mutex_trade_combo,
        extend_info,
        ctime, mtime)
        VALUES
        <foreach collection="collection" item="item" index="index" open="" close="" separator=",">
            (#{item.comboId}, #{item.payWay}, #{item.feeRate}, #{item.subPayWayStatus}, #{item.mutexTradeCombo},
            #{item.extendInfo, jdbcType=OTHER, typeHandler=com.wosai.trade.repository.dao.JsonBlobTypeHandler},
            UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000)
        </foreach>
    </insert>

    <update id="updateById" parameterType="TradeComboDetailUpsertDalParam">
        UPDATE trade_combo_detail
        <set>
            <if test="comboId != null">
                combo_id = #{comboId},
            </if>
            <if test="payWay != null">
                payway = #{payWay},
            </if>
            <if test="feeRate != null">
                fee_rate = #{feeRate},
            </if>
            <if test="subPayWayStatus != null">
                sub_payway_status = #{subPayWayStatus},
            </if>
            <if test="mutexTradeCombo != null">
                mutex_trade_combo = #{mutexTradeCombo},
            </if>
            <if test="extendInfo != null">
                extend_info = #{extend_info},
            </if>
            mtime = UNIX_TIMESTAMP() * 1000
        </set>
        WHERE id = #{id}
    </update>

    <select id="selectList" parameterType="TradeComboDetailQueryDalParam" resultMap="TradeComboDetailResultMap">
        SELECT id,
        combo_id,
        payway,
        fee_rate,
        fee_rate_min,
        fee_rate_max,
        sub_payway_status,
        mutex_trade_combo,
        extend_info,
        ctime,
        mtime,
        ladder_fee_rates,
        channel_fee_rates
        FROM trade_combo_detail
        <where>
            <if test="comboId != null">
                AND combo_id = #{comboId}
            </if>
            <if test="payWay != null">
                AND payway = #{payWay}
            </if>
            <if test="comboIds != null">
                AND combo_id in
                <foreach collection="comboIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectById" resultMap="TradeComboDetailResultMap">
        SELECT id,
               combo_id,
               payway,
               fee_rate,
               fee_rate_min,
               fee_rate_max,
               sub_payway_status,
               mutex_trade_combo,
               extend_info,
               ctime,
               mtime,
               ladder_fee_rates,
               channel_fee_rates
        FROM trade_combo_detail
        WHERE id = #{id}
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.trade.repository.dao.TerminalFeeRateDao">

    <select id="count" parameterType="com.wosai.trade.model.dal.TerminalFeeRateCountDalParam" resultType="int">
        SELECT COUNT(*)
        FROM terminal_fee_rate
        <where>
            <if test="merchantSn != null">
                AND merchant_sn = #{merchantSn}
            </if>
            <if test="terminalSn != null">
                AND terminal_sn = #{terminalSn}
            </if>
            <if test="tradeComboId != null">
                AND trade_combo_id = #{tradeComboId}
            </if>
            <if test="status != null">
                AND `status` = #{status}
            </if>
            <if test="payWays != null">
                AND `pay_way` in
                <foreach item="payWayItem" collection="payWays" open="(" close=")" separator=",">
                    #{payWayItem}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectById" resultType="com.wosai.trade.repository.dao.entity.TerminalFeeRateEntity">
        SELECT id,
               app_id,
               merchant_sn,
               terminal_sn,
               trade_combo_id,
               pay_way,
               begin_date,
               end_date,
               b2c_in_use,
               c2b_in_use,
               app_in_use,
               mini_in_use,
               h5_in_use,
               wap_in_use,
               fee_rate_type,
               fee_rate,
               audit_sn,
               `status`,
               create_at,
               update_at
        FROM terminal_fee_rate
        WHERE id = #{id}
    </select>

    <select id="selectList" parameterType="com.wosai.trade.model.dal.TerminalFeeRateQueryDalParam"
            resultType="com.wosai.trade.repository.dao.entity.TerminalFeeRateEntity">
        SELECT id,
        app_id,
        merchant_sn,
        terminal_sn,
        trade_combo_id,
        pay_way,
        begin_date,
        end_date,
        b2c_in_use,
        c2b_in_use,
        app_in_use,
        mini_in_use,
        h5_in_use,
        wap_in_use,
        fee_rate_type,
        fee_rate,
        audit_sn,
        `status`,
        create_at,
        update_at
        FROM terminal_fee_rate
        <where>
            <if test="appId != null">
                AND app_id = #{appId}
            </if>
            <if test="merchantSn != null">
                AND merchant_sn = #{merchantSn}
            </if>
            <if test="terminalSn != null">
                AND terminal_sn = #{terminalSn}
            </if>
            <if test="tradeComboId != null">
                AND trade_combo_id = #{tradeComboId}
            </if>
            <if test="payWay != null">
                AND pay_way = #{payWay}
            </if>
            <if test="feeRateType != null">
                AND fee_rate_type = #{feeRateType}
            </if>
            <if test="endDate != null">
                AND end_date = #{endDate}
            </if>
            <if test="status != null">
                AND `status` = #{status}
            </if>
        </where>
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <update id="updateById" parameterType="com.wosai.trade.model.dal.TerminalFeeRateUpsertDalParam">
        UPDATE terminal_fee_rate
        <set>
            <if test="b2cInUse != null">
                b2c_in_use = #{b2cInUse},
            </if>
            <if test="c2bInUse != null">
                c2b_in_use = #{c2bInUse},
            </if>
            <if test="appInUse != null">
                app_in_use = #{appInUse},
            </if>
            <if test="miniInUse != null">
                mini_in_use = #{miniInUse},
            </if>
            <if test="h5InUse != null">
                h5_in_use = #{h5InUse},
            </if>
            <if test="wapInUse != null">
                wap_in_use = #{wapInUse},
            </if>
            <if test="status != null">
                `status` = #{status},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id"
            parameterType="com.wosai.trade.model.dal.TerminalFeeRateUpsertDalParam">
        INSERT INTO terminal_fee_rate(app_id, merchant_sn, terminal_sn, trade_combo_id, pay_way, begin_date, end_date,
                                      b2c_in_use, c2b_in_use, app_in_use, mini_in_use, h5_in_use, wap_in_use,
                                      fee_rate_type, fee_rate, audit_sn, `status`)
            VALUE (#{appId}, #{merchantSn}, #{terminalSn}, #{tradeComboId}, #{payWay}, #{beginDate}, #{endDate},
                   #{b2cInUse}, #{c2bInUse}, #{appInUse}, #{miniInUse}, #{h5InUse}, #{wapInUse},
                   #{feeRateType}, #{feeRate}, #{auditSn}, #{status});
    </insert>

    <update id="updateEndDate" parameterType="com.wosai.trade.model.dal.TerminalFeeRateUpsertDalParam">
        UPDATE terminal_fee_rate

        set end_date=#{endDate}

        WHERE id = #{id}
    </update>

</mapper>
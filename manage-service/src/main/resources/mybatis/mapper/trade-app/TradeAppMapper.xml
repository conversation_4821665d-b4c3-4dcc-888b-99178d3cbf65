<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.trade.repository.dao.TradeAppDao">

    <resultMap id="TradeAppResultMap" type="com.wosai.trade.repository.dao.entity.TradeAppEntity">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="payApp" column="pay_app" jdbcType="TINYINT"/>
        <result property="businessRefundUrl" column="business_refund_url" jdbcType="VARCHAR"/>
        <result property="ctime" column="ctime" jdbcType="BIGINT"/>
        <result property="mtime" column="mtime" jdbcType="BIGINT"/>
    </resultMap>


    <sql id="table">
        trade_app
    </sql>

    <sql id="allColumn">
        `id`,
        `name`,
        `description`,
        `creator`,
        `status`,
        `pay_app`,
        `business_refund_url`,
        `ctime`,
        `mtime`
    </sql>

    <sql id="insertColumn">
        `name`,
        `description`,
        `creator`,
        `status`,
        `pay_app`,
        `business_refund_url`,
        `ctime`,
        `mtime`
    </sql>

    <sql id="insertColumnValue">
        #{name},
        #{description},
        #{creator},
        #{status},
        #{payApp},
        #{businessRefundUrl},
        #{ctime},
        #{mtime}
    </sql>

    <insert id="insert" parameterType="TradeAppUpsertDalParam" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into
        <include refid="table"/>
        (
        `name`, `description`, `creator`, `status`,`pay_app`,`business_refund_url`, `ctime`, `mtime`
        )
        values
        (
        #{name}, #{description}, #{creator}, #{status}, #{payApp}, #{businessRefundUrl},unix_timestamp()*1000, unix_timestamp()*1000
        )
    </insert>

    <insert id="update" parameterType="TradeAppUpsertDalParam">
        update
        <include refid="table"/>
        set `name` = #{name}, `description` = #{description}, `creator`= #{creator}, `status` = #{status},
        `pay_app` = #{payApp}, `business_refund_url` = #{businessRefundUrl},
        `mtime` =
        unix_timestamp()*1000
        where id = #{id}
    </insert>

    <select id="list" parameterType="TradeAppQueryDalParam" resultMap="TradeAppResultMap">
        select
        <include refid="allColumn"/>
        from
        <include refid="table"/>
        <where>
            <if test="name != null">
                and name like concat(concat('%',#{name}),'%')
            </if>
            <if test="creator != null">
                and `creator` = #{creator}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="pageInfo.dateStart != null">
                and ctime &gt; #{pageInfo.dateStart}
            </if>
            <if test="pageInfo.dateEnd != null">
                and ctime &lt; #{pageInfo.dateEnd}
            </if>
        </where>
        <if test="pageInfo.orderBy != null">
            order by
            <foreach collection="pageInfo.orderBy" separator="," item="orderBy">
                ${orderBy.field} ${orderBy.order.name}
            </foreach>
        </if>
        limit #{pageInfo.pageStart}, #{pageInfo.pageSize}
    </select>

    <select id="selectAll" resultType="com.wosai.trade.repository.dao.entity.TradeAppEntity">
        SELECT `id`,
               `name`,
               description,
               creator,
               status,
               pay_app,
        business_refund_url,
               ctime,
               mtime
        FROM trade_app
        LIMIT 10000
    </select>

    <select id="count" parameterType="TradeAppQueryDalParam" resultType="java.lang.Long">
        select count(1) from
        <include refid="table"/>
        <where>
            <if test="name != null">
                and name like concat(concat('%',#{name}),'%')
            </if>
            <if test="creator != null">
                and `creator` = #{creator}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="pageInfo.dateStart != null">
                and ctime &gt; #{pageInfo.dateStart}
            </if>
            <if test="pageInfo.dateEnd != null">
                and ctime &lt; #{pageInfo.dateEnd}
            </if>
        </where>
    </select>

    <update id="updateStatus" parameterType="TradeAppUpsertDalParam">
        update
        <include refid="table"/>
        set
        `status` = #{status}, `mtime` = unix_timestamp()*1000
        where id = #{id}
    </update>

    <select id="query" parameterType="TradeAppQueryDalParam" resultMap="TradeAppResultMap">
        select
        <include refid="allColumn"/>
        from
        <include refid="table"/>
        where name = #{name}
        <if test="status != null">
            and status = #{status}
        </if>
        <if test="creator != null">
            and `creator` = #{creator}
        </if>
    </select>
    <select id="queryTradeAppById" parameterType="java.lang.Long" resultMap="TradeAppResultMap">
        select * from
        <include refid="table"/>
        where id = #{value}
    </select>

    <select id="selectByIds" resultMap="TradeAppResultMap">
        select * from
        <include refid="table"/>
        where id in (
        <foreach collection="ids" item="item" index="index" open="" separator="," close="">
            #{item}
        </foreach>
        )
    </select>

    <delete id="delete" parameterType="java.lang.Long">
        delete from
        <include refid="table"/>
        where id = #{value}
    </delete>
</mapper>
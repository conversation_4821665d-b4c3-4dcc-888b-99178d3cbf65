<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.trade.repository.dao.MerchantFeeRateApplyLogDao">

    <select id="selectList" parameterType="com.wosai.trade.model.dal.MchFeeRateApplyLogQueryDalParam"
            resultType="com.wosai.trade.repository.dao.entity.MerchantFeeRateApplyLogEntity">
        SELECT id,
        merchant_sn,
        audit_sn,
        pay_way,
        trade_app_name,
        trade_combo_id,
        trade_combo_short_name,
        `description`,
        begin_date,
        end_date,
        fee_rate_type,
        fee_rate,
        create_at
        FROM merchant_fee_rate_apply_log
        <where>
            <if test="merchantSn != null">
                AND merchant_sn = #{merchantSn}
            </if>
        </where>
    </select>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id"
            parameterType="com.wosai.trade.model.dal.MchFeeRateApplyLogUpsertDalParam">
        INSERT INTO merchant_fee_rate_apply_log(merchant_sn, audit_sn, pay_way, trade_app_name, trade_combo_id, trade_combo_short_name,
                                                `description`, begin_date, end_date, fee_rate_type, fee_rate)
            VALUE (#{merchantSn}, #{auditSn}, #{payWay}, #{tradeAppName}, #{tradeComboId}, #{tradeComboShortName},
                   #{description}, #{beginDate}, #{endDate}, #{feeRateType}, #{feeRate})
    </insert>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.trade.repository.dao.SharingAuditTaskDao">
    <resultMap id="SharingAuditTaskResultMap" type="com.wosai.trade.repository.dao.entity.SharingAuditTaskEntity">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="auditId" column="audit_id" jdbcType="BIGINT"/>
        <result property="auditType" column="audit_type" jdbcType="INTEGER"/>
        <result property="bizParams" column="biz_params"
                typeHandler="com.wosai.trade.repository.dao.JsonBlobTypeHandler"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="ctime" column="ctime" jdbcType="BIGINT"/>
        <result property="mtime" column="mtime" jdbcType="BIGINT"/>
        <result property="version" column="version" jdbcType="BIGINT"/>
        <result property="deleted" column="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <insert id="insert" parameterType="com.wosai.trade.repository.dao.entity.SharingAuditTaskEntity">
        insert into sharing_audit_task
            (`audit_id`, `audit_type`, `biz_params`, `status`, `ctime`, `mtime`)
        values (#{auditId},
                #{auditType},
                #{bizParams,jdbcType=OTHER,typeHandler=com.wosai.trade.repository.dao.JsonBlobTypeHandler},
                #{status},
                unix_timestamp() * 1000,
                unix_timestamp() * 1000)
    </insert>


    <update id="updateStatusAndBizParams">
        update sharing_audit_task
        set mtime=unix_timestamp(now())*1000
          ,version = version+1
        <if test="status != null">
            ,status = #{status}
        </if>
        <if test="bizParams != null">
            ,biz_params = #{bizParams,jdbcType=OTHER,typeHandler=com.wosai.trade.repository.dao.JsonBlobTypeHandler}
        </if>
        where id = #{id}
        <if test="version != null">
            and version = #{version}
        </if>
    </update>

    <select id="querySharingAuditTaskByStatus" resultMap="SharingAuditTaskResultMap">
        select *
        from sharing_audit_task
        where `status` in (#{status}) limit #{limit}
    </select>

    <select id="selectByAuditIdWithType" resultMap="SharingAuditTaskResultMap">
        select *
        from sharing_audit_task
        where `audit_id` = #{audit_id}
        <if test="audit_type != null">
            and `audit_type` = #{audit_type}
        </if>
    </select>
</mapper>
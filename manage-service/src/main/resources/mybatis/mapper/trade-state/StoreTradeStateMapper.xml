<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.trade.repository.dao.StoreTradeStateDao">

    <resultMap id="StoreTradeStateResultMap" type="com.wosai.trade.repository.dao.entity.TradeStateEntity">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="storeId" column="store_id" jdbcType="VARCHAR"/>
        <result property="biz" column="biz" jdbcType="INTEGER"/>
        <result property="state" column="state" jdbcType="BIGINT"/>
        <result property="extra" column="extra" typeHandler="com.wosai.trade.repository.dao.JsonBlobTypeHandler"/>
        <result property="ctime" column="ctime" jdbcType="BIGINT"/>
        <result property="mtime" column="mtime" jdbcType="BIGINT"/>
        <result property="version" column="version" jdbcType="BIGINT"/>
    </resultMap>


    <insert id="insert" parameterType="com.wosai.trade.repository.dao.entity.TradeStateEntity">
        insert into store_trade_state
        (
            `store_id`, `biz`, `state`, `extra`, `ctime`, `mtime`
        )
        values
        (
            #{storeId}, #{biz}, #{state},
            #{extra,jdbcType=OTHER,typeHandler=com.wosai.trade.repository.dao.JsonBlobTypeHandler},
            unix_timestamp()*1000,
            unix_timestamp()*1000
        )
    </insert>

    <update id="updateStateByBizAndTypeAndStoreId">
        update store_trade_state
        set state   = #{state},
        <if test="extra != null">
            extra = #{extra,jdbcType=OTHER,typeHandler=com.wosai.trade.repository.dao.JsonBlobTypeHandler},
        </if>
            mtime   = unix_timestamp() * 1000,
            version = version + 1
        where store_id = #{storeId}
          and biz = #{biz}
          and version = #{lastVersion}
    </update>

    <select id="queryStateByBizIdAndStoreId"
            resultMap="StoreTradeStateResultMap">
        select * from store_trade_state where store_id = #{storeId} and biz = #{biz}
    </select>

    <select id="listStateByBizIdAndStoreIds" resultMap="StoreTradeStateResultMap">
        SELECT *
        FROM store_trade_state
        WHERE store_id IN
        <foreach collection="storeIds" index="index" item="storeId" open="(" separator="," close=")">
            #{storeId}
        </foreach>
        AND biz = #{biz}
    </select>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.trade.repository.dao.TradeStateDao">

    <resultMap id="TradeStateResultMap" type="com.wosai.trade.repository.dao.entity.TradeStateEntity">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="merchantId" column="merchant_id" jdbcType="VARCHAR"/>
        <result property="biz" column="biz" jdbcType="INTEGER"/>
        <result property="state" column="state" jdbcType="BIGINT"/>
        <result property="extra" column="extra" typeHandler="com.wosai.trade.repository.dao.JsonBlobTypeHandler"/>
        <result property="ctime" column="ctime" jdbcType="BIGINT"/>
        <result property="mtime" column="mtime" jdbcType="BIGINT"/>
        <result property="version" column="version" jdbcType="BIGINT"/>
    </resultMap>


    <insert id="insert" parameterType="com.wosai.trade.repository.dao.entity.TradeStateEntity">
        insert into trade_state
        (
            `merchant_id`, `biz`, `state`, `extra`, `ctime`, `mtime`
        )
        values
        (
            #{merchantId}, #{biz}, #{state},
            #{extra,jdbcType=OTHER,typeHandler=com.wosai.trade.repository.dao.JsonBlobTypeHandler},
            unix_timestamp()*1000,
            unix_timestamp()*1000
        )
    </insert>

    <update id="updateStateByBizAndTypeAndMerchantId">
        update trade_state
        set state   = #{state},
        <if test="extra != null">
            extra = #{extra,jdbcType=OTHER,typeHandler=com.wosai.trade.repository.dao.JsonBlobTypeHandler},
        </if>
            mtime   = unix_timestamp() * 1000,
            version = version + 1
        where merchant_id = #{merchantId}
          and biz = #{biz}
          and version = #{lastVersion}
    </update>

    <select id="queryStateByBizIdAndMerchantId"
            resultMap="TradeStateResultMap">
        select * from trade_state where merchant_id = #{merchantId} and biz = #{biz}
    </select>

    <select id="listStateByBizIdAndMerchantIds" resultMap="TradeStateResultMap">
        SELECT *
        FROM trade_state
        WHERE merchant_id IN
        <foreach collection="merchantIds" index="index" item="merchantId" open="(" separator="," close=")">
            #{merchantId}
        </foreach>
        AND biz = #{biz}
    </select>

</mapper>
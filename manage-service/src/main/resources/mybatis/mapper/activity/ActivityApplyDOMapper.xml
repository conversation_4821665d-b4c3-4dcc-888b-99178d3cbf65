<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.trade.repository.dao.ActivityApplyDOMapper" >
  <resultMap id="BaseResultMap" type="com.wosai.trade.repository.dao.entity.ActivityApplyEntity" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="audit_id" property="audit_id" jdbcType="BIGINT" />
    <result column="activity_id" property="activity_id" jdbcType="BIGINT" />
    <result column="biz_id" property="biz_id" jdbcType="BIGINT" />
    <result column="activity_type" property="activity_type" jdbcType="TINYINT" />
    <result column="activity_name" property="activity_name" jdbcType="VARCHAR" />
    <result column="merchant_sn" property="merchant_sn" jdbcType="VARCHAR" />
    <result column="merchant_id" property="merchant_id" jdbcType="VARCHAR" />
    <result column="sn" property="sn" jdbcType="VARCHAR" />
    <result column="sub_mch_id" property="sub_mch_id" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="TINYINT" />
    <result column="apply_success_time" property="apply_success_time" jdbcType="TIMESTAMP" />
    <result column="effective_time" property="effective_time" jdbcType="TIMESTAMP" />
    <result column="expiration_time" property="expiration_time" jdbcType="TIMESTAMP" />
    <result column="fee_rate" property="fee_rate" jdbcType="VARCHAR" />
    <result column="lowest_fee_rate" property="lowest_fee_rate" jdbcType="VARCHAR" />
    <result column="discount_quota_status" property="discount_quota_status" jdbcType="TINYINT" />
    <result column="discount_quota_record_id" property="discount_quota_record_id" jdbcType="BIGINT" />
    <result column="create_at" property="create_at" jdbcType="TIMESTAMP" />
    <result column="update_at" property="update_at" jdbcType="TIMESTAMP" />
    <result column="activity_sub_status_id" property="activity_sub_status_id" jdbcType="BIGINT" />
    <result column="combo_id" property="combo_id" jdbcType="BIGINT" />
    <result column="parent_id" property="parent_id" jdbcType="BIGINT" />
    <result column="assessment_time" property="assessment_time" jdbcType="TIMESTAMP" />

  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.wosai.trade.repository.dao.entity.ActivityApplyEntity" extends="BaseResultMap" >
    <result column="effective_rule" property="effective_rule" jdbcType="LONGVARCHAR" />
    <result column="expiration_rule" property="expiration_rule" jdbcType="LONGVARCHAR" />
    <result column="ladder_fee_rates" property="ladder_fee_rates" jdbcType="LONGVARCHAR" />
    <result column="payway" property="payway" jdbcType="LONGVARCHAR" />
    <result column="extra" property="extra" jdbcType="LONGVARCHAR" />
    <result column="process" property="process" jdbcType="LONGVARCHAR" />
    <result column="audit_info" property="audit_info" jdbcType="LONGVARCHAR" />
    <result column="remark" property="remark" jdbcType="LONGVARCHAR" />
    <result column="trade_assessment_rule" property="trade_assessment_rule" jdbcType="LONGVARCHAR"/>
  </resultMap>

  <sql id="Base_Column_List" >
    id, audit_id, activity_id, biz_id, activity_type, activity_name, merchant_sn, merchant_id,sn,
    sub_mch_id, status, apply_success_time, effective_time, expiration_time, fee_rate,
    lowest_fee_rate, discount_quota_status, discount_quota_record_id, create_at, update_at,activity_sub_status_id,combo_id,parent_id,assessment_time
  </sql>
  <sql id="Blob_Column_List" >
    effective_rule, expiration_rule, ladder_fee_rates, payway, extra, process, audit_info,
    remark, trade_assessment_rule
  </sql>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from activity_apply
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from activity_apply
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" keyColumn="id"
          parameterType="com.wosai.trade.repository.dao.entity.ActivityApplyEntity" >
    insert into activity_apply
    <trim prefix="(" suffix=")" suffixOverrides="," >

      <if test="audit_id != null" >
        audit_id,
      </if>
        activity_id,
        biz_id,
        activity_type,
        activity_name,
        merchant_sn,
        merchant_id,
        sn,
      <if test="sub_mch_id != null" >
        sub_mch_id,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="apply_success_time != null" >
        apply_success_time,
      </if>
      <if test="effective_time != null" >
        effective_time,
      </if>
      <if test="expiration_time != null" >
        expiration_time,
      </if>
      <if test="fee_rate != null" >
        fee_rate,
      </if>
      <if test="lowest_fee_rate != null" >
        lowest_fee_rate,
      </if>
      <if test="discount_quota_status != null" >
        discount_quota_status,
      </if>
      <if test="discount_quota_record_id != null" >
        discount_quota_record_id,
      </if>

      <if test="effective_rule != null" >
        effective_rule,
      </if>
      <if test="expiration_rule != null" >
        expiration_rule,
      </if>
      <if test="ladder_fee_rates != null" >
        ladder_fee_rates,
      </if>
      <if test="payway != null" >
        payway,
      </if>
      <if test="extra != null" >
        extra,
      </if>
      <if test="process != null" >
        process,
      </if>
      <if test="audit_info != null" >
        audit_info,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="activity_sub_status_id != null" >
        activity_sub_status_id,
      </if>
      <if test="combo_id != null" >
        combo_id,
      </if>
      <if test="parent_id != null" >
        parent_id,
      </if>
      <if test="assessment_time != null" >
        assessment_time,
      </if>
      <if test="trade_assessment_rule != null" >
        trade_assessment_rule,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >

      <if test="audit_id != null" >
        #{audit_id,jdbcType=BIGINT},
      </if>
        #{activity_id,jdbcType=BIGINT},
        #{biz_id,jdbcType=BIGINT},
        #{activity_type,jdbcType=TINYINT},
        #{activity_name,jdbcType=VARCHAR},
        REPLACE(REPLACE(#{merchant_sn,jdbcType=VARCHAR}, '　',''),' ',''),
        #{merchant_id,jdbcType=VARCHAR},
        REPLACE(REPLACE(#{sn,jdbcType=VARCHAR}, '　',''),' ',''),
      <if test="sub_mch_id != null" >
        #{sub_mch_id,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=TINYINT},
      </if>
      <if test="apply_success_time != null" >
        #{apply_success_time,jdbcType=TIMESTAMP},
      </if>
      <if test="effective_time != null" >
        #{effective_time,jdbcType=TIMESTAMP},
      </if>
      <if test="expiration_time != null" >
        #{expiration_time,jdbcType=TIMESTAMP},
      </if>
      <if test="fee_rate != null" >
        #{fee_rate,jdbcType=VARCHAR},
      </if>
      <if test="lowest_fee_rate != null" >
        #{lowest_fee_rate,jdbcType=VARCHAR},
      </if>
      <if test="discount_quota_status != null" >
        #{discount_quota_status,jdbcType=TINYINT},
      </if>
      <if test="discount_quota_record_id != null" >
        #{discount_quota_record_id,jdbcType=BIGINT},
      </if>

      <if test="effective_rule != null" >
        #{effective_rule,jdbcType=LONGVARCHAR},
      </if>
      <if test="expiration_rule != null" >
        #{expiration_rule,jdbcType=LONGVARCHAR},
      </if>
      <if test="ladder_fee_rates != null" >
        #{ladder_fee_rates,jdbcType=LONGVARCHAR},
      </if>
      <if test="payway != null" >
        #{payway,jdbcType=LONGVARCHAR},
      </if>
      <if test="extra != null" >
        #{extra,jdbcType=LONGVARCHAR},
      </if>
      <if test="process != null" >
        #{process,jdbcType=LONGVARCHAR},
      </if>
      <if test="audit_info != null" >
        #{audit_info,jdbcType=LONGVARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=LONGVARCHAR},
      </if>
      <if test="activity_sub_status_id != null" >
        #{activity_sub_status_id,jdbcType=BIGINT},
      </if>
      <if test="combo_id != null" >
        #{combo_id,jdbcType=BIGINT},
      </if>
      <if test="parent_id != null" >
        #{parent_id,jdbcType=BIGINT},
      </if>
      <if test="assessment_time != null" >
        #{assessment_time,jdbcType=TIMESTAMP},
      </if>
      <if test="trade_assessment_rule != null" >
        #{trade_assessment_rule,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.trade.repository.dao.entity.ActivityApplyEntity" >
    update activity_apply
    <set >
      <if test="audit_id != null" >
        audit_id = #{audit_id,jdbcType=BIGINT},
      </if>
      <if test="activity_id != null" >
        activity_id = #{activity_id,jdbcType=BIGINT},
      </if>
      <if test="biz_id != null" >
        biz_id = #{biz_id,jdbcType=BIGINT},
      </if>
      <if test="activity_type != null" >
        activity_type = #{activity_type,jdbcType=TINYINT},
      </if>
      <if test="activity_name != null" >
        activity_name = #{activity_name,jdbcType=VARCHAR},
      </if>
      <if test="merchant_sn != null" >
        merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="merchant_id != null" >
        merchant_id = #{merchant_id,jdbcType=VARCHAR},
      </if>
      <if test="sub_mch_id != null" >
        sub_mch_id = #{sub_mch_id,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="apply_success_time != null" >
        apply_success_time = #{apply_success_time,jdbcType=TIMESTAMP},
      </if>
      <if test="effective_time != null" >
        effective_time = #{effective_time,jdbcType=TIMESTAMP},
      </if>
      <if test="expiration_time != null" >
        expiration_time = #{expiration_time,jdbcType=TIMESTAMP},
      </if>
      <if test="fee_rate != null" >
        fee_rate = #{fee_rate,jdbcType=VARCHAR},
      </if>
      <if test="lowest_fee_rate != null" >
        lowest_fee_rate = #{lowest_fee_rate,jdbcType=VARCHAR},
      </if>
      <if test="discount_quota_status != null" >
        discount_quota_status = #{discount_quota_status,jdbcType=TINYINT},
      </if>
      <if test="discount_quota_record_id != null" >
        discount_quota_record_id = #{discount_quota_record_id,jdbcType=BIGINT},
      </if>
      <if test="create_at != null" >
        create_at = #{create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="update_at != null" >
        update_at = #{update_at,jdbcType=TIMESTAMP},
      </if>
      <if test="effective_rule != null" >
        effective_rule = #{effective_rule,jdbcType=LONGVARCHAR},
      </if>
      <if test="expiration_rule != null" >
        expiration_rule = #{expiration_rule,jdbcType=LONGVARCHAR},
      </if>
      <if test="ladder_fee_rates != null" >
        ladder_fee_rates = #{ladder_fee_rates,jdbcType=LONGVARCHAR},
      </if>
      <if test="payway != null" >
        payway = #{payway,jdbcType=LONGVARCHAR},
      </if>
      <if test="extra != null" >
        extra = #{extra,jdbcType=LONGVARCHAR},
      </if>
      <if test="process != null" >
        process = #{process,jdbcType=LONGVARCHAR},
      </if>
      <if test="audit_info != null" >
        audit_info = #{audit_info,jdbcType=LONGVARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=LONGVARCHAR},
      </if>
      <if test="activity_sub_status_id != null" >
        activity_sub_status_id = #{activity_sub_status_id,jdbcType=BIGINT},
      </if>
      <if test="combo_id != null" >
        combo_id = #{combo_id,jdbcType=BIGINT},
      </if>
      <if test="parent_id != null" >
        parent_id = #{parent_id,jdbcType=BIGINT},
      </if>
      <if test="assessment_time != null" >
        assessment_time = #{assessment_time,jdbcType=TIMESTAMP},
      </if>
      <if test="trade_assessment_rule != null" >
        trade_assessment_rule = #{trade_assessment_rule,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="countByActivityIdAndStatusAndSn" resultType="int">
    select
    count(*)
    from activity_apply
    where
    activity_id = #{activityId} and merchant_sn=#{merchantSn}
    <if test="subMerchantId != null">
      and sub_mch_id = #{subMerchantId}
    </if>

    and status in
    <foreach item="item" index="index" collection="status" open="(" separator="," close=")">
      #{item}
    </foreach>

  </select>


  <select id="listTaskActivityApplyDO" resultMap="ResultMapWithBLOBs" parameterType="com.wosai.trade.model.dal.ActivityApplyQueryDalParam" >
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from activity_apply
    where id &gt; #{startId}
    <if test="status!=null">
      and status = #{status}
    </if>
    <if test="inStatus!=null">
      and status in
      <foreach item="item" index="index" collection="inStatus" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="maxEffectTime != null">
      and effective_time <![CDATA[<=]]> #{maxEffectTime} and effective_time != '1999-01-01 00:00:00'
    </if>
    <if test="maxExpireTime != null">
      and expiration_time <![CDATA[<=]]> #{maxExpireTime} and expiration_time != '1999-01-01 00:00:00'
    </if>
    <if test="taskType != null and taskType == 0">
      and (expiration_time IS NULL OR expiration_time = '1999-01-01 00:00:00')
    </if>
    <if test="maxApplySuccessTime != null">
      and apply_success_time <![CDATA[<=]]> #{maxApplySuccessTime} and apply_success_time != '1999-01-01 00:00:00'
    </if>
    <if test="activityId!=null">
      and activity_id = #{activityId}
    </if>
    <if test="comboId!=null">
      and combo_id = #{comboId}
    </if>
    <if test="activityIds!=null">
      and activity_id in
      <foreach collection="activityIds" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="subStatusIds!=null">
      and activity_sub_status_id in
      <foreach collection="subStatusIds" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="auditId!=null">
      and audit_id = #{auditId}
    </if>
    order by id asc limit #{limit}
  </select>

  <update id="updateApplyStatusById">
    update activity_apply
    <set>
      status=#{status},
      <if test="process != null" >
        process = #{process},
      </if>
        </set>
    where id = #{id}
  </update>
  <select id="getMerchantLastActivityApply" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List"/>
    ,
    <include refid="Blob_Column_List"/>
    from activity_apply
    where
    activity_id = #{activityId} and merchant_sn=#{merchantSn}
    and status not in (3,6)
    order by id desc limit 1
  </select>
  <select id="getLastActivityApply" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List"/>
    ,
    <include refid="Blob_Column_List"/>
    from activity_apply
    where
    activity_id = #{activityId} and sn=#{sn}
    and status not in (3,6)
    order by id desc limit 1
  </select>

  <select id="countByCondition"  parameterType="com.wosai.trade.model.dal.ActivityApplyQueryDalParam" resultType="int" >
    select
    count(*)
    from activity_apply as a
    inner join activity_sub_status_detail as d on a.activity_sub_status_id=d.id
    inner join trade_combo as c on a.combo_id=c.id
    <where>
      <if test="activityName != null">
        and a.activity_name like CONCAT('%',#{activityName}, '%')
      </if>
      <if test="activityType != null">
        and a.activity_type =#{activityType}
      </if>
      <if test="sn !=null">
        and a.sn=#{sn}
      </if>
      <if test="startTime != null">
        and a.create_at >= #{startTime}
      </if>
      <if test="endTime != null">
        and a.create_at <![CDATA[<=]]> #{endTime}
      </if>
      <if test="inStatus != null">
        and a.status in
        <foreach item="item" index="index" collection="inStatus" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="activityIds != null">
        and a.activity_id in
        <foreach item="item" collection="activityIds" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="comboId !=null">
        and c.id=#{comboId}
      </if>
      <if test="comboName !=null">
        and c.name like CONCAT('%',#{comboName}, '%')
      </if>
      <if test="subStatusName !=null">
        and d.name like CONCAT('%',#{subStatusName}, '%')
      </if>
    </where>
  </select>

  <select id="queryByCondition" resultType="com.wosai.trade.model.dal.ActivityApplyQueryDalResult">
    select
      a.id as id,
      a.activity_id as activityId,
      a.activity_type as activityType,
      a.status as status,
      a.activity_name as activityName,
      a.sn as sn,
      (UNIX_TIMESTAMP(a.create_at)*1000) as createTime,
      a.sub_mch_id as subMchId,
      a.combo_id as comboId,
      c.name as comboName,
      d.name as subStatusName,
      a.effective_time as effectiveTime,
      a.trade_assessment_rule as tradeAssessmentRule,
      a.process as process
    from activity_apply as a
    inner join activity_sub_status_detail as d on a.activity_sub_status_id=d.id
    inner join trade_combo as c on a.combo_id=c.id
    <where>
      <if test="activityName != null">
        and a.activity_name like CONCAT('%',#{activityName}, '%')
      </if>
      <if test="activityType != null">
        and a.activity_type =#{activityType}
      </if>
      <if test="sn !=null">
        and a.sn=#{sn}
      </if>
      <if test="startTime != null">
        and a.create_at >= #{startTime}
      </if>
      <if test="endTime != null">
        and a.create_at <![CDATA[<=]]> #{endTime}
      </if>
      <if test="inStatus != null">
        and a.status in
        <foreach item="item" index="index" collection="inStatus" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="activityIds != null">
        and a.activity_id in
        <foreach item="item" collection="activityIds" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="comboId !=null">
        and c.id=#{comboId}
      </if>
      <if test="comboName !=null">
        and c.name like CONCAT('%',#{comboName}, '%')
      </if>
      <if test="subStatusName !=null">
        and d.name like CONCAT('%',#{subStatusName}, '%')
      </if>
    </where>
    <if test="orderBy != null">
      order by
      <foreach collection="orderBy" separator="," item="orderBy">
        ${orderBy.field} ${orderBy.order.name}
      </foreach>
    </if>
    limit #{start}, #{limit}
  </select>

  <select id="queryByAuditId" resultMap="ResultMapWithBLOBs" >
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from activity_apply
    where
    audit_id = #{auditId}
  </select>


  <select id="getFeeRateCheckList" resultType="long">
    select id
    from activity_apply
    where create_at >= #{startTime}
      and create_at <![CDATA[<]]> #{endTime}
      and status not in (3, 6)
  </select>

  <select id="getEffectIngApply" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List"/>
    ,
    <include refid="Blob_Column_List"/>
    from activity_apply
    where
    activity_id = #{activityId} and merchant_id=#{merchantId}
    and status in (4,5,8) order by effective_time desc limit 1
  </select>


  <select id="getApplyCanCancel" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List"/>
    ,
    <include refid="Blob_Column_List"/>
    from activity_apply
    where
    merchant_sn=#{merchantSn}

    <if test="activityId != null">
        and activity_id = #{activityId}
    </if>
    and status in (0,1,2,4,7,8)
  </select>

  <select id="getApplyListBySource" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List"/>
    ,
    <include refid="Blob_Column_List"/>
    from activity_apply
    where
    merchant_sn=#{merchantSn}
    <if test="activityId != null">
      and activity_id = #{activityId}
    </if>
    and activity_type in (2, 3)
    <if test="statusList!=null">
      and status in
      <foreach collection="statusList" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </select>

  <select id="getEffectIngApplyBySn" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List"/>
    ,
    <include refid="Blob_Column_List"/>
    from activity_apply
    where merchant_sn=#{merchantSn}
    and status in (4,5,8)
  </select>

  <select id="getEffectIngApplyByMerchantSn" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List"/>
    ,
    <include refid="Blob_Column_List"/>
    from activity_apply
    where
    activity_id = #{activityId} and merchant_sn=#{merchantSn}
    and status in (4,5,8) order by effective_time desc limit 1
  </select>

  <update id="updateExtraById">
    update activity_apply
    <set>
      extra=#{extra}
    </set>
    where id = #{id}
  </update>

  <update id="updateQuotaStatusById">
    update activity_apply
    <set>
      discount_quota_status=#{quotaStatus}
    </set>
    where id = #{id}
  </update>

  <select id="getApplyCanceledByLastUpdateTime" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List"/>
    ,
    <include refid="Blob_Column_List"/>
    from activity_apply
    where merchant_sn=#{merchantSn}
    and activity_id in
    <foreach collection="activityIdList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    and status=6
    order by update_at desc
    limit 1
  </select>
  <select id="queryByActivityAndStatusList" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List"/>
    ,
    <include refid="Blob_Column_List"/>
    from activity_apply
    where merchant_sn=#{merchantSn}
    <if test="activityIdList">
      and activity_id in
      <foreach collection="activityIdList" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="statusList!=null">
      and status in
      <foreach collection="statusList" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </select>
  <select id="queryByDashBoard" resultType="com.wosai.trade.model.dal.ActivityDashboardResult">
    SELECT activityId,
           activityName,
           subStatusDetailId,
           subStatusName,
           sum(applySuccessTotal) AS applySuccessTotal,
           sum(effectTotal)       AS effectTotal,
           sum(canceledTotal)     AS canceledTotal
    FROM (SELECT aa.activity_id                 AS activityId,
                 aa.activity_name               AS activityName,
                 IF(aa.STATUS = 2, count(*), 0) AS applySuccessTotal,
                 IF(aa.STATUS = 4, count(*), 0) AS effectTotal,
                 IF(aa.STATUS = 6, count(*), 0) AS canceledTotal,
                 sd.`id`                        AS subStatusDetailId,
                 sd.`name`                      AS subStatusName
          FROM `activity_apply` AS aa
            LEFT JOIN activity_sub_status_detail AS sd ON aa.activity_sub_status_id = sd.id
          WHERE aa.`status` IN (2, 4, 6)
            AND aa.activity_id = #{activityId}
          GROUP BY aa.`status`, sd.id) AS t
    GROUP BY subStatusDetailId
  </select>
  <select id="queryByOffsetPage" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List"/>
    ,
    <include refid="Blob_Column_List"/>
    from activity_apply
    <where>
      <if test="activityId != null">
        and activity_id=#{activityId}
      </if>
      <if test="statusList != null">
        and status in
        <foreach collection="statusList" item="item" separator="," open="(" close=")">
          #{item}
        </foreach>
      </if>
      <if test="pageable.lastId != null">
        and id > #{pageable.lastId}
      </if>
    </where>
    order by id
    limit #{pageable.limit}
  </select>
  <select id="queryBySourceList" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List"/>
    ,
    <include refid="Blob_Column_List"/>
    from activity_apply
    where activity_type in(2,3)
    and status in (0,1,2,4,7)
    <if test="merchantSn!=null">
      and merchant_sn=#{merchantSn}
    </if>
  </select>
  <select id="queryByMerchantSnAndStatusList"
          resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List"/>
    ,
    <include refid="Blob_Column_List"/>
    from activity_apply
    where merchant_sn=#{merchantSn}
    and status in
    <foreach collection="statusList" item="item" separator="," open="(" close=")">
      #{item}
    </foreach>
  </select>
  <select id="queryAssessmentList" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List"/>
    ,
    <include refid="Blob_Column_List"/>
    from activity_apply
    where assessment_time=#{assessmentTime}
    and status in
    <foreach collection="statusList" item="item" separator="," open="(" close=")">
      #{item}
    </foreach>
    <if test="merchantSn!=null">
      and merchant_sn=#{merchantSn}
    </if>
    <if test="pageable.lastId != null and pageable.limit > 0">
      and id &gt; #{pageable.lastId}
    </if>
    order by id asc
    <if test="pageable.limit != null and pageable.limit > 0">
      limit #{pageable.limit}
    </if>
  </select>

  <update id="updateAssessmentTime" parameterType="com.wosai.trade.repository.dao.entity.ActivityApplyEntity" >
    update activity_apply SET assessment_time = #{assessment_time}
    where id = #{id} and merchant_sn = #{merchant_sn}
  </update>

  <select id="selectByParentId" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from activity_apply
    where parent_id = #{parentId,jdbcType=BIGINT}
  </select>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.trade.repository.dao.ActivityApplyAuditMapper">

    <resultMap id="BaseResultMap" type="com.wosai.trade.repository.dao.entity.ActivityApplyAuditEntity">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="merchantSn" column="merchant_sn" jdbcType="VARCHAR"/>
        <result property="templateType" column="template_type" jdbcType="VARCHAR"/>
        <result property="auditApplyId" column="audit_apply_id" jdbcType="VARCHAR"/>
        <result property="auditSn" column="audit_sn" jdbcType="VARCHAR"/>
        <result property="auditTemplateId" column="audit_template_id" jdbcType="VARCHAR"/>
        <result property="activityId" column="activity_id" jdbcType="BIGINT"/>
        <result property="activityName" column="activity_name" jdbcType="VARCHAR"/>
        <result property="activityDesc" column="activity_desc" jdbcType="VARCHAR"/>
        <result property="appId" column="app_id" jdbcType="BIGINT"/>
        <result property="auditStatus" column="audit_status" jdbcType="TINYINT"/>
        <result property="operatorName" column="operator_name" jdbcType="VARCHAR"/>
        <result property="detail" column="detail" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="extra" column="extra" jdbcType="VARCHAR"/>
        <result property="ctime" column="ctime" jdbcType="TIMESTAMP"/>
        <result property="mtime" column="mtime" jdbcType="TIMESTAMP"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,merchant_sn,template_type,
        audit_apply_id,audit_sn,audit_template_id,
        activity_id,activity_name,activity_desc,
        app_id,audit_status,operator_name,
        detail,description,extra,
        ctime,mtime,version
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from activity_apply_audit
        where  id = #{id,jdbcType=VARCHAR}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from activity_apply_audit
        where  id = #{id,jdbcType=VARCHAR} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.wosai.trade.repository.dao.entity.ActivityApplyAuditEntity" useGeneratedKeys="true">
        insert into activity_apply_audit
        ( id,merchant_sn,template_type
        ,audit_apply_id,audit_sn,audit_template_id
        ,activity_id,activity_name,activity_desc
        ,app_id,audit_status,operator_name
        ,detail,description,extra
        ,ctime,mtime,version
        )
        values (#{id,jdbcType=VARCHAR},#{merchantSn,jdbcType=VARCHAR},#{templateType,jdbcType=VARCHAR}
               ,#{auditApplyId,jdbcType=VARCHAR},#{auditSn,jdbcType=VARCHAR},#{auditTemplateId,jdbcType=VARCHAR}
               ,#{activityId,jdbcType=BIGINT},#{activityName,jdbcType=VARCHAR},#{activityDesc,jdbcType=VARCHAR}
               ,#{appId,jdbcType=BIGINT},#{auditStatus,jdbcType=TINYINT},#{operatorName,jdbcType=VARCHAR}
               ,#{detail,jdbcType=VARCHAR},#{description,jdbcType=VARCHAR},#{extra,jdbcType=VARCHAR}
               ,#{ctime,jdbcType=TIMESTAMP},#{mtime,jdbcType=TIMESTAMP},#{version,jdbcType=INTEGER}
               )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.wosai.trade.repository.dao.entity.ActivityApplyAuditEntity" useGeneratedKeys="true">
        insert into activity_apply_audit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="merchantSn != null">merchant_sn,</if>
            <if test="templateType != null">template_type,</if>
            <if test="auditApplyId != null">audit_apply_id,</if>
            <if test="auditSn != null">audit_sn,</if>
            <if test="auditTemplateId != null">audit_template_id,</if>
            <if test="activityId != null">activity_id,</if>
            <if test="activityName != null">activity_name,</if>
            <if test="activityDesc != null">activity_desc,</if>
            <if test="appId != null">app_id,</if>
            <if test="auditStatus != null">audit_status,</if>
            <if test="operatorName != null">operator_name,</if>
            <if test="detail != null">detail,</if>
            <if test="description != null">description,</if>
            <if test="extra != null">extra,</if>
            <if test="ctime != null">ctime,</if>
            <if test="mtime != null">mtime,</if>
            <if test="version != null">version,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=VARCHAR},</if>
            <if test="merchantSn != null">#{merchantSn,jdbcType=VARCHAR},</if>
            <if test="templateType != null">#{templateType,jdbcType=VARCHAR},</if>
            <if test="auditApplyId != null">#{auditApplyId,jdbcType=VARCHAR},</if>
            <if test="auditSn != null">#{auditSn,jdbcType=VARCHAR},</if>
            <if test="auditTemplateId != null">#{auditTemplateId,jdbcType=VARCHAR},</if>
            <if test="activityId != null">#{activityId,jdbcType=BIGINT},</if>
            <if test="activityName != null">#{activityName,jdbcType=VARCHAR},</if>
            <if test="activityDesc != null">#{activityDesc,jdbcType=VARCHAR},</if>
            <if test="appId != null">#{appId,jdbcType=BIGINT},</if>
            <if test="auditStatus != null">#{auditStatus,jdbcType=TINYINT},</if>
            <if test="operatorName != null">#{operatorName,jdbcType=VARCHAR},</if>
            <if test="detail != null">#{detail,jdbcType=VARCHAR},</if>
            <if test="description != null">#{description,jdbcType=VARCHAR},</if>
            <if test="extra != null">#{extra,jdbcType=VARCHAR},</if>
            <if test="ctime != null">#{ctime,jdbcType=TIMESTAMP},</if>
            <if test="mtime != null">#{mtime,jdbcType=TIMESTAMP},</if>
            <if test="version != null">#{version,jdbcType=INTEGER},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.wosai.trade.repository.dao.entity.ActivityApplyAuditEntity">
        update activity_apply_audit
        <set>
            <if test="merchantSn != null">
                merchant_sn = #{merchantSn,jdbcType=VARCHAR},
            </if>
            <if test="templateType != null">
                template_type = #{templateType,jdbcType=VARCHAR},
            </if>
            <if test="auditApplyId != null">
                audit_apply_id = #{auditApplyId,jdbcType=VARCHAR},
            </if>
            <if test="auditSn != null">
                audit_sn = #{auditSn,jdbcType=VARCHAR},
            </if>
            <if test="auditTemplateId != null">
                audit_template_id = #{auditTemplateId,jdbcType=VARCHAR},
            </if>
            <if test="activityId != null">
                activity_id = #{activityId,jdbcType=BIGINT},
            </if>
            <if test="activityName != null">
                activity_name = #{activityName,jdbcType=VARCHAR},
            </if>
            <if test="activityDesc != null">
                activity_desc = #{activityDesc,jdbcType=VARCHAR},
            </if>
            <if test="appId != null">
                app_id = #{appId,jdbcType=BIGINT},
            </if>
            <if test="auditStatus != null">
                audit_status = #{auditStatus,jdbcType=TINYINT},
            </if>
            <if test="operatorName != null">
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="detail != null">
                detail = #{detail,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="extra != null">
                extra = #{extra,jdbcType=VARCHAR},
            </if>
            <if test="ctime != null">
                ctime = #{ctime,jdbcType=TIMESTAMP},
            </if>
            <if test="mtime != null">
                mtime = #{mtime,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                version = #{version,jdbcType=INTEGER},
            </if>
        </set>
        where   id = #{id,jdbcType=VARCHAR} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.wosai.trade.repository.dao.entity.ActivityApplyAuditEntity">
        update activity_apply_audit
        set 
            merchant_sn =  #{merchantSn,jdbcType=VARCHAR},
            template_type =  #{templateType,jdbcType=VARCHAR},
            audit_apply_id =  #{auditApplyId,jdbcType=VARCHAR},
            audit_sn =  #{auditSn,jdbcType=VARCHAR},
            audit_template_id =  #{auditTemplateId,jdbcType=VARCHAR},
            activity_id =  #{activityId,jdbcType=BIGINT},
            activity_name =  #{activityName,jdbcType=VARCHAR},
            activity_desc =  #{activityDesc,jdbcType=VARCHAR},
            app_id =  #{appId,jdbcType=BIGINT},
            audit_status =  #{auditStatus,jdbcType=TINYINT},
            operator_name =  #{operatorName,jdbcType=VARCHAR},
            detail =  #{detail,jdbcType=VARCHAR},
            description =  #{description,jdbcType=VARCHAR},
            extra =  #{extra,jdbcType=VARCHAR},
            ctime =  #{ctime,jdbcType=TIMESTAMP},
            mtime =  #{mtime,jdbcType=TIMESTAMP},
            version =  #{version,jdbcType=INTEGER}
        where   id = #{id,jdbcType=VARCHAR}
    </update>
    <select id="queryAuditByApplyListCount" resultType="java.lang.Long">
        SELECT count(1)
        FROM activity_apply_audit AS audit
        LEFT JOIN activity_apply AS apply ON audit.id = apply.audit_id
        LEFT JOIN activity_sub_status_detail AS sd ON apply.activity_sub_status_id = sd.id
        WHERE audit.merchant_sn = #{merchantSn}
        AND audit.template_type = 'APPLY'
        <if test="auditStatusList!=null">
            AND audit.audit_status IN
            <foreach collection="auditStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="allQuery==false">
            AND (
            apply.id IS NULL
            OR apply.STATUS IN (0, 1, 2, 4, 5, 7)
            )
        </if>
    </select>
    <select id="queryAuditByApplyList"
            resultType="com.wosai.trade.model.dal.activity.audit.result.AuditByApplyListResult">
        SELECT audit.id as id,
        apply.id as applyId,
        apply.combo_id as comboId,
        audit.merchant_sn as merchantSn,
        audit.activity_id as activityId,
        audit.activity_name as activityName,
        audit.description as description,
        apply.fee_rate as feeRate,
        audit.audit_status as auditStatus,
        apply.status as activityStatus,
        audit.ctime as ctime,
        apply.effective_time as effectiveTime,
        apply.expiration_time as expirationTime,
        sd.name as activitySubStatusName,
        audit.extra as activityApplyAuditExtra
        FROM activity_apply_audit AS audit
        LEFT JOIN activity_apply AS apply ON audit.id = apply.audit_id
        LEFT JOIN activity_sub_status_detail AS sd ON apply.activity_sub_status_id = sd.id
        WHERE audit.merchant_sn = #{merchantSn}
        AND audit.template_type = 'APPLY'
        <if test="auditStatusList!=null">
            AND audit.audit_status IN
            <foreach collection="auditStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="allQuery==false">
            AND (
            apply.id IS NULL
            OR apply.STATUS IN (0, 1, 2, 4, 5, 7)
            )
        </if>
        <if test="pageInfo!=null">
            <if test="pageInfo.orderBy!=null">
                order by
                <foreach collection="pageInfo.orderBy" separator="," item="orderBy">
                    ${orderBy.field} ${orderBy.order.name}
                </foreach>
            </if>
            <if test="pageInfo.pageStart!=null and pageInfo.pageSize!=null">
                LIMIT #{pageInfo.pageStart}, #{pageInfo.pageSize}
            </if>
        </if>
    </select>
    <select id="queryJoinApplyAuditList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from activity_apply_audit
        where  merchant_sn=#{merchantSn} and (audit_apply_id=#{auditApplyId} or id=#{auditApplyId})
        order by ctime desc
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.trade.repository.dao.QuotaActivityEntityMapper">
    <resultMap id="BaseResultMap" type="com.wosai.trade.repository.dao.entity.QuotaActivityEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="audit_id" property="audit_id" jdbcType="BIGINT"/>
        <result column="audit_sn" property="audit_sn" jdbcType="VARCHAR"/>
        <result column="biz_id" property="biz_id" jdbcType="BIGINT"/>
        <result column="config_level" property="config_level" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="acquirer" property="acquirer" jdbcType="VARCHAR"/>
        <result column="bank_name" property="bank_name" jdbcType="VARCHAR"/>
        <result column="cancel_reapply" property="cancel_reapply" jdbcType="TINYINT"/>
        <result column="start_time" property="start_time" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="end_time" jdbcType="TIMESTAMP"/>
        <result column="merchant_notice" property="merchant_notice" jdbcType="VARCHAR"/>
        <result column="sale_notice" property="sale_notice" jdbcType="VARCHAR"/>
        <result column="operator" property="operator" jdbcType="VARCHAR"/>
        <result column="create_at" property="create_at" jdbcType="TIMESTAMP"/>
        <result column="update_at" property="update_at" jdbcType="TIMESTAMP"/>
        <result column="payway_category" property="payway_category" jdbcType="TINYINT"/>
        <result column="mobile_bank_provider" property="mobile_bank_provider" jdbcType="INTEGER"/>
    </resultMap>
    <resultMap id="ResultMapWithBLOBs" type="com.wosai.trade.repository.dao.entity.QuotaActivityEntity"
               extends="BaseResultMap">
        <result column="cities" property="cities" jdbcType="LONGVARCHAR"/>
        <result column="bind_combo_ids" property="bind_combo_ids" jdbcType="LONGVARCHAR"/>
        <result column="discount_quota" property="discount_quota" jdbcType="LONGVARCHAR"/>
        <result column="tags" property="tags" jdbcType="LONGVARCHAR"/>
        <result column="extra" property="extra" jdbcType="LONGVARCHAR"/>
        <result column="update_info" property="update_info" jdbcType="LONGVARCHAR"/>
        <result column="process" property="process" jdbcType="LONGVARCHAR"/>
        <result column="scenes_types" property="scenes_types" jdbcType="LONGVARCHAR"/>
        <result column="scenes_codes" property="scenes_codes" jdbcType="LONGVARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , name, `type`, description, audit_id, audit_sn, biz_id, config_level, status, acquirer,
    bank_name, cancel_reapply, start_time, end_time, merchant_notice, sale_notice, operator, 
    create_at, update_at,payway_category,mobile_bank_provider, scenes_types, scenes_codes
    </sql>
    <sql id="Blob_Column_List">
        cities
        , bind_combo_ids, discount_quota, tags, extra, update_info, process
    </sql>
    <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from quota_activity
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from quota_activity
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" keyColumn="id"
            parameterType="com.wosai.trade.repository.dao.entity.QuotaActivityEntity">
        insert into quota_activity
        <trim prefix="(" suffix=")" suffixOverrides=",">
            name,
            <if test="type != null">
                type,
            </if>
            description,
            <if test="audit_id != null">
                audit_id,
            </if>
            <if test="audit_sn != null">
                audit_sn,
            </if>
            biz_id,
            config_level,
            <if test="status != null">
                status,
            </if>
            <if test="acquirer != null">
                acquirer,
            </if>
            <if test="bank_name != null">
                bank_name,
            </if>
            cancel_reapply,
            <if test="start_time != null">
                start_time,
            </if>
            <if test="end_time != null">
                end_time,
            </if>
            <if test="merchant_notice != null">
                merchant_notice,
            </if>
            <if test="sale_notice != null">
                sale_notice,
            </if>
            <if test="operator != null">
                operator,
            </if>
            <if test="cities != null">
                cities,
            </if>
            <if test="bind_combo_ids != null">
                bind_combo_ids,
            </if>
            <if test="discount_quota != null">
                discount_quota,
            </if>
            <if test="tags != null">
                tags,
            </if>
            <if test="extra != null">
                extra,
            </if>
            <if test="update_info != null">
                update_info,
            </if>
            <if test="process != null">
                process,
            </if>
            <if test="payway_category != null">
                payway_category,
            </if>
            <if test="mobile_bank_provider != null">
                mobile_bank_provider,
            </if>
            <if test="scenes_types != null">
                scenes_types,
            </if>
            <if test="scenes_codes != null">
                scenes_codes,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{name,jdbcType=VARCHAR},
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            #{description,jdbcType=VARCHAR},
            <if test="audit_id != null">
                #{audit_id,jdbcType=BIGINT},
            </if>
            <if test="audit_sn != null">
                #{audit_sn,jdbcType=VARCHAR},
            </if>
            #{biz_id,jdbcType=BIGINT},
            #{config_level,jdbcType=VARCHAR},
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="acquirer != null">
                #{acquirer,jdbcType=VARCHAR},
            </if>
            <if test="bank_name != null">
                #{bank_name,jdbcType=VARCHAR},
            </if>
            #{cancel_reapply,jdbcType=TINYINT},
            <if test="start_time != null">
                #{start_time,jdbcType=TIMESTAMP},
            </if>
            <if test="end_time != null">
                #{end_time,jdbcType=TIMESTAMP},
            </if>
            <if test="merchant_notice != null">
                #{merchant_notice,jdbcType=VARCHAR},
            </if>
            <if test="sale_notice != null">
                #{sale_notice,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="cities != null">
                #{cities,jdbcType=LONGVARCHAR},
            </if>
            <if test="bind_combo_ids != null">
                #{bind_combo_ids,jdbcType=LONGVARCHAR},
            </if>
            <if test="discount_quota != null">
                #{discount_quota,jdbcType=LONGVARCHAR},
            </if>
            <if test="tags != null">
                #{tags,jdbcType=LONGVARCHAR},
            </if>
            <if test="extra != null">
                #{extra,jdbcType=LONGVARCHAR},
            </if>
            <if test="update_info != null">
                #{update_info,jdbcType=LONGVARCHAR},
            </if>
            <if test="process != null">
                #{process,jdbcType=LONGVARCHAR},
            </if>
            <if test="payway_category != null">
                #{payway_category,jdbcType=TINYINT},
            </if>
            <if test="mobile_bank_provider != null">
                #{mobile_bank_provider,jdbcType=INTEGER},
            </if>
            <if test="scenes_types != null">
                #{scenes_types,jdbcType=LONGVARCHAR},
            </if>
            <if test="scenes_codes != null">
                #{scenes_codes,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.wosai.trade.repository.dao.entity.QuotaActivityEntity">
        update quota_activity
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                `type` = #{type,jdbcType=INTEGER},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="audit_id != null">
                audit_id = #{audit_id,jdbcType=BIGINT},
            </if>
            <if test="audit_sn != null">
                audit_sn = #{audit_sn,jdbcType=VARCHAR},
            </if>
            <if test="biz_id != null">
                biz_id = #{biz_id,jdbcType=BIGINT},
            </if>
            <if test="config_level != null">
                config_level = #{config_level,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="acquirer != null">
                acquirer = #{acquirer,jdbcType=VARCHAR},
            </if>
            <if test="bank_name != null">
                bank_name = #{bank_name,jdbcType=VARCHAR},
            </if>
            <if test="cancel_reapply != null">
                cancel_reapply = #{cancel_reapply,jdbcType=TINYINT},
            </if>
            <if test="start_time != null">
                start_time = #{start_time,jdbcType=TIMESTAMP},
            </if>
            <if test="end_time != null">
                end_time = #{end_time,jdbcType=TIMESTAMP},
            </if>
            <if test="merchant_notice != null">
                merchant_notice = #{merchant_notice,jdbcType=VARCHAR},
            </if>
            <if test="sale_notice != null">
                sale_notice = #{sale_notice,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="create_at != null">
                create_at = #{create_at,jdbcType=TIMESTAMP},
            </if>
            <if test="update_at != null">
                update_at = #{update_at,jdbcType=TIMESTAMP},
            </if>
            <if test="cities != null">
                cities = #{cities,jdbcType=LONGVARCHAR},
            </if>
            <if test="bind_combo_ids != null">
                bind_combo_ids = #{bind_combo_ids,jdbcType=LONGVARCHAR},
            </if>
            <if test="discount_quota != null">
                discount_quota = #{discount_quota,jdbcType=LONGVARCHAR},
            </if>
            <if test="tags != null">
                tags = #{tags,jdbcType=LONGVARCHAR},
            </if>
            <if test="extra != null">
                extra = #{extra,jdbcType=LONGVARCHAR},
            </if>
            <if test="update_info != null">
                update_info = #{update_info,jdbcType=LONGVARCHAR},
            </if>
            <if test="process != null">
                process = #{process,jdbcType=LONGVARCHAR},
            </if>
            <if test="payway_category != null">
                payway_category = #{payway_category,jdbcType=TINYINT},
            </if>
            <if test="mobile_bank_provider != null">
                mobile_bank_provider = #{mobile_bank_provider,jdbcType=INTEGER},
            </if>
            <if test="scenes_types != null">
                scenes_types = #{scenes_types,jdbcType=LONGVARCHAR},
            </if>
            <if test="scenes_codes != null">
                scenes_codes = #{scenes_codes,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>


    <select id="countQueryByCondition" parameterType="com.wosai.trade.model.dal.ActivityQueryParam" resultType="int">
        SELECT
        count(*)
        FROM quota_activity
        <where>
            <if test="name != null">
                and name like CONCAT('%',#{name},'%')
            </if>
            <if test="status != null">
                and status=#{status}
            </if>
            <if test="startTime != null">
                and create_at >= #{startTime}
            </if>
            <if test="endTime != null">
                and create_at <![CDATA[<=]]> #{endTime}
            </if>
            <if test="activityIds != null">
                and id in
                <foreach collection="activityIds" item="activityId" open="(" separator="," close=")">
                    #{activityId}
                </foreach>
            </if>
            <if test="paywayCategory != null">
                and payway_category=#{paywayCategory}
            </if>
            <if test="type != null">
                and `type`=#{type}
            </if>
        </where>
    </select>

    <select id="queryByCondition" parameterType="com.wosai.trade.model.dal.ActivityQueryParam"
            resultMap="ResultMapWithBLOBs">
        SELECT
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        FROM quota_activity
        <where>
            <if test="name != null">
                and name like CONCAT('%',#{name}, '%')
            </if>
            <if test="status != null">
                and status=#{status}
            </if>
            <if test="startTime != null">
                and create_at >= #{startTime}
            </if>
            <if test="endTime != null">
                and create_at <![CDATA[<=]]> #{endTime}
            </if>
            <if test="activityIds != null">
                and id in
                <foreach collection="activityIds" item="activityId" open="(" separator="," close=")">
                    #{activityId}
                </foreach>
            </if>
            <if test="paywayCategory != null">
                and payway_category=#{paywayCategory}
            </if>
            <if test="type != null">
                and `type`=#{type}
            </if>
        </where>

        <if test="orderBy != null">
            order by
            <foreach collection="orderBy" separator="," item="orderBy">
                ${orderBy.field} ${orderBy.order.name}
            </foreach>
        </if>
        limit #{start}, #{limit}
    </select>


    <select id="selectAuditId" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from quota_activity
        where audit_id = #{auditId}
    </select>

    <update id="updateUpdateInfo">
        update quota_activity
        <set>
            update_info=#{updateInfo}
        </set>
        where id = #{id}
    </update>

    <select id="getExpireActivity" resultType="long">
        select id
        from quota_activity
        where status = 1
          and end_time <![CDATA[<=]]> now()
          and end_time != '1999-01-01 00:00:00' order by id asc
    </select>

    <select id="usableActivity" resultMap="ResultMapWithBLOBs">
        SELECT
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        FROM quota_activity
        where status=1 order by create_at desc
    </select>
    <select id="pageList" parameterType="com.wosai.trade.model.dal.ActivityQueryParam" resultMap="ResultMapWithBLOBs">
        SELECT
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        FROM quota_activity
        <where>
            <if test="status != null">
                and status=#{status}
            </if>
            <if test="activityIds!=null">
                and id in
                <foreach collection="activityIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        <if test="orderBy != null">
            order by
            <foreach collection="orderBy" separator="," item="orderBy">
                ${orderBy.field} ${orderBy.order.name}
            </foreach>
        </if>
        limit #{start}, #{limit}
    </select>

</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.trade.repository.dao.ActivitySubStatusDetailMapper" >
  <resultMap id="BaseResultMap" type="com.wosai.trade.repository.dao.entity.ActivitySubStatusDetailEntity" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="activity_id" property="activityId" jdbcType="BIGINT" />
    <result column="combo_id" property="comboId" jdbcType="BIGINT" />
    <result column="take_effect_notice" property="takeEffectNotice" jdbcType="VARCHAR" />
    <result column="description" property="description" jdbcType="VARCHAR" />
    <result column="ctime" property="ctime" jdbcType="TIMESTAMP" />
    <result column="mtime" property="mtime" jdbcType="TIMESTAMP" />
    <result column="version" property="version" jdbcType="INTEGER" />
    <result column="tag" property="tag" jdbcType="VARCHAR" />
    <result column="effective_rule" property="effectiveRule" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, name, activity_id, combo_id, take_effect_notice, description, ctime, mtime, version,tag,effective_rule
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from activity_sub_status_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from activity_sub_status_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.wosai.trade.repository.dao.entity.ActivitySubStatusDetailEntity" >
    insert into activity_sub_status_detail (id, name, activity_id, 
      combo_id, take_effect_notice, description, ctime, mtime,
      version,tag,effective_rule)
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{activityId,jdbcType=BIGINT}, 
      #{comboId,jdbcType=BIGINT}, #{takeEffectNotice,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP},
      #{version,jdbcType=INTEGER}, #{tag,jdbcType=VARCHAR}, #{effectiveRule,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.trade.repository.dao.entity.ActivitySubStatusDetailEntity"
          useGeneratedKeys="true" keyProperty="id" keyColumn="id" >
    insert into activity_sub_status_detail
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="name != null" >
        name,
      </if>
      <if test="activityId != null" >
        activity_id,
      </if>
      <if test="comboId != null" >
        combo_id,
      </if>
      <if test="takeEffectNotice != null" >
        take_effect_notice,
      </if>
      <if test="description != null" >
        description,
      </if>
      <if test="ctime != null" >
        ctime,
      </if>
      <if test="mtime != null" >
        mtime,
      </if>
      <if test="version != null" >
        version,
      </if>
      <if test="tag != null" >
        tag,
      </if>
      <if test="effectiveRule != null" >
        effective_rule,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="activityId != null" >
        #{activityId,jdbcType=BIGINT},
      </if>
      <if test="comboId != null" >
        #{comboId,jdbcType=BIGINT},
      </if>
      <if test="takeEffectNotice != null" >
        #{takeEffectNotice,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null" >
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null" >
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null" >
        #{version,jdbcType=INTEGER},
      </if>
      <if test="tag != null" >
        #{tag,jdbcType=VARCHAR},
      </if>
      <if test="effectiveRule != null" >
        #{effectiveRule,jdbcType=LONGNVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.trade.repository.dao.entity.ActivitySubStatusDetailEntity" >
    update activity_sub_status_detail
    <set >
      <if test="name != null" >
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="activityId != null" >
        activity_id = #{activityId,jdbcType=BIGINT},
      </if>
      <if test="comboId != null" >
        combo_id = #{comboId,jdbcType=BIGINT},
      </if>
      <if test="takeEffectNotice != null" >
        take_effect_notice = #{takeEffectNotice,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null" >
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null" >
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null" >
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="tag != null" >
        tag = #{tag,jdbcType=VARCHAR},
      </if>
      <if test="effectiveRule != null" >
        effective_rule = #{effectiveRule,jdbcType=LONGNVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wosai.trade.repository.dao.entity.ActivitySubStatusDetailEntity" >
    update activity_sub_status_detail
    set name = #{name,jdbcType=VARCHAR},
      activity_id = #{activityId,jdbcType=BIGINT},
      combo_id = #{comboId,jdbcType=BIGINT},
      take_effect_notice = #{takeEffectNotice,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      version = #{version,jdbcType=INTEGER},
      tag = #{tag,jdbcType=VARCHAR},
      effective_rule = #{effectiveRule,jdbcType=LONGNVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByComboId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from activity_sub_status_detail
    where combo_id = #{comboId}
  </select>

  <select id="selectActivityComboList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from activity_sub_status_detail
    where activity_id in(
      select activity_id
      from activity_sub_status_detail
      where combo_id = #{comboId}
    )
  </select>

  <select id="selectByActivityId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from activity_sub_status_detail
    where activity_id = #{activityId}
  </select>

  <select id="selectActivityIdList" resultType="java.lang.Long">
    select distinct(ss.activity_id) from activity_sub_status_detail as ss, trade_combo as t
    where ss.combo_id=t.id
    <if test="comboId!=null">
      and t.id = #{comboId}
    </if>
    <if test="comboName!=null">
      and t.name like CONCAT('%',#{comboName},'%')
    </if>
  </select>

  <select id="selectByIdList" resultMap="BaseResultMap">
    select * from activity_sub_status_detail
    where id in
    <foreach collection="idList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <select id="selectNotEmptyTakeNotice" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from activity_sub_status_detail
    where take_effect_notice !=''
  </select>
  <select id="findAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from activity_sub_status_detail
  </select>
  <select id="selectByTag" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from activity_sub_status_detail
    where tag=#{tag}
  </select>


</mapper>
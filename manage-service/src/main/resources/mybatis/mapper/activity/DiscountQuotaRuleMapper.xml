<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.trade.repository.dao.DiscountQuotaRuleMapper" >
  <resultMap id="BaseResultMap" type="com.wosai.trade.repository.dao.entity.DiscountQuotaRuleEntity" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="activity_id" property="activity_id" jdbcType="BIGINT" />
    <result column="quota_min" property="quota_min" jdbcType="BIGINT" />
    <result column="quota_max" property="quota_max" jdbcType="BIGINT" />
    <result column="quota_fee_rate_min" property="quota_fee_rate_min" jdbcType="VARCHAR" />
    <result column="quota_fee_rate_max" property="quota_fee_rate_max" jdbcType="VARCHAR" />
    <result column="create_at" property="create_at" jdbcType="TIMESTAMP" />
    <result column="update_at" property="update_at" jdbcType="TIMESTAMP" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.wosai.trade.repository.dao.entity.DiscountQuotaRuleEntity" extends="BaseResultMap" >
    <result column="payway_status" property="payway_status" jdbcType="LONGVARCHAR" />
    <result column="extra" property="extra" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, activity_id,  quota_min, quota_max, quota_fee_rate_min, quota_fee_rate_max,
    create_at, update_at
  </sql>
  <sql id="Blob_Column_List" >
    payway_status, extra
  </sql>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from discount_quota_rule
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from discount_quota_rule
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" keyColumn="id"
          parameterType="com.wosai.trade.repository.dao.entity.DiscountQuotaRuleEntity">
    insert into discount_quota_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      activity_id,
      quota_min,
      quota_max,
      quota_fee_rate_min,
      quota_fee_rate_max,
      <if test="payway_status != null">
        payway_status,
      </if>
      <if test="extra != null">
        extra,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{activity_id,jdbcType=BIGINT},
      #{quota_min,jdbcType=BIGINT},
      #{quota_max,jdbcType=BIGINT},
      #{quota_fee_rate_min,jdbcType=VARCHAR},
      #{quota_fee_rate_max,jdbcType=VARCHAR},
      <if test="payway_status != null">
        #{payway_status,jdbcType=LONGVARCHAR},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.trade.repository.dao.entity.DiscountQuotaRuleEntity" >
    update discount_quota_rule
    <set >
      <if test="activity_id != null" >
        activity_id = #{activity_id,jdbcType=BIGINT},
      </if>

      <if test="quota_min != null" >
        quota_min = #{quota_min,jdbcType=BIGINT},
      </if>
      <if test="quota_max != null" >
        quota_max = #{quota_max,jdbcType=BIGINT},
      </if>
      <if test="quota_fee_rate_min != null" >
        quota_fee_rate_min = #{quota_fee_rate_min,jdbcType=VARCHAR},
      </if>
      <if test="quota_fee_rate_max != null" >
        quota_fee_rate_max = #{quota_fee_rate_max,jdbcType=VARCHAR},
      </if>
      <if test="create_at != null" >
        create_at = #{create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="update_at != null" >
        update_at = #{update_at,jdbcType=TIMESTAMP},
      </if>
      <if test="payway_status != null" >
        payway_status = #{payway_status,jdbcType=LONGVARCHAR},
      </if>
      <if test="extra != null" >
        extra = #{extra,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>


  <select id="selectByActivityId" resultType="com.wosai.trade.repository.dao.entity.DiscountQuotaRuleEntity">
    select
    <include refid="Base_Column_List"/>
    ,
    <include refid="Blob_Column_List"/>
    from discount_quota_rule
    where activity_id = #{activityId}
  </select>
</mapper>
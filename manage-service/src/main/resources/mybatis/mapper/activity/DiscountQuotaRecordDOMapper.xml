<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.trade.repository.dao.DiscountQuotaRecordDOMapper" >
  <resultMap id="BaseResultMap" type="com.wosai.trade.repository.dao.entity.DiscountQuotaRecordEntity" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="activity_id" property="activity_id" jdbcType="BIGINT" />
    <result column="discount_quota" property="discount_quota" jdbcType="BIGINT" />
    <result column="discount_quota_fee_rate" property="discount_quota_fee_rate" jdbcType="VARCHAR" />
    <result column="discount_quota_usable" property="discount_quota_usable" jdbcType="BIGINT" />
    <result column="create_at" property="create_at" jdbcType="TIMESTAMP" />
    <result column="update_at" property="update_at" jdbcType="TIMESTAMP" />
    <result column="quota_unavailable_time" property="quota_unavailable_time" jdbcType="TIMESTAMP" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.wosai.trade.repository.dao.entity.DiscountQuotaRecordEntity" extends="BaseResultMap" >
    <result column="extra" property="extra" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, activity_id, discount_quota, discount_quota_fee_rate, discount_quota_usable, 
    create_at, update_at, quota_unavailable_time
  </sql>
  <sql id="Blob_Column_List" >
    extra
  </sql>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from discount_quota_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from discount_quota_record
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" keyColumn="id"
          parameterType="com.wosai.trade.repository.dao.entity.DiscountQuotaRecordEntity" >
    insert into discount_quota_record
    <trim prefix="(" suffix=")" suffixOverrides="," >

        activity_id,
      <if test="discount_quota != null" >
        discount_quota,
      </if>
      <if test="discount_quota_fee_rate != null" >
        discount_quota_fee_rate,
      </if>
      <if test="discount_quota_usable != null" >
        discount_quota_usable,
      </if>

      <if test="extra != null" >
        extra,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
        #{activity_id,jdbcType=BIGINT},
      <if test="discount_quota != null" >
        #{discount_quota,jdbcType=BIGINT},
      </if>
      <if test="discount_quota_fee_rate != null" >
        #{discount_quota_fee_rate,jdbcType=VARCHAR},
      </if>
      <if test="discount_quota_usable != null" >
        #{discount_quota_usable,jdbcType=BIGINT},
      </if>
      <if test="extra != null" >
        #{extra,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.trade.repository.dao.entity.DiscountQuotaRecordEntity" >
    update discount_quota_record
    <set >
      <if test="activity_id != null" >
        activity_id = #{activity_id,jdbcType=BIGINT},
      </if>
      <if test="discount_quota != null" >
        discount_quota = #{discount_quota,jdbcType=BIGINT},
      </if>
      <if test="discount_quota_fee_rate != null" >
        discount_quota_fee_rate = #{discount_quota_fee_rate,jdbcType=VARCHAR},
      </if>
      <if test="discount_quota_usable != null" >
        discount_quota_usable = #{discount_quota_usable,jdbcType=BIGINT},
      </if>
      <if test="create_at != null" >
        create_at = #{create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="update_at != null" >
        update_at = #{update_at,jdbcType=TIMESTAMP},
      </if>
      <if test="extra != null" >
        extra = #{extra,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>



  <update id="updateUsableQuotaById">
    update discount_quota_record
    set discount_quota_usable = discount_quota_usable - #{deduct},
        extra = #{extra}
    where id = #{id}
  </update>

  <update id="adjustUsableQuotaById">
    update discount_quota_record
    set discount_quota_usable = discount_quota_usable + #{changeQuota},
        discount_quota=discount_quota + #{changeQuota}
    where id = #{id}
  </update>
  <update id="rollbackUsableQuotaById">
    update discount_quota_record
    set discount_quota_usable = discount_quota_usable + #{quota}
    where id = #{id}
  </update>
  <update id="updateQuotaUnavailableTime">
    update discount_quota_record
    set quota_unavailable_time = #{quotaUnavailableTime}
    where id = #{id}
  </update>
  <select id="queryByActivityId" resultMap="ResultMapWithBLOBs" >
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from discount_quota_record
    where activity_id=#{activityId}
  </select>
  <select id="queryByIdList" resultMap="ResultMapWithBLOBs" >
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from discount_quota_record
    where id in
    <foreach collection="ids" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>
</mapper>
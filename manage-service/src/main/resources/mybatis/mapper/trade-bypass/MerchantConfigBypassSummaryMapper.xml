<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.trade.repository.dao.MerchantConfigBypassSummaryDao">

    <resultMap id="MerchantConfigBypassSummaryResultMap" type="com.wosai.trade.repository.dao.entity.MerchantConfigBypassSummaryEntity">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="batchNo" column="batch_no" jdbcType="VARCHAR"/>
        <result property="merchantId" column="merchant_id" jdbcType="VARCHAR"/>
        <result property="startTime" column="start_time" jdbcType="BIGINT"/>
        <result property="endTime" column="end_time" jdbcType="BIGINT"/>
        <result property="sourceOrganization" column="source_organization" jdbcType="INTEGER"/>
        <result property="bypassOrganization" column="bypass_organization" jdbcType="INTEGER"/>
        <result property="tradeCount" column="trade_count" jdbcType="INTEGER"/>
        <result property="tradeAmount" column="trade_amount" jdbcType="BIGINT"/>
        <result property="clearanceAmount" column="clearance_amount" jdbcType="BIGINT"/>
        <result property="status" column="status" jdbcType="TINYINT"/>
        <result property="ctime" column="ctime" jdbcType="BIGINT"/>
        <result property="mtime" column="mtime" jdbcType="BIGINT"/>
        <result property="deleted" column="deleted" jdbcType="TINYINT"/>
        <result property="version" column="version" jdbcType="BIGINT"/>
    </resultMap>

    <insert id="summaryByBatchNo" parameterType="java.lang.String">
            INSERT INTO `merchant_config_bypass_summary`(`batch_no`, `merchant_id`, `start_time`, `end_time`,
                        `source_organization`, `bypass_organization`,`trade_count`, `trade_amount`, 
                        `ctime`,`mtime`,`deleted`, `version`)
            select `batch_no`, `merchant_id`, min(trade_time) as start_time, max(trade_time) as end_time, `source_organization`, `bypass_organization`, 
                   count(1) as trade_count, sum(case when `type`=30 or `type`=31 or `type`=13 then amount when `type`=32 then 0 else -amount end) as trade_amount, unix_timestamp()*1000, unix_timestamp()*1000, 0, 1
              from merchant_config_bypass_tradeinfo 
            where batch_no = #{batchNo} group by `batch_no`, `merchant_id`, `source_organization`
            ON DUPLICATE KEY UPDATE `batch_no` = values(batch_no), merchant_id = values(merchant_id), start_time = values(start_time), end_time = values(end_time),
              source_organization=values(source_organization), bypass_organization = values(bypass_organization), trade_count = values(trade_count),
              trade_amount = values(trade_amount), mtime = unix_timestamp()*1000,version = version+1
    </insert>

    <select id="selectList" parameterType="com.wosai.trade.model.dal.MerchantConfigBypassSummaryDalParam"
            resultMap="MerchantConfigBypassSummaryResultMap">
        SELECT `id`,
        `batch_no`,
        `merchant_id`,
        `start_time`,
        `end_time`,
        `source_organization`,
        `bypass_organization`,
        `trade_count`,
        `trade_amount`,
        `clearance_amount`,
        `status`,
        `ctime`,
        `mtime`,
        `deleted`,
        `version`
        FROM `merchant_config_bypass_summary`
        <where>
            <if test="merchantId != null">
                AND merchant_id = #{merchantId}
            </if>
            <if test="batchNo != null">
                AND `batch_no` = #{batchNo}
            </if>
            <if test="status != null">
                AND `status` = #{status}
            </if>
            <if test="notStatus != null">
                AND `status` != #{notStatus}
            </if>
            <if test="pageInfo.dateStart != null">
                and start_time &gt; #{pageInfo.dateStart}
            </if>
            <if test="pageInfo.dateEnd != null">
                and start_time &lt; #{pageInfo.dateEnd}
            </if>
        </where>
        <if test="pageInfo.orderBy != null">
            order by
            <foreach collection="pageInfo.orderBy" separator="," item="orderBy">
                ${orderBy.field} ${orderBy.order.name}
            </foreach>
        </if>
        limit #{pageInfo.pageStart}, #{pageInfo.pageSize}
    </select>

    <select id="count" parameterType="com.wosai.trade.model.dal.MerchantConfigBypassSummaryDalParam"
            resultType="java.lang.Long">
        SELECT count(1)
        FROM `merchant_config_bypass_summary`
        <where>
            <if test="merchantId != null">
                AND merchant_id = #{merchantId}
            </if>
            <if test="batchNo != null">
                AND `batch_no` = #{batchNo}
            </if>
            <if test="status != null">
                AND `status` = #{status}
            </if>
            <if test="pageInfo.dateStart != null">
                and start_time &gt; #{pageInfo.dateStart}
            </if>
            <if test="pageInfo.dateEnd != null">
                and start_time &lt; #{pageInfo.dateEnd}
            </if>
        </where>
    </select>

    <select id="selectById" resultMap="MerchantConfigBypassSummaryResultMap">
       SELECT `id`,
        `batch_no`,
        `merchant_id`,
        `start_time`,
        `end_time`,
        `source_organization`,
        `bypass_organization`,
        `trade_count`,
        `trade_amount`,
        `clearance_amount`,
        `status`,
        `ctime`,
        `mtime`,
        `deleted`,
        `version`
        FROM `merchant_config_bypass_summary`
        WHERE `id` = #{id}
    </select>

    <update id="updateSummary">
       update `merchant_config_bypass_summary` set `mtime`=unix_timestamp()*1000, `version`=`version`+1
           <if test="status != null">
            ,`status`= #{status}
           </if>
           <if test="clearanceAmount != null">
            ,`clearance_amount`= #{clearanceAmount}
           </if>       
        where `id`=#{id}
    </update>

    <select id="statisticsByBatchNo" resultType="com.wosai.trade.repository.dao.entity.MerchantConfigBypassSummaryStatisticsEntity">
        select count(distinct merchant_id) as mchCount, 
               count(1) as count, 
               sum(case when `status`=1 then 1 else 0 end) as succCount, 
               sum(case when `status`=0 then 1 else 0 end) as initCount, 
               sum(case when `status`=2 then 1 else 0 end) as failCount, 
               sum(trade_count) as tradeCount, 
               sum(trade_amount) as tradeAmount, 
               min(start_time) as startTime, 
               max(end_time) as endTime,
               sum(case when `clearance_amount` is not null then 1 else 0 end) as clearanceCount, 
               sum(case when `clearance_amount` is not null then `clearance_amount` else 0 end) as clearanceAmount
        from merchant_config_bypass_summary
            where batch_no = #{batchNo}
    </select>
</mapper>
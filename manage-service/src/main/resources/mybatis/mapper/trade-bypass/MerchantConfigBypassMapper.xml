<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.trade.repository.dao.MerchantConfigBypassDao">

    <resultMap id="MerchantConfigBypassResultMap" type="com.wosai.trade.repository.dao.entity.MerchantConfigBypassEntity">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="merchantId" column="merchant_id" jdbcType="VARCHAR"/>
        <result property="currentProvider" column="current_provider" jdbcType="INTEGER"/>
        <result property="payway" column="payway" jdbcType="INTEGER"/>
        <result property="b2cAgentName" column="b2c_agent_name" jdbcType="VARCHAR"/>
        <result property="c2bAgentName" column="c2b_agent_name" jdbcType="VARCHAR"/>
        <result property="wapAgentName" column="wap_agent_name" jdbcType="VARCHAR"/>
        <result property="miniAgentName" column="mini_agent_name" jdbcType="VARCHAR"/>
        <result property="appAgentName" column="app_agent_name" jdbcType="VARCHAR"/>
        <result property="h5AgentName" column="h5_agent_name" jdbcType="VARCHAR"/>
        <result property="params" column="params" typeHandler="com.wosai.trade.repository.dao.JsonBlobTypeHandler"/>
        <result property="ctime" column="ctime" jdbcType="BIGINT"/>
        <result property="mtime" column="mtime" jdbcType="BIGINT"/>
        <result property="deleted" column="deleted" jdbcType="TINYINT"/>
        <result property="version" column="version" jdbcType="BIGINT"/>
    </resultMap>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" keyColumn="id"
            parameterType="com.wosai.trade.model.dal.MerchantConfigBypassDalParam">
        INSERT INTO `merchant_config_bypass`(`merchant_id`, `current_provider`, `payway`, `b2c_agent_name`, `c2b_agent_name`, 
                                `wap_agent_name`, `mini_agent_name`, `app_agent_name`, `h5_agent_name`, `params`, 
                                `ctime`, `mtime`, `deleted`, `version`)
            VALUES(#{merchantId}, #{currentProvider}, #{payway}, #{b2cAgentName}, #{c2bAgentName}, #{wapAgentName}, 
                   #{miniAgentName}, #{appAgentName}, #{h5AgentName}, #{params,jdbcType=OTHER,
                   typeHandler=com.wosai.trade.repository.dao.JsonBlobTypeHandler},UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000, 0, 1)
            ON DUPLICATE KEY UPDATE `current_provider` = values(`current_provider`), `b2c_agent_name` = values(`b2c_agent_name`), `c2b_agent_name` = values(`c2b_agent_name`),
                   `wap_agent_name` = values(`wap_agent_name`), `mini_agent_name` = values(`mini_agent_name`), `app_agent_name` = values(`app_agent_name`), 
                   `h5_agent_name` = values(`h5_agent_name`), `params` = values(`params`), `mtime` = unix_timestamp()*1000, `version` = `version` +1
    </insert>

    <update id="updateById" parameterType="com.wosai.trade.model.dal.MerchantConfigBypassDalParam">
        UPDATE `merchant_config_bypass`
        <set>
            <if test="currentProvider != null ">
                `current_provider` = #{currentProvider}
            </if>
            <if test="b2cAgentName != null ">
                `b2c_agent_name` = #{b2cAgentName}
            </if>
            <if test="c2bAgentName != null">
                `c2b_agent_name` = #{c2bAgentName}
            </if>
            <if test="wapAgentName != null">
                `wap_agent_name` = #{wapAgentName}
            </if>
            <if test="miniAgentName != null">
                `mini_agent_name` = #{miniAgentName}
            </if>
            <if test="appAgentName != null">
                `app_agent_name` = #{appAgentName}
            </if>
            <if test="h5AgentName != null">
                `h5_agent_name` = #{h5AgentName}
            </if>
            <if test="params != null">
                `params` = #{params,jdbcType=OTHER,typeHandler=com.wosai.trade.repository.dao.JsonBlobTypeHandler}
            </if>
        </set>
        WHERE id = #{id}
        <if test="version != null">
               and `version` = #{version}
        </if>
    </update>

    <select id="selectList" parameterType="com.wosai.trade.model.dal.MerchantConfigBypassDalParam"
            resultMap="MerchantConfigBypassResultMap">
        SELECT `id`,
        `merchant_id`,
        `current_provider`,
        `payway`,
        `b2c_agent_name`,
        `c2b_agent_name`,
        `wap_agent_name`,
        `mini_agent_name`,
        `app_agent_name`,
        `h5_agent_name`,
        `params`,
        `ctime`,
        `mtime`,
        `deleted`,
        `version`
        FROM `merchant_config_bypass`
        <where>
            <if test="merchantId != null">
                AND merchant_id = #{merchantId}
            </if>
            <if test="currentProvider != null">
                AND `current_provider` = #{currentProvider}
            </if>
            <if test="payway != null">
                AND `payway` = #{payway}
            </if>
            <if test="pageInfo.dateStart != null">
                and ctime &gt; #{pageInfo.dateStart}
            </if>
            <if test="pageInfo.dateEnd != null">
                and ctime &lt; #{pageInfo.dateEnd}
            </if>
        </where>
        <if test="pageInfo.orderBy != null">
            order by
            <foreach collection="pageInfo.orderBy" separator="," item="orderBy">
                ${orderBy.field} ${orderBy.order.name}
            </foreach>
        </if>
        limit #{pageInfo.pageStart}, #{pageInfo.pageSize}
    </select>

    <select id="count" parameterType="com.wosai.trade.model.dal.MerchantConfigBypassDalParam"
            resultType="java.lang.Long">
        SELECT count(1)
        FROM `merchant_config_bypass`
        <where>
            <if test="merchantId != null">
                AND merchant_id = #{merchantId}
            </if>
            <if test="currentProvider != null">
                AND `current_provider` = #{currentProvider}
            </if>
            <if test="payway != null">
                AND `payway` = #{payway}
            </if>
            <if test="pageInfo.dateStart != null">
                and ctime &gt; #{pageInfo.dateStart}
            </if>
            <if test="pageInfo.dateEnd != null">
                and ctime &lt; #{pageInfo.dateEnd}
            </if>
        </where>
    </select>

    <select id="countByCurrentProvider" resultType="java.lang.Long">
        SELECT count(1)
        FROM `merchant_config_bypass`
        where `current_provider` = #{currentProvider}
    </select>

    <select id="selectByMerchantId" resultMap="MerchantConfigBypassResultMap">
        SELECT `id`,
        `merchant_id`,
        `current_provider`,
        `payway`,
        `b2c_agent_name`,
        `c2b_agent_name`,
        `wap_agent_name`,
        `mini_agent_name`,
        `app_agent_name`,
        `h5_agent_name`,
        `params`,
        `ctime`,
        `mtime`,
        `deleted`,
        `version`
        FROM `merchant_config_bypass`
        WHERE `merchant_id` = #{merchantId}
    </select>

    <select id="selectById" resultMap="MerchantConfigBypassResultMap">
        SELECT `id`,
        `merchant_id`,
        `current_provider`,
        `payway`,
        `b2c_agent_name`,
        `c2b_agent_name`,
        `wap_agent_name`,
        `mini_agent_name`,
        `app_agent_name`,
        `h5_agent_name`,
        `params`,
        `ctime`,
        `mtime`,
        `deleted`,
        `version`
        FROM `merchant_config_bypass`
        WHERE `id` = #{id}
    </select>

    <select id="selectByIds" parameterType="java.util.List" resultMap="MerchantConfigBypassResultMap">
        SELECT `id`,
        `merchant_id`,
        `current_provider`,
        `payway`,
        `b2c_agent_name`,
        `c2b_agent_name`,
        `wap_agent_name`,
        `mini_agent_name`,
        `app_agent_name`,
        `h5_agent_name`,
        `params`,
        `ctime`,
        `mtime`,
        `deleted`,
        `version`
        FROM `merchant_config_bypass`
        WHERE `id` IN (
        <foreach collection="list" item="item" index="index" open="" separator="," close="">
            #{item}
        </foreach>
        )
    </select>

    <select id="selectByMerchantIdAndPayway" resultMap="MerchantConfigBypassResultMap">
        SELECT `id`,
        `merchant_id`,
        `current_provider`,
        `payway`,
        `b2c_agent_name`,
        `c2b_agent_name`,
        `wap_agent_name`,
        `mini_agent_name`,
        `app_agent_name`,
        `h5_agent_name`,
        `params`,
        `ctime`,
        `mtime`,
        `deleted`,
        `version`
        FROM `merchant_config_bypass`
        WHERE `merchant_id` = #{param1} and `payway` = #{param2}
    </select>

    <select id="deleteById">
        delete FROM `merchant_config_bypass` WHERE `id` = #{id}
    </select>

    <select id="deleteByMerchantId">
        delete FROM `merchant_config_bypass` WHERE `merchant_Id` = #{merchantId}
    </select>
</mapper>
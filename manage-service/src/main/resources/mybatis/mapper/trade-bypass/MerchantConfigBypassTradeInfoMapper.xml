<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.trade.repository.dao.MerchantConfigBypassTradeInfoDao">

    <resultMap id="MerchantConfigBypassTradeInfoResultMap" type="com.wosai.trade.repository.dao.entity.MerchantConfigBypassTradeInfoEntity">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="batchNo" column="batch_no" jdbcType="VARCHAR"/>
        <result property="merchantId" column="merchant_id" jdbcType="VARCHAR"/>
        <result property="tsn" column="tsn" jdbcType="VARCHAR"/>
        <result property="sourceOrganization" column="source_organization" jdbcType="INTEGER"/>
        <result property="bypassOrganization" column="bypass_organization" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="amount" column="amount" jdbcType="BIGINT"/>
        <result property="tradeTime" column="trade_time" jdbcType="BIGINT"/>
        <result property="ctime" column="ctime" jdbcType="BIGINT"/>
        <result property="mtime" column="mtime" jdbcType="BIGINT"/>
        <result property="deleted" column="deleted" jdbcType="TINYINT"/>
        <result property="version" column="version" jdbcType="BIGINT"/>
    </resultMap>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" keyColumn="id"
            parameterType="com.wosai.trade.model.dal.MerchantConfigBypassTradeInfoDalParam">
        INSERT INTO `merchant_config_bypass_tradeinfo`(`batch_no`, `merchant_id`, `tsn`, `source_organization`, `bypass_organization`,
                                `type`, `amount`,`trade_time`, `ctime`, `mtime`, `deleted`, `version`)
            VALUES (#{batchNo}, #{merchantId}, #{tsn}, #{sourceOrganization}, #{bypassOrganization},
                   #{type}, #{amount}, #{tradeTime}, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000, 0, 1)
    </insert>

    <insert id="batchInsert">
        INSERT INTO `merchant_config_bypass_tradeinfo`(`batch_no`, `merchant_id`, `tsn`, `source_organization`, `bypass_organization`,
                                `type`, `amount`, `trade_time`, `ctime`, `mtime`, `deleted`, `version`) VALUE
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.batchNo}, #{item.merchantId}, #{item.tsn}, #{item.sourceOrganization}, #{item.bypassOrganization},
                   #{item.type}, #{item.amount}, #{item.tradeTime}, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000, 0, 1)
        </foreach>
    </insert>

    <select id="selectList" parameterType="com.wosai.trade.model.dal.MerchantConfigBypassDalParam"
            resultMap="MerchantConfigBypassTradeInfoResultMap">
        SELECT `id`,
        `batch_no`,
        `merchant_id`,
        `tsn`,
        `source_organization`,
        `bypass_organization`,
        `type`,
        `amount`,
        `trade_time`,
        `ctime`,
        `mtime`,
        `deleted`,
        `version`
        FROM `merchant_config_bypass_tradeinfo`
        <where>
            <if test="merchantId != null">
                AND merchant_id = #{merchantId}
            </if>
            <if test="batchNo != null">
                AND `batch_no` = #{batchNo}
            </if>
            <if test="pageInfo.dateStart != null">
                and trade_time &gt; #{pageInfo.dateStart}
            </if>
            <if test="pageInfo.dateEnd != null">
                and trade_time &lt; #{pageInfo.dateEnd}
            </if>
        </where>
        <if test="pageInfo.orderBy != null">
            order by
            <foreach collection="pageInfo.orderBy" separator="," item="orderBy">
                ${orderBy.field} ${orderBy.order.name}
            </foreach>
        </if>
        limit #{pageInfo.pageStart}, #{pageInfo.pageSize}
    </select>

    <select id="count" parameterType="com.wosai.trade.model.dal.MerchantConfigBypassTradeInfoDalParam"
            resultType="java.lang.Long">
        SELECT count(1)
        FROM `merchant_config_bypass_tradeinfo`
        <where>
            <if test="merchantId != null">
                AND merchant_id = #{merchantId}
            </if>
            <if test="batchNo != null">
                AND `batch_no` = #{batchNo}
            </if>
            <if test="pageInfo.dateStart != null">
                and trade_time &gt; #{pageInfo.dateStart}
            </if>
            <if test="pageInfo.dateEnd != null">
                and trade_time &lt; #{pageInfo.dateEnd}
            </if>
        </where>
    </select>

    <select id="selectById" resultMap="MerchantConfigBypassTradeInfoResultMap">
         SELECT `id`,
        `batch_no`,
        `merchant_id`,
        `tsn`,
        `source_organization`,
        `bypass_organization`,
        `type`,
        `amount`,
        `trade_time`,
        `ctime`,
        `mtime`,
        `deleted`,
        `version`
        FROM `merchant_config_bypass_tradeinfo`
        WHERE `id` = #{id}
    </select>

    <select id="deleteById">
        delete FROM `merchant_config_bypass_tradeinfo` WHERE `id` = #{id}
    </select>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.trade.repository.dao.RefundApprovalRecordEntityMapper">
  <resultMap id="BaseResultMap" type="com.wosai.trade.repository.dao.entity.RefundApprovalRecordEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_order_sn" jdbcType="VARCHAR" property="businessOrderSn" />
    <result column="merchant_id" jdbcType="VARCHAR" property="merchantId" />
    <result column="order_store_sn" jdbcType="VARCHAR" property="orderStoreSn" />
    <result column="refund_store_sn" jdbcType="VARCHAR" property="refundStoreSn" />
    <result column="trade_app" jdbcType="BIGINT" property="tradeApp" />
    <result column="apply_user_id" jdbcType="VARCHAR" property="applyUserId" />
    <result column="audit_user_id" jdbcType="VARCHAR" property="auditUserId" />
    <result column="authorized_user_ids" jdbcType="VARCHAR" property="authorizedUserIds" />
    <result column="apply_amount" jdbcType="BIGINT" property="applyAmount" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="audit_remark" jdbcType="VARCHAR" property="auditRemark" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="extra" jdbcType="VARCHAR" property="extra" />
    <result column="audit_log" jdbcType="VARCHAR" property="auditLog" />
    <result column="ctime" jdbcType="BIGINT" property="ctime" />
    <result column="mtime" jdbcType="BIGINT" property="mtime" />
    <result column="audit_time" jdbcType="BIGINT" property="auditTime" />
    <result column="pay_source" jdbcType="VARCHAR" property="paySource" />
    <result column="category" jdbcType="TINYINT" property="category" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.wosai.trade.repository.dao.entity.RefundApprovalRecordEntity">
    <result column="business_show_info" jdbcType="LONGVARCHAR" property="businessShowInfo" />
  </resultMap>
  <sql id="Base_Column_List">
    id, business_order_sn, merchant_id, order_store_sn, refund_store_sn, trade_app, apply_user_id, 
    audit_user_id, authorized_user_ids, apply_amount, status, audit_remark, remark, extra,
    audit_log, ctime, mtime, audit_time,pay_source,category
  </sql>
  <sql id="Blob_Column_List">
    business_show_info
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from refund_approval_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from refund_approval_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.wosai.trade.repository.dao.entity.RefundApprovalRecordEntity">
    insert into refund_approval_record (id, business_order_sn, merchant_id, 
      order_store_sn, refund_store_sn, trade_app, 
      apply_user_id, audit_user_id, authorized_user_ids,
      apply_amount, status, audit_remark,
      remark, extra, audit_log,
      ctime, mtime, audit_time, 
      business_show_info,pay_source,category)
    values (#{id,jdbcType=BIGINT}, #{businessOrderSn,jdbcType=VARCHAR}, #{merchantId,jdbcType=VARCHAR}, 
      #{orderStoreSn,jdbcType=VARCHAR}, #{refundStoreSn,jdbcType=VARCHAR}, #{tradeApp,jdbcType=BIGINT}, 
      #{applyUserId,jdbcType=VARCHAR}, #{auditUserId,jdbcType=VARCHAR}, #{authorizedUserIds,jdbcType=VARCHAR},
      #{applyAmount,jdbcType=BIGINT}, #{status,jdbcType=TINYINT}, #{auditRemark,jdbcType=VARCHAR},
      #{remark,jdbcType=VARCHAR}, #{extra,jdbcType=VARCHAR}, #{auditLog,jdbcType=VARCHAR},
      #{ctime,jdbcType=BIGINT}, #{mtime,jdbcType=BIGINT}, #{auditTime,jdbcType=BIGINT}, 
      #{businessShowInfo,jdbcType=LONGVARCHAR},#{paySource,jdbcType=VARCHAR},#{category,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.trade.repository.dao.entity.RefundApprovalRecordEntity">
    insert into refund_approval_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="businessOrderSn != null">
        business_order_sn,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="orderStoreSn != null">
        order_store_sn,
      </if>
      <if test="refundStoreSn != null">
        refund_store_sn,
      </if>
      <if test="tradeApp != null">
        trade_app,
      </if>
      <if test="applyUserId != null">
        apply_user_id,
      </if>
      <if test="auditUserId != null">
        audit_user_id,
      </if>
      <if test="authorizedUserIds != null">
        authorized_user_ids,
      </if>
      <if test="applyAmount != null">
        apply_amount,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="auditRemark != null">
        audit_remark,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="extra != null">
        extra,
      </if>
      <if test="auditLog != null">
        audit_log,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="auditTime != null">
        audit_time,
      </if>
      <if test="paySource != null">
        pay_source,
      </if>
      <if test="category != null">
        category,
      </if>
      <if test="businessShowInfo != null">
        business_show_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="businessOrderSn != null">
        #{businessOrderSn,jdbcType=VARCHAR},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=VARCHAR},
      </if>
      <if test="orderStoreSn != null">
        #{orderStoreSn,jdbcType=VARCHAR},
      </if>
      <if test="refundStoreSn != null">
        #{refundStoreSn,jdbcType=VARCHAR},
      </if>
      <if test="tradeApp != null">
        #{tradeApp,jdbcType=BIGINT},
      </if>
      <if test="applyUserId != null">
        #{applyUserId,jdbcType=VARCHAR},
      </if>
      <if test="auditUserId != null">
        #{auditUserId,jdbcType=VARCHAR},
      </if>
      <if test="authorizedUserIds != null">
        #{authorizedUserIds,jdbcType=VARCHAR},
      </if>
      <if test="applyAmount != null">
        #{applyAmount,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="auditRemark != null">
        #{auditRemark,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=VARCHAR},
      </if>
      <if test="auditLog != null">
        #{auditLog,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=BIGINT},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=BIGINT},
      </if>
      <if test="auditTime != null">
        #{auditTime,jdbcType=BIGINT},
      </if>
      <if test="paySource != null">
        #{paySource,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        #{category,jdbcType=TINYINT},
      </if>
      <if test="businessShowInfo != null">
        #{businessShowInfo,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.trade.repository.dao.entity.RefundApprovalRecordEntity">
    update refund_approval_record
    <set>
      <if test="businessOrderSn != null">
        business_order_sn = #{businessOrderSn,jdbcType=VARCHAR},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=VARCHAR},
      </if>
      <if test="orderStoreSn != null">
        order_store_sn = #{orderStoreSn,jdbcType=VARCHAR},
      </if>
      <if test="refundStoreSn != null">
        refund_store_sn = #{refundStoreSn,jdbcType=VARCHAR},
      </if>
      <if test="tradeApp != null">
        trade_app = #{tradeApp,jdbcType=BIGINT},
      </if>
      <if test="applyUserId != null">
        apply_user_id = #{applyUserId,jdbcType=VARCHAR},
      </if>
      <if test="auditUserId != null">
        audit_user_id = #{auditUserId,jdbcType=VARCHAR},
      </if>
      <if test="authorizedUserIds != null">
        authorized_user_ids = #{authorizedUserIds,jdbcType=VARCHAR},
      </if>
      <if test="applyAmount != null">
        apply_amount = #{applyAmount,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="auditRemark != null">
        audit_remark = #{auditRemark,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="extra != null">
        extra = #{extra,jdbcType=VARCHAR},
      </if>
      <if test="auditLog != null">
        audit_log = #{auditLog,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=BIGINT},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=BIGINT},
      </if>
      <if test="auditTime != null">
        audit_time = #{auditTime,jdbcType=BIGINT},
      </if>
      <if test="paySource != null">
        pay_source = #{paySource,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        category = #{category,jdbcType=TINYINT},
      </if>
      <if test="businessShowInfo != null">
        business_show_info = #{businessShowInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.wosai.trade.repository.dao.entity.RefundApprovalRecordEntity">
    update refund_approval_record
    set business_order_sn = #{businessOrderSn,jdbcType=VARCHAR},
      merchant_id = #{merchantId,jdbcType=VARCHAR},
      order_store_sn = #{orderStoreSn,jdbcType=VARCHAR},
      refund_store_sn = #{refundStoreSn,jdbcType=VARCHAR},
      trade_app = #{tradeApp,jdbcType=BIGINT},
      apply_user_id = #{applyUserId,jdbcType=VARCHAR},
      audit_user_id = #{auditUserId,jdbcType=VARCHAR},
      authorized_user_ids = #{authorizedUserIds,jdbcType=VARCHAR},
      apply_amount = #{applyAmount,jdbcType=BIGINT},
      status = #{status,jdbcType=TINYINT},
      audit_remark = #{auditRemark,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      extra = #{extra,jdbcType=VARCHAR},
      audit_log = #{auditLog,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=BIGINT},
      mtime = #{mtime,jdbcType=BIGINT},
      audit_time = #{auditTime,jdbcType=BIGINT},
      pay_source = #{paySource,jdbcType=VARCHAR},
      category = #{category,jdbcType=TINYINT},
      business_show_info = #{businessShowInfo,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wosai.trade.repository.dao.entity.RefundApprovalRecordEntity">
    update refund_approval_record
    set business_order_sn = #{businessOrderSn,jdbcType=VARCHAR},
      merchant_id = #{merchantId,jdbcType=VARCHAR},
      order_store_sn = #{orderStoreSn,jdbcType=VARCHAR},
      refund_store_sn = #{refundStoreSn,jdbcType=VARCHAR},
      trade_app = #{tradeApp,jdbcType=BIGINT},
      apply_user_id = #{applyUserId,jdbcType=VARCHAR},
      audit_user_id = #{auditUserId,jdbcType=VARCHAR},
      authorized_user_ids = #{authorizedUserIds,jdbcType=VARCHAR},
      apply_amount = #{applyAmount,jdbcType=BIGINT},
      status = #{status,jdbcType=TINYINT},
      audit_remark = #{auditRemark,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      extra = #{extra,jdbcType=VARCHAR},
      audit_log = #{auditLog,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=BIGINT},
      mtime = #{mtime,jdbcType=BIGINT},
      audit_time = #{auditTime,jdbcType=BIGINT},
      pay_source = #{paySource,jdbcType=VARCHAR},
      category = #{category,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateByStatus" parameterType="com.wosai.trade.model.dal.UpdateRefundApprovalParam">
    update refund_approval_record
    set
    status = #{afterStatus,jdbcType=TINYINT},
    remark = #{rejectReason,jdbcType=VARCHAR},
    where id = #{id,jdbcType=BIGINT} and status=#{beforeStatus,jdbcType=INTEGER}
  </update>

  <select id="selectList" parameterType="com.wosai.trade.model.dal.RefundApprovalQueryDalParam" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from refund_approval_record rar
    <where>
      <if test="applyId!= null">
        AND rar.id = #{applyId}
      </if>
      <if test="tradeAppId!= null">
        AND rar.trade_app = #{tradeAppId}
      </if>
      <if test="businessOrderSn!= null">
        AND rar.business_order_sn = #{businessOrderSn}
      </if>
      <if test="businessOrderSnList!= null and businessOrderSnList.size() &gt; 0">
        and rar.business_order_sn in
        <foreach close=")" collection="businessOrderSnList" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="statusList!= null and statusList.size() &gt; 0">
        and status in
        <foreach close=")" collection="statusList" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </if>
    </where>
    ORDER BY rar.id DESC
      <![CDATA[ LIMIT #{pageInfo.pageStart}, #{pageInfo.pageSize} ]]>
  </select>

  <select id="selectListWithCondition" parameterType="com.wosai.trade.model.dal.RefundApprovalQueryParam" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from refund_approval_record rar
    <where>
      <if test="statusList!= null and statusList.size() &gt; 0">
        and status in
        <foreach close=")" collection="statusList" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="startTime!= null and endTime!= null">
        and ctime between #{startTime} and #{endTime}
      </if>
      <if test="startTime!= null and endTime == null">
        and ctime &gt;= #{startTime}
      </if>
      <if test="startTime == null and endTime!= null">
        and ctime &lt;= #{endTime}
      </if>
    </where>
    ORDER BY rar.id DESC
      <![CDATA[ LIMIT #{pageInfo.pageStart}, #{pageInfo.pageSize} ]]>
  </select>

</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.trade.repository.dao.RefundOrderSnRelationEntityMapper" >
  <resultMap id="BaseResultMap" type="com.wosai.trade.repository.dao.entity.RefundOrderSnRelationEntity" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="business_order_sn" property="businessOrderSn" jdbcType="VARCHAR" />
    <result column="origin_order_sn" property="originOrderSn" jdbcType="VARCHAR" />
    <result column="ctime" property="ctime" jdbcType="BIGINT" />
    <result column="mtime" property="mtime" jdbcType="BIGINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, business_order_sn, origin_order_sn, ctime, mtime
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from refund_order_sn_relation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from refund_order_sn_relation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.wosai.trade.repository.dao.entity.RefundOrderSnRelationEntity" >
    insert into refund_order_sn_relation (id, business_order_sn, origin_order_sn, 
      ctime, mtime)
    values (#{id,jdbcType=BIGINT}, #{businessOrderSn,jdbcType=VARCHAR}, #{originOrderSn,jdbcType=VARCHAR}, 
      #{ctime,jdbcType=BIGINT}, #{mtime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.trade.repository.dao.entity.RefundOrderSnRelationEntity" >
    insert into refund_order_sn_relation
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="businessOrderSn != null" >
        business_order_sn,
      </if>
      <if test="originOrderSn != null" >
        origin_order_sn,
      </if>
      <if test="ctime != null" >
        ctime,
      </if>
      <if test="mtime != null" >
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="businessOrderSn != null" >
        #{businessOrderSn,jdbcType=VARCHAR},
      </if>
      <if test="originOrderSn != null" >
        #{originOrderSn,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null" >
        #{ctime,jdbcType=BIGINT},
      </if>
      <if test="mtime != null" >
        #{mtime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.trade.repository.dao.entity.RefundOrderSnRelationEntity" >
    update refund_order_sn_relation
    <set >
      <if test="businessOrderSn != null" >
        business_order_sn = #{businessOrderSn,jdbcType=VARCHAR},
      </if>
      <if test="originOrderSn != null" >
        origin_order_sn = #{originOrderSn,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null" >
        ctime = #{ctime,jdbcType=BIGINT},
      </if>
      <if test="mtime != null" >
        mtime = #{mtime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wosai.trade.repository.dao.entity.RefundOrderSnRelationEntity" >
    update refund_order_sn_relation
    set business_order_sn = #{businessOrderSn,jdbcType=VARCHAR},
      origin_order_sn = #{originOrderSn,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=BIGINT},
      mtime = #{mtime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectWithCondition" parameterType="com.wosai.trade.model.dal.RefundOrderSnQueryParam" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List"/>
    from refund_order_sn_relation rar
    <where>
      <if test="orderSn != null">
        rar.origin_order_sn = #{orderSn,jdbcType=VARCHAR}
      </if>
    </where>
  </select>


</mapper>
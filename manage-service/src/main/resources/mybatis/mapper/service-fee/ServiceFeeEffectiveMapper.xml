<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.trade.repository.dao.ServiceFeeEffectiveMapper">

    <resultMap id="BaseResultMap" type="com.wosai.trade.repository.dao.entity.ServiceFeeEffectiveEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="feeId" column="fee_id" jdbcType="BIGINT"/>
            <result property="tradeAppId" column="trade_app_id" jdbcType="BIGINT"/>
            <result property="dimensionNo" column="dimension_no" jdbcType="VARCHAR"/>
            <result property="level" column="level" jdbcType="VARCHAR"/>
            <result property="merchantSn" column="merchant_sn" jdbcType="VARCHAR"/>
            <result property="sn" column="sn" jdbcType="VARCHAR"/>
            <result property="auditSn" column="audit_sn" jdbcType="VARCHAR"/>
            <result property="scenesType" column="scenes_type" jdbcType="VARCHAR"/>
            <result property="scenesCode" column="scenes_code" jdbcType="VARCHAR"/>
            <result property="profitShareRatio" column="profit_share_ratio" jdbcType="VARCHAR"/>
            <result property="minCharge" column="min_charge" jdbcType="BIGINT"/>
            <result property="chargeAmount" column="charge_amount" jdbcType="BIGINT"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="beginDate" column="begin_date" jdbcType="TIMESTAMP"/>
            <result property="endDate" column="end_date" jdbcType="TIMESTAMP"/>
            <result property="operatorId" column="operator_id" jdbcType="VARCHAR"/>
            <result property="process" column="process" jdbcType="VARCHAR"/>
            <result property="extra" column="extra" jdbcType="VARCHAR"/>
            <result property="ctime" column="ctime" jdbcType="TIMESTAMP"/>
            <result property="mtime" column="mtime" jdbcType="TIMESTAMP"/>
            <result property="version" column="version" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,fee_id,trade_app_id,
        dimension_no,level,merchant_sn,
        sn,audit_sn,scenes_type,
        scenes_code,profit_share_ratio,min_charge,
        charge_amount,status,begin_date,
        end_date,operator_id,process,
        extra,ctime,mtime,
        version
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from service_fee_effective
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from service_fee_effective
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.wosai.trade.repository.dao.entity.ServiceFeeEffectiveEntity" useGeneratedKeys="true">
        insert into service_fee_effective
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="feeId != null">fee_id,</if>
                <if test="tradeAppId != null">trade_app_id,</if>
                <if test="dimensionNo != null">dimension_no,</if>
                <if test="level != null">level,</if>
                <if test="merchantSn != null">merchant_sn,</if>
                <if test="sn != null">sn,</if>
                <if test="auditSn != null">audit_sn,</if>
                <if test="scenesType != null">scenes_type,</if>
                <if test="scenesCode != null">scenes_code,</if>
                <if test="profitShareRatio != null">profit_share_ratio,</if>
                <if test="minCharge != null">min_charge,</if>
                <if test="chargeAmount != null">charge_amount,</if>
                <if test="status != null">status,</if>
                <if test="beginDate != null">begin_date,</if>
                <if test="endDate != null">end_date,</if>
                <if test="operatorId != null">operator_id,</if>
                <if test="process != null">process,</if>
                <if test="extra != null">extra,</if>
                <if test="ctime != null">ctime,</if>
                <if test="mtime != null">mtime,</if>
                <if test="version != null">version,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="feeId != null">#{feeId,jdbcType=BIGINT},</if>
                <if test="tradeAppId != null">#{tradeAppId,jdbcType=BIGINT},</if>
                <if test="dimensionNo != null">#{dimensionNo,jdbcType=VARCHAR},</if>
                <if test="level != null">#{level,jdbcType=VARCHAR},</if>
                <if test="merchantSn != null">#{merchantSn,jdbcType=VARCHAR},</if>
                <if test="sn != null">#{sn,jdbcType=VARCHAR},</if>
                <if test="auditSn != null">#{auditSn,jdbcType=VARCHAR},</if>
                <if test="scenesType != null">#{scenesType,jdbcType=VARCHAR},</if>
                <if test="scenesCode != null">#{scenesCode,jdbcType=VARCHAR},</if>
                <if test="profitShareRatio != null">#{profitShareRatio,jdbcType=VARCHAR},</if>
                <if test="minCharge != null">#{minCharge,jdbcType=BIGINT},</if>
                <if test="chargeAmount != null">#{chargeAmount,jdbcType=BIGINT},</if>
                <if test="status != null">#{status,jdbcType=TINYINT},</if>
                <if test="beginDate != null">#{beginDate,jdbcType=TIMESTAMP},</if>
                <if test="endDate != null">#{endDate,jdbcType=TIMESTAMP},</if>
                <if test="operatorId != null">#{operatorId,jdbcType=VARCHAR},</if>
                <if test="process != null">#{process,jdbcType=VARCHAR},</if>
                <if test="extra != null">#{extra,jdbcType=VARCHAR},</if>
                <if test="ctime != null">#{ctime,jdbcType=TIMESTAMP},</if>
                <if test="mtime != null">#{mtime,jdbcType=TIMESTAMP},</if>
                <if test="version != null">#{version,jdbcType=INTEGER},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.wosai.trade.repository.dao.entity.ServiceFeeEffectiveEntity">
        update service_fee_effective
        <set>
                <if test="feeId != null">
                    fee_id = #{feeId,jdbcType=BIGINT},
                </if>
                <if test="tradeAppId != null">
                    trade_app_id = #{tradeAppId,jdbcType=BIGINT},
                </if>
                <if test="dimensionNo != null">
                    dimension_no = #{dimensionNo,jdbcType=VARCHAR},
                </if>
                <if test="level != null">
                    level = #{level,jdbcType=VARCHAR},
                </if>
                <if test="merchantSn != null">
                    merchant_sn = #{merchantSn,jdbcType=VARCHAR},
                </if>
                <if test="sn != null">
                    sn = #{sn,jdbcType=VARCHAR},
                </if>
                <if test="auditSn != null">
                    audit_sn = #{auditSn,jdbcType=VARCHAR},
                </if>
                <if test="scenesType != null">
                    scenes_type = #{scenesType,jdbcType=VARCHAR},
                </if>
                <if test="scenesCode != null">
                    scenes_code = #{scenesCode,jdbcType=VARCHAR},
                </if>
                <if test="profitShareRatio != null">
                    profit_share_ratio = #{profitShareRatio,jdbcType=VARCHAR},
                </if>
                <if test="minCharge != null">
                    min_charge = #{minCharge,jdbcType=BIGINT},
                </if>
                <if test="chargeAmount != null">
                    charge_amount = #{chargeAmount,jdbcType=BIGINT},
                </if>
                <if test="status != null">
                    status = #{status,jdbcType=TINYINT},
                </if>
                <if test="beginDate != null">
                    begin_date = #{beginDate,jdbcType=TIMESTAMP},
                </if>
                <if test="endDate != null">
                    end_date = #{endDate,jdbcType=TIMESTAMP},
                </if>
                <if test="operatorId != null">
                    operator_id = #{operatorId,jdbcType=VARCHAR},
                </if>
                <if test="process != null">
                    process = #{process,jdbcType=VARCHAR},
                </if>
                <if test="extra != null">
                    extra = #{extra,jdbcType=VARCHAR},
                </if>
                <if test="ctime != null">
                    ctime = #{ctime,jdbcType=TIMESTAMP},
                </if>
                <if test="mtime != null">
                    mtime = #{mtime,jdbcType=TIMESTAMP},
                </if>
                <if test="version != null">
                    version = version + 1,
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <select id="countByCondition" resultType="java.lang.Integer">
        select
        count(*)
        from service_fee_effective
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="feeId != null">
                and fee_id = #{feeId}
            </if>
            <if test="tradeAppId != null">
                and trade_app_id = #{tradeAppId}
            </if>
            <if test="level != null">
                and `level` = #{level}
            </if>
            <if test="merchantSn != null">
                and merchant_sn = #{merchantSn}
            </if>
            <if test="dimensionNo != null">
                and dimension_no = #{dimensionNo}
            </if>
            <if test="sn != null">
                and sn = #{sn}
            </if>
            <if test="scenesType != null">
                and scenes_type = #{scenesType}
            </if>
            <if test="scenesCode != null">
                and scenes_code = #{scenesCode}
            </if>
            <if test="statusList != null and statusList.size > 0">
                and status in
                <foreach collection="statusList" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="queryByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from service_fee_effective fe
        <where>
            <if test="id != null">
                and fe.id = #{id}
            </if>
            <if test="feeId != null">
                and fe.fee_id = #{feeId}
            </if>
            <if test="tradeAppId != null">
                and fe.trade_app_id = #{tradeAppId}
            </if>
            <if test="level != null">
                and fe.`level` = #{level}
            </if>
            <if test="merchantSn != null">
                and fe.merchant_sn = #{merchantSn}
            </if>
            <if test="dimensionNo != null">
                and fe.dimension_no = #{dimensionNo}
            </if>
            <if test="sn != null">
                and fe.sn = #{sn}
            </if>
            <if test="scenesType != null">
                and fe.scenes_type = #{scenesType}
            </if>
            <if test="scenesCode != null">
                and fe.scenes_code = #{scenesCode}
            </if>
            <if test="auditSn != null">
                and fe.audit_sn = #{auditSn}
            </if>
            <if test="isDirect != null and isDirect">
                and EXISTS(select 1 from service_fee f where f.id=fe.fee_id and f.charge_cycle IS NOT NULL AND f.charge_cycle!='')
            </if>
            <if test="statusList != null and statusList.size > 0">
                and fe.status in
                <foreach collection="statusList" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        <if test="pageInfo != null">
            <if test="pageInfo.orderBy != null">
                order by
                <foreach collection="pageInfo.orderBy" separator="," item="orderBy">
                    ${orderBy.field} ${orderBy.order.name}
                </foreach>
            </if>
            limit #{pageInfo.pageStart}, #{pageInfo.pageSize}
        </if>
    </select>
    <select id="queryPkIdByEndDate" resultMap="BaseResultMap">
        select id, merchant_sn from service_fee_effective
        <where>
            <if test="endDate != null">
                and end_date <![CDATA[>=]]> #{endDate}
            </if>
            <if test="statusList != null">
                and status in
                <foreach collection="statusList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="pageable.lastId != null">
                and id <![CDATA[>]]> #{pageable.lastId}
            </if>
        </where>
        order by id
        limit #{pageable.limit}
    </select>
    <select id="queryByMerchantStatus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from service_fee_effective
        where merchant_sn=#{merchantSn} and fee_id=#{serverFeeId} and status=#{status} LIMIT 1
    </select>
    <select id="queryActiveReceiverAccountBusinessExtra"
            resultType="com.wosai.trade.repository.dao.entity.ServiceFeeEntity">
        SELECT b.id
             , b.name
             , JSON_UNQUOTE(JSON_EXTRACT(b.extra, '$.receiverAccountBusinessType')) as receiverAccountBusinessType
        FROM service_fee_effective a,
             service_fee b
        WHERE a.fee_id = b.id
          AND a.merchant_sn = #{merchantSn}
          AND a.status = 2
          AND JSON_UNQUOTE(JSON_EXTRACT(b.extra, '$.receiverAccountBusinessType')) = #{receiverAccountBusiness}
        GROUP BY b.id, b.name, receiverAccountBusinessType
        LIMIT 1
    </select>
</mapper>

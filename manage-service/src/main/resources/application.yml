spring:
  profiles:
    active: default
  application:
    name : manage-service
  server:
    core-business: http://core-business.beta.iwosai.com/
    sp-workflow: http://sp-workflow-service.beta.iwosai.com/
    business-log: http://business-log.beta.iwosai.com/
    data-tag: http://192.168.101.149:18081/
    remit: http://remit-gateway.beta.iwosai.com/
    aop: http://aop-gateway.beta.iwosai.com/
    user-service: http://user-service.beta.iwosai.com/
    merchant-user-service: http://merchant-user-service.beta.iwosai.com/
    terminal-sale-service: http://shouqianba-terminal-service.beta.iwosai.com/
    sales-system-service: http://sales-system-service.beta.iwosai.com/
    upay-grayscale: http://upay-grayscale.beta.iwosai.com/
    signature-proxy: http://signature-proxy.beta.iwosai.com/
    withdraw-service: http://shouqianba-withdraw-service.beta.iwosai.com/
    clearance-service: http://clearance-service.beta.iwosai.com/
    credit-pay-backend-service: http://credit-pay-backend.beta.iwosai.com/
    customer-relation-service: http://crm-customer-relation.beta.iwosai.com/
    face-recognition-service: http://face-recognition-service.beta.iwosai.com/
    mail-service: https://mail-callback.shouqianba.com/test
    upay-transaction-service: http://upay-transaction.beta.iwosai.com/
    upay-wallet: http://upay-wallet.beta.iwosai.com/
    enterprise: http://enterprise.beta.iwosai.com/
#    enterprise: http://localhost:11146/
    mail-gateway: http://mail-gateway.beta.iwosai.com/mail/send
    marketing-saas-prepaid-card: http://marketing-saas-prepaid-card.beta.iwosai.com/
    tethys: http://tethys.beta.iwosai.com/
    market-merchant-service: http://merchant.beta.iwosai.com/
    shouqianba-risk-service: http://shouqianba-risk-service.beta.iwosai.com/
    merchant-contract-activity: http://merchant-contract-activity.beta.iwosai.com/
    merchant-contract-job: http://merchant-contract-job.beta.iwosai.com/
#    merchant-contract-job: http://merchant-contract-job-cua7941.iwosai.com/
    merchant-contract-access: http://merchant-contract-access.beta.iwosai.com/
    apollo-portal: https://apollo-vpn.wosai-inc.com
    merchant-business-open: http://merchant-business-open.beta.iwosai.com/
    transaction-report: http://transaction-report.beta.iwosai.com/
    bank-business-service: http://bank-business.beta.iwosai.com/
    pay-business-open: http://pay-business-open.beta.iwosai.com/
    bank-info: http://bank-info-service.beta.iwosai.com/
    data-cooperation: http://data-cooperation.beta.iwosai.com/
    profit-sharing: http://profit-sharing.beta.iwosai.com/
    period-pay: http://period-pay.beta.iwosai.com/
    business-logstash: http://business-logstash.beta.iwosai.com/
    scene-manage-service: http://scene-manage-service.beta.iwosai.com/
    deposit-pay: http://deposit-pay.beta.iwosai.com/
    uc-user-service: http://uc-user-service.beta.iwosai.com/
    upay-gateway-query: http://upay-gateway.beta.iwosai.com/upay/v2/query
    upay-gateway-refund: http://upay-gateway.beta.iwosai.com/upay/v2/refund
    upay-charge-service: http://upay-charging.beta.iwosai.com/
  kafka:
    bootstrap-servers: 192.168.101.89:9092,192.168.100.52:9092,192.168.101.90:9092
    consumer:
      group-id: trade-manage-service-default-1
      enableAutoCommit: true
      maxPollRecords: 5000
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: io.confluent.kafka.serializers.KafkaAvroDeserializer
    producer:
      acks: all
      retries: 5
    properties:
      schema:
        registry:
          url: http://192.168.101.89:8081,http://192.168.100.52:8081,http://192.168.101.90:8081
    #数据中心-火山系统
    data-center-volcano:
      bootstrap-servers: ${spring.kafka.ali-test.bootstrap-servers}
      topic: analytics_data_volcengine_push
      b-merchant-app-id: 10000002
      b-store-app-id: 10000012
    #AOP审批中心
    aop-center-consumer:
      bootstrap-servers: ${spring.kafka.bootstrap-servers}
      schema-registry: ${spring.kafka.properties.schema.registry.url}
      group-id: trade-manage-service-default-1
    #crm数据处理
    crm-databus:
      bootstrap-servers: ${spring.kafka.bootstrap-servers}
      schema-registry: ${spring.kafka.properties.schema.registry.url}
    #续费订阅服务
    period-pay:
      topic: callback_PAY_period-pay-complete
      bootstrap-servers: ${spring.kafka.ali-test.bootstrap-servers}
      schema-registry: ${spring.kafka.ali-test.schema-registry}
      group-id: ${spring.kafka.ali-test.group-id}
    #阿里云
    aliyun:
      bootstrap-servers: ${spring.kafka.ali-test.bootstrap-servers}
      schema-registry: ${spring.kafka.ali-test.schema-registry}
      group-id: ${spring.kafka.ali-test.group-id}
    bank-recommend:
      topic: events_CUA_merchant-pay-amount
      bootstrap-servers: 192.168.158.250:9092,192.168.158.248:9092,192.168.158.249:9092
      schema-registry: http://192.168.103.171:8081,http://192.168.103.172:8081,http://192.168.103.173:8081
    #阿里云测试
    ali-test:
      bootstrap-servers: aliyun-beta-kafka-01.iwosai.com:9092,aliyun-beta-kafka-02.iwosai.com:9092,aliyun-beta-kafka-03.iwosai.com:9092
      schema-registry: http://aliyun-beta-schema-01.iwosai.com:8081,http://aliyun-beta-schema-02.iwosai.com:8081,http://aliyun-beta-schema-03.iwosai.com:8081
      group-id: trade-manage-service-default
    micro-upgrade-success:
      topic: events_CUA_micro_upgrade_success
      bootstrap-servers: aliyun-beta-kafka-01.iwosai.com:9092,aliyun-beta-kafka-02.iwosai.com:9092,aliyun-beta-kafka-03.iwosai.com:9092
      schema-registry: http://aliyun-beta-schema-01.iwosai.com:8081,http://aliyun-beta-schema-02.iwosai.com:8081,http://aliyun-beta-schema-03.iwosai.com:8081
      group-id: ${spring.kafka.consumer.group-id}
    merchant-config-change:
      topic: databus_PAY_merchant_config_allin
      bootstrap-servers: ${spring.kafka.bank-recommend.bootstrap-servers}
      schema-registry: ${spring.kafka.bank-recommend.schema-registry}
      group-id: ${spring.kafka.consumer.group-id}
    # 集权商户添加或删除通知
    group-merchant-change:
      topic: events_CUA_group_merchant_change
      bootstrap-servers: aliyun-beta-kafka-01.iwosai.com:9092,aliyun-beta-kafka-02.iwosai.com:9092,aliyun-beta-kafka-03.iwosai.com:9092
      schema-registry: http://aliyun-beta-schema-01.iwosai.com:8081,http://aliyun-beta-schema-02.iwosai.com:8081,http://aliyun-beta-schema-03.iwosai.com:8081
      group-id: ${spring.kafka.consumer.group-id}
    # 费率（减免）活动同步供下游使用
    fee-rate-activity-sync:
      topic: events_pay_fee_rate_activity_sync
      bootstrap-servers: ${spring.kafka.aliyun.bootstrap-servers}
      schema-registry: ${spring.kafka.aliyun.schema-registry}
  datasource:
    type: com.zaxxer.hikari.util.DriverDataSource
    hikari:
      driver-class-name: com.mysql.cj.jdbc.Driver
      jdbc-url: tk-trade-manage-service-trade_manage-1087?serverTimezone=Asia/Shanghai&rewriteBatchedStatements=true
      minimum-idle: 5
      idle-timeout: 300000
      maximum-pool-size: 20
      connection-timeout: 5000
      connection-test-query: select 1
#      不要改隔离级别
      transaction-isolation: TRANSACTION_READ_COMMITTED
  redis:
    host: r-8vbkddg0ez3eak2rzq.redis.zhangbei.rds.aliyuncs.com
    port: 6379
    password: roFXzHwXPY3RnI%5
    pool:
      max-wait: 1000


config:
  aop:
    dev-code: I0UDXL7CVVCT
    terminal-institutional-dev-code: USMFJNKRJUGN
    terminal-institutional-template-code: RXNU5MRBWMQE
    refund-approval-dev-code: AHUHKRKWXQS2
    refund-approval-app-template-code: OEKGFW20GV0R
    refund-approval-s-shou-template-code: L8M2I1SASMEQ
    refund-approval-app-push-template-code: 353Y3MD4AL6G
    template-code-notice: PFOADZKQ1NSH
    withdraw_service_dev-code: XWYEZTB4MXVM
    system_d0_template-code-notice: DLIITTQ4CXN7
    activity-merchant-notice-apply-success: 8XE4TXKG0GS9
    activity-merchant-notice-cancel: 8VCQPYNNOUV2
    activity-sale-notice-apply-success: QWNHH9W4NVTV
    activity-sale-notice-cancel:  KY0HXLOJZSDT
    activity-merchant-notice-change-success: PVDOHIVEDMAR
    activity-sale-notice-change-success: VW8QWSJEZNEH
    bank-quota-tag: 9de2599c-8a83-42f5-8f28-a8d1f2355b39
    #额度包活动通知
    quota-activity:
      activity-merchant-notice-apply-success: 25XANWIGMX8J
      activity-merchant-notice-cancel: AB35JQLMWLYW
      activity-sale-notice-apply-success: Y57WKPBVPBCT
      activity-sale-notice-cancel: QZFA5Z9XY5RA
    system_d0_thumbnail_url: https://opr-content-h5.shouqianba.com/rt?newH5Container=true&_k=OTllZDVkZjktNTdiOS00NWFjLWExN2MtMGE0ZWUyNDVjYTc2&pcid=NTUzYWU5NDMtYWFmMy00ZTRhLWJkODEtYzFjZmU3MTBjM2Rm
    bypass:
      dev-code: XWYEZTB4MXVM
      template-code: MT291MZMMGXQ
  puhui-month-change-mail-id: 217
  fee-rate-check-mail-id: 227
  #crm app 银行卡动态跳转链接地址
  crm-app-bank-card-bar-jump-url: https://trade-config-app.iwosai.com/cardPaymentFee?merchant_id={merchantId}&token=:token
  crm-indirect-dev-code: PQPBJGNJC26N
  activity-monitor:
    lark-url: https://open.feishu.cn/open-apis/bot/v2/hook/eb712f71-bba9-4171-aa40-46b999c05a65
    #费率同步钉钉通知
    fee-rate-sync-lark-url: https://open.feishu.cn/open-apis/bot/v2/hook/9a3869f2-f5f0-41c3-94a7-74e1caaf2462
    prep-offline-day: 30
    page-size: 500
    prep-offline-mail-id: 272
    abnormal-activity-mail-id: 274
    combo-activity-dashboard-mail-id: 275
    quota-activity-dashboard-mail-id: 273
  service-fee:
    #按时段付费统一URL
    direct-icon-url: https://wosai-statics.oss-cn-hangzhou.aliyuncs.com/app/images/icons/SaaS%E4%BB%98%E8%B4%B9%E9%BB%98%E8%AE%A4%E5%9B%BE%E6%A0%87.png
    #去续费统计跳转url
    renewal-redirect-url: https://app-sass-payment.iwosai.com/businessRenewal?bizSn={bizSn}

#mybatis
mybatis:
  config-location: classpath:mybatis/mybatis-config.xml
  mapper-locations: classpath:mybatis/**/*Mapper.xml

#databus:
#  consumer:
#    brokers: 192.168.101.89:9092,192.168.100.52:9092,192.168.101.90:9092
#    group-id: trade-manage-service-beta
#    topic: databus.event.crm.audit.allin
#    schema-registry-url: http://192.168.101.89:8081,http://192.168.100.52:8081,http://192.168.101.90:8081
#    additional:
#      auto-offset-reset: latest
#port
server:
  port : 8080

lark:
  url: https://open.feishu.cn/open-apis/bot/v2/hook/9a3869f2-f5f0-41c3-94a7-74e1caaf2462
  saasUrl: https://open.feishu.cn/open-apis/bot/v2/hook/cd1c0390-79d3-4936-a28e-dffc7efa7923

data:
  ladder-freezing-tag: 89a76ba7-ce57-4ce2-ac35-ef32b10825db
  ladder-new-one-tag: c4268f69-48b1-47dc-9c03-c827ea9f6930
  ladder-new-zero-tag: bd439810-7a28-49c1-8fb6-58db52567e31
  ladder-micro-mch-subsidy-tag: d3f92818-7020-485d-959b-431ab28206d7
  ladder-protect-trade-tag: 2719dd2e-a051-49d6-a040-9b8af4dfd867
  hbfq-tag: 7169e7b1-c3e3-4554-8e87-9d49e35f04b5

flags:
  consumer:
    run: true
  jobs:
    enabled: true


oss:
  micro-puhui-progress-bar: https://statics.shouqianba.com/app/images/Puhui/

auditTemplate:
  id: 66041
  platform: sp
  operatorId: 0af4121d-6cf5-19cc-816c-f66ffddc0000

tradeCombo:
  auditTemplate:
    id: 147080
    platform: ${auditTemplate.platform}
    operatorId: ${auditTemplate.operatorId}

transaction:
  topic: trade

jedis:
  pool:
    maxTotal: 48
    maxIdle: 6
    maxWaitMillis: 5000
    minEvictableIdleTimeMillis: 300000
    numTestsPerEvictionRun: 3
    timeBetweenEvictionRunsMillis: 60000

quotaAuditTemplate:
  id: 57167
  operatorId: 0af4121d-6cf5-19cc-816c-f66ffddc0000

app:
  name: trade-manage-service
  group: pay

pay:
  common:
    config:
      state:
        manager:
          business_log_stash_url: http://business-logstash
          kafka:
            bootstrap:
              servers: ${spring.kafka.aliyun.bootstrap-servers}
            state_change_topic: databus_pay_state_change
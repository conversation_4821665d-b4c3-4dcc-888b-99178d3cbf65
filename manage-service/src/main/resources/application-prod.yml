spring:
  application:
    name : manage-service
  server:
    core-business: http://app-core-business/
    sp-workflow: http://sp-workflow-service/
    business-log: http://business-log/
    data-tag: http://crow-server.internal.shouqianba.com/
    remit: http://remit-gateway/
    aop: http://aop-gateway/
    user-service: http://user-service/
    merchant-user-service: http://merchant-user-service/
    terminal-sale-service: http://shouqianba-terminal-service/
    sales-system-service: http://sales-system-service/
    upay-grayscale: http://upay-grayscale/
    signature-proxy: http://signature-proxy/
    withdraw-service: http://withdraw-service.internal.shouqianba.com/
    clearance-service: http://clearance-service/
    credit-pay-backend-service: http://credit-pay-backend/
    customer-relation-service: http://crm-customer-relation/
    face-recognition-service: http://face-recognition-service/
    mail-service: http://mail-gw-internal.shouqianba.com/email
    upay-transaction-service: http://upay-transaction-query.internal.shouqianba.com/
    upay-wallet: http://upay-wallet.vpc.shouqianba.com/
    enterprise: http://enterprise/
    mail-gateway: http://mail-gateway/mail/send
    marketing-saas-prepaid-card: http://marketing-saas-prepaid-card/
    tethys: http://tethys/
    market-merchant-service: http://merchant/
    shouqianba-risk-service: http://shouqianba-risk-service/
    merchant-contract-activity: http://merchant-contract-activity/
    merchant-contract-job: http://merchant-contract-job/
    merchant-contract-access: http://merchant-contract-access/
    merchant-business-open: http://merchant-business-open/
    transaction-report: http://transaction-report/
    bank-business-service: http://bank-business/
    pay-business-open: http://pay-business-open/
    bank-info: http://bank-info-service/
    data-cooperation: http://data-cooperation/
    profit-sharing: http://profit-sharing/
    period-pay: http://period-pay/
    business-logstash: http://business-logstash/
    scene-manage-service: http://scene-manage-service/
    deposit-pay: http://deposit-pay/
    uc-user-service: http://uc-user-service/
    upay-gateway-query: http://upay-gateway.vpc.shouqianba.com/upay/v2/query
    upay-gateway-refund: http://upay-gateway.vpc.shouqianba.com/upay/v2/refund
    upay-charge-service: http://upay-charging/
  kafka:
    bootstrap-servers: conflunt-kafka-01.shouqianba.com:9092,conflunt-kafka-02.shouqianba.com:9092,conflunt-kafka-03.shouqianba.com:9092,conflunt-kafka-04.shouqianba.com:9092,conflunt-kafka-05.shouqianba.com:9092
    consumer:
      group-id: trade-manage-service-prod
      enableAutoCommit: true
      maxPollRecords: 5000
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: io.confluent.kafka.serializers.KafkaAvroDeserializer
    #AOP审批中心
    aop-center-consumer:
      bootstrap-servers: aliyun-kafka-01.shouqianba.com:9092,aliyun-kafka-02.shouqianba.com:9092,aliyun-kafka-03.shouqianba.com:9092
      schema-registry: http://aliyun-schema-01.shouqianba.com:8081,http://aliyun-schema-02.shouqianba.com:8081,http://aliyun-schema-03.shouqianba.com:8081
      group-id: trade-manage-service-default-1
    producer:
      acks: all
      retries: 5
    properties:
      schema:
        registry:
          url: http://conflunt-schema-01.shouqianba.com:8081,http://conflunt-schema-02.shouqianba.com:8081,http://conflunt-schema-03.shouqianba.com:8081,http://conflunt-schema-04.shouqianba.com:8081,http://conflunt-schema-05.shouqianba.com:8081
    #数据中心-火山系统
    data-center-volcano:
      bootstrap-servers: ${spring.kafka.aop-center-consumer.bootstrap-servers}
      topic: analytics_data_volcengine_push
      b-merchant-app-id: 10000005
      b-store-app-id: 10000011
    #crm数据处理
    crm-databus:
      bootstrap-servers: ${spring.kafka.aop-center-consumer.bootstrap-servers}
      schema-registry: ${spring.kafka.aop-center-consumer.schema-registry}
    #续费管理
    period-pay:
      topic: callback_PAY_period-pay-complete
      bootstrap-servers: ${spring.kafka.aop-center-consumer.bootstrap-servers}
      schema-registry: ${spring.kafka.aop-center-consumer.schema-registry}
      group-id: ${spring.kafka.aop-center-consumer.group-id}
    #阿里云
    aliyun:
      bootstrap-servers: ${spring.kafka.aop-center-consumer.bootstrap-servers}
      schema-registry: ${spring.kafka.aop-center-consumer.schema-registry}
      group-id: ${spring.kafka.aop-center-consumer.group-id}
    bank-recommend:
      topic: events_CUA_merchant-pay-amount
      bootstrap-servers: aliyun-kafka-01.shouqianba.com:9092,aliyun-kafka-02.shouqianba.com:9092,aliyun-kafka-03.shouqianba.com:9092
      schema-registry: http://aliyun-schema-01.shouqianba.com:8081,http://aliyun-schema-02.shouqianba.com:8081,http://aliyun-schema-03.shouqianba.com:8081
    micro-upgrade-success:
      topic: events_CUA_micro_upgrade_success
      bootstrap-servers: aliyun-kafka-01.shouqianba.com:9092,aliyun-kafka-02.shouqianba.com:9092,aliyun-kafka-03.shouqianba.com:9092
      schema-registry: http://aliyun-schema-01.shouqianba.com:8081,http://aliyun-schema-02.shouqianba.com:8081,http://aliyun-schema-03.shouqianba.com:8081
      group-id: ${spring.kafka.consumer.group-id}
    merchant-config-change:
      topic: databus.event.merchant.config.allin
      bootstrap-servers: ${spring.kafka.bootstrap-servers}
      schema-registry: ${spring.kafka.properties.schema.registry.url}
      group-id: ${spring.kafka.consumer.group-id}
    # 集权商户添加或删除通知
    group-merchant-change:
      topic: events_CUA_group_merchant_change
      bootstrap-servers: aliyun-kafka-01.shouqianba.com:9092,aliyun-kafka-02.shouqianba.com:9092,aliyun-kafka-03.shouqianba.com:9092
      schema-registry: http://aliyun-schema-01.shouqianba.com:8081,http://aliyun-schema-02.shouqianba.com:8081,http://aliyun-schema-03.shouqianba.com:8081
      group-id: ${spring.kafka.consumer.group-id}
    # 费率（减免）活动同步供下游使用
    fee-rate-activity-sync:
      topic: events_pay_fee_rate_activity_sync
      bootstrap-servers: ${spring.kafka.aliyun.bootstrap-servers}
      schema-registry: ${spring.kafka.aliyun.schema-registry}

  datasource:
    type: com.zaxxer.hikari.util.DriverDataSource
    hikari:
      driver-class-name: com.mysql.cj.jdbc.Driver
      jdbc-url: pk-trade-manage-service-trade_manage-2480?serverTimezone=Asia/Shanghai&rewriteBatchedStatements=true
      minimum-idle: 5
      idle-timeout: 300000
      maximum-pool-size: 20
      connection-timeout: 5000
      connection-test-query: select 1
      #      不要改隔离级别
      transaction-isolation: TRANSACTION_READ_COMMITTED

  redis:
    host: r-bp192915766f85a4.redis.rds.aliyuncs.com
    port: 6379
    database: 13
    password: Ud9YAFb1jRoAYOG3OVS5
    pool:
      max-idle: 40
      max-active: 40
      max-wait: 1000


server:
  port : 8080

config:
  aop:
    dev-code: I0UDXL7CVVCT
    template-code-notice: PFOADZKQ1NSH
    terminal-institutional-dev-code: USMFJNKRJUGN
    terminal-institutional-template-code: RXNU5MRBWMQE
    refund-approval-dev-code: AHUHKRKWXQS2
    refund-approval-app-template-code: OEKGFW20GV0R
    refund-approval-s-shou-template-code: L8M2I1SASMEQ
    refund-approval-app-push-template-code: 353Y3MD4AL6G
    withdraw_service_dev-code: XWYEZTB4MXVM
    system_d0_template-code-notice: DLIITTQ4CXN7
    activity-merchant-notice-apply-success: 8XE4TXKG0GS9
    activity-merchant-notice-cancel: 8VCQPYNNOUV2
    activity-sale-notice-apply-success: QWNHH9W4NVTV
    activity-sale-notice-cancel: KY0HXLOJZSDT
    bank-quota-tag: b6cadae3-67f9-4c2b-8479-5ef7e263eb12
    system_d0_thumbnail_url: https://opr-content-h5.shouqianba.com/rt?newH5Container=true&_k=OTllZDVkZjktNTdiOS00NWFjLWExN2MtMGE0ZWUyNDVjYTc2&pcid=NTUzYWU5NDMtYWFmMy00ZTRhLWJkODEtYzFjZmU3MTBjM2Rm
  puhui-month-change-mail-id: 217
  fee-rate-check-mail-id: 227
  #crm app 银行卡动态跳转链接地址
  crm-app-bank-card-bar-jump-url: https://trade-config-app.shouqianba.com/cardPaymentFee?merchant_id={merchantId}&token=:token
  crm-indirect-dev-code: OEBIBCAY2K1H
  activity-monitor:
    #费率同步飞书通知
    fee-rate-sync-lark-url: https://open.feishu.cn/open-apis/bot/v2/hook/eb712f71-bba9-4171-aa40-46b999c05a65
  service-fee:
    #按时段付费统一URL
    direct-icon-url: https://wosai-statics.oss-cn-hangzhou.aliyuncs.com/app/images/icons/SaaS%E4%BB%98%E8%B4%B9%E9%BB%98%E8%AE%A4%E5%9B%BE%E6%A0%87.png
    #去续费统计跳转url
    renewal-redirect-url: https://app-sass-payment.shouqianba.com/businessRenewal?bizSn={bizSn}

data:
  ladder-freezing-tag: ********-1d3a-4359-ba1b-427a16f83edd
  ladder-new-one-tag: a7ea27bd-3a20-4cd6-a03c-6ff047a8918b
  ladder-new-zero-tag: 4230e653-526e-4599-8290-c4ed25bf3a2b
  ladder-micro-mch-subsidy-tag: c9fe4671-b01c-48a9-9d0d-c5f982fe21ba
  ladder-protect-trade-tag: 2c28848b-6bec-49cc-b209-1fdd638d1d0f
  hbfq-tag: 354e0eda-a238-4463-ae72-5dcf3ff76ffc

oss:
  micro-puhui-progress-bar: https://statics.shouqianba.com/app/images/Puhui/

lark:
  url: https://open.feishu.cn/open-apis/bot/v2/hook/9cab5680-8d0b-46a3-b968-50f0b6d7024c
  saasUrl: https://open.feishu.cn/open-apis/bot/v2/hook/2488fa2a-c42d-4d1e-810c-9817c41f504c

auditTemplate:
  id: 2249
  platform: sp
  operatorId: 0af01c1b-7410-1340-8175-120817690b9a

tradeCombo:
  auditTemplate:
    id: 2519
    platform: ${auditTemplate.platform}
    operatorId: ${auditTemplate.operatorId}

transaction:
  topic: events.upay.trade

jedis:
  pool:
    maxTotal: 48
    maxIdle: 6
    maxWaitMillis: 5000
    minEvictableIdleTimeMillis: 300000
    numTestsPerEvictionRun: 3
    timeBetweenEvictionRunsMillis: 60000


trade:
  activity:
    bindBankCardMessage:
      templateCode: YHBKHDSQTZMB
      devCode: XTISBCRB2WXW
  saas:
    #saas服费app展示
    appServiceAgreementFeeRates:
      #校园外卖
      xywm: 4
      #扫码点单
      smdd: 2
  #CRM费率调整通知
  crmFeeRateAdjustNotify:
    latelyDay: 3
    #延迟时间，单位：分钟
    delayMinute: 30
    #商户配置
    merchant:
      templateCode: HMF2AG6DGEZZ
      devCode: I0UDXL7CVVCT
    #销售配置
    sale:
      templateCode: VEGQRLVTB8J2
      devCode: I0UDXL7CVVCT
  # 新希望集团Id
  xinxiwangGroupId: aa78c1ed-3917-40ee-9d1c-55054ec3d065

quotaAuditTemplate:
  id: 2151
  operatorId: 0af01c1b-7410-1340-8175-120817690b9a

pay:
  common:
    config:
      state:
        manager:
          business_log_stash_url: http://business-logstash
          kafka:
            bootstrap:
              servers: ${spring.kafka.aliyun.bootstrap-servers}
            state_change_topic: databus_pay_state_change
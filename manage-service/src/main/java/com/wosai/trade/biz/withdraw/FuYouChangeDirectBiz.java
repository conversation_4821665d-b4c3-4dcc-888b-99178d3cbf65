package com.wosai.trade.biz.withdraw;

import com.wosai.pantheon.util.MapUtil;
import com.wosai.sp.business.logstash.dto.PlatformEnum;
import com.wosai.trade.impl.TradeCommonService;
import com.wosai.trade.util.LocalDateTimeUtil;
import com.wosai.trade.util.ValidationUtils;
import com.wosai.upay.core.constant.CoreCommonConstants;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.wallet.constant.ProviderWalletAccountTypeEnum;
import com.wosai.upay.wallet.model.EventLog;
import com.wosai.upay.wallet.request.WalletQueryReq;
import com.wosai.upay.wallet.request.v3.DeductBalanceReqV3;
import com.wosai.upay.wallet.service.WalletServiceV3;
import com.wosai.upay.wallet.util.SignUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 富友间清转接清
 *
 * <AUTHOR>
 *
 * @date 2025/8/25
 **/
@Slf4j
@Component
public class FuYouChangeDirectBiz {

    @Resource
    private TradeConfigService tradeConfigService;
    @Resource
    private TradeCommonService tradeCommonService;
    @Resource
    private SupportService supportService;
    @Resource
    private WalletServiceV3 walletServiceV3;

    public void execute(String merchantSn, String appid, String key, String operatorName, String remark) {
        ValidationUtils.notEmpty(merchantSn, "merchantSn不能为空");
        ValidationUtils.notEmpty(appid, "appid不能为空");
        ValidationUtils.notEmpty(key, "key不能为空");
        ValidationUtils.notEmpty(operatorName, "operatorName不能为空");
        ValidationUtils.notEmpty(remark, "remark不能为空");
        String merchantId = tradeCommonService.getMerchantIdByMerchantSn(merchantSn);
        Map<String, Object> request = MapUtil.hashMap(MerchantConfig.MERCHANT_ID, merchantId,
                CoreCommonConstants.KEY_COMMON_SWITCH_TYPE, TransactionParam.TYPE_COMMON_SWITCH_LIQUIDATION_NEXT_DAY_INDIRECT,
                CoreCommonConstants.KEY_COMMON_SWITCH_STATUS, TransactionParam.STATUS_OPENED
        );

        com.wosai.upay.core.bean.request.OpLogCreateRequestV2 opLogCreateRequestV2 = new com.wosai.upay.core.bean.request.OpLogCreateRequestV2();
        opLogCreateRequestV2.setOpUserName(operatorName);
        opLogCreateRequestV2.setOpUserId(operatorName);
        opLogCreateRequestV2.setRemark(remark);
        opLogCreateRequestV2.setPlatformCode(PlatformEnum.SPA.getCode());
        tradeConfigService.configCommonSwitchAndLog(request, opLogCreateRequestV2);

        supportService.removeCachedParams(merchantSn);

        long amount = walletServiceV3.getActualWithdrawableAmount(new WalletQueryReq(merchantId,
                TransactionParam.CLEARANCE_PROVIDER_FUYOU, ProviderWalletAccountTypeEnum.DEFAULT.getValue()));
        log.info("富友间清转直清处理，余额调减. merchantSn={},amount={}", merchantSn, amount);
        if (amount > 0) {
            String actionId = LocalDateTimeUtil.getFormatDateTime(LocalDateTimeUtil.YYYYMMDDHHMMSSSSS);
            DeductBalanceReqV3 deductReq = new DeductBalanceReqV3(
                    merchantId, amount, EventLog.TYPE_ADJUST_OUT, remark,
                    actionId, actionId, appid, "",
                    TransactionParam.CLEARANCE_PROVIDER_FUYOU, ProviderWalletAccountTypeEnum.DEFAULT.getValue()
            );
            String sign = SignUtil.getSign(deductReq.toMap(), key, "utf8");
            deductReq.setSign(sign);
            walletServiceV3.deductBalance(deductReq);
        }
        log.info("富友间清转直清处理完毕. merchantSn={}", merchantSn);
    }
}

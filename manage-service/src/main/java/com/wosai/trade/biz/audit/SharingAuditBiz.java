package com.wosai.trade.biz.audit;

import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;
import com.shouqianba.workflow.bean.dto.req.AuditHandleRobotReqDto;
import com.shouqianba.workflow.service.CallBackService;
import com.wosai.databus.event.audit.AuditInstanceEvent;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.profit.sharing.constant.CommonConstant;
import com.wosai.profit.sharing.constant.ModelConstant;
import com.wosai.profit.sharing.model.MerchantSharingOpenApply;
import com.wosai.profit.sharing.model.ReceiverApplyStatus;
import com.wosai.profit.sharing.model.request.IsSharingOpenedReq;
import com.wosai.profit.sharing.model.request.QuerySharingApplyReq;
import com.wosai.profit.sharing.model.response.ReceiverApplyDto;
import com.wosai.profit.sharing.model.response.SharingApplyDto;
import com.wosai.profit.sharing.service.SharingOpenService;
import com.wosai.trade.biz.audit.model.HaikeProfitSharingAuditEventBusinessMapDto;
import com.wosai.trade.config.apollo.SharingAuditConfig;
import com.wosai.trade.constant.enums.ApplyAuditProcStatusEnum;
import com.wosai.trade.constant.enums.ApplyAuditSubProcStatusEnum;
import com.wosai.trade.impl.TradeCommonService;
import com.wosai.trade.repository.ApplyAuditRecordRepository;
import com.wosai.trade.repository.dao.SharingAuditTaskDao;
import com.wosai.trade.repository.dao.entity.ApplyAuditRecordEntity;
import com.wosai.trade.repository.dao.entity.SharingAuditTaskEntity;
import com.wosai.trade.service.AuditEventService;
import com.wosai.trade.service.constant.SharingAuditConstant;
import com.wosai.trade.util.OssUtil;
import com.wosai.upay.common.util.JacksonUtil;
import com.wosai.upay.user.api.util.DateTimeUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class SharingAuditBiz {
    @Autowired
    private SharingOpenService sharingOpenService;
    @Resource
    CallBackService callBackService;
    @Autowired
    SharingAuditConfig sharingAuditConfig;
    @Autowired
    SharingAuditTaskDao sharingAuditTaskDao;
    @Autowired
    ApplyAuditRecordRepository applyAuditRecordRepository;
    @Autowired
    TradeCommonService tradeCommonService;
    @Autowired
    @Lazy
    AuditEventService auditEventService;

    public static final int SUCCESS = 1;
    public static final int FAIL = 2;

    String NOT_IN_TIME_OPEN_SHARING_MSG_FORMAT = "分账签约已超时，以下商户未在7个自然日里完成签约，点击查看下载文件：%s";



    /**
     * 保存商户开通分账审批事件
     * @param event
     */
    public void safeHandlerSharingAuditCreate(AuditInstanceEvent event){
        try {
            List<ApplyAuditRecordEntity> applyAuditRecordEntities = buildApplyAuditRecordForHaikeSharingAutoOpen(event);
            for (ApplyAuditRecordEntity applyAuditRecordEntity : applyAuditRecordEntities) {
                applyAuditRecordRepository.save(applyAuditRecordEntity);
            }
        }catch (Exception e){
            log.error("handlerSharingAuditCreate error {}", event, e);
        }
    }

    /**
     * 审批结束更新记录
     * @param event
     */
    public void safeHandlerSharingAuditFinish(AuditInstanceEvent event) {
        try {
            String eventType = event.getEventType();
            ApplyAuditProcStatusEnum applyAuditProcStatusEnum = null;
            if (Objects.equals(AuditInstanceEvent.EVENT_TYPE_AUDIT_CANCEL, eventType) || Objects.equals(AuditInstanceEvent.EVENT_TYPE_AUDIT_REJECT, eventType)) {
                applyAuditProcStatusEnum = ApplyAuditProcStatusEnum.FAILURE;
            } else if (Objects.equals(AuditInstanceEvent.EVENT_TYPE_AUDIT_APPROVE, eventType)) {
                applyAuditProcStatusEnum = ApplyAuditProcStatusEnum.SUCCESS;
            }
            String auditSn = event.getAuditSn();
            if (applyAuditProcStatusEnum != null) {
                applyAuditRecordRepository.updateProcStatusByAuditSn(auditSn, applyAuditProcStatusEnum);
            }
        }catch (Exception e){
            log.error("handlerSharingAuditFinish error {}", event, e);
        }
    }

    private List<ApplyAuditRecordEntity> buildApplyAuditRecordForHaikeSharingAutoOpen(AuditInstanceEvent event) {
        List<ApplyAuditRecordEntity> auditRecords = new ArrayList<>();
        Map businessMap = event.getBusinessMap();
        try {
            HaikeProfitSharingAuditEventBusinessMapDto haikeProfitSharingAuditEventBusinessMapDto = JsonUtil.jsonStringToObject(JsonUtil.objectToJsonString(businessMap), HaikeProfitSharingAuditEventBusinessMapDto.class);
            Map commonBizMap = buildCommonBizMap(haikeProfitSharingAuditEventBusinessMapDto);
            haikeProfitSharingAuditEventBusinessMapDto.removeEmptyRelations();
            //只处理开通分账的审批
            if (haikeProfitSharingAuditEventBusinessMapDto.getAuditType() == 1) {
                List<HaikeProfitSharingAuditEventBusinessMapDto.SharingRelationInfo> sharingRelationInfos = haikeProfitSharingAuditEventBusinessMapDto.getSharingRelationInfos();
                for (HaikeProfitSharingAuditEventBusinessMapDto.SharingRelationInfo sharingRelationInfo : sharingRelationInfos) {
                    String payerMerchantSn = sharingRelationInfo.getPayerMerchantInfo().getMerchInfoSn().getValue();
                    String payerMerchantId = tradeCommonService.getMerchantIdByMerchantSn(payerMerchantSn);
                    Map copyBizMap = new TreeMap(commonBizMap);
                    copyBizMap.put("receiver_merchant_sn", sharingRelationInfo.getReceiverMerchantInfo().getMerchInfoSn().getValue());
                    copyBizMap.put("fix_ratio",sharingRelationInfo.getFixRatio());
                    copyBizMap.put("fix_amount",sharingRelationInfo.getFixAmount());
                    ApplyAuditRecordEntity applyAuditRecordEntity = new ApplyAuditRecordEntity();
                    applyAuditRecordEntity.setId(event.getAuditId() + "-" + payerMerchantSn);
                    applyAuditRecordEntity.setMerchantSn(payerMerchantSn);
                    applyAuditRecordEntity.setMerchantId(payerMerchantId);
                    applyAuditRecordEntity.setAuditSn(event.getAuditSn());
                    applyAuditRecordEntity.setAuditTemplateId(event.getTemplateId());
                    applyAuditRecordEntity.setTemplateEvent(event.getTemplateEvent());
                    applyAuditRecordEntity.setOperatorName(event.getOperatorName());
                    applyAuditRecordEntity.setProcStatus(ApplyAuditProcStatusEnum.IN_PROGRESS.getStatus());
                    applyAuditRecordEntity.setSubProcStatus(ApplyAuditSubProcStatusEnum.UNKNOWN.getStatus());
                    applyAuditRecordEntity.setExtra(JsonUtil.toJsonStr(copyBizMap));
                    auditRecords.add(applyAuditRecordEntity);
                }
            }
        } catch (Exception e) {
            log.error("buildApplyAuditRecordForHaikeSharingAutoOpen error {}", businessMap, e);
        }
        return auditRecords;
    }


    private Map<String, Object> buildCommonBizMap(HaikeProfitSharingAuditEventBusinessMapDto haikeProfitSharingAuditEventBusinessMapDto) {
        Map bizMap = new TreeMap();
        //分账模型的属性
        bizMap.put("audit_type", haikeProfitSharingAuditEventBusinessMapDto.getAuditType());
        bizMap.put("basis_of_sharing", haikeProfitSharingAuditEventBusinessMapDto.getBasisOfSharing());
        bizMap.put("type", haikeProfitSharingAuditEventBusinessMapDto.getType());
        bizMap.put("sharing_type", haikeProfitSharingAuditEventBusinessMapDto.getSharingType());
        bizMap.put("sharing_cardinal", haikeProfitSharingAuditEventBusinessMapDto.getSharingCardinal());
        bizMap.put("sharing_ladder_enable", haikeProfitSharingAuditEventBusinessMapDto.getSharingLadderEnable());
        bizMap.put("sharing_period", haikeProfitSharingAuditEventBusinessMapDto.getSharingPeriod());
        bizMap.put("capped_amount", haikeProfitSharingAuditEventBusinessMapDto.getCappedAmount());
        bizMap.put("max_ratio", haikeProfitSharingAuditEventBusinessMapDto.getMaxRatio());
        bizMap.put("name", haikeProfitSharingAuditEventBusinessMapDto.getName());
        bizMap.put("restitute", haikeProfitSharingAuditEventBusinessMapDto.getRestitute());
        bizMap.put("entry_time", haikeProfitSharingAuditEventBusinessMapDto.getEntryTime());
        bizMap.put("underfunding_strategy", haikeProfitSharingAuditEventBusinessMapDto.getUnderfundingStrategy());
        return bizMap;
    }


    /**
     * 分账开通审批
     * @param sharingAuditTaskEntity
     */
    public void handlerSharingOpenAudit(SharingAuditTaskEntity sharingAuditTaskEntity) {
        SharingAuditTaskEntity.BizParams bizParams = sharingAuditTaskEntity.buildBizParams();
        Map<String, String> submitHaikeOpenBizTaskMap = bizParams.getSubmitHaikeOpenBizTaskMap();
        List<String> submitIds = submitHaikeOpenBizTaskMap.values().stream().collect(Collectors.toList());
        BiMap<String, String> merchantOpenIdMapping = HashBiMap.create(submitHaikeOpenBizTaskMap);
        QuerySharingApplyReq queryHaikeSharingApplyReq = new QuerySharingApplyReq();
        queryHaikeSharingApplyReq.setApplyIds(submitIds);
        List<SharingApplyDto> sharingApplyDtos = sharingOpenService.queryOpenSharing(queryHaikeSharingApplyReq);
        Map<String, Integer> idStatusMap = sharingApplyDtos.stream().collect(Collectors.toMap(o -> o.getId(), v -> v.getStatus()));
        Map<String, String> auditMsg = new HashMap<>();
        boolean unfinishedFlag = false;
        for (String submitId : submitIds) {
            Integer status = idStatusMap.get(submitId);
            if (status == null || status == MerchantSharingOpenApply.STATUS_INIT || status == MerchantSharingOpenApply.STATUS_WAIT_PROVIDER_AUDIT) {
                unfinishedFlag = true;
            } else {
                if (status == MerchantSharingOpenApply.STATUS_PROVIDER_AUDIT_REJECT) {
                    String merchantSn = merchantOpenIdMapping.inverse().get(submitId);
                    auditMsg.put(merchantSn, "通道审核拒绝");
                }
            }
        }
        if (!unfinishedFlag) {
            Long auditId = sharingAuditTaskEntity.getAuditId();
            AuditHandleRobotReqDto auditHandleRobotReqDto = AuditHandleRobotReqDto.builder()
                    .accessKey(sharingAuditConfig.getHaikeAuditAccessKey())
                    .message(buildRobotMessage(auditMsg, sharingAuditConfig.getHaikeSharingOpenQueryUrl() + auditId))
                    .auditInstanceId(auditId)
                    .preApprovalLevel(sharingAuditConfig.getHaikeOpenRobotLevel())
                    .resultType(auditMsg.isEmpty() ? SUCCESS : FAIL)
                    .build();
            callBackService.auditHandleForRobot(auditHandleRobotReqDto);
            log.info("开通分账审批流程完成 审批结果:{}", JacksonUtil.toJsonString(auditHandleRobotReqDto));
            bizParams.setHaikeOpenBizResponse(auditMsg);
            finish(sharingAuditTaskEntity, bizParams);
        }
    }


    /**
     * 分账建立关系审批
     * @param sharingAuditTaskEntity
     */
    public void handlerSharingRelationAudit(SharingAuditTaskEntity sharingAuditTaskEntity){
        SharingAuditTaskEntity.BizParams bizParams = sharingAuditTaskEntity.buildBizParams();
        Map<String, String> submitHaikeRelationBizTaskMap = bizParams.getSubmitHaikeRelationBizTaskMap();
        QuerySharingApplyReq queryHaikeSharingApplyReq = new QuerySharingApplyReq();
        queryHaikeSharingApplyReq.setApplyIds(submitHaikeRelationBizTaskMap.values().stream().collect(Collectors.toList()));
        List<ReceiverApplyDto> receiverApplyDtos = sharingOpenService.queryReceiverApply(queryHaikeSharingApplyReq);
        Map<String, ReceiverApplyDto> sharingRelationMap = receiverApplyDtos.stream().collect(Collectors.toMap(o -> o.getId(), v -> v));
        boolean unfinishedFlag = false;
        Map<String, String> auditMsg = new HashMap<>();

        for (Map.Entry<String, String> entry : submitHaikeRelationBizTaskMap.entrySet()) {
            String submitId = entry.getValue();
            ReceiverApplyDto receiverApplyDto = sharingRelationMap.get(submitId);
            if (receiverApplyDto == null ||
                    receiverApplyDto.getStatus() == ReceiverApplyStatus.WAIT_SUBMIT.getStatus() ||
                    receiverApplyDto.getStatus() == ReceiverApplyStatus.VERIFY_INIT.getStatus()) {
                unfinishedFlag = true;
            } else {
                if (receiverApplyDto.getStatus() == ReceiverApplyStatus.VERIFY_FAIL.getStatus()) {
                    String[] splitKey = entry.getKey().split("-");
                    String payerMerchantSn = splitKey[0];
                    String receiverMerchantSn = splitKey[1];
                    auditMsg.put(payerMerchantSn, String.format("申请分账接收方%s关系失败，原因：%s", receiverMerchantSn, receiverApplyDto.getErrorMsg()));
                }
            }
        }
        if (!unfinishedFlag) {
            Long auditId = sharingAuditTaskEntity.getAuditId();
            AuditHandleRobotReqDto auditHandleRobotReqDto = AuditHandleRobotReqDto.builder()
                    .accessKey(sharingAuditConfig.getHaikeAuditAccessKey())
                    .message(buildRobotMessage(auditMsg))
                    .auditInstanceId(auditId)
                    .preApprovalLevel(sharingAuditConfig.getHaikeRelationRobotLevel())
                    .resultType(auditMsg.isEmpty() ? SUCCESS : FAIL)
                    .build();
            callBackService.auditHandleForRobot(auditHandleRobotReqDto);
            log.info("分账关系审批流程完成 审批结果:", JacksonUtil.toJsonString(auditHandleRobotReqDto));
            bizParams.setSubmitHaikeRelationBizResponse(auditMsg);
            finish(sharingAuditTaskEntity, bizParams);
        }
    }

    /**
     * 检查分账商户是否开通成功
     * @param sharingAuditTaskEntity
     */
    public void handlerCheckMerchantSharingOpenAndSubmitSharingRelation(SharingAuditTaskEntity sharingAuditTaskEntity){
        SharingAuditTaskEntity.BizParams bizParams = sharingAuditTaskEntity.buildBizParams();
        List<String> notOpenSharingMerchantSns = bizParams.getNotOpenSharingMerchantSns();
        List<String> currentNotOpenSharingMerchantSns = new ArrayList<>();
        Integer organization = bizParams.getOrganization();
        if(organization == null){
            //兼容历史第一版只有海科的情况 没有存organization
            organization = ModelConstant.ORGANIZATION_HAIKE;
        }
        Long auditId = sharingAuditTaskEntity.getAuditId();
        for (String notOpenSharingMerchantSn : notOpenSharingMerchantSns) {
            String merchantId = tradeCommonService.getMerchantIdByMerchantSn(notOpenSharingMerchantSn);
            //是否开通过分账（收钱吧系统内的校验
            IsSharingOpenedReq isSharingOpenedReq = new IsSharingOpenedReq();
            isSharingOpenedReq.setMerchantId(merchantId);
            isSharingOpenedReq.setOrganization(organization);
            if (!sharingOpenService.isSharingOpened(isSharingOpenedReq)) {
                currentNotOpenSharingMerchantSns.add(notOpenSharingMerchantSn);
                continue;
            }
            if (Objects.equals(ModelConstant.ORGANIZATION_HAIKE, organization)) {
                //是否在通道侧开通过分账
                SharingApplyDto merchantSharingOpenApply = sharingOpenService.getMerchantSharingOpenApply(MapUtil.hashMap(
                        CommonConstant.MERCHANT_SN, notOpenSharingMerchantSn,
                        SharingOpenService.CLEARANCE_PROVIDER, ModelConstant.CLEARANCE_PROVIDER_HAIKE
                ));
                if (merchantSharingOpenApply == null || merchantSharingOpenApply.getStatus() != MerchantSharingOpenApply.STATUS_SUCCESS) {
                    currentNotOpenSharingMerchantSns.add(notOpenSharingMerchantSn);
                    continue;
                }
            }
        }

        if (CollectionUtils.isEmpty(currentNotOpenSharingMerchantSns)) {
            if (Objects.equals(ModelConstant.ORGANIZATION_LAKALA, organization)) {
                AuditHandleRobotReqDto auditHandleRobotReqDto = AuditHandleRobotReqDto.builder()
                        .accessKey(sharingAuditConfig.getLakalaAuditAccessKey())
                        .message("审批通过")
                        .auditInstanceId(auditId)
                        .preApprovalLevel(sharingAuditConfig.getLaklaCheckOpenSharingRobotLevel())
                        .resultType(SUCCESS)
                        .build();
                callBackService.auditHandleForRobot(auditHandleRobotReqDto);
            } else {
                Map<String, Object> originalSubmitHaikeRelationRequest = bizParams.getOriginalSubmitHaikeRelationRequest();
                originalSubmitHaikeRelationRequest.remove(SharingAuditConstant.KEY_NOT_OPEN_SHARING_MERCHANT_SNS);
                auditEventService.submitSharingRelation(originalSubmitHaikeRelationRequest);
            }
            finish(sharingAuditTaskEntity, bizParams);
        }

        if (CollectionUtils.isNotEmpty(currentNotOpenSharingMerchantSns) && System.currentTimeMillis() - sharingAuditTaskEntity.getCtime() > sharingAuditConfig.getSharingOpenExpireTime(30 * DateTimeUtil.ONE_DAY_MILLIS)) {
            //超过7天还没签约系统自动驳回
            bizParams.setNotInTimeOpenSharingFlag(true);
            AuditHandleRobotReqDto auditHandleRobotReqDto = AuditHandleRobotReqDto.builder()
                    .accessKey(Objects.equals(ModelConstant.ORGANIZATION_LAKALA, organization) ? sharingAuditConfig.getLakalaAuditAccessKey() : sharingAuditConfig.getHaikeAuditAccessKey())
                    .message(buildUnOpenSharingMerchantMsg(auditId,notOpenSharingMerchantSns))
                    .auditInstanceId(auditId)
                    .preApprovalLevel(Objects.equals(ModelConstant.ORGANIZATION_LAKALA, organization) ? sharingAuditConfig.getLaklaCheckOpenSharingRobotLevel() : sharingAuditConfig.getHaikeRelationRobotLevel())
                    .resultType(FAIL)
                    .build();
            callBackService.auditHandleForRobot(auditHandleRobotReqDto);
            finish(sharingAuditTaskEntity, bizParams);
        }
    }



    @SneakyThrows
    private String buildUnOpenSharingMerchantMsg(Long auditId, List<String> notOpenSharingMerchantSns) {
        String filePath = "/tmp/sharing/not_in_time_open_" + auditId + ".csv";
        File file = new File(filePath);
        try {
            List<String> notOpenSharingMerchantMsg = new LinkedList<>();
            notOpenSharingMerchantMsg.add("未及时完成签约商户号");
            for (String merchantSn : notOpenSharingMerchantSns) {
                notOpenSharingMerchantMsg.add("`" + merchantSn);
            }
            FileUtils.writeLines(file, notOpenSharingMerchantMsg);
            String fileToOSSUrl = OssUtil.uploadFileToOSS(filePath);
            return String.format(NOT_IN_TIME_OPEN_SHARING_MSG_FORMAT, fileToOSSUrl);
        } finally {
            file.deleteOnExit();
        }
    }

    private void finish(SharingAuditTaskEntity sharingAuditTaskEntity,SharingAuditTaskEntity.BizParams bizParams){
        SharingAuditTaskEntity update = new SharingAuditTaskEntity();
        update.setId(sharingAuditTaskEntity.getId());
        update.setStatus(SharingAuditConstant.AUDIT_STATUS_FINISH);
        update.setBizParams(bizParams.toMap());
        update.setVersion(update.getVersion());
        sharingAuditTaskDao.updateStatusAndBizParams(update);
    }

    private String buildRobotMessage(Map<String, String> auditMsg) {
        StringBuilder stringBuilder = new StringBuilder();
        if (MapUtil.isNotEmpty(auditMsg)) {
            for (Map.Entry<String, String> entry : auditMsg.entrySet()) {
                stringBuilder.append("商户号：").append(entry.getKey()).append("审核失败原因：").append(entry.getValue()).append("\n");
            }
        } else {
            stringBuilder.append("审批完成");
        }
        return stringBuilder.toString();
    }


    private String buildRobotMessage(Map<String, String> auditMsg, String jumpUrl) {
        StringBuilder stringBuilder = new StringBuilder();
        if (MapUtil.isNotEmpty(auditMsg)) {
            for (Map.Entry<String, String> entry : auditMsg.entrySet()) {
                stringBuilder.append("商户号：").append(entry.getKey()).append("审核失败原因：").append(entry.getValue()).append("\n");
            }
        } else {
            stringBuilder.append("点击链接查看审批进度：" + jumpUrl);
        }
        return stringBuilder.toString();
    }
}

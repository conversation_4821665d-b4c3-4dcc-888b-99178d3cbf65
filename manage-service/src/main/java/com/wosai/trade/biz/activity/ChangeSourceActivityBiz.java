package com.wosai.trade.biz.activity;

import com.wosai.databus.event.pay.TradeManageChangeApplyActivityEvent;
import com.wosai.trade.biz.activity.converter.ApplyActivityBuildParams;
import com.wosai.trade.biz.fee.AlarmFeeRateBiz;
import com.wosai.trade.constant.ExpireTimeConstant;
import com.wosai.trade.constant.RedisKeyConstant;
import com.wosai.trade.model.constant.ManageConstant;
import com.wosai.trade.repository.ActivityApplyRepository;
import com.wosai.trade.repository.dao.ActivitySubStatusDetailMapper;
import com.wosai.trade.repository.dao.entity.ActivityApplyEntity;
import com.wosai.trade.repository.dao.entity.ActivitySubStatusDetailEntity;
import com.wosai.trade.service.ApplyActivityService;
import com.wosai.trade.service.FeeRateService;
import com.wosai.trade.service.activity.constant.ActivityConstants;
import com.wosai.trade.service.activity.enums.ActivityTypeEnum;
import com.wosai.trade.service.activity.request.CancelActivityApplyRequest;
import com.wosai.trade.service.activity.response.ApplyProcessInfo;
import com.wosai.trade.service.enums.ChangeActivityFeeRateEnum;
import com.wosai.trade.service.request.ApplyFeeRateRequest;
import com.wosai.trade.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 支付源活动变动联动业务类
 *
 * 主要功能：
 * 1. 处理支付源活动变更事件,同步更新挂靠子商户的活动信息
 * 2. 支持费率变更和活动取消两种操作
 * 3. 提供完整的异常处理和预警机制
 *
 * <AUTHOR>
 * @date 2025/8/22
 */
@Slf4j
@Component
public class ChangeSourceActivityBiz {

    // ==================== 依赖注入 ====================

    @Resource
    private ActivityApplyRepository activityApplyRepository;

    @Resource
    private ApplyActivityService applyActivityService;

    @Resource
    private AlarmFeeRateBiz alarmFeeRateBiz;

    @Resource
    private FeeRateService feeRateService;

    @Resource
    private ActivitySubStatusDetailMapper activitySubStatusDetailMapper;

    @Autowired
    private ApplyActivityBuildParams applyActivityBuildParams;

    // ==================== 常量定义 ====================

    /** 费率变更操作类型 */
    private static final String OPERATION_TYPE_CHANGE = "change";

    /** 取消操作类型 */
    private static final String OPERATION_TYPE_CANCEL = "cancel";

    // ==================== 主要业务方法 ====================

    /**
     * 处理支付源活动变更事件
     *
     * 主要流程：
     * 1. 验证活动类型是否为支付方式活动
     * 2. 获取主商户报名记录
     * 3. 查询所有绑定的子商户报名申请记录
     * 4. 根据事件类型执行相应的费率变更或取消操作
     *
     * @param event 支付源活动变更事件
     */
    public void process(TradeManageChangeApplyActivityEvent event) {
        String lockName = RedisKeyConstant.LOCK_APPLY_ACTIVITY_PREFIX + event.getMerchantSn();
        RedisLockUtil.tryLock(lockName, 0, ExpireTimeConstant.FIVE_MINUTES_IN_SECONDS, () -> {
            process0(event);
            return null;
        });
    }

    private void process0(TradeManageChangeApplyActivityEvent event) {
        log.info("支付源活动变更事件,applyId={},活动类型={}", event.getApplyId(), event.getActivityType());

        // 验证活动类型是否为支付方式活动
        if (!ActivityTypeEnum.payWayActivities.contains(event.getActivityType())) {
            log.info("活动类型[{}]不是支付方式活动,跳过处理", event.getActivityType());
            return;
        }
        log.info("开始处理支付源活动变更事件,event={}", JsonUtil.encode(event));
        // 获取主商户报名记录
        ActivityApplyEntity masterApply = getMasterApplyRecord(event.getApplyId());

        // 查询绑定的子商户报名申请记录
        List<ActivityApplyEntity> childApplies = getChildApplyRecords(event.getApplyId());
        if (CollectionUtils.isEmpty(childApplies)) {
            log.info("支付源活动变更事件处理完成,当前无绑定子商户报名申请记录,事件={}", JsonUtil.encode(event));
            return;
        }

        // 处理每个子商户的报名记录
        processChildApplies(event, childApplies, masterApply);

        log.info("支付源活动变更事件处理完成,共处理子商户数量={}", childApplies.size());
    }

    // ==================== 私有业务方法 ====================

    /**
     * 获取主商户报名记录
     *
     * @param applyId 报名申请ID
     * @return 主商户报名记录
     * @throws IllegalArgumentException 当主商户报名记录不存在时
     */
    private ActivityApplyEntity getMasterApplyRecord(Long applyId) {
        ActivityApplyEntity masterApply = activityApplyRepository.selectByPrimaryKey(applyId);
        ValidationUtils.notNull(masterApply, "主商户报名记录不存在,applyId：" + applyId);
        return masterApply;
    }

    /**
     * 获取绑定的子商户报名申请记录
     *
     * @param parentId 父级报名申请ID
     * @return 子商户报名申请记录列表
     */
    private List<ActivityApplyEntity> getChildApplyRecords(Long parentId) {
        return activityApplyRepository.selectByParentId(parentId)
                .stream()
                .filter(apply -> ActivityConstants.PROCESSING_STATUS.contains(apply.getStatus()))
                .collect(Collectors.toList());
    }

    /**
     * 处理子商户报名记录
     *
     * @param event 支付源活动变更事件
     * @param childApplies 子商户报名记录列表
     * @param masterApply 主商户报名记录
     */
    private void processChildApplies(TradeManageChangeApplyActivityEvent event,
                                     List<ActivityApplyEntity> childApplies,
                                     ActivityApplyEntity masterApply) {
        for (ActivityApplyEntity childApply : childApplies) {
            try {
                if (event.getIsApply()) {
                    // 子状态切换
                    changeSubStatus(event, childApply, masterApply);
                } else {
                    // 执行活动取消操作
                    cancel(event, childApply);
                }
            } catch (Exception e) {
                log.error("处理子商户报名记录失败,子商户={},申请ID={},状态={}",
                        childApply.getMerchant_sn(), childApply.getId(), childApply.getStatus(), e);
            }
        }
    }

    /**
     * 子状态切换
     *
     * 主要流程：
     * 1. 创建追踪ID用于日志追踪
     * 2. 根据子商户状态执行不同的费率变更逻辑
     * 3. 更新子商户报名记录信息
     * 4. 应用新的费率配置
     * 5. 异常处理和预警发送
     *
     * @param event 支付源活动变更事件
     * @param childApply 子商户报名记录
     * @param masterApply 主商户报名记录
     */
    private void changeSubStatus(TradeManageChangeApplyActivityEvent event,
                                 ActivityApplyEntity childApply,
                                 ActivityApplyEntity masterApply) {
        String traceId = OPERATION_TYPE_CHANGE + "-" + childApply.getMerchant_sn() + "-" + childApply.getId();
        try {
            TraceUtil.createTraceId(traceId);
            if (!isChangeSubStatus(childApply, masterApply)) {
                return;
            }
            log.info("开始变更挂靠子商户支付源活动费率,子商户={},申请ID={},状态={}",
                    childApply.getMerchant_sn(), childApply.getId(), childApply.getStatus());

            // 构建流程信息
            String processMessage = String.format("%s,子状态变更联动,挂靠主商户sn：%s", event.getRemark(), masterApply.getMerchant_sn());
            List<ApplyProcessInfo> processList = applyActivityBuildParams.buildProcess(
                    childApply, childApply.getStatus(), processMessage);

            // 更新子商户报名记录
            updateChildApplyRecord(childApply, masterApply, processList);

            // 如果子商户状态为生效状态,则应用费率
            if (ActivityConstants.EFFECT == childApply.getStatus()) {
                applyFeeRateToChild(event, childApply, masterApply);
            }

            log.info("挂靠子商户支付源活动费率变更成功,子商户={},申请ID={}",
                    childApply.getMerchant_sn(), childApply.getId());

        } catch (Exception e) {
            log.error("挂靠子商户支付源活动费率变更失败,子商户={},申请ID={},状态={}",
                    childApply.getMerchant_sn(), childApply.getId(), childApply.getStatus(), e);
            // 发送费率变更失败预警
            alarmFeeRateBiz.changeActivitySendFailed(true, childApply, e);
        } finally {
            TraceUtil.removeTraceId();
        }
    }

    /**
     * 取消子商户活动
     *
     * 主要流程：
     * 1. 创建追踪ID用于日志追踪
     * 2. 调用活动取消服务
     * 3. 异常处理和预警发送
     *
     * @param event 支付源活动变更事件
     * @param childApply 子商户报名记录
     */
    private void cancel(TradeManageChangeApplyActivityEvent event, ActivityApplyEntity childApply) {
        String traceId = OPERATION_TYPE_CANCEL + "-" + childApply.getMerchant_sn() + "-" + childApply.getId();

        try {
            TraceUtil.createTraceId(traceId);
            log.info("开始取消挂靠子商户支付源活动,子商户={},申请ID={},状态={}",
                    childApply.getMerchant_sn(), childApply.getId(), childApply.getStatus());

            // 构建取消请求并执行取消操作
            CancelActivityApplyRequest cancelRequest = buildCancelRequest(childApply.getId(), event);
            applyActivityService.cancel(cancelRequest);

            log.info("挂靠子商户支付源活动取消成功,子商户={},申请ID={}",
                    childApply.getMerchant_sn(), childApply.getId());

        } catch (Exception e) {
            log.error("取消挂靠子商户支付源活动失败,子商户={},申请ID={},状态={}",
                    childApply.getMerchant_sn(), childApply.getId(), childApply.getStatus(), e);
            // 发送活动取消失败预警
            alarmFeeRateBiz.cancelSourceActivityFailed(childApply, e);
        } finally {
            TraceUtil.removeTraceId();
        }
    }

    // ==================== 辅助方法 ====================

    /**
     * 是否联动变更
     *
     * @param childApply
     * @param masterApply
     * @return
     */
    private boolean isChangeSubStatus(ActivityApplyEntity childApply, ActivityApplyEntity masterApply) {
        if (Objects.equals(childApply.getActivity_sub_status_id(), masterApply.getActivity_sub_status_id())) {
            log.info("子状态无变动,无需变更挂靠子商户支付源活动费率,子商户={},申请ID={},状态={}",
                    childApply.getMerchant_sn(), childApply.getId(), childApply.getStatus());
            return false;
        }
        // 存在子状态标签才进行联动。目前只有微信支付源活动才有
        ActivitySubStatusDetailEntity masterSubStatusDo = activitySubStatusDetailMapper.selectByPrimaryKey(masterApply.getActivity_sub_status_id());
        ActivitySubStatusDetailEntity childSubStatusDo = activitySubStatusDetailMapper.selectByPrimaryKey(childApply.getActivity_sub_status_id());
        ValidationUtils.notNull(masterSubStatusDo, "主商户活动子状态不存在. id=" + masterApply.getActivity_sub_status_id());
        ValidationUtils.notNull(childSubStatusDo, "二级商户活动子状态不存在. id=" + childApply.getActivity_sub_status_id());
        if (!Objects.equals(masterSubStatusDo.getTag(), childSubStatusDo.getTag())) {
            log.info("子状态发生变动,活动ID={},子商户=[{},{},{}],主商户=[{},{},{}]",
                    masterApply.getActivity_id(),
                    childApply.getMerchant_sn(), childApply.getId(), childSubStatusDo.getTag(),
                    masterApply.getMerchant_sn(), masterApply.getId(), masterSubStatusDo.getTag());
            return true;
        }
        log.info("子状态无变动,活动ID={},子商户=[{},{},{}],主商户=[{},{},{}]",
                masterApply.getActivity_id(),
                childApply.getMerchant_sn(), childApply.getId(), childSubStatusDo.getTag(),
                masterApply.getMerchant_sn(), masterApply.getId(), masterSubStatusDo.getTag());
        return false;
    }

    /**
     * 更新子商户报名记录
     *
     * @param childApply 子商户报名记录
     * @param masterApply 主商户报名记录
     * @param processList 流程信息列表
     */
    private void updateChildApplyRecord(ActivityApplyEntity childApply,
                                        ActivityApplyEntity masterApply,
                                        List<ApplyProcessInfo> processList) {
        ActivityApplyEntity updateEntity = new ActivityApplyEntity();
        updateEntity.setId(childApply.getId());
        updateEntity.setMerchant_sn(childApply.getMerchant_sn());
        updateEntity.setProcess(JsonUtil.encode(processList));
        updateEntity.setActivity_sub_status_id(masterApply.getActivity_sub_status_id());
        updateEntity.setCombo_id(masterApply.getCombo_id());
        updateEntity.setFee_rate(masterApply.getFee_rate());

        activityApplyRepository.updateByPrimaryKeySelective(updateEntity);
    }

    /**
     * 向子商户应用费率
     *
     * @param event 支付源活动变更事件
     * @param childApply 子商户报名记录
     * @param masterApply 主商户报名记录
     */
    private void applyFeeRateToChild(TradeManageChangeApplyActivityEvent event,
                                     ActivityApplyEntity childApply,
                                     ActivityApplyEntity masterApply) {
        ApplyFeeRateRequest feeRateRequest = buildApplyFeeRateRequest(event, childApply, masterApply);
        feeRateService.applyFeeRateOne(feeRateRequest);
    }

    /**
     * 构建取消活动申请请求
     *
     * @param applyId 报名申请ID
     * @return 取消活动申请请求对象
     */
    public CancelActivityApplyRequest buildCancelRequest(Long applyId, TradeManageChangeApplyActivityEvent event) {
        CancelActivityApplyRequest request = new CancelActivityApplyRequest();

        // 设置取消描述信息
        CancelActivityApplyRequest.CancelDescribe desc = new CancelActivityApplyRequest.CancelDescribe();
        desc.setOperator(ConstantUtil.SYSTEM_NAME);
        desc.setOperatorName(ConstantUtil.SYSTEM_NAME);
        desc.setReason(String.format("取消挂靠子商户支付源活动,主体商户sn：%s", event.getMerchantSn()));
        request.setCancelDescribe(desc);

        // 设置取消实体信息
        CancelActivityApplyRequest.CancelByActivityApply byApplyId = new CancelActivityApplyRequest.CancelByActivityApply();
        byApplyId.setActivityApplyId(applyId);

        CancelActivityApplyRequest.CancelEntity cancelEntity = new CancelActivityApplyRequest.CancelEntity();
        cancelEntity.setCancelByActivityApply(byApplyId);
        request.setCancelEntity(cancelEntity);

        return request;
    }

    /**
     * 构建应用费率请求
     *
     * @param event 支付源活动变更事件
     * @param childApply 子商户报名记录
     * @param masterApply 主商户报名记录
     * @return 应用费率请求对象
     */
    private ApplyFeeRateRequest buildApplyFeeRateRequest(TradeManageChangeApplyActivityEvent event,
                                                         ActivityApplyEntity childApply,
                                                         ActivityApplyEntity masterApply) {
        ApplyFeeRateRequest request = new ApplyFeeRateRequest();

        // 设置基本信息
        request.setMerchantSn(childApply.getMerchant_sn());
        request.setSubStatusId(masterApply.getActivity_sub_status_id());
        request.setTradeComboId(masterApply.getCombo_id());
        request.setOperator(ManageConstant.USER_SYSTEM);
        request.setOperatorName(ManageConstant.USER_SYSTEM);

        // 设置费率变更相关参数
        request.setChangeActivityFeeRateEnum(ChangeActivityFeeRateEnum.APPLY_TAKE_EFFECT);
        request.setCheck(true);
        request.setChangeActivityFeeRateEnum(event.getChangeActivityFeeRateEnum());
        request.setApplyFeeRateMap(masterApply.buildFeeRate());

        // 构建审批编号
        String auditSn = event.getRemark();
        request.setAuditSn(auditSn);

        return request;
    }
}

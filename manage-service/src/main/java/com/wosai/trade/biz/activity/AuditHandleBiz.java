package com.wosai.trade.biz.activity;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.shouqianba.workflow.bean.CallBackBean;
import com.shouqianba.workflow.service.CallBackService;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.databus.event.audit.AuditInstanceApproveEvent;
import com.wosai.databus.event.audit.AuditInstanceCreateEvent;
import com.wosai.databus.event.audit.AuditInstanceEvent;
import com.wosai.service.PaywayActivityService;
import com.wosai.trade.biz.activity.converter.ApplyActivityBuildParams;
import com.wosai.trade.biz.activity.model.CacheActivityInfo;
import com.wosai.trade.biz.activity.model.RebateSuccessParam;
import com.wosai.trade.biz.activity.model.RebateSuccessResult;
import com.wosai.trade.constant.AuditConstant;
import com.wosai.trade.model.constant.ManageConstant;
import com.wosai.trade.model.enums.ActivitySubStatusTagEnum;
import com.wosai.trade.model.enums.PayWayEnum;
import com.wosai.trade.repository.dao.ActivityApplyDOMapper;
import com.wosai.trade.repository.dao.ActivitySubStatusDetailMapper;
import com.wosai.trade.repository.dao.entity.ActivityApplyEntity;
import com.wosai.trade.repository.dao.entity.ActivitySubStatusDetailEntity;
import com.wosai.trade.service.ApplyActivityService;
import com.wosai.trade.service.activity.constant.ActivityConstants;
import com.wosai.trade.service.activity.enums.ApplyActivityPlatformEnum;
import com.wosai.trade.service.activity.request.ApplyActivityRequest;
import com.wosai.trade.service.activity.request.BulkAddActivityRequest;
import com.wosai.trade.service.activity.request.CancelActivityApplyRequest;
import com.wosai.trade.service.activity.response.ApplyActivityResponse;
import com.wosai.trade.service.exception.AuditBizException;
import com.wosai.trade.service.exception.TradeManageBizException;
import com.wosai.trade.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.wosai.trade.util.AuditGetParamUtil.*;

/**
 * @Description: 费率活动审批处理
 * <AUTHOR>
 * @Date: 2022/6/7 2:51 下午
 */
@Component
@Slf4j
public class AuditHandleBiz {

    @Autowired
    private ApplyActivityService applyService;

    @Autowired
    private CallBackService callBackService;

    @Autowired
    private ActivityApplyDOMapper applyMapper;

    @Autowired
    private PaywayActivityService paywayActivityService;

    @Autowired
    private CacheActivity cacheActivity;

    @Autowired
    private ActivitySubStatusDetailMapper activitySubStatusDetailMapper;

    @Autowired
    private ApplyActivityBuildParams applyActivityBuildParams;

    @Autowired
    private RebateSuccessBiz rebateSuccessBiz;

    @Autowired
    private ModifyActivityPaySourceFeeRateBiz modifyActivityPaySourceFeeRateBiz;
    @Autowired
    private AdjustPaySourceActivityFeeRateBiz adjustPaySourceActivityFeeRateBiz;
    @Autowired
    private ModifyActivityComboFeeRateBiz modifyActivityComboFeeRateBiz;

    @Autowired
    private ModifyApplyActivityBiz modifyApplyActivityBiz;


    /**
     * 支付源审批关闭
     *
     * @param event
     */
    public void paySourceAuditClose(AuditInstanceEvent event) {
        Map businessMap = event.getBusinessMap();
        String action = WosaiMapUtils.getString(businessMap, "action");
        if (StringUtils.isBlank(action)) {
            action = "apply";
        }
        //apply
        if (action.equals("apply")) {
            applyService.paySourceAuditClose(Long.valueOf(event.getAuditId()), event);
        }

    }


    /**
     * 支付源审批创建
     *
     * @param event
     */
    public void paySourceAuditCreate(AuditInstanceCreateEvent event) {
        Map businessMap = event.getBusinessMap();
        String action = WosaiMapUtils.getString(businessMap, "action");
        if (StringUtils.isBlank(action)) {
            action = "apply";
        }
        //apply
        if (action.equals("apply")) {
            //参数组装
            ApplyActivityRequest request = buildPaySourceApplyActivityRequest(event);
            doSingleApply(request, event);
        }

    }

    /**
     * 支付源活动 审批通过
     * <p>
     * 包括申请 和  取消
     *
     * @param event
     */
    public void paySourceAuditApprove(AuditInstanceApproveEvent event) {

        Map businessMap = event.getBusinessMap();
        String action = WosaiMapUtils.getString(businessMap, "action");
        if (StringUtils.isBlank(action)) {
            action = "apply";
        }

        //申请通过 报名
        if (action.equals("apply")) {
            doPaySourceApply(event);
        }

        // 支付源活动取消cancel
        if (action.equals("cancel")) {
            doPaySourceCancel(event);
        }
    }

    /**
     * 处理支付源申请
     *
     * @param event
     */
    public void doPaySourceApply(AuditInstanceApproveEvent event) {
        ApplyActivityResponse response = applyService.paySourceAuditApproved(Long.valueOf(event.getAuditId()), event);
        int type;
        if (response.isSuccess()) {
            type = ManageConstant.AUDIT_EXECUTE_SUCCESS;
        } else {
            type = ManageConstant.AUDIT_EXECUTE_FAIL;
        }
        //写评论
        CallBackBean callBackBean = CallBackBean.builder()
                .auditId(Long.valueOf(event.getAuditId()))
                .templateId(Long.valueOf(event.getTemplateId()))
                .resultType(type)
                .message(response.getMsg())
                .build();
        callBackService.addComment(callBackBean);
    }


    /**
     * 处理支付源取消
     *
     * @param event
     */
    public void doPaySourceCancel(AuditInstanceApproveEvent event) {
        // 支付源活动 取消
        String msg = "活动取消成功";
        int type = ManageConstant.AUDIT_EXECUTE_SUCCESS;
        try {

            Map businessMap = event.getBusinessMap();
            Pair<Long, String> pair = applyActivityBuildParams.analyzeAuditPaySourceCancel(businessMap);
            Long activity_id = pair.getLeft();
            if (activity_id == null) {
                throw new AuditBizException("商户申请活动ID不能为空");
            }

            String merchantSn = pair.getRight();
            List<ActivityApplyEntity> canCancel = applyMapper.getApplyCanCancel(activity_id, merchantSn);
            if (CollectionUtils.isEmpty(canCancel)) {
                throw new AuditBizException("暂无需要取消的活动申请");
            }
            for (ActivityApplyEntity applyDO : canCancel) {
                CancelActivityApplyRequest request = buildPaySourceCancelApply(applyDO.getId());
                applyService.cancel(request);
            }

        } catch (Exception e) {
            log.error("支付源活动取消异常", e);
            msg = "支付源活动取消失败" + e.getMessage();
            type = ManageConstant.AUDIT_EXECUTE_FAIL;
        }
        //写评论
        CallBackBean callBackBean = CallBackBean.builder()
                .auditId(Long.valueOf(event.getAuditId()))
                .templateId(Long.valueOf(event.getTemplateId()))
                .resultType(type)
                .message(msg)
                .build();
        callBackService.addComment(callBackBean);
    }


    /**
     * 非支付源审批 通过（申请 取消）
     *
     * @param event
     */
    public void auditApprove(AuditInstanceApproveEvent event) {
        Map businessMap = event.getBusinessMap();
        String action = WosaiMapUtils.getString(businessMap, "action");
        //apply 费率申请
        if (action.equals("apply")) {
            doApplyAuditApprove(event);
        }
        //cancel 费率取消
        if (action.equals("cancel")) {
            doCancelAuditApprove(event);
        }
    }

    /**
     * 处理非支付源 取消
     *
     * @param event
     */
    public void doCancelAuditApprove(AuditInstanceApproveEvent event) {
        Map businessMap = event.getBusinessMap();
        String cancelMerchantType = WosaiMapUtils.getString(businessMap, "cancel_merchant");
        String msg = "";
        int type = ManageConstant.AUDIT_EXECUTE_SUCCESS;
        try {
            //单个商户取消
            if (cancelMerchantType.equals("single")) {
                Long activity_id = WosaiMapUtils.getLong(businessMap, "activity_id");
                if (activity_id == null) {
                    throw new AuditBizException("商户取消活动ID不能为空");
                }
                String merchantSn = getMerchantSn(businessMap);
                //查询商户申请的活动
                List<ActivityApplyEntity> canCancel = applyMapper.getApplyCanCancel(activity_id, merchantSn);
                if (CollectionUtils.isEmpty(canCancel)) {
                    throw new AuditBizException("暂无需要取消的活动申请");
                }
                for (ActivityApplyEntity applyDO : canCancel) {
                    CancelActivityApplyRequest request = buildCancelApply(event, applyDO.getId());
                    applyService.cancel(request);
                }
                msg = "活动申请取消成功";
            } else {
                //批量取消
                CancelActivityApplyRequest request = buildBulkCancelApply(event);
                msg = applyService.bulkCancelV2(request);
            }

        } catch (Exception e) {
            type = ManageConstant.AUDIT_EXECUTE_FAIL;
            msg = e.getMessage();
        }
        //写评论
        CallBackBean callBackBean = CallBackBean.builder()
                .auditId(Long.valueOf(event.getAuditId()))
                .templateId(Long.valueOf(event.getTemplateId()))
                .resultType(type)
                .message(msg)
                .build();
        callBackService.addComment(callBackBean);
    }


    /**
     * 处理非支付源活动的审批通过
     *
     * @param event
     */
    public void doApplyAuditApprove(AuditInstanceApproveEvent event) {

        Map businessMap = event.getBusinessMap();
        String applyMerchantType = WosaiMapUtils.getString(businessMap, "apply_merchant");
        //单个商户申请
        if (applyMerchantType.equals("single")) {
            //参数组装
            ApplyActivityRequest request = buildApplyActivityRequest(event);
            //申请
            ApplyActivityResponse response = modifyApplyActivityBiz.apply(request);
            //处理返回值 写评论
            int type;
            if (response.isSuccess()) {
                type = ManageConstant.AUDIT_EXECUTE_SUCCESS;
            } else {
                type = ManageConstant.AUDIT_EXECUTE_FAIL;
            }
            String msg = response.getMsg();
            if (response.isSuccess() && response.isDelaySyncFeeRateToProvider()) {
                msg = "实际调整时间为次日零时，若调整时间前商户申请了其他费率，本次调整仍然会在调整时间生效";
                log.info("是否延迟同步费率: msg={}, request={}", msg, JsonUtil.encode(request));
            }

            //写评论
            CallBackBean callBackBean = CallBackBean.builder()
                    .auditId(Long.valueOf(event.getAuditId()))
                    .templateId(Long.valueOf(event.getTemplateId()))
                    .resultType(type)
                    .message(msg)
                    .build();
            callBackService.addComment(callBackBean);
        } else {
            //多个商户申请 multiple
            //
            BulkAddActivityRequest request = buildBulkAddActivityRequest(event);
            //批量申请
            String msg = applyService.bulkApplyV2(request);
            //写评论
            CallBackBean callBackBean = CallBackBean.builder()
                    .auditId(Long.valueOf(event.getAuditId()))
                    .templateId(Long.valueOf(event.getTemplateId()))
                    .resultType(ManageConstant.AUDIT_EXECUTE_SUCCESS)
                    .message(msg)
                    .build();
            callBackService.addComment(callBackBean);
        }
    }


    public void doSingleApply(ApplyActivityRequest request, AuditInstanceEvent event) {
        //申请
        ApplyActivityResponse response = applyService.apply(request);
        //处理返回值 写评论
        int type;
        if (response.isSuccess()) {
            type = ManageConstant.AUDIT_EXECUTE_SUCCESS;
        } else {
            type = ManageConstant.AUDIT_EXECUTE_FAIL;
        }
        //写评论
        CallBackBean callBackBean = CallBackBean.builder()
                .auditId(Long.valueOf(event.getAuditId()))
                .templateId(Long.valueOf(event.getTemplateId()))
                .resultType(type)
                .message(response.getMsg())
                .build();
        callBackService.addComment(callBackBean);
    }


    public BulkAddActivityRequest buildBulkAddActivityRequest(AuditInstanceApproveEvent event) {
        Map businessMap = event.getBusinessMap();
        String remark = MapUtils.getString(businessMap, AuditConstant.FIELD_REMARK);
        if (StringUtils.isNotEmpty(remark)) {
            remark = " 备注：" + StringUtils.substring(remark, NumberUtils.INTEGER_ZERO, AuditConstant.REMARK_MAX_LENGTH);
        }
        BulkAddActivityRequest request = new BulkAddActivityRequest();
        BulkAddActivityRequest.AddDescribe addDescribe = new BulkAddActivityRequest.AddDescribe();
        addDescribe.setOperator(event.getOperator());
        addDescribe.setOperatorName(event.getOperatorName());
        addDescribe.setReason("活动批量开通" + remark);
        addDescribe.setAuditSn(event.getAuditSn());
        addDescribe.setAuditId(NumberUtils.toLong(event.getAuditId()));


        BulkAddActivityRequest.AddActivityByFile addActivityByFile = new BulkAddActivityRequest.AddActivityByFile();
        addActivityByFile.setPlatform(ApplyActivityPlatformEnum.APPROVAL.getCode());
        Pair<Long, Long> activityPair = applyActivityBuildParams.analyzeAuditActivity0(businessMap);
        Long activityId = activityPair.getLeft();
        if (Objects.isNull(activityId)) {
            throw new AuditBizException("商户申请活动ID为空");
        }
        ActivitySubStatusDetailEntity subStatusDetail = applyActivityBuildParams.findApplySubStatusDetailDo(activityId, activityPair.getRight());
        addActivityByFile.setActivityId(activityId);
        addActivityByFile.setSubStatusDetailId(subStatusDetail.getId());
        addActivityByFile.setComboId(subStatusDetail.getComboId());

        List<String> fileUrl = AttachmentFileUtil.getInstance().convertFileUrlList(BeanUtil.getProperty(businessMap, "fileurl"));
        if (CollectionUtils.isEmpty(fileUrl)) {
            throw new AuditBizException("申请文件链接不能为空");
        }
        addActivityByFile.setOssPath(fileUrl.get(0));
        Double quota = WosaiMapUtils.getDouble(businessMap, "quota");
        if (quota != null) {
            addActivityByFile.setDiscountQuota(quota);
        }
        String quotaFeeRate = WosaiMapUtils.getString(businessMap, "quota_fee_rate");
        if (StringUtils.isNotBlank(quotaFeeRate)) {
            addActivityByFile.setDiscountQuotaFeeRate(quotaFeeRate);
        }

        request.setAddDescribe(addDescribe);
        request.setAddActivityByFile(addActivityByFile);

        request.setAuditTemplateId(event.getAuditTemplateId());
        return request;
    }

    public ApplyActivityRequest buildApplyActivityRequest(AuditInstanceEvent event) {
        ApplyActivityRequest request = new ApplyActivityRequest();
        Map businessMap = event.getBusinessMap();

        //商户SN
        request.setMerchantSn(getMerchantSn(businessMap));
        request.setPlatform(ApplyActivityPlatformEnum.convertByName(event.getOperatorPlatform()).getCode());
        //活动层级SN
        String sn = AuditGetParamUtil.getSn(businessMap);
        if (StringUtils.isNotBlank(sn)) {
            request.setSn(sn);
        } else {
            request.setSn(request.getMerchantSn());
        }

        Pair<Long, Long> activityPair = applyActivityBuildParams.analyzeAuditActivity0(businessMap);
        Long activityId = activityPair.getLeft();
        if (Objects.isNull(activityId)) {
            throw new AuditBizException("商户申请活动ID为空");
        }
        request.setActivityId(activityId);
        request.setAuditId(Long.valueOf(event.getAuditId()));
        //构建活动报名费率明细
        List<ApplyActivityRequest.ApplyPayFeeRate> applyPayFeeRates;
        //刷卡审批费率参数构建
        if (Objects.equals(AuditConstant.EVENT_BANKCARD_CHANNEL_ACTIVITY_APPLY, event.getTemplateEvent())) {
            applyPayFeeRates = applyActivityBuildParams.buildApplyActivityByChannelFeeRate(businessMap);
        } else {
            applyPayFeeRates = applyActivityBuildParams.buildApplyActivityFixOrRange(businessMap);
        }
        if (CollectionUtils.isEmpty(applyPayFeeRates)) {
            List<ApplyActivityRequest.ApplyPayFeeRate> applyLadderPayFeeRates = buildLadder(businessMap);
            if (CollectionUtils.isNotEmpty(applyLadderPayFeeRates)) {
                request.setApplyPayFeeRates(applyLadderPayFeeRates);
            }
        } else {
            request.setApplyPayFeeRates(applyPayFeeRates);
        }

        Double quota = WosaiMapUtils.getDouble(businessMap, "quota");
        if (quota != null) {
            request.setDiscountQuota(quota);
        }
        String quotaFeeRate = WosaiMapUtils.getString(businessMap, "quota_fee_rate");
        if (StringUtils.isNotBlank(quotaFeeRate)) {
            request.setDiscountQuotaFeeRate(quotaFeeRate);
        }

        request.setAuditTemplateId(event.getAuditTemplateId());
        ActivitySubStatusDetailEntity subStatusDetail = applyActivityBuildParams.findApplySubStatusDetailDo(activityId, activityPair.getRight());
        request.setSubStatusDetailId(subStatusDetail.getId());
        request.setComboId(subStatusDetail.getComboId());
        request.setAuditInstanceEvent(JSONObject.parseObject(JSONObject.toJSONString(event), AuditInstanceCreateEvent.class));
        request.setOperator(event.getOperatorName());

        List<String> payWayNameList = request.getApplyPayFeeRates().stream()
                .map(applyPayFeeRate -> PayWayEnum.getNameByCode(applyPayFeeRate.getPayWay())).collect(Collectors.toList());
        String remark = WosaiMapUtils.getString(businessMap, AuditConstant.FIELD_REMARK);
        remark = StringUtils.isBlank(remark) ? "申请活动无原因" : StringUtils.substring(remark, NumberUtils.INTEGER_ZERO, AuditConstant.REMARK_MAX_LENGTH);
        remark = "活动报名成功，报名通道：" + StringUtils.join(payWayNameList, "、") + "。" + remark;
        request.setRemark(remark);
        return request;

    }

    public List<ApplyActivityRequest.ApplyPayFeeRate> buildLadder(Map businessMap) {
        //费率明细
        List<String> payList = (List<String>) BeanUtil.getProperty(businessMap, "pay_way_list");
        if (CollectionUtils.isEmpty(payList)) {
            return null;
        }
        List<ApplyActivityRequest.ApplyPayFeeRate> applyPayFeeRates = new ArrayList<>();
        for (String payWay : payList) {
            ApplyActivityRequest.ApplyPayFeeRate applyPayFeeRate = new ApplyActivityRequest.ApplyPayFeeRate();
            applyPayFeeRate.setPayWay(PayWayEnum.getCodeByName(payWay));
            applyPayFeeRates.add(applyPayFeeRate);
        }
        return applyPayFeeRates;
    }

    public CancelActivityApplyRequest buildCancelApply(AuditInstanceApproveEvent event, Long applyId) {

        Map businessMap = event.getBusinessMap();
        CancelActivityApplyRequest request = new CancelActivityApplyRequest();

        CancelActivityApplyRequest.CancelDescribe desc = new CancelActivityApplyRequest.CancelDescribe();
        String operator = StringUtils.isBlank(event.getOperator()) ? "system" : event.getOperator();
        String operatorName = StringUtils.isBlank(event.getOperator()) ? "system" : event.getOperator();

        String remark = WosaiMapUtils.getString(businessMap, "remark");
        remark = StringUtils.isBlank(remark) ? "商户取消活动" : remark;
        desc.setOperator(operator);
        desc.setOperatorName(operatorName);
        desc.setReason(remark);

        CancelActivityApplyRequest.CancelEntity cancelEntity = new CancelActivityApplyRequest.CancelEntity();


        List<String> payList = (List<String>) BeanUtil.getProperty(businessMap, "pay_way_list");

        CancelActivityApplyRequest.CancelByActivityApply cancelByActivityApply = new CancelActivityApplyRequest.CancelByActivityApply();

        cancelByActivityApply.setActivityApplyId(applyId);
        if (CollectionUtils.isNotEmpty(payList)) {
            List<Integer> pays = new ArrayList<>();
            for (String payWayValue : payList) {
                pays.add(PayWayEnum.getCodeByName(payWayValue));
            }
            cancelByActivityApply.setPayways(pays);
        }
        cancelEntity.setCancelByActivityApply(cancelByActivityApply);

        request.setCancelDescribe(desc);
        request.setCancelEntity(cancelEntity);

        return request;
    }


    public ApplyActivityRequest buildPaySourceApplyActivityRequest(AuditInstanceCreateEvent event) {
        ApplyActivityRequest request = new ApplyActivityRequest();
        Map businessMap = event.getBusinessMap();
        String merchantSn = getMerchantSn(businessMap);
        request.setMerchantSn(merchantSn);
        Pair<Long, Long> activityPair;
        String applyType = MapUtils.getString(businessMap, AuditConstant.FIELD_APPLY_TYPE);
        // 挂靠主商户
        if (Objects.equals(applyType, AuditConstant.APPLY_TYPE_INHERITED)) {
            String parentMerchantSn = getMerchantSn(businessMap, AuditConstant.FIELD_PARENT_MERCHANT);
            ValidationUtils.notNull(parentMerchantSn, "挂靠主商户sn不能为空");
            final Integer bindActivityType = AuditConstant.getSourceActivityTypeFromApprovalTemplate(event.getTemplateEvent());
            ValidationUtils.notNull(bindActivityType, "挂靠审批模板类型不正确");
            ActivityApplyEntity parentApplyDo = applyMapper.getApplyListBySource(null, parentMerchantSn, ImmutableList.of(ActivityConstants.EFFECT))
                    .stream().filter(entity -> Objects.equals(entity.getActivity_type(), bindActivityType)).findAny().orElse(null);
            if (Objects.isNull(parentApplyDo)) {
                throw new AuditBizException("挂靠主体商户没有生效中的报名申请记录. merchantSn=" + merchantSn);
            }
            activityPair = Pair.of(parentApplyDo.getActivity_id(), parentApplyDo.getActivity_sub_status_id());
            // 设置主体商户信息
            request.setMasterInfo(new ApplyActivityRequest.MasterInfo(parentMerchantSn, parentApplyDo.getId()));
        } else {
            activityPair = applyActivityBuildParams.analyzeAuditActivity0(businessMap);
        }
        Long activityId = activityPair.getLeft();
        if (Objects.isNull(activityId)) {
            throw new AuditBizException("商户申请活动ID为空");
        }
        CacheActivityInfo activity = cacheActivity.getById(activityId);
        if (activity == null) {
            throw new AuditBizException("商户申请活动不存在");
        }

        request.setActivityId(activityId);
        String subMerchantSn = getSubMerchantSn(businessMap);
        if (StringUtils.isBlank(subMerchantSn)) {
            //根据活动类型 确认是微信还是支付宝
            subMerchantSn = paywayActivityService.getPaymerchantId(merchantSn, activity.getType(), event);
            if (StringUtils.isBlank(subMerchantSn)) {
                throw new AuditBizException("支付源子商户号不能为空");
            }
        }
        List<ApplyActivityRequest.ApplyPayFeeRate> applyPayFeeRates = applyActivityBuildParams.buildApplyActivityFixOrRange(businessMap);
        if (CollectionUtils.isEmpty(applyPayFeeRates)) {
            applyPayFeeRates = buildLadder(businessMap);
        }
        if (CollectionUtils.isNotEmpty(applyPayFeeRates)) {
            request.setApplyPayFeeRates(applyPayFeeRates);
        }
        ActivitySubStatusDetailEntity subStatusDetail = applyActivityBuildParams.findApplySubStatusDetailDo(activityId, activityPair.getRight());
        request.setSubStatusDetailId(subStatusDetail.getId());
        request.setComboId(subStatusDetail.getComboId());
        request.setSubMerchantSn(subMerchantSn);
        request.setAuditInstanceEvent(event);
        request.setAuditId(Long.valueOf(event.getAuditId()));
        request.setOperator(event.getOperatorName());
        String remark = WosaiMapUtils.getString(businessMap, "remark", "申请活动无原因");
        request.setRemark(remark);

        return request;
    }


    public CancelActivityApplyRequest buildBulkCancelApply(AuditInstanceApproveEvent event) {
        Map businessMap = event.getBusinessMap();
        CancelActivityApplyRequest request = new CancelActivityApplyRequest();

        CancelActivityApplyRequest.CancelDescribe desc = new CancelActivityApplyRequest.CancelDescribe();
        desc.setOperator(event.getOperator());
        desc.setOperatorName(event.getOperatorName());
        desc.setReason("商户批量取消活动");

        Long activity_id = WosaiMapUtils.getLong(businessMap, "activity_id");
        if (activity_id == null) {
            throw new AuditBizException("商户取消活动ID不能为空");
        }

        CancelActivityApplyRequest.CancelEntity cancelEntity = new CancelActivityApplyRequest.CancelEntity();
        CancelActivityApplyRequest.CancelByFile byFile = new CancelActivityApplyRequest.CancelByFile();
        byFile.setActivityId(activity_id);
        byFile.setPlatform(ApplyActivityPlatformEnum.APPROVAL.getCode());

        List<String> fileUrl = AttachmentFileUtil.getInstance().convertFileUrlList(BeanUtil.getProperty(businessMap, "fileurl"));
        if (CollectionUtils.isEmpty(fileUrl)) {
            throw new AuditBizException("申请文件链接不能为空");
        }
        byFile.setOssPath(fileUrl.get(0));
        cancelEntity.setCancelByFile(byFile);


        request.setCancelDescribe(desc);
        request.setCancelEntity(cancelEntity);

        return request;

    }


    public CancelActivityApplyRequest buildPaySourceCancelApply(Long applyId) {
        CancelActivityApplyRequest request = new CancelActivityApplyRequest();
        CancelActivityApplyRequest.CancelDescribe desc = new CancelActivityApplyRequest.CancelDescribe();
        desc.setOperatorName("system");
        desc.setOperator("system");
        desc.setReason("申请取消活动");
        request.setCancelDescribe(desc);

        CancelActivityApplyRequest.CancelByActivityApply byApplyId = new CancelActivityApplyRequest.CancelByActivityApply();
        byApplyId.setActivityApplyId(applyId);

        CancelActivityApplyRequest.CancelEntity cancelEntity = new CancelActivityApplyRequest.CancelEntity();
        cancelEntity.setCancelByActivityApply(byApplyId);
        request.setCancelEntity(cancelEntity);

        return request;


    }

    /**
     * 返佣成功处理
     *
     * @param event
     */
    public void activityRebateSuccessApprove(AuditInstanceApproveEvent event) {
        Map map = event.getBusinessMap();
        List<String> fileUrl = AttachmentFileUtil.getInstance().convertFileUrlList(BeanUtil.getProperty(map, "fileurl"));
        if (CollectionUtils.isEmpty(fileUrl)) {
            throw new AuditBizException("申请文件链接不能为空");
        }
        List<ActivitySubStatusDetailEntity> rebateSuccessSubStatusList = activitySubStatusDetailMapper.selectByTag(ActivitySubStatusTagEnum.WECHAT_REBATE_SUCCESS.name());
        if (CollectionUtils.isEmpty(rebateSuccessSubStatusList)) {
            throw TradeManageBizException.createExc("当前子存态列表未配置. name:" + ActivitySubStatusTagEnum.WECHAT_REBATE_SUCCESS.getDesc());
        }
        List<String> commentList = Lists.newArrayList();
        List<Long> activityIds = rebateSuccessSubStatusList.stream().map(ActivitySubStatusDetailEntity::getActivityId).collect(Collectors.toList());
        // key = activityId
        Map<Long, ActivitySubStatusDetailEntity> subStatusMap = rebateSuccessSubStatusList
                .stream().collect(Collectors.toMap(ActivitySubStatusDetailEntity::getActivityId, Function.identity(), (o, n) -> o));
        processExcelFile(map, cells -> {
            String merchantSn = ExcelUtil.enumToString(ExcelUtil.toString(cells.getCell(0)));
            if (StringUtils.isBlank(merchantSn)) {
                return;
            }
            try {
                MDC.put(ConstantUtil.TRACE_ID, merchantSn);
                RebateSuccessResult result = rebateSuccessBiz.execute(RebateSuccessParam.builder()
                        .merchantSn(merchantSn)
                        .activityIds(activityIds)
                        .subStatusMap(subStatusMap)
                        .auditSn(event.getAuditSn())
                        .operator(event.getOperatorName())
                        .build());
                if (!result.isSuccess()) {
                    commentList.add(result.getCommentMsg());
                }
            } catch (Exception e) {
                log.error("切换活动子状态失败. merchantSn:{}", merchantSn, e);
                commentList.add("切换活动子状态失败. merchantSn:" + merchantSn + ",msg:" + e.getMessage());
            } finally {
                MDC.clear();
            }
        });
        try {
            //通过审批
            CallBackBean callBackBean = CallBackBean.builder()
                    .auditId(Long.valueOf(event.getAuditId()))
                    .templateId(Long.valueOf(event.getTemplateId()))
                    .resultType(ManageConstant.AUDIT_EXECUTE_SUCCESS)
                    .message(CollectionUtils.isEmpty(commentList) ? "批量切换成功" : "批量切换失败。原因：" + StringUtils.join(commentList, ",\n"))
                    .build();
            callBackService.addComment(callBackBean);
        } catch (Exception e1) {
            log.error("pass audit fail", e1);
        }
    }

    /**
     * 更新支付源费率
     *
     * @param event
     */
    public void modifyPaySourceFeeRate(AuditInstanceApproveEvent event) {
        adjustPaySourceActivityFeeRateBiz.execute(event);
        try {
            //通过审批
            CallBackBean callBackBean = CallBackBean.builder()
                    .auditId(Long.valueOf(event.getAuditId()))
                    .templateId(Long.valueOf(event.getTemplateId()))
                    .resultType(ManageConstant.AUDIT_EXECUTE_SUCCESS)
                    .message("更新支付源费率成功")
                    .build();
            callBackService.addComment(callBackBean);
        } catch (Exception e1) {
            log.error("pass audit fail", e1);
        }
    }

    /**
     * 更新支付源费率 - 阶梯费率
     *
     * @param event
     */
    public void modifyPaySourceLadderFeeRate(AuditInstanceApproveEvent event) {
        modifyActivityPaySourceFeeRateBiz.modifyPaySourceLadderFeeRate(event);
        try {
            //通过审批
            CallBackBean callBackBean = CallBackBean.builder()
                    .auditId(Long.valueOf(event.getAuditId()))
                    .templateId(Long.valueOf(event.getTemplateId()))
                    .resultType(ManageConstant.AUDIT_EXECUTE_SUCCESS)
                    .message("更新支付源费率成功")
                    .build();
            callBackService.addComment(callBackBean);
        } catch (Exception e1) {
            log.error("pass audit fail", e1);
        }
    }

    /**
     * 活动套餐切换
     *
     * @param event
     */
    public void modifyActivityComboFeeRate(AuditInstanceApproveEvent event) {
        modifyActivityComboFeeRateBiz.execute(ModifyActivityComboFeeRateBiz.ChangeActivityComboParam.build(event));
        try {
            //通过审批
            CallBackBean callBackBean = CallBackBean.builder()
                    .auditId(Long.valueOf(event.getAuditId()))
                    .templateId(Long.valueOf(event.getTemplateId()))
                    .resultType(ManageConstant.AUDIT_EXECUTE_SUCCESS)
                    .message("活动套餐切换成功")
                    .build();
            callBackService.addComment(callBackBean);
        } catch (Exception e1) {
            log.error("pass audit fail", e1);
        }
    }

    private void processExcelFile(Map businessMap, Consumer<Row> consumer) {
        List<String> fileUrl = AttachmentFileUtil.getInstance().convertFileUrlList(BeanUtil.getProperty(businessMap, "fileurl"));
        if (CollectionUtils.isEmpty(fileUrl)) {
            throw new AuditBizException("申请文件链接不能为空");
        }
        String ossPath = fileUrl.get(0);
        File file = null;
        try {
            file = OssUtil.downloadOssFile(ossPath);
            if (file == null) {
                throw TradeManageBizException.createExc("文件读取失败");
            }
            Workbook workbook = ExcelUtil.getExcelFromFile(file);
            Sheet sheet = workbook.getSheetAt(0);
            if (sheet == null) {
                throw TradeManageBizException.createExc("文件不能为空");
            }
            for (int rowNum = 0; rowNum < sheet.getPhysicalNumberOfRows(); rowNum++) {
                Row hssfRow = sheet.getRow(rowNum);
                consumer.accept(hssfRow);
            }
        } catch (Throwable e) {
            log.error("processExcelFile failed. ", e);
            throw e;
        } finally {
            if (file != null) {
                file.delete();
            }
        }
    }
}
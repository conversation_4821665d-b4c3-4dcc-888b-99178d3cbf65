package com.wosai.trade.biz.fee;

import com.wosai.middleware.hera.toolkit.trace.TraceContext;
import com.wosai.trade.biz.activity.context.TradeAssessmentCountContext;
import com.wosai.trade.client.LarkClient;
import com.wosai.trade.constant.LockConst;
import com.wosai.trade.repository.dao.entity.ActivityApplyEntity;
import com.wosai.trade.repository.dao.entity.CrmFeeRateChangeEntity;
import com.wosai.trade.util.SpringBeanUtil;
import com.wosai.trade.util.TraceUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.Objects;

/**
 * 费率相关告警业务类
 *
 * <AUTHOR>
 * @date 2024/12/5
 **/
@Slf4j
@Component
public class AlarmFeeRateBiz {
    @Value("${config.activity-monitor.fee-rate-sync-lark-url}")
    private String feeRateSyncLarkUrl;
    @Resource
    private LarkClient larkClient;
    @Resource
    private RedissonClient redissonClient;

    /**
     * 延迟生效［报名/修改］活动费率告警
     */
    public void adjustDelayApplyActivityFeeRate(CrmFeeRateChangeEntity crmFeeRateChange, ActivityApplyEntity applyEntity, String errorMsg) {
        String merchantSn = null;
        try {
            merchantSn = crmFeeRateChange.getMerchantSn();
            merchantSn = applyEntity.getMerchant_sn();
            String key = String.format(LockConst.ALARM_TAKE_EFFECT_CD, "delayTakeEffect", crmFeeRateChange.getId(), merchantSn);
            //1天通知一次
            boolean cd = redissonClient.getBucket(key).setIfAbsent("1", Duration.ofDays(NumberUtils.INTEGER_ONE));
            if (!cd) {
                return;
            }
            String typeName = crmFeeRateChange.getType() + StringUtils.EMPTY;
            if (Objects.equals(crmFeeRateChange.getType(), CrmFeeRateChangeEntity.SP_DELAY_TAKE_AFFECT_V1)) {
                typeName = "报名";
            } else if (Objects.equals(crmFeeRateChange.getType(), CrmFeeRateChangeEntity.SP_DELAY_TAKE_AFFECT_V2)) {
                typeName = "调整";
            }
            StringBuilder msg = new StringBuilder(256);
            msg.append("环境：").append(SpringBeanUtil.getEnvName()).append("\n");
            msg.append("商户号：").append(crmFeeRateChange.getMerchantSn()).append("\n");
            msg.append("生效类型：").append(typeName).append("\n");
            if (Objects.nonNull(applyEntity)) {
                msg.append("活动id：").append(applyEntity.getActivity_id()).append("\n");
                msg.append("活动名称：").append(applyEntity.getActivity_name()).append("\n");
            }
            msg.append("失败原因：").append(errorMsg).append("\n");
            msg.append("trace_id：").append(TraceUtil.getTraceId()).append("\n");
            larkClient.sendMsg(feeRateSyncLarkUrl, "延迟生效［费率套餐活动］费率失败", msg.toString());
        } catch (Throwable e) {
            log.error("延迟生效［报名/修改］活动费率告警失败. merchantSn={}", merchantSn, e);
        }
    }

    /**
     * 生效报名活动费率告警
     */
    public void takeEffect(ActivityApplyEntity applyEntity, String errorMsg) {
        String merchantSn = null;
        try {
            merchantSn = applyEntity.getMerchant_sn();
            String key = String.format(LockConst.ALARM_TAKE_EFFECT_CD, "takeEffect", applyEntity.getId(), merchantSn);
            //1天通知一次
            boolean cd = redissonClient.getBucket(key).setIfAbsent("1", Duration.ofDays(NumberUtils.INTEGER_ONE));
            if (!cd) {
                return;
            }
            String typeName = "报名";
            StringBuilder msg = new StringBuilder(256);
            msg.append("环境：").append(SpringBeanUtil.getEnvName()).append("\n");
            msg.append("商户号：").append(applyEntity.getMerchant_sn()).append("\n");
            msg.append("生效类型：").append(typeName).append("\n");
            msg.append("活动id：").append(applyEntity.getActivity_id()).append("\n");
            msg.append("活动名称：").append(applyEntity.getActivity_name()).append("\n");
            msg.append("失败原因：").append(errorMsg).append("\n");
            msg.append("trace_id：").append(TraceUtil.getTraceId()).append("\n");
            larkClient.sendMsg(feeRateSyncLarkUrl, "生效［费率套餐活动］费率失败", msg.toString());
        } catch (Throwable e) {
            log.error("生效报名活动费率告警失败. merchantSn={}", merchantSn, e);
        }
    }

    /**
     * 交易考核通过但费率切换失败告警
     * 当考核通过但部分支付方式正在使用其他活动，无法切换费率时发送预警
     */
    public void assessmentPassButFeeRateSwitchFailed(ActivityApplyEntity applyEntity) {
        String merchantSn = null;
        try {
            merchantSn = applyEntity.getMerchant_sn();
            StringBuilder msg = new StringBuilder(256);
            msg.append("环境：").append(SpringBeanUtil.getEnvName()).append("\n");
            msg.append("商户号：").append(applyEntity.getMerchant_sn()).append("\n");
            msg.append("活动id：").append(applyEntity.getActivity_id()).append("\n");
            msg.append("活动名称：").append(applyEntity.getActivity_name()).append("\n");
            msg.append("告警类型：交易考核通过但费率切换失败").append("\n");
            msg.append("问题描述：考核通过，但是支付方式正在使用其他活动或基础费率套餐，无法切换费率").append("\n");
            msg.append("建议处理：请检查相关商户的活动配置和支付方式占用情况").append("\n");
            msg.append("trace_id：").append(TraceUtil.getTraceId()).append("\n");
            larkClient.sendMsg(feeRateSyncLarkUrl, "交易考核定时任务处理异常", msg.toString());
        } catch (Throwable e) {
            log.error("交易考核通过但费率切换失败告警失败. merchantSn={}", merchantSn, e);
        }
    }

    /**
     * 交易考核通过但费率申请失败告警
     * 当考核通过但在申请费率过程中发生异常时发送预警
     */
    public void restoreEffectNotStandard(ActivityApplyEntity applyEntity, Exception exception) {
        String merchantSn = null;
        try {
            merchantSn = applyEntity.getMerchant_sn();
            StringBuilder msg = new StringBuilder(256);
            msg.append("环境：").append(SpringBeanUtil.getEnvName()).append("\n");
            msg.append("商户号：").append(applyEntity.getMerchant_sn()).append("\n");
            msg.append("活动id：").append(applyEntity.getActivity_id()).append("\n");
            msg.append("活动名称：").append(applyEntity.getActivity_name()).append("\n");
            msg.append("告警类型：交易考核通过恢复费率失败").append("\n");
            msg.append("异常信息：").append(exception.getMessage()).append("\n");
            msg.append("建议处理：请检查相关商户的活动配置和费率申请流程").append("\n");
            msg.append("trace_id：").append(TraceUtil.getTraceId()).append("\n");
            larkClient.sendMsg(feeRateSyncLarkUrl, "交易考核定时任务处理异常", msg.toString());
        } catch (Throwable e) {
            log.error("交易考核通过但费率申请失败告警失败. merchantSn={}", merchantSn, e);
        }
    }

    public void assessmentFailedNoTradeData(ActivityApplyEntity applyEntity, String assessmentTimeStr) {
        String merchantSn = null;
        try {
            merchantSn = applyEntity.getMerchant_sn();
            StringBuilder msg = new StringBuilder(256);
            msg.append("环境：").append(SpringBeanUtil.getEnvName()).append("\n");
            msg.append("商户号：").append(applyEntity.getMerchant_sn()).append("\n");
            msg.append("报名申请记录ID：").append(applyEntity.getId()).append("\n");
            msg.append("活动id：").append(applyEntity.getActivity_id()).append("\n");
            msg.append("活动名称：").append(applyEntity.getActivity_name()).append("\n");
            msg.append("考核时间：").append(assessmentTimeStr).append("\n");
            msg.append("告警类型：交易考核无交易汇总数据").append("\n");
            msg.append("trace_id：").append(TraceUtil.getTraceId()).append("\n");
            larkClient.sendMsg(feeRateSyncLarkUrl, "交易考核定时任务处理异常", msg.toString());
        } catch (Throwable e) {
            log.error("交易考核通过但费率申请失败告警失败. merchantSn={}", merchantSn, e);
        }
    }

    /**
     * 交易考核数据未同步告警
     * 当交易月份数据暂未从odps同步回rds时发送预警
     */
    public void assessmentDataNotSynced(String assessmentTime) {
        try {
            String key = String.format(LockConst.ALARM_ASSESSMENT_JOB_CD, assessmentTime);
            //1小时预警一次
            boolean cd = redissonClient.getBucket(key).setIfAbsent("1", Duration.ofHours(NumberUtils.INTEGER_ONE));
            if (!cd) {
                return;
            }
            StringBuilder msg = new StringBuilder(256);
            msg.append("环境：").append(SpringBeanUtil.getEnvName()).append("\n");
            msg.append("考核时间：").append(assessmentTime).append("\n");
            msg.append("告警类型：交易考核数据未同步").append("\n");
            msg.append("问题描述：交易月份数据暂未从odps同步回rds，无法进行考核处理").append("\n");
            msg.append("建议处理：请检查数据同步状态，确认odps数据是否正常同步到rds").append("\n");
            msg.append("trace_id：").append(TraceUtil.getTraceId()).append("\n");
            larkClient.sendMsg(feeRateSyncLarkUrl, "交易考核定时任务处理异常", msg.toString());
        } catch (Throwable e) {
            log.error("交易考核数据未同步告警失败.　tradeMonth={}", assessmentTime, e);
        }
    }

    /**
     * 交易考核数据完成后数据
     * 当交易月份数据暂未从odps同步回rds时发送预警
     */
    public void assessmentComplete(int applyTotal, TradeAssessmentCountContext context, String assessmentTime) {
        try {
            StringBuilder msg = new StringBuilder(256);
            msg.append("环境：").append(SpringBeanUtil.getEnvName()).append("\n");
            msg.append("考核时间：").append(assessmentTime).append("\n");
            msg.append("预警类型：交易考核完成").append("\n");
            msg.append("考核总数：").append(applyTotal).append("\n");
            msg.append("考核达标数量：").append(context.getPassedCount()).append("\n");
            msg.append("考核达标-恢复活动数量：").append(context.getRestoredActivityCount()).append("\n");
            msg.append("考核不达标数量：").append(context.getFailedCount()).append("\n");
            msg.append("考核不达标-取消活动数量：").append(context.getCanceledActivityCount()).append("\n");
            msg.append("trace_id：").append(TraceContext.traceId()).append("\n");
            larkClient.sendMsg(feeRateSyncLarkUrl, "交易考核定时任务完成汇总数据", msg.toString());
        } catch (Throwable e) {
            log.error("交易考核定时任务完成汇总数据.　tradeMonth={}", assessmentTime, e);
        }
    }

    /**
     * 取消支付源活动失败告警
     * 当取消挂靠子商户支付源活动过程中发生异常时发送预警
     */
    public void cancelSourceActivityFailed(ActivityApplyEntity entity, Exception exception) {
        String merchantSn = null;
        try {
            merchantSn = entity.getMerchant_sn();
            StringBuilder msg = new StringBuilder(256);
            msg.append("环境：").append(SpringBeanUtil.getEnvName()).append("\n");
            msg.append("商户号：").append(entity.getMerchant_sn()).append("\n");
            msg.append("报名申请记录ID：").append(entity.getId()).append("\n");
            msg.append("活动id：").append(entity.getActivity_id()).append("\n");
            msg.append("活动名称：").append(entity.getActivity_name()).append("\n");
            msg.append("告警类型：取消挂靠子商户支付源活动失败").append("\n");
            msg.append("异常信息：").append(exception.getMessage()).append("\n");
            msg.append("建议处理：请检查相关商户的活动配置和取消流程").append("\n");
            msg.append("trace_id：").append(TraceUtil.getTraceId()).append("\n");
            larkClient.sendMsg(feeRateSyncLarkUrl, "取消支付源活动失败告警", msg.toString());
        } catch (Throwable e) {
            log.error("取消支付源活动失败告警失败. merchantSn={}", merchantSn, e);
        }
    }

    /**
     * 取消支付源活动失败告警
     * 当取消挂靠子商户支付源活动过程中发生异常时发送预警
     */
    public void changeActivitySendFailed(boolean isApply, ActivityApplyEntity entity, Exception exception) {
        String merchantSn = null;
        try {
            String changeName = isApply ? "生效" : "取消";
            merchantSn = entity.getMerchant_sn();
            StringBuilder msg = new StringBuilder(256);
            msg.append("环境：").append(SpringBeanUtil.getEnvName()).append("\n");
            msg.append("商户号：").append(entity.getMerchant_sn()).append("\n");
            msg.append("报名申请记录ID：").append(entity.getId()).append("\n");
            msg.append("活动id：").append(entity.getActivity_id()).append("\n");
            msg.append("活动名称：").append(entity.getActivity_name()).append("\n");
            msg.append("变动方式：").append(changeName).append("\n");
            msg.append("告警类型：商户活动费率变更发送kafka消息失败").append("\n");
            msg.append("异常信息：").append(exception.getMessage()).append("\n");
            msg.append("建议处理：请检查相关商户的活动配置和取消流程").append("\n");
            msg.append("trace_id：").append(TraceUtil.getTraceId()).append("\n");
            larkClient.sendMsg(feeRateSyncLarkUrl, "支付源活动变动发送kafka消息失败告警", msg.toString());
        } catch (Throwable e) {
            log.error("支付源活动变动发送kafka消息失败告警失败. merchantSn={}", merchantSn, e);
        }
    }

}

package com.wosai.trade.biz.activity;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableList;
import com.shouqianba.workflow.bean.CallBackBean;
import com.shouqianba.workflow.service.CallBackService;
import com.wosai.databus.event.audit.AuditInstanceCreateEvent;
import com.wosai.databus.event.pay.TradeManageChangeApplyActivityEvent;
import com.wosai.pantheon.util.CollectionUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.trade.biz.activity.converter.ApplyActivityBuildParams;
import com.wosai.trade.biz.activity.model.CacheActivityInfo;
import com.wosai.trade.biz.fee.AlarmFeeRateBiz;
import com.wosai.trade.biz.fee.ProviderFeeRateSyncBiz;
import com.wosai.trade.impl.CacheService;
import com.wosai.trade.model.biz.ActivityFeeRateParam;
import com.wosai.trade.model.constant.ManageConstant;
import com.wosai.trade.model.dal.ApplyExtraParam;
import com.wosai.trade.producer.TradeManageKafkaProducer;
import com.wosai.trade.repository.ActivityApplyRepository;
import com.wosai.trade.repository.CrmFeeRateChangeRepository;
import com.wosai.trade.repository.FeeRateRepository;
import com.wosai.trade.repository.dao.ActivityApplyDOMapper;
import com.wosai.trade.repository.dao.ActivityDOMapper;
import com.wosai.trade.repository.dao.entity.ActivityApplyEntity;
import com.wosai.trade.repository.dao.entity.ActivityEntity;
import com.wosai.trade.repository.dao.entity.CrmFeeRateChangeEntity;
import com.wosai.trade.service.FeeRateService;
import com.wosai.trade.service.activity.constant.ActivityConstants;
import com.wosai.trade.service.activity.enums.ActivityTypeEnum;
import com.wosai.trade.service.activity.model.TradeAssessmentRule;
import com.wosai.trade.service.activity.request.ActivityEffectiveRule;
import com.wosai.trade.service.activity.request.ActivityExpirationRule;
import com.wosai.trade.service.activity.request.CancelActivityApplyRequest;
import com.wosai.trade.service.activity.request.PaySourceHandelRequest;
import com.wosai.trade.service.activity.response.ApplyProcessInfo;
import com.wosai.trade.service.enums.ComboConfigLevelEnum;
import com.wosai.trade.service.exception.TradeManageBizException;
import com.wosai.trade.service.request.CancelFeeRateRequest;
import com.wosai.trade.util.DateTimeUtils;
import com.wosai.trade.util.JsonUtil;
import com.wosai.trade.util.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.wosai.trade.service.activity.constant.ActivityConstants.APPLY_SUCCESS;
import static com.wosai.trade.service.activity.constant.PaySourceHandleConstants.HANDLE_FAIL;
import static com.wosai.trade.service.activity.constant.PaySourceHandleConstants.HANDLE_SUCCESS;

/**
 * @Description:
 * <AUTHOR>
 * @Date: 2022/5/26 9:56 下午
 */
@Component
@Slf4j
public class ApplyActivityBiz {

    @Autowired
    private ActivityApplyDOMapper applyMapper;
    @Autowired
    private ActivityApplyRepository activityApplyRepository;

    @Autowired
    private ActivityDOMapper activityMapper;

    @Autowired
    private ApplyActivityBuildParams buildParams;
    @Autowired
    private FeeRateService feeRateService;

    @Autowired
    private CallBackService callBackService;
    @Autowired
    private CacheService cacheService;
    @Resource
    private CacheActivity cacheActivity;
    @Resource
    private FeeRateRepository feeRateRepository;
    @Resource
    private ProviderFeeRateSyncBiz providerFeeRateSyncBiz;
    @Autowired
    private CrmFeeRateChangeRepository crmFeeRateChangeRepository;
    @Resource
    private TradeManageKafkaProducer tradeManageKafkaProducer;
    @Autowired
    private AlarmFeeRateBiz alarmFeeRateBiz;

    /**
     * 发送活动取消事件
     *
     * @param activityInfo
     */
    public void sendChangeFeeRateCompletedEvent(boolean isApply, ActivityFeeRateParam activityInfo, String remark) {
        if (Objects.isNull(activityInfo)) {
            return;
        }
        ActivityApplyEntity applyEntity = activityInfo.getActivityApplyEntity();
        // 本次先只发送支付源活动取消事件
        if (!ActivityTypeEnum.payWayActivities.contains(applyEntity.getActivity_type())) {
            return;
        }
        TradeManageChangeApplyActivityEvent event = null;
        try {
            event = new TradeManageChangeApplyActivityEvent();
            event.setActivityId(applyEntity.getActivity_id());
            event.setActivityType(applyEntity.getActivity_type());
            event.setApplyId(applyEntity.getId());
            event.setMerchantSn(applyEntity.getMerchant_sn());
            event.setMerchantId(applyEntity.getMerchant_id());
            event.setIsApply(isApply);
            event.setRemark(remark);
            event.setChangeActivityFeeRateEnum(activityInfo.getRequest().getChangeActivityFeeRateEnum());
            event.setTimestamp(System.currentTimeMillis());
            log.info("发送支付源活动费率变动事件. event={}", JsonUtil.encode(event));
            tradeManageKafkaProducer.doSendEvent(event, exception ->
                    alarmFeeRateBiz.changeActivitySendFailed(isApply, applyEntity, exception));
        } catch (Exception e) {
            log.error("支付源活动费率变动发送kafka消息失败 event={}", JsonUtil.encode(event), e);
            alarmFeeRateBiz.changeActivitySendFailed(isApply, applyEntity, e);
        }
    }

    /**
     * 活动取消
     *
     * @param activityEntity
     * @param activityApplyEntity
     * @param auditSn
     * @param operator
     * @param operatorName
     * @param cancelPayWays
     */
    public void cancelActivityApply(ActivityEntity activityEntity, ActivityApplyEntity activityApplyEntity,
                                    String auditSn, String operator, String operatorName,
                                    List<Integer> cancelPayWays, String occurSource) {
        //活动状态
        Integer status = activityApplyEntity.getStatus();
        if (status == ActivityConstants.CANCEL) {
            return;
        } else if (status == ActivityConstants.CANCEL_ING) {
            TradeManageBizException.createExc("活动取消中 勿重复取消");
        }

        if (status != ActivityConstants.EFFECT) {
            // 增加风控来源标识
            if (Objects.equals(occurSource, CancelActivityApplyRequest.OCCUR_SOURCE_RISK)) {
                ApplyExtraParam extra = Objects.nonNull(activityApplyEntity.buildJsonExtra()) ? activityApplyEntity.buildJsonExtra() : new ApplyExtraParam();
                extra.setOccurSource(occurSource);
                activityApplyEntity.setExtra(JSONObject.toJSONString(extra));
            }
            // 活动未生效时，直接将活动置为失效
            updateApplyStatus(activityApplyEntity, ActivityConstants.CANCEL, operator, auditSn);
        } else {
            CancelFeeRateRequest request = new CancelFeeRateRequest();
            request.setMerchantSn(activityApplyEntity.getMerchant_sn());
            if (ComboConfigLevelEnum.STORE.name().equalsIgnoreCase(activityEntity.getConfig_level())) {
                request.setStoreSn(activityApplyEntity.getSn());
            } else if (ComboConfigLevelEnum.TERMINAL.name().equalsIgnoreCase(activityEntity.getConfig_level())) {
                request.setTerminalSn(activityApplyEntity.getSn());
            }
            request.setTradeComboId(activityApplyEntity.getCombo_id());
            request.setOperator(operator);
            request.setOperatorName(operatorName);
            if (CollectionUtil.isNotEmpty(cancelPayWays)) {
                request.setPayWays(cancelPayWays);
            }
            if (StringUtils.isBlank(auditSn)) {
                request.setAuditSn("取消活动无原因");
            } else {
                request.setActivityApplyLog(auditSn);
                request.setBusinessOpLog(auditSn);
                request.setAuditSn("N/A");
            }
            request.setOccurSource(occurSource);
            request.setSubStatusId(activityApplyEntity.getActivity_sub_status_id());
            feeRateService.cancelFeeRate(request);
        }
        //取消延迟待生效记录
        cancelDelayFeeRateChange(activityApplyEntity.getId());
    }

    /**
     * 取消延迟待生效记录
     *
     * @param applyId 报名申请记录ID
     */
    private void cancelDelayFeeRateChange(Long applyId) {
        ActivityApplyEntity afterApplyDo = activityApplyRepository.selectByPrimaryKey(applyId);
        //取消
        if (!Objects.equals(afterApplyDo.getStatus(), ActivityConstants.CANCEL)) {
            return;
        }
        //只处理延迟生效处理
        ApplyExtraParam extra = afterApplyDo.buildJsonExtra();
        if (Objects.isNull(extra) || !extra.isDelayTakeEffect()) {
            return;
        }
        try {
            //找出当前待取消延迟生效［报名/修改］活动费率change记录
            CrmFeeRateChangeEntity changeDo = crmFeeRateChangeRepository.findByMerchantStatus(afterApplyDo.getMerchant_sn(), null,
                            CrmFeeRateChangeEntity.STATUS_UNDONE_LIST,
                            ImmutableList.of(CrmFeeRateChangeEntity.SP_DELAY_TAKE_AFFECT_V1, CrmFeeRateChangeEntity.SP_DELAY_TAKE_AFFECT_V2))
                    .stream().filter(crmFeeRateChangeEntity -> {
                        Map<String, Object> value = crmFeeRateChangeEntity.getValue();
                        Long activityApplyId = MapUtil.getLong(value, "activityApplyId");
                        return Objects.equals(applyId, activityApplyId);
                    }).findAny().orElse(null);
            if (Objects.isNull(changeDo)) {
                throw new TradeManageBizException("待取消延迟生效［报名/修改］活动费率change记录不存在");
            }
            Long crmChangeId = changeDo.getId();
            log.info("取消延迟生效［报名/修改］活动费率change记录. crmChangeId={},applyId={},merchantSn={},activityId={}",
                    applyId, crmChangeId, afterApplyDo.getMerchant_sn(), afterApplyDo.getActivity_id());
            Map<String, Object> value = changeDo.getValue();
            //更新状态
            CrmFeeRateChangeEntity updateEntity = new CrmFeeRateChangeEntity();
            updateEntity.setId(crmChangeId);
            updateEntity.setStatus(CrmFeeRateChangeEntity.STATUS_CANCEL);
            value.put(CrmFeeRateChangeEntity.EXECUTE_RESULT, "取消活动［报名/修改］活动费率");
            updateEntity.setValue(value);
            updateEntity.setVersion(changeDo.getVersion());
            crmFeeRateChangeRepository.update(updateEntity);
        } catch (Throwable e) {
            log.error("取消延迟生效［报名/修改］活动费率change记录失败. applyId={},merchantSn={},activityId={}",
                    applyId, afterApplyDo.getMerchant_sn(), afterApplyDo.getActivity_id(), e);
        }
    }

    /**
     * 前置处理结果
     *
     * @param apply
     * @param request
     */
    public void paySourcePreHandle(ActivityApplyEntity apply, PaySourceHandelRequest request) {
        int type = ManageConstant.AUDIT_EXECUTE_SUCCESS;
        String msg = "前置处理中:" + request.getMsg();
        if (HANDLE_SUCCESS == request.getStatus()) {
            msg = "前置处理完成，审批可以通过";
            updateApplyStatus(apply, ActivityConstants.PRE_HANDLE_END, "前置活动处理完成");
        } else if (HANDLE_FAIL == request.getStatus()) {
            type = ManageConstant.AUDIT_EXECUTE_FAIL;
            msg = "前置处理失败:" + request.getMsg();
            updateApplyStatus(apply, ActivityConstants.APPLY_FAIL, "前置活动处理失败" + request.getMsg());
        }
        if (StringUtils.isNotBlank(apply.getAudit_info())) {
            AuditInstanceCreateEvent event = apply.buildAuditInfo();
            if (event != null) {
                //写评论
                CallBackBean callBackBean = CallBackBean.builder()
                        .auditId(Long.valueOf(event.getAuditId()))
                        .templateId(Long.valueOf(event.getTemplateId()))
                        .resultType(type)
                        .message(msg)
                        .build();
                callBackService.addComment(callBackBean);
            }
        }
    }

    /**
     * 报名结果
     *
     * @param apply
     * @param request
     */
    public void paySourceApply(ActivityApplyEntity apply, PaySourceHandelRequest request) {
        int type = ManageConstant.AUDIT_EXECUTE_SUCCESS;
        String msg = "活动报名中:" + request.getMsg();
        if (HANDLE_SUCCESS == request.getStatus()) {
            msg = "活动报名成功,自动生效后 即可使用优惠费率";
            updateApplyStatus(apply, ActivityConstants.APPLY_SUCCESS, "活动报名成功");
        } else if (HANDLE_FAIL == request.getStatus()) {
            type = ManageConstant.AUDIT_EXECUTE_FAIL;
            msg = "活动报名失败:" + request.getMsg();
            updateApplyStatus(apply, ActivityConstants.APPLY_FAIL, "活动报名失败" + request.getMsg());
        }
        if (StringUtils.isNotBlank(apply.getAudit_info())) {
            AuditInstanceCreateEvent event = apply.buildAuditInfo();
            if (event != null) {
                //写评论
                CallBackBean callBackBean = CallBackBean.builder()
                        .auditId(Long.valueOf(event.getAuditId()))
                        .templateId(Long.valueOf(event.getTemplateId()))
                        .resultType(type)
                        .message(msg)
                        .build();
                callBackService.addComment(callBackBean);
            }
        }
    }

    /**
     * 支付源取消处理
     *
     * @param apply
     * @param request
     */
    public void paySourceCancel(ActivityApplyEntity apply, PaySourceHandelRequest request) {
        int type = ManageConstant.AUDIT_EXECUTE_SUCCESS;
        String msg = "活动取消中:" + request.getMsg();
        if (HANDLE_SUCCESS == request.getStatus()) {
            msg = "活动取消成功,不再使用优惠费率";
            ActivityEntity activityEntity = activityMapper.selectByPrimaryKey(apply.getActivity_id());
            try {
                cancelActivityApply(activityEntity, apply, "支付源活动取消", "system", "system", null, null);
            } catch (Exception e) {
                type = ManageConstant.AUDIT_EXECUTE_FAIL;
                msg = "活动取消失败 仍可以使用优惠费率 失败原因:" + e.getMessage();
            }
        } else if (HANDLE_FAIL == request.getStatus()) {
            type = ManageConstant.AUDIT_EXECUTE_FAIL;
            msg = "活动取消失败 仍可以使用优惠费率 失败原因:" + request.getMsg();
        }
        if (StringUtils.isNotBlank(apply.getAudit_info())) {
            AuditInstanceCreateEvent event = apply.buildAuditInfo();
            if (event != null) {
                //写评论
                CallBackBean callBackBean = CallBackBean.builder()
                        .auditId(Long.valueOf(event.getAuditId()))
                        .templateId(Long.valueOf(event.getTemplateId()))
                        .resultType(type)
                        .message(msg)
                        .build();
                callBackService.addComment(callBackBean);
            }
        }
    }

    /**
     * 更新申请状态
     *
     * @param apply
     * @param status
     * @param info
     */
    public void updateApplyStatus(ActivityApplyEntity apply, int status, String info) {
        updateApplyStatus(apply, status, "system", info);
    }

    public void updateApplyStatus(ActivityApplyEntity apply, int status, String operator, String info) {
        if (APPLY_SUCCESS == status) {
            updateApplySuccessStatus(apply, operator, info);
        } else if (ActivityConstants.EFFECT == status) {
            updateEffectStatus(apply, operator, info);
        } else if (ActivityConstants.CANCEL == status) {
            updateExpirationStatus(apply, operator, info);
        } else if (ActivityConstants.EFFECT_NOT_STANDARD == status) {
            updateEffectNotStandardStatus(apply, operator, info);
        } else {
            List<ApplyProcessInfo> process = buildParams.buildProcess(apply, status, operator, info);
            ActivityApplyEntity update = new ActivityApplyEntity();
            update.setId(apply.getId());
            update.setStatus(status);
            update.setProcess(JSONObject.toJSONString(process));
            activityApplyRepository.updateByPrimaryKeySelective(update);
        }
    }

    /**
     * 增加操作记录
     * @param apply
     * @param operator
     * @param info
     */
    public void updateApplyPayWays(ActivityApplyEntity apply, Map<String, String> feeRateMap, String operator, String info) {
        List<ApplyProcessInfo> process = buildParams.buildProcess(apply, apply.getStatus(), operator, info);
        ActivityApplyEntity update = new ActivityApplyEntity();
        update.setId(apply.getId());
        update.setFee_rate(JsonUtil.encode(feeRateMap));
        update.setPayway(JsonUtil.encode(feeRateMap.keySet()));
        update.setProcess(JsonUtil.encode(process));
        activityApplyRepository.updateByPrimaryKeySelective(update);
    }

    public void removeRiskFlag(ActivityApplyEntity applyDO) {
        ApplyExtraParam extra = applyDO.buildJsonExtra();
        if (Objects.isNull(extra)) {
            return;
        }
        if (!Objects.equals(extra.getOccurSource(), CancelActivityApplyRequest.OCCUR_SOURCE_RISK)) {
            return;
        }
        extra.setOccurSource(null);
        ActivityApplyEntity update = new ActivityApplyEntity();
        update.setId(applyDO.getId());
        update.setExtra(JSONObject.toJSONString(extra));
        activityApplyRepository.updateByPrimaryKeySelective(update);
    }

    /**
     * 更新报名成功状态
     *
     * @param apply
     * @param info
     */
    public void updateApplySuccessStatus(ActivityApplyEntity apply, String operator, String info) {
        Date now = new Date();
        ActivityApplyEntity update = new ActivityApplyEntity();
        update.setId(apply.getId());
        update.setStatus(ActivityConstants.APPLY_SUCCESS);
        update.setApply_success_time(now);
        update.setExtra(apply.getExtra());
        if (providerFeeRateSyncBiz.isDelaySyncFeeRateToProvider(apply.getAuditTemplateId())) {
            //延迟生效，生效时间为次日凌晨0:00
            log.info("同步费率给通道: 次日凌晨生效, applyId={}, auditTemplateId={}, activityId={}, merchantSn={}",
                    apply.getId(), apply.getAuditTemplateId(), apply.getActivity_id(), apply.getMerchant_sn());
            update.setEffective_time(DateTimeUtils.getTheTimeOfTomorrowAt0AM());
        } else {
            log.info("同步费率给通道: 立即生效, applyId={}, auditTemplateId={}, activityId={}, merchantSn={}",
                    apply.getId(), apply.getAuditTemplateId(), apply.getActivity_id(), apply.getMerchant_sn());
            Date effectTime = getEffectTimeIfOnlyEffectWithTimeWhenApplySuccess(apply.buildActivityEffectiveRule());
            if (effectTime != null) {
                update.setEffective_time(effectTime);
            }
        }
        Date expireTime = getExpireTimeIfOnlyExpireWithTimeWhenApplySuccess(apply.buildActivityExpirationRule());
        if (expireTime != null) {
            update.setExpiration_time(expireTime);
        }
        List<ApplyProcessInfo> process = buildParams.buildProcess(apply, ActivityConstants.APPLY_SUCCESS, operator, info);
        update.setProcess(JSONObject.toJSONString(process));
        activityApplyRepository.updateByPrimaryKeySelective(update);
    }

    /**
     * 更新生效状态
     *
     * @param apply
     * @param info
     */
    private void updateEffectStatus(ActivityApplyEntity apply, String operator, String info) {
        Date now = new Date();
        // 同步更新 计算过期时间时需要用
        apply.setEffective_time(now);
        Date expireTime = apply.fetchExpirationTime();

        ActivityApplyEntity update = new ActivityApplyEntity();
        update.setId(apply.getId());
        update.setStatus(ActivityConstants.EFFECT);
        update.setEffective_time(now);
        update.setCombo_id(apply.getCombo_id());
        update.setActivity_sub_status_id(apply.getActivity_sub_status_id());
        update.setFee_rate(apply.getFee_rate());
        update.setPayway(apply.getPayway());
        update.setExpiration_time(expireTime); //设置失效时间
        List<ApplyProcessInfo> process = buildParams.buildProcess(apply, ActivityConstants.EFFECT, operator, info);
        update.setProcess(JSONObject.toJSONString(process));
        activityApplyRepository.updateByPrimaryKeySelective(update);
    }

    /**
     * 更新下一次考核时间
     *
     * @param applyEntity
     * @return
     */
    public void updateNextAssessmentTime(ActivityApplyEntity applyEntity, boolean isFirstTakeEffect) {
        if (!applyEntity.isTradeAssessment()) {
            return;
        }
        // 计算并更新下一次考核时间
        Date nextAssessmentTime = calcNextAssessmentTime(applyEntity, isFirstTakeEffect);
        ActivityApplyEntity update = new ActivityApplyEntity();
        update.setId(applyEntity.getId());
        update.setAssessment_time(nextAssessmentTime);
        update.setMerchant_sn(applyEntity.getMerchant_sn());
        activityApplyRepository.updateAssessmentTime(update);
    }

    /**
     * 更新失效状态
     *
     * @param apply
     * @param info
     */
    private void updateExpirationStatus(ActivityApplyEntity apply, String operator, String info) {
        ActivityApplyEntity update = new ActivityApplyEntity();
        update.setId(apply.getId());
        update.setStatus(ActivityConstants.CANCEL);
        update.setExpiration_time(new Date());
        update.setExtra(apply.getExtra());
        List<ApplyProcessInfo> process = buildParams.buildProcess(apply, ActivityConstants.CANCEL, operator, info);
        update.setProcess(JSONObject.toJSONString(process));
        activityApplyRepository.updateByPrimaryKeySelective(update);

        //删除缓存 商户可用额度返回值
        cacheService.delQuotaResponse(apply.getMerchant_id());
    }

    /**
     * 更新为不达标状态
     *
     * @param apply
     * @param operator
     * @param info
     */
    private void updateEffectNotStandardStatus(ActivityApplyEntity apply, String operator, String info) {
        ActivityApplyEntity update = new ActivityApplyEntity();
        update.setId(apply.getId());
        update.setStatus(ActivityConstants.EFFECT_NOT_STANDARD);
        update.setExtra(apply.getExtra());
        List<ApplyProcessInfo> process = buildParams.buildProcess(apply, ActivityConstants.EFFECT_NOT_STANDARD, operator, info);
        update.setProcess(JSONObject.toJSONString(process));
        activityApplyRepository.updateByPrimaryKeySelective(update);
    }

    /**
     * 不达标转为生效中
     *
     * @param apply
     * @param operator
     * @param info
     */
    public void updateEffectNotStandardEffectStatus(ActivityApplyEntity apply, String operator, String info) {
        ActivityApplyEntity update = new ActivityApplyEntity();
        update.setId(apply.getId());
        update.setStatus(ActivityConstants.EFFECT);
        List<ApplyProcessInfo> process = buildParams.buildProcess(apply, ActivityConstants.EFFECT, operator, info);
        update.setProcess(JSONObject.toJSONString(process));
        activityApplyRepository.updateByPrimaryKeySelective(update);
    }

    /**
     * 活动生效规则校验
     *
     * @param activityApplyEntity
     * @return true 达到生效规则 false没有达到生效规则
     */
    public boolean checkEffectiveRule(ActivityApplyEntity activityApplyEntity) {
        Date effectTime = activityApplyEntity.getEffective_time();
        return effectTime.before(new Date());
    }

    /**
     * 校验是否满足失效条件
     * @param applyEntity
     * @return true 达到失效规则 false没有达到失效规则
     */
    public boolean checkExpireRule(ActivityApplyEntity applyEntity) {
        Date expirationTime = applyEntity.fetchExpirationTime();
        if (Objects.isNull(expirationTime)) {
            log.info("活动申请记录不存在失效时间. applyId={}, activityId={}, merchantSn={}",
                    applyEntity.getId(), applyEntity.getActivity_id(), applyEntity.getMerchant_sn());
            return false;
        }
        boolean check = expirationTime.before(new Date());
        if (check) {
            log.info("活动申请记录达到失效时间. applyId={}, activityId={}, merchantSn={}, expirationTime={}",
                    applyEntity.getId(), applyEntity.getActivity_id(), applyEntity.getMerchant_sn(),
                    LocalDateTimeUtil.getFormatDateTime(expirationTime));
        } else {
            log.info("活动申请记录未达到失效时间. applyId={}, activityId={}, merchantSn={}, expirationTime={}",
                    applyEntity.getId(), applyEntity.getActivity_id(), applyEntity.getMerchant_sn(),
                    LocalDateTimeUtil.getFormatDateTime(expirationTime));
        }
        return check;
    }

    /**
     * 申请成功时，当只有时间生效这一个规则时，返回生效时间
     * @param rule
     * @return
     */
    public Date getEffectTimeIfOnlyEffectWithTimeWhenApplySuccess(ActivityEffectiveRule rule) {
        if (Objects.nonNull(rule) && rule.getByTimeRule() != null) {
            Date effectDate = null;
            if (rule.getByTimeRule().getFixedTime() != null) {
                effectDate = new Date(rule.getByTimeRule().getFixedTime());
            }
            return effectDate;
        }
        return null;
    }


    /**
     * 申请成功时，当只有时间失效这一个规则时，返回失效时间
     * @param rule
     * @return
     */
    public Date getExpireTimeIfOnlyExpireWithTimeWhenApplySuccess(ActivityExpirationRule rule) {
        if (Objects.nonNull(rule) && rule.getByTimeRule() != null) {
            Date effectDate = null;
            if (rule.getByTimeRule().getFixedTime() != null) {
                effectDate = new Date(rule.getByTimeRule().getFixedTime());
            }
            return effectDate;
        }
        return null;
    }

    /**
     * 获取进行中的活动申请记录
     *
     * @param merchantSn
     * @param payWay
     * @return
     */
    public ActivityApplyEntity getProcessingApply(String merchantSn, Integer payWay) {
        List<ActivityApplyEntity> processingList = applyMapper.queryByActivityAndStatusList(merchantSn, null, ActivityConstants.PROCESSING_STATUS);
        if (CollectionUtils.isEmpty(processingList)) {
            return null;
        }
        //活动->套餐->payWay
        List<ActivityApplyEntity> applyEntities = processingList.stream().filter(entity -> {
            CacheActivityInfo activity = cacheActivity.getById(entity.getActivity_id());
            int count = feeRateRepository.countEffectFeeRate(entity.getSn(), entity.getCombo_id(),
                    ComboConfigLevelEnum.valueOf(StringUtils.upperCase(activity.getConfigLevel())), ImmutableList.of(payWay));
            return count > NumberUtils.INTEGER_ZERO;
        }).collect(Collectors.toList());
        return CollectionUtils.isEmpty(applyEntities) ? null : applyEntities.get(0);
    }


    /**
     * 计算下一次考核时间
     *
     * @return
     */
    public Date calcNextAssessmentTime(ActivityApplyEntity applyEntity, boolean isFirstTakeEffect) {
        TradeAssessmentRule rule = applyEntity.buildTradeAssessmentRule();
        if (rule == null || rule.getTradeAssessmentType() == null) {
            return null;
        }
        if (Objects.isNull(rule.getCycle())) {
            TradeManageBizException.createExc("活动考核周期不能为空");
        }
        LocalDateTime assessmentTime = LocalDateTimeUtil.valueOf(applyEntity.getAssessment_time());
        if (isFirstTakeEffect) {
            log.info("[下一次交易考核时间计算]首次报名生效，时间取下月。 applyId={},activityId={}", applyEntity.getId(), applyEntity.getActivity_id());
            //计算至下一月
            assessmentTime = LocalDateTime.now().plusMonths(NumberUtils.LONG_ONE);
            // 更新生效时间 防止上层未设置生效时间
            applyEntity.setEffective_time(new Date());
        }
        if (Objects.isNull(assessmentTime)) {
            throw TradeManageBizException.createExc("活动申请记录考核时间为空.");
        }
        // 下一次考核时间
        Date nextAssessmentTime = LocalDateTimeUtil.toDate(LocalDateTimeUtil.getStartOfMonth(assessmentTime.plusMonths(1)));
        // 获取失效时间
        Date expireTime = applyEntity.fetchExpirationTime();
        // 如果下一次考核时间大于等于失效时间，则返回null
        if (nextAssessmentTime.getTime() >= expireTime.getTime()) {
            log.info("[下一次交易考核时间计算]下一次考核时间大于等于失效时间，返回null. applyId={},activityId={},考核方式={},当前考核时间={},计算得出下一次考核时间={},过期时间={}",
                    applyEntity.getId(),
                    applyEntity.getActivity_id(),
                    rule.getTradeAssessmentType(),
                    LocalDateTimeUtil.getFormatDateTime(assessmentTime),
                    LocalDateTimeUtil.getFormatDateTime(nextAssessmentTime),
                    LocalDateTimeUtil.getFormatDateTime(expireTime));
            return null;
        }
        log.info("[下一次交易考核时间计算]计算下一次考核时间. applyId={},activityId={},考核方式={},当前考核时间={},计算得出下一次考核时间={},过期时间={}",
                applyEntity.getId(),
                applyEntity.getActivity_id(),
                rule.getTradeAssessmentType(),
                LocalDateTimeUtil.getFormatDateTime(assessmentTime),
                LocalDateTimeUtil.getFormatDateTime(nextAssessmentTime),
                LocalDateTimeUtil.getFormatDateTime(expireTime));
        return nextAssessmentTime;
    }
}

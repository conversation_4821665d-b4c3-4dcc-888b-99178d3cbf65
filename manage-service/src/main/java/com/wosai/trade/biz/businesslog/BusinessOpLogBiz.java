package com.wosai.trade.biz.businesslog;

import com.wosai.data.bean.BeanUtil;
import com.wosai.middleware.hera.toolkit.trace.TraceContext;
import com.wosai.pay.common.base.log.OperationLogRequest;
import com.wosai.sp.business.logstash.dto.PlatformEnum;
import com.wosai.sp.business.logstash.dto.ValidList;
import com.wosai.sp.business.logstash.dto.req.BsOpLogCreateReqDto;
import com.wosai.sp.business.logstash.service.BusinessOpLogService;
import com.wosai.trade.util.ConstantUtil;
import com.wosai.upay.core.model.MerchantConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description: 商户配置业务日志配置
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/3/6
 */
@Slf4j
@Component
public class BusinessOpLogBiz {

    @Resource
    private BusinessOpLogService businessOpLogService;
    /**
     * 多层分隔符
     */
    public static final String LAYER_DELIMITER_ORI = ".";
    /**
     * 多层分隔业务日志替换符
     */
    public static final String LAYER_DELIMITER_TARGET = "-";

    public String joinMerchantConfigFiledName(String filedName) {
        //这一步要做替换，原因是新版业务日志侧不再支持点分隔符
        return "merchant_config#" + StringUtils.replace(filedName, LAYER_DELIMITER_ORI, LAYER_DELIMITER_TARGET);
    }

    /**
     * 创建merchantConfig费率修改业务日志
     *
     * @param merchantId
     * @param before
     * @param after
     * @param remark
     * @param opUserId
     * @param opUserName
     * @param platform
     */
    public void sendFeeRateMerchantConfigBusinessLog(String merchantId, String platform, String opUserId, String opUserName,
                                                     String remark, Map<String, Object> before, Map<String, Object> after) {
        ValidList<BsOpLogCreateReqDto.Diff> diffValidList = buildDiffMerchantConfig(before, after);
        createBusinessLogForAsync(TemplateCodeEnum.FEE_RATE_LOG.getCode(), merchantId, diffValidList,
                String.format("%s#%s", TemplateCodeEnum.FEE_RATE_LOG.getTableName(), MerchantConfig.PAYWAY),
                remark, opUserId, opUserName, convertPlatformEnum(platform));
    }

    /**
     * 创建merchantConfig交易权限开通日志
     *
     * @param merchantId
     * @param before
     * @param after
     * @param remark
     * @param opUserId
     * @param opUserName
     * @param platform
     */
    public void sendOpenMerchantPayBusinessLog(String merchantId, String platform, String opUserId, String opUserName,
                                                     String remark, Map<String, Object> before, Map<String, Object> after) {
        ValidList<BsOpLogCreateReqDto.Diff> diffValidList = buildValidList(TemplateCodeEnum.OPEN_MERCHANT_PAY, before, after);
        createBusinessLogForAsync(TemplateCodeEnum.OPEN_MERCHANT_PAY.getCode(), merchantId, diffValidList,
                String.format("%s#%s", TemplateCodeEnum.OPEN_MERCHANT_PAY.getTableName(), TemplateCodeEnum.OPEN_MERCHANT_PAY.getFiledNameList().get(0)),
                remark, opUserId, opUserName, convertPlatformEnum(platform));
    }

    /**
     * 创建merchantConfig自定义对账周期d0日志
     *
     * @param merchantId
     * @param before
     * @param after
     * @param remark
     * @param opUserId
     * @param opUserName
     * @param platform
     */
    public void sendBankCooperationD0BusinessLog(String merchantId, String platform, String opUserId, String opUserName,
                                               String remark, Map<String, Object> before, Map<String, Object> after) {
        ValidList<BsOpLogCreateReqDto.Diff> diffValidList = buildValidList(TemplateCodeEnum.D0_CONFIIG, before, after);
        createBusinessLogForAsync(TemplateCodeEnum.D0_CONFIIG.getCode(), merchantId, diffValidList,
                String.format("%s#%s", TemplateCodeEnum.D0_CONFIIG.getTableName(), TemplateCodeEnum.D0_CONFIIG.getFiledNameList().get(0)),
                remark, opUserId, opUserName, convertPlatformEnum(platform));
    }

    /**
     * 创建merchantConfig交易通道切换日志
     *
     * @param merchantId
     * @param before
     * @param after
     * @param remark
     * @param opUserId
     * @param opUserName
     * @param platform
     */
    public void sendMerchantConfigBypassSummaryQueryLog(String merchantId, String platform, String opUserId, String opUserName,
                                                        String remark, Map<String, Object> before, Map<String, Object> after) {
        ValidList<BsOpLogCreateReqDto.Diff> diffValidList = buildValidList(TemplateCodeEnum.BYPASS_SUMMARY_QUERY, before, after);
        createBusinessLogForAsync(TemplateCodeEnum.BYPASS_SUMMARY_QUERY.getCode(), merchantId, diffValidList,
                String.format("%s#%s", TemplateCodeEnum.BYPASS_SUMMARY_QUERY.getTableName(), TemplateCodeEnum.BYPASS_SUMMARY_QUERY.getFiledNameList().get(0)),
                remark, opUserId, opUserName, convertPlatformEnum(platform));
    }

    /**
     * 取消额度发送日志
     * @param traceId
     * @param merchantId
     * @param platform
     * @param opUserId
     * @param opUserName
     * @param remark
     * @param before
     * @param after
     */
    public void sendCancelQuotaLog(String traceId, String merchantId, String platform, String opUserId, String opUserName,
                                      String remark, Map<String, Object> before, Map<String, Object> after) {
        ValidList<BsOpLogCreateReqDto.Diff> diffValidList = buildValidListV2(TemplateCodeEnum.CANCEL_QUOTA_CONFIG, before, after);
        createOuterBusinessLogForAsync(traceId, TemplateCodeEnum.CANCEL_QUOTA_CONFIG.getCode(), merchantId, null, diffValidList,
                String.format("%s#%s", TemplateCodeEnum.CANCEL_QUOTA_CONFIG.getTableName(), TemplateCodeEnum.CANCEL_QUOTA_CONFIG.getFiledNameList().get(0)),
                remark, opUserId, opUserName, convertPlatformEnum(platform));
    }


    /**
     * 创建添加固定额度业务日志
     * @param traceId
     * @param merchantId
     * @param before
     * @param after
     * @param remark
     * @param opUserId
     * @param opUserName
     * @param platform
     */
    public void sendAddFixedQuotaLog(String traceId, String merchantId, String platform, String opUserId, String opUserName,
                                     String remark, Map<String, Object> before, Map<String, Object> after) {
        ValidList<BsOpLogCreateReqDto.Diff> diffValidList = buildValidListV2(TemplateCodeEnum.ADD_FIXED_QUOTA_CONFIG, before, after);
        createOuterBusinessLogForAsync(traceId, TemplateCodeEnum.ADD_FIXED_QUOTA_CONFIG.getCode(), merchantId, null, diffValidList,
                String.format("%s#%s", TemplateCodeEnum.ADD_FIXED_QUOTA_CONFIG.getTableName(), TemplateCodeEnum.ADD_FIXED_QUOTA_CONFIG.getFiledNameList().get(0)),
                remark, opUserId, opUserName, convertPlatformEnum(platform));
    }


    /**
     * 修改固定额度业务日志
     *
     * @param traceId
     * @param merchantId
     * @param before
     * @param after
     * @param remark
     * @param opUserId
     * @param opUserName
     * @param platform
     */
    public void sendModifyFixedQuotaLog(String traceId, String merchantId, String platform, String opUserId, String opUserName,
                                        String remark, Map<String, Object> before, Map<String, Object> after) {
        ValidList<BsOpLogCreateReqDto.Diff> diffValidList = buildValidListV2(TemplateCodeEnum.MODIFY_FIXED_QUOTA_CONFIG, before, after);
        createOuterBusinessLogForAsync(traceId, TemplateCodeEnum.MODIFY_FIXED_QUOTA_CONFIG.getCode(), merchantId, null, diffValidList,
                String.format("%s#%s", TemplateCodeEnum.MODIFY_FIXED_QUOTA_CONFIG.getTableName(), TemplateCodeEnum.MODIFY_FIXED_QUOTA_CONFIG.getFiledNameList().get(0)),
                remark, opUserId, opUserName, convertPlatformEnum(platform));
    }


    /**
     * 修改商户间连和余额信息
     *
     * @param traceId
     * @param merchantId
     * @param before
     * @param after
     * @param remark
     * @param opUserId
     * @param opUserName
     * @param platform
     */
    public void sendUpdateMerchantConfigBusinessLog(String traceId, String merchantId, String platform, String opUserId, String opUserName,
                                                    String remark, Map<String, Object> before, Map<String, Object> after) {
        ValidList<BsOpLogCreateReqDto.Diff> diffValidList = buildValidListV2(TemplateCodeEnum.UPDATE_MERCHANT_CONFIG, before, after);
        createOuterBusinessLogForAsync(traceId, TemplateCodeEnum.UPDATE_MERCHANT_CONFIG.getCode(), merchantId, null, diffValidList,
                String.format("%s#%s", TemplateCodeEnum.UPDATE_MERCHANT_CONFIG.getTableName(), TemplateCodeEnum.UPDATE_MERCHANT_CONFIG.getFiledNameList().get(0)),
                remark, opUserId, opUserName, convertPlatformEnum(platform));
    }


    /**
     * 修改门店间连状态
     *
     * @param traceId
     * @param storeId
     * @param before
     * @param after
     * @param remark
     * @param opUserId
     * @param opUserName
     * @param platform
     */
    public void sendUpdateStoreConfigBusinessLog(String traceId, String storeId, String merchantId, String platform, String opUserId, String opUserName,
                                                 String remark, Map<String, Object> before, Map<String, Object> after) {
        ValidList<BsOpLogCreateReqDto.Diff> diffValidList = buildValidListV2(TemplateCodeEnum.UPDATE_STORE_CONFIG, before, after);
        createOuterBusinessLogForAsync(traceId, TemplateCodeEnum.UPDATE_STORE_CONFIG.getCode(), storeId, merchantId, diffValidList,
                String.format("%s#%s", TemplateCodeEnum.UPDATE_STORE_CONFIG.getTableName(), TemplateCodeEnum.UPDATE_STORE_CONFIG.getFiledNameList().get(0)),
                remark, opUserId, opUserName, convertPlatformEnum(platform));
    }


    /**
     * 修改教培状态推送日志
     *
     * @param traceId
     * @param merchantId
     * @param before
     * @param after
     * @param remark
     * @param opUserId
     * @param opUserName
     * @param platform
     */
    public void sendWxPayActivityConfigLog(String traceId, String merchantId, String platform, String opUserId, String opUserName,
                                           String remark, Map<String, Object> before, Map<String, Object> after) {
        ValidList<BsOpLogCreateReqDto.Diff> diffValidList = buildValidListV2(TemplateCodeEnum.WXPAY_EDU_ACTIVITY_CONFIG, before, after);
        createOuterBusinessLogForAsync(traceId, TemplateCodeEnum.WXPAY_EDU_ACTIVITY_CONFIG.getCode(), merchantId, null, diffValidList,
                String.format("%s#%s", TemplateCodeEnum.WXPAY_EDU_ACTIVITY_CONFIG.getTableName(), TemplateCodeEnum.WXPAY_EDU_ACTIVITY_CONFIG.getFiledNameList().get(0)),
                remark, opUserId, opUserName, convertPlatformEnum(platform));
    }


    /**
     * 发送活动取消恢复日志
     *
     * @param traceId
     * @param merchantId
     * @param before
     * @param after
     * @param remark
     * @param opUserId
     * @param opUserName
     * @param platform
     */
    public void sendActivityLog(String traceId, String merchantId, String platform, String opUserId, String opUserName,
                                String remark, Map<String, Object> before, Map<String, Object> after) {
        ValidList<BsOpLogCreateReqDto.Diff> diffValidList = buildValidListV2(TemplateCodeEnum.APPLY_ACTIVITY_CONFIG, before, after);
        createOuterBusinessLogForAsync(traceId, TemplateCodeEnum.APPLY_ACTIVITY_CONFIG.getCode(), merchantId, null, diffValidList,
                String.format("%s#%s", TemplateCodeEnum.APPLY_ACTIVITY_CONFIG.getTableName(), TemplateCodeEnum.APPLY_ACTIVITY_CONFIG.getFiledNameList().get(0)),
                remark, opUserId, opUserName, convertPlatformEnum(platform));
    }

    /**
     * 发送APPBSC限额日志
     *
     * @param traceId
     * @param merchantId
     * @param before
     * @param after
     * @param remark
     * @param opUserId
     * @param opUserName
     * @param platform
     */
    public void sendAppBSCLimitLog(String traceId, String merchantId, String platform, String opUserId, String opUserName,
                                   String remark, Map<String, Object> before, Map<String, Object> after) {
        ValidList<BsOpLogCreateReqDto.Diff> diffValidList = buildValidListV2(TemplateCodeEnum.APPLY_BSC_LIMIT_CONFIG, before, after);
        createOuterBusinessLogForAsync(traceId, TemplateCodeEnum.APPLY_BSC_LIMIT_CONFIG.getCode(), merchantId, null, diffValidList,
                String.format("%s#%s", TemplateCodeEnum.APPLY_BSC_LIMIT_CONFIG.getTableName(), TemplateCodeEnum.APPLY_BSC_LIMIT_CONFIG.getFiledNameList().get(0)),
                remark, opUserId, opUserName, convertPlatformEnum(platform));
    }


    /**
     * 发送临时额度日志
     *
     * @param traceId
     * @param merchantId
     * @param before
     * @param after
     * @param remark
     * @param opUserId
     * @param opUserName
     * @param platform
     */
    public void sendTempQuotaLog(String traceId, String merchantId, String platform, String opUserId, String opUserName,
                                 String remark, Map<String, Object> before, Map<String, Object> after) {
        ValidList<BsOpLogCreateReqDto.Diff> diffValidList = buildValidListV2(TemplateCodeEnum.TEMP_QUOTA_LOG_CONFIG, before, after);
        createOuterBusinessLogForAsync(traceId, TemplateCodeEnum.TEMP_QUOTA_LOG_CONFIG.getCode(), merchantId, null, diffValidList,
                String.format("%s#%s", TemplateCodeEnum.TEMP_QUOTA_LOG_CONFIG.getTableName(), TemplateCodeEnum.TEMP_QUOTA_LOG_CONFIG.getFiledNameList().get(0)),
                remark, opUserId, opUserName, convertPlatformEnum(platform));
    }




    /**
     * 发送CRM商户暂存日志
     *
     * @param merchantId
     * @param platform
     * @param operator
     * @param operatorName
     * @param before
     * @param after
     */
    public void sendExchangeBusinessLog(String merchantId, String platform, String operator, String operatorName,
                                        Map<String, Object> before, Map<String, Object> after) {
        ValidList<BsOpLogCreateReqDto.Diff> diffValidList = buildValidList(TemplateCodeEnum.CRM_FEE_RATES, before, after);
        createBusinessLogForAsync(TemplateCodeEnum.CRM_FEE_RATES.getCode(), merchantId, diffValidList,
                String.format("%s#%s", TemplateCodeEnum.CRM_FEE_RATES.getTableName(), MerchantConfig.PAYWAY),
                "商户暂存日志", operator, operatorName, convertPlatformEnum(platform));
    }

    /**
     * 修改云闪付阶梯费率设置
     *
     * @param merchantId
     * @param before
     * @param after
     * @param remark
     * @param opUserId
     * @param opUserName
     */
    public void sendSettingsUnionLadderFeeRatesBusinessLog(String merchantId, String opUserId, String opUserName,
                                                           String remark, Map<String, Object> before, Map<String, Object> after) {
        ValidList<BsOpLogCreateReqDto.Diff> diffValidList = buildValidList(TemplateCodeEnum.SETTINGS_UNION_LADDER_FEE_RATES, before, after);
        createBusinessLogForAsync(TemplateCodeEnum.SETTINGS_UNION_LADDER_FEE_RATES.getCode(), merchantId, diffValidList, null,
                remark, opUserId, opUserName, PlatformEnum.SPA);
    }

    /**
     * 自定义D0权益
     *
     * @param merchantId
     * @param operator
     * @param operatorName
     * @param before
     * @param after
     */
    public void sendD0WithdrawHandleBusinessLog(String merchantId, String operator, String operatorName,
                                                Map<String, Object> before, Map<String, Object> after) {
        ValidList<BsOpLogCreateReqDto.Diff> diffValidList = buildValidList(TemplateCodeEnum.D0_WITHDRAW, before, after);
        createBusinessLogForAsync(TemplateCodeEnum.D0_WITHDRAW.getCode(), merchantId, diffValidList, null,
                "商户换绑银行卡，取消d0提现，转为自动提现", operator, operatorName, PlatformEnum.SPA);
    }

    /**
     * SAAS
     *
     * @param merchantId
     * @param operator
     * @param operatorName
     * @param before
     * @param after
     */
    public void sendSaasBusinessLog(String merchantId, String platform, String operator, String operatorName, String remark,
                                    Map<String, Object> before, Map<String, Object> after) {
        ValidList<BsOpLogCreateReqDto.Diff> diffValidList = buildValidList(TemplateCodeEnum.SAAS, before, after);
        createBusinessLogForAsync(TemplateCodeEnum.SAAS.getCode(), merchantId, diffValidList,
                String.format("%s#feeId", TemplateCodeEnum.SAAS.getTableName()),
                remark, operator, operatorName, convertPlatformEnum(platform));
    }

    /**
     * 发送通用业务日志
     *
     * @param merchantId
     * @param operator
     * @param operatorName
     * @param before
     * @param after
     */
    public void sendByTemplateCodeBusinessLog(TemplateCodeEnum templateCode, String merchantId, String operator, String operatorName, String remark,
                                               Map<String, Object> before, Map<String, Object> after) {
        ValidList<BsOpLogCreateReqDto.Diff> diffValidList = buildValidList(templateCode, before, after);
        createBusinessLogForAsync(templateCode.getCode(), merchantId, diffValidList, null,
                remark, operator, operatorName, PlatformEnum.SPA);
    }

    /**
     * 创建业务日志
     *
     * @param logTemplateCode 模块CODE
     * @param opObjectId      操作对象
     * @param diffValidList
     * @param remark
     * @param opUserId
     * @param opUserName
     * @link https://sqb.feishu.cn/wiki/ST0KwOl6kilNIqkhfjdcuiZunOc
     */
    public void createBusinessLogForAsync(String logTemplateCode, String opObjectId,
                                          ValidList<BsOpLogCreateReqDto.Diff> diffValidList,
                                          String sameSaveColumnCode,
                                          String remark, String opUserId, String opUserName, PlatformEnum platform) {
        try {
            createOuterBusinessLogForAsync(TraceContext.traceId(), logTemplateCode, opObjectId, null, diffValidList, sameSaveColumnCode, remark, opUserId, opUserName, platform);
        } catch (Exception e) {
            log.error("createBusinessLogForAsync failed. opObjectId:{}", opObjectId, e);
        }
    }

    /**
     * 构建merchantConfig差异数据上送列表
     *
     * @param before 　更新前
     * @param after  　更新后
     * @return
     */
    private ValidList<BsOpLogCreateReqDto.Diff> buildDiffMerchantConfig(Map<String, Object> before, Map<String, Object> after) {
        List<BsOpLogCreateReqDto.Diff> diffValidList = TemplateCodeEnum.FEE_RATE_LOG.getFiledNameList().stream().map(filedName -> {
            String valueBefore = BeanUtil.getPropString(before, filedName);
            String valueAfter = BeanUtil.getPropString(after, filedName);
            return BsOpLogCreateReqDto.Diff.builder()
                    .columnCode(joinMerchantConfigFiledName(filedName))
                    .valueBefore(valueBefore)
                    .valueAfter(valueAfter)
                    .build();
        }).collect(Collectors.toList());
        return ValidList.createInstance(diffValidList);
    }

    /**
     * 构建差异列表
     *
     * @param before 　更新前
     * @param after  　更新后
     * @return
     */
    public ValidList<BsOpLogCreateReqDto.Diff> buildValidList(TemplateCodeEnum templateCodeEnum, Map<String, Object> before, Map<String, Object> after) {
        List<BsOpLogCreateReqDto.Diff> diffValidList = templateCodeEnum.getFiledNameList().stream().map(filedName -> {
            String valueBefore = BeanUtil.getPropString(before, filedName);
            String valueAfter = BeanUtil.getPropString(after, filedName);
            return BsOpLogCreateReqDto.Diff.builder()
                    .columnCode(String.format("%s#%s", templateCodeEnum.getTableName(), filedName))
                    .valueBefore(valueBefore)
                    .valueAfter(valueAfter)
                    .build();
        }).collect(Collectors.toList());
        return ValidList.createInstance(diffValidList);
    }

    /**
     * 构建差异列表
     *
     * @param before 　更新前
     * @param after  　更新后
     * @return
     */
    private ValidList<BsOpLogCreateReqDto.Diff> buildValidListV2(TemplateCodeEnum templateCodeEnum, Map<String, Object> before, Map<String, Object> after) {
        List<BsOpLogCreateReqDto.Diff> diffValidList = new ArrayList<>();
        for (String filedName : templateCodeEnum.getFiledNameList()) {
            String valueBefore = BeanUtil.getPropString(before, filedName);
            String valueAfter = BeanUtil.getPropString(after, filedName);
            if (!Objects.equals(valueBefore, valueAfter)) {
                BsOpLogCreateReqDto.Diff build = BsOpLogCreateReqDto.Diff.builder()
                        .columnCode(String.format("%s#%s", templateCodeEnum.getTableName(), filedName))
                        .valueBefore(valueBefore)
                        .valueAfter(valueAfter)
                        .build();
                diffValidList.add(build);
            }
        }
        return ValidList.createInstance(diffValidList);
    }

    private PlatformEnum convertPlatformEnum(String platform) {
        try {
            return PlatformEnum.valueOf(platform);
        } catch (Exception e) {
            return PlatformEnum.SPA;
        }
    }

    /**
     * 创建外部业务日志
     *
     * @param logRequest
     * @param diffValidList
     */
    public void createOuterBusinessLogForAsync(OperationLogRequest logRequest, ValidList<BsOpLogCreateReqDto.Diff> diffValidList, String sameSaveColumnCode) {
        String traceId = StringUtils.isBlank(logRequest.getExternalSceneTraceId()) ? TraceContext.traceId() : logRequest.getExternalSceneTraceId();
        createOuterBusinessLogForAsync(
                traceId,
                logRequest.getSceneTemplateCode(),
                logRequest.getOpObjectId(),
                logRequest.getRootObjectId(),
                diffValidList,
                sameSaveColumnCode,
                logRequest.getRemark(),
                logRequest.getOperatorUserId(),
                logRequest.getOperatorUserName(),
                logRequest.getPlatformCode()
        );
    }

    /**
     * 创建外部业务日志
     */
    public void createOuterBusinessLogForAsync(String traceId, String logTemplateCode, String opObjectId, String rootObjectId,
                                               ValidList<BsOpLogCreateReqDto.Diff> diffValidList,
                                               String sameSaveColumnCode,
                                               String remark, String opUserId, String opUserName, PlatformEnum platform) {
        String platformCode = Objects.isNull(platform) ? PlatformEnum.SPA.getCode() : platform.getCode();
        createOuterBusinessLogForAsync(traceId, logTemplateCode, opObjectId, rootObjectId, diffValidList, sameSaveColumnCode,
                remark, opUserId, opUserName, platformCode);
    }

    /**
     * 创建外部业务日志
     *
     * @param logTemplateCode 模块CODE
     * @param opObjectId      操作对象
     * @param diffValidList
     * @param remark
     * @param opUserId
     * @param opUserName
     * @link https://sqb.feishu.cn/wiki/ST0KwOl6kilNIqkhfjdcuiZunOc
     */
    private void createOuterBusinessLogForAsync(String traceId, String logTemplateCode, String opObjectId, String rootObjectId,
                                                ValidList<BsOpLogCreateReqDto.Diff> diffValidList,
                                                String sameSaveColumnCode,
                                                String remark, String opUserId, String opUserName, String platformCode) {
        try {
            BsOpLogCreateReqDto reqDto = BsOpLogCreateReqDto.builder()
                    .outerSceneTraceId(StringUtils.isBlank(traceId) ? TraceContext.traceId() : traceId)
                    .logTemplateCode(logTemplateCode)
                    .opObjectId(opObjectId)
                    .rootObjectId(rootObjectId)
                    .opUserId(StringUtils.defaultIfEmpty(opUserId, ConstantUtil.SYSTEM_NAME))
                    .opUserName(StringUtils.defaultIfEmpty(opUserName, ConstantUtil.SYSTEM_NAME))
                    .remark(remark)
                    .sameSaveColumnCode(sameSaveColumnCode)
                    .platformCode(StringUtils.isNotEmpty(platformCode) ? platformCode : PlatformEnum.SPA.getCode())
                    .diffList(diffValidList)
                    .build();
            businessOpLogService.createBusinessLogForAsync(reqDto);
        } catch (Exception e) {
            log.error("createOuterBusinessLogForAsync failed. opObjectId:{},traceId:{}", opObjectId, traceId, e);
        }
    }
}
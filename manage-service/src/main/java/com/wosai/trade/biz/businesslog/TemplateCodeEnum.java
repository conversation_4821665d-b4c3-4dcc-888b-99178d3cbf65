package com.wosai.trade.biz.businesslog;

import com.wosai.trade.constant.BusinessLogConstant;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public enum TemplateCodeEnum {
    // 费率日志模板log code
    FEE_RATE_LOG("UOU0Q6F31M0A", "merchant_config", Arrays.asList(
            "b2c_fee_rate",
            "mini_fee_rate",
            "payway",
            "b2c_status",
            "c2b_status",
            "mini_status",
            "wap_status",
            "c2b_fee_rate",
            "wap_fee_rate",
            "params.alipay_v2_trade_params",
            "params.deposit.alipay",
            "params.deposit.weixin",
            "params.ladder_fee_rates",
            "params.bankcard_fee",
            "params.ladder_status",
            "params.history_trade_refund_flag"
    )),
    // CRM费率调整
    CRM_FEE_RATES("U53G7LE1U3M3", "merchant_config", Arrays.asList(
            "payway",
            "b2c_fee_rate",
            "mini_fee_rate",
            "c2b_fee_rate",
            "wap_fee_rate"
    )),
    // saas服务费
    SAAS("8GHGWGLHDG5M", "service_fee_effective", Arrays.asList(
            "feeId", "chargeAmount", "profitShareRatio", "minChargeAmount", "commission", "tradeAppName"
    )),
    // 修改云闪付阶梯费率设置
    SETTINGS_UNION_LADDER_FEE_RATES("LND8PAVRHSC3", "merchant", Collections.singletonList("union_pay_ladder_apply")),
    // 自定义对账周期d0权益
    D0_WITHDRAW("W6M2EOK8WLC3", "merchant_gray", Collections.singletonList("status")),
    // 提现留存金额设置
    WITHDRAW_RETAINED_AMOUNT_WITHDRAW("4FNNGCS75WWT", "merchant_withdraw_config", Collections.singletonList("retained_amount")),
    // 移出普惠考核普惠结束
    CLOSE_PUHUI_AUDIT("EDIL6SGDQSGU", "merchant", Collections.singletonList("content")),
    // 关闭信用卡/花呗支付方式
    CLOSE_CREDIT_LIMIT_PAY("V0TAUPUE2L4W", "credit_limit", Collections.singletonList("credit_pay_status")),
    // 关闭微信信用卡支付方式
    CLOSE_CREDIT_LIMIT_WECHAT("8R1M1CPKWEBG", "credit_limit", Collections.singletonList("wechat")),
    // 关闭支付宝信用卡/花呗支付方式
    CLOSE_CREDIT_LIMIT_ALIPAY("H2UTT81726UO", "credit_limit", Collections.singletonList("alipay")),
    //结算备注
    WITHDRAW_REMARK("V3A8VQTEPNG7", "merchant_withdraw_config_remark", Arrays.asList("status", "remark_type", "customize_remark", "reason")),
    // 开启商户交易权限
    OPEN_MERCHANT_PAY("I037GBLQHPEQ", "merchant_config", Collections.singletonList("merchant_trade_status")),
    // 结算通道切换
    BYPASS_SUMMARY_QUERY("N6E7ATMPBU3E", "merchant_config", Collections.singletonList("clearance_provider")),
    // 银行合作商户自定义对账周期d0权益
    D0_CONFIIG("6TUVPNL8QQ6A", "merchant_gray", Collections.singletonList("status")),
    /**
     * 结算模式配置
     */
    WITHDRAW_MODE_CONFIG("EFBKH7G4O8K7", "merchant", Arrays.asList("withdraw_mode", "withdraw_type")),

    /**
     * 取消额度信息
     */
    CANCEL_QUOTA_CONFIG("UG8FS1WWG1AK", "transaction_quota", Arrays.asList("quota")),

    /**
     * 添加固定额度信息
     */
    ADD_FIXED_QUOTA_CONFIG("7WCP10KMIUC2", "transaction_quota", Arrays.asList("daily_credit_limit", "single_bank_card_limit", "risk_quota", "risk_quota_status", "monthly_credit_limit", "special_fixed_quota", "monthly_special_credit_limit")),

    /**
     * 修改固定额度信息
     */
    MODIFY_FIXED_QUOTA_CONFIG("K27W34HQV2RD", "transaction_quota", Arrays.asList("daily_credit_limit", "single_bank_card_limit", "risk_quota", "risk_quota_status", "monthly_credit_limit", "special_fixed_quota", "monthly_special_credit_limit")),


    /**
     * 修改商户状态信息(余额和间连)
     */
    UPDATE_MERCHANT_CONFIG("HV2I2TKAF5F2", "trade_state", Arrays.asList("withdraw_transfer", "clearance_delay_reason_id", "jl_trade_status")),

    /**
     * 修改门店状态信息(余额和间连)
     */
    UPDATE_STORE_CONFIG("SAI2IKAAAOK8", "trade_state", Arrays.asList("jl_trade_status")),


    /**
     * 修改教培活动状态
     */
    WXPAY_EDU_ACTIVITY_CONFIG("D4W6EV2BSWG1", "trade_activity", Arrays.asList("merchant_id", "wxpay_edu_activity_status")),


    /**
     * 申请活动配置
     */
    APPLY_ACTIVITY_CONFIG("4CK2FQ0L8LE4", "activity_apply", Arrays.asList("activity_desc", "activity_status")),


    /**
     * APP BSC限制
     */
    APPLY_BSC_LIMIT_CONFIG("ER0WLFPSMAQE", "merchant_config", Arrays.asList("merchant_id", "app_bsc_limit")),


    /**
     * 商户添加临时额度
     */
    TEMP_QUOTA_LOG_CONFIG("7Q7GHNAGGGCK", "transaction_quota_detail", Arrays.asList("merchant_apply_temp_quota", "end_date")),


    /**
     * 收单机构商户状态管控
     */
    CLEARANCE_PROVIDER_MCH_CONFIG("7ABK3FG2U26D", "clearance_provider_mch", Arrays.asList("settlement", "extra", "providerMchId")),

    /**
     * 部分资金结算延迟
     */
    RISK_FREEZE_WALLET_CONFIG("43M4G0AI03C8", "risk_manage", Arrays.asList(BusinessLogConstant.FREEZE_AMOUNT, BusinessLogConstant.CLEARANCE_PROVIDER)),
    /**
     * 部分资金结算解除延迟
     */
    RISK_UNFREEZE_WALLET_CONFIG("4HTTG75CMIKD", "risk_manage", Arrays.asList(BusinessLogConstant.UNFREEZE_AMOUNT, BusinessLogConstant.CLEARANCE_PROVIDER)),
    /**
     * 结算批次延迟
     */
    RISK_DELAY_WITHDRAW_CONFIG("GW4KM4LO5A0K", "risk_manage", Collections.singletonList(BusinessLogConstant.DELAY_WITHDRAW)),
    /**
     * 结算批次解除延迟
     */
    RISK_CANCEL_DELAY_WITHDRAW_CONFIG("A1ULGKBW5EW3", "risk_manage", Collections.singletonList(BusinessLogConstant.DELAY_WITHDRAW)),
    ;

    private final String code;

    private final String tableName;

    private final List<String> filedNameList;

    TemplateCodeEnum(String code, String tableName, List<String> filedNameList) {
        this.code = code;
        this.tableName = tableName;
        this.filedNameList = filedNameList;
    }

    public String getCode() {
        return code;
    }

    public String getTableName() {
        return tableName;
    }

    public List<String> getFiledNameList() {
        return filedNameList;
    }

    public String buildFieldFullName(String fieldName) {
        return tableName + "#" + fieldName;
    }
}
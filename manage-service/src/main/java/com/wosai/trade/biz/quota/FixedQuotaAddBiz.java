package com.wosai.trade.biz.quota;

import com.wosai.trade.config.apollo.QuotaTypeConfig;
import com.wosai.trade.constant.TransactionQuotaConstants;
import com.wosai.trade.model.biz.FixedQuotaAddBizParam;
import com.wosai.trade.model.dal.*;
import com.wosai.trade.model.enums.FixedQuotaTypeEnum;
import com.wosai.trade.model.enums.QuotaDetailTypeEnum;
import com.wosai.trade.service.enums.QuotaDetailStatusEnum;
import com.wosai.trade.service.exception.TradeManageBizException;
import com.wosai.trade.service.request.FixedQuotaAddRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

import static com.wosai.trade.service.exception.enums.TradeManageRespCodeEnum.*;

/**
 * <AUTHOR> Date: 2020/4/20 Time: 11:34 上午
 */
@Service
public class FixedQuotaAddBiz extends AbstractQuotaBiz<FixedQuotaAddRequest, FixedQuotaAddBizParam, Void> {

    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private QuotaTypeConfig quotaTypeConfig;

    @Override
    protected void before(FixedQuotaAddRequest request) {
        super.before(request);

        if (!quotaTypeConfig.isLegalType(QuotaDetailTypeEnum.FIXED_QUOTA.getCode(), request.getType())) {
            throw new TradeManageBizException(ILLEGAL_QUOTA_TYPE_OR_SUBTYPE);
        }

        if (isRiskControlSpecialQuotaEffect(request.getMerchantSn(), request.getBizType())) {
            throw new TradeManageBizException(RISK_CONTROL_NOT_ALLOW_OPERATION);
        }
    }

    @Override
    protected FixedQuotaAddBizParam buildParam(FixedQuotaAddRequest request) throws Throwable {

        return FixedQuotaAddBizParam.builder()
                .merchantSn(request.getMerchantSn())
                .quota(request.getFixedQuota())
                .type(QuotaDetailTypeEnum.FIXED_QUOTA.getCode())
                .subType(request.getType())
                .bizType(request.getBizType())
                .currentDateTime(LocalDateTime.now())
                .build();
    }

    @Override
    protected Void execute(FixedQuotaAddBizParam bizParam) throws Throwable {
        addFixedQuota(bizParam);
        return null;
    }
    //detail每次都插入（如果不冲突，否则就是update)  Summary每次都会更新，如果之前有就更新，否则执行初始化。
    private void addFixedQuota(FixedQuotaAddBizParam bizParam) {
        transactionTemplate.execute(transactionStatus -> {

            String merchantSn = bizParam.getMerchantSn();
            //如果额度汇总不存在则初始化，返回初始化参数，否则返回null
            TransactionQuotaSummaryUpsertDalParam summaryUpsertDalParam
                    = initQuotaSummary(bizParam, merchantSn);
            //汇总记录加X锁
            TransactionQuotaSummaryDalDO summaryDalDO
                    = queryQuotaSummaryForUpdateByMchSnAndBizType(merchantSn, bizParam.getBizType());
            //新加detail 生效
            TransactionQuotaDetailUpsertDalParam detailSaveDalParam
                    = buildDetailSaveDalParam(bizParam);

            //将已有的固额|临额状态改为未生效
            updateDetail(bizParam);
            //有重复的记录就更新，没有就插入，新的detail置为生效。
            detailRepository.insertOnDuplicateUpdate(detailSaveDalParam);
            //汇总记录本就存在的情况，需更新汇总记录，null则是原本就有汇总记录。
            if (Objects.isNull(summaryUpsertDalParam)) {
                summaryUpsertDalParam
                        = buildSummarySaveDalParam(bizParam, summaryDalDO);
                summaryRepository.update(summaryUpsertDalParam);
            }
            syncQuotaToCoreBusiness(summaryUpsertDalParam);

            return null;
        });
    }
        //但前添加的类型是风控额度，那么之前的所有额度都失效
    private void updateDetail(FixedQuotaAddBizParam bizParam) {
        //风控特殊额度需要将全部临时额度置为失效
        if (FixedQuotaTypeEnum.RISK_CONTROL_SPECIAL_FIXED_QUOTA.getCode() == bizParam.getSubType()) {
            //所有额度都置为失效
            TransactionQuotaDetailUpsertDalParam detailUpsertDalParam
                    = TransactionQuotaDetailUpsertDalParam.builder()
                    .merchantSn(bizParam.getMerchantSn())
                    .status(QuotaDetailStatusEnum.NOT_EFFECTIVE.getCode())
                    .beginDate(null)
                    .endDate(null)
                    .bizType(bizParam.getBizType())
                    .mtime(bizParam.getCurrentDateTime())
                    .build();
            detailRepository.updateStatusAndDate(detailUpsertDalParam);
            return;
        }

        //仅将固额置为失效
        TransactionQuotaDetailUpsertDalParam detailUpsertDalParam
                = TransactionQuotaDetailUpsertDalParam.builder()
                .merchantSn(bizParam.getMerchantSn())
                .type(bizParam.getType())
                .status(QuotaDetailStatusEnum.NOT_EFFECTIVE.getCode())
                .mtime(bizParam.getCurrentDateTime())
                .bizType(bizParam.getBizType())
                .build();
        detailRepository.updateStatus(detailUpsertDalParam);
    }

    protected TransactionQuotaSummaryUpsertDalParam buildSummarySaveDalParam(FixedQuotaAddBizParam bizParam
            , TransactionQuotaSummaryDalDO summaryDalDO) {

        String merchantSn = bizParam.getMerchantSn();
        String merchantId;
        Byte bizType = bizParam.getBizType();
        long fixedQuota = bizParam.getQuota();
        long temporaryQuota = TransactionQuotaConstants.DEFAULT_TEMPORARY_QUOTA;
        long oriTotalQuota = TransactionQuotaConstants.DEFAULT_TOTAL_QUOTA;
        LocalDate nextComputeDate = TransactionQuotaConstants.DEFAULT_NEXT_COMPUTE_DATE;

        if (Objects.nonNull(summaryDalDO)) {
            merchantId = summaryDalDO.getMerchantId();
            oriTotalQuota = summaryDalDO.getTotalQuota();
            temporaryQuota = summaryDalDO.getTemporaryQuota();
            nextComputeDate = summaryDalDO.getNextComputeDate();
        } else {
            merchantId = getMerchantIdByMchSn(merchantSn);
        }

        //商户不存在
        if (StringUtils.isBlank(merchantId)) {
            throw new TradeManageBizException(MERCHANT_NOT_FOUND);
        }

        //风控特殊额度需要无效化全部临时额度，将下一次计算日期设为null
        if (bizParam.getSubType() == FixedQuotaTypeEnum.RISK_CONTROL_SPECIAL_FIXED_QUOTA.getCode()) {
            nextComputeDate = null;
            temporaryQuota = TransactionQuotaConstants.DEFAULT_TEMPORARY_QUOTA;
        }

        return TransactionQuotaSummaryUpsertDalParam.builder()
                .merchantSn(merchantSn)
                .merchantId(merchantId)
                .fixedQuota(fixedQuota)
                .temporaryQuota(temporaryQuota)
                .oriTotalQuota(oriTotalQuota)
                .totalQuota(fixedQuota + temporaryQuota)
                .nextComputeDate(nextComputeDate)
                .bizType(bizType)
                .ext(StringUtils.EMPTY)
                .ctime(bizParam.getCurrentDateTime())
                .mtime(bizParam.getCurrentDateTime())
                .build();
    }


    protected TransactionQuotaSummaryDalDO queryQuotaSummaryByMchSn(String mchSn, FixedQuotaAddBizParam fixedQuotaAddBizParam) {
        TransactionQuotaSummaryQueryDalParam queryDalParam
                = TransactionQuotaSummaryQueryDalParam.builder()
                .merchantSn(mchSn)
                .bizType(fixedQuotaAddBizParam.getBizType())
                .build();

        return summaryRepository.query(queryDalParam);
    }



    private TransactionQuotaDetailUpsertDalParam buildDetailSaveDalParam(FixedQuotaAddBizParam bizParam) {

        return TransactionQuotaDetailUpsertDalParam.builder()
                .merchantSn(bizParam.getMerchantSn())
                .quota(bizParam.getQuota())
                .type(bizParam.getType())
                .subType(bizParam.getSubType())
                .beginDate(null)
                .endDate(null)
                .status(QuotaDetailStatusEnum.EFFECTIVE.getCode())
                .bizType(bizParam.getBizType())
                .ext(StringUtils.EMPTY)
                .ctime(bizParam.getCurrentDateTime())
                .mtime(bizParam.getCurrentDateTime())
                .build();

    }


}

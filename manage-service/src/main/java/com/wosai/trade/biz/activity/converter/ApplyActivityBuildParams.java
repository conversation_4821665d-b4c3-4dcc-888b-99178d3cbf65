package com.wosai.trade.biz.activity.converter;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.databus.event.audit.AuditInstanceCreateEvent;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.trade.biz.audit.filter.ChannelFeeRateFilter;
import com.wosai.trade.biz.audit.model.ApplyChannelFeeRate;
import com.wosai.trade.biz.audit.model.ApplyLadderFeeRate;
import com.wosai.trade.config.apollo.CommonApolloConfig;
import com.wosai.trade.constant.AuditConstant;
import com.wosai.trade.constant.TradeComboConstants;
import com.wosai.trade.model.biz.ApplyFeeRateChainExtra;
import com.wosai.trade.model.dal.ActivityApplyQueryDalParam;
import com.wosai.trade.model.dal.ApplyExtraParam;
import com.wosai.trade.model.dal.TradeComboDetailQueryDalParam;
import com.wosai.trade.model.enums.PayWayEnum;
import com.wosai.trade.repository.dao.*;
import com.wosai.trade.repository.dao.entity.*;
import com.wosai.trade.repository.dao.entity.TradeComboDetailEntity.FixedFeeRate;
import com.wosai.trade.service.activity.constant.ActivityConstants;
import com.wosai.trade.service.activity.enums.ActivityTypeEnum;
import com.wosai.trade.service.activity.request.*;
import com.wosai.trade.service.activity.response.ApplyProcessInfo;
import com.wosai.trade.service.enums.FeeRateTypeEnum;
import com.wosai.trade.service.exception.TradeManageBizException;
import com.wosai.trade.service.request.PageInfo;
import com.wosai.trade.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.wosai.trade.util.AuditGetParamUtil.getMerchantSn;

/**
 * @Description:
 * <AUTHOR>
 * @Date: 2022/5/26 11:14 上午
 */
@Slf4j
@Component
public class ApplyActivityBuildParams {
    @Autowired
    private DiscountQuotaRuleMapper quotaRuleMapper;
    @Autowired
    private ActivitySubStatusDetailMapper activitySubStatusDetailMapper;
    @Autowired
    private ActivityDOMapper activityDOMapper;
    @Autowired
    private TradeComboDetailDao tradeComboDetailDao;
    @Autowired
    private ActivityApplyDOMapper activityApplyDOMapper;
    @Autowired
    private MerchantFeeRateDao merchantFeeRateDao;
    @Autowired
    private CommonApolloConfig commonApolloConfig;
    @Autowired
    private TradeComboDao tradeComboDao;

    /**
     * 构建活动申请
     *
     * @param activity
     * @param request
     * @return
     */
    public ActivityApplyEntity buildAddActivityApply(ActivityEntity activity, ApplyActivityRequest request) {

        ActivityApplyEntity applyDO = ActivityApplyEntity.builder()
                .activity_name(activity.getName())
                .activity_type(activity.getType())
                .activity_id(activity.getId())
                .biz_id(activity.getBiz_id())
                .merchant_sn(request.getMerchantSn())
                .merchant_id(request.getMerchantId())
                .sn(request.getSn())
                .auditTemplateId(request.getAuditTemplateId())
                .parent_id(request.fetchParentApplyId()) // 设置主体applyId
                .build();

        if (StringUtils.isNotBlank(request.getSubMerchantSn())) {
            applyDO.setSub_mch_id(request.getSubMerchantSn());
        }

        if (request.getAuditId() != null) {
            applyDO.setAudit_id(request.getAuditId());
        }
        if (request.getAuditInstanceEvent() != null) {
            applyDO.setAudit_info(JSONObject.toJSONString(request.getAuditInstanceEvent()));
        }

        //fee_rate 为空 生效时按照套餐取值 不为空 申请时传入的值
        //payway 为空 生效时按照套餐取值 不为空 申请时传入的值
        if (CollectionUtils.isNotEmpty(request.getApplyPayFeeRates())) {
            List<Integer> payWays = new ArrayList<>();
            Map<String, String> feeRateMap = new HashMap<>();
            for (ApplyActivityRequest.ApplyPayFeeRate applyPayFeeRate : request.getApplyPayFeeRates()) {
                payWays.add(applyPayFeeRate.getPayWay());
                if (StringUtils.isNotBlank(applyPayFeeRate.getFeeRate())) {
                    feeRateMap.put(String.valueOf(applyPayFeeRate.getPayWay()), applyPayFeeRate.getFeeRate());
                }
            }
            applyDO.setPayway(JSONObject.toJSONString(payWays));
            if (WosaiMapUtils.isNotEmpty(feeRateMap)) {
                applyDO.setFee_rate(JSONObject.toJSONString(feeRateMap));
            }
        }

        if (activity.buildDiscountQuota() != null) {
            applyDO.setDiscount_quota_status(ActivityConstants.DISCOUNT_QUOTA_STATUS_SUPPORT);
        }
        applyDO.setEffective_rule(activity.getEffective_rule());
        applyDO.setExpiration_rule(activity.getExpiration_rule());
        // 交易考核规则
        applyDO.setTrade_assessment_rule(activity.getTrade_assessment_rule());
        //填充操作信息
        fullProcessInfo(request, applyDO);
        //附加子状态相关信息
        applyDO.setActivity_sub_status_id(request.getSubStatusDetailId());
        applyDO.setCombo_id(request.getComboId());
        if (Objects.nonNull(request.getSourceApplyId())) {
            ApplyExtraParam extraParam = new ApplyExtraParam();
            extraParam.setSourceApplyId(request.getSourceApplyId());
            applyDO.setExtra(JSONObject.toJSONString(extraParam));
        }
        //设置请求体到扩展字段
        ApplyExtraParam extraParam = applyDO.buildJsonExtra();
        if (Objects.isNull(extraParam)) {
            extraParam = new ApplyExtraParam();
        }
        extraParam.setApplyActivityRequest(request);
        applyDO.setExtra(JSONObject.toJSONString(extraParam));

        if (StringUtils.isBlank(applyDO.getPayway())) {
            List<Integer> payWayList = tradeComboDetailDao.selectList(TradeComboDetailQueryDalParam.builder().comboId(applyDO.getCombo_id())
                    .build()).stream().map(TradeComboDetailEntity::getPayway).distinct().collect(Collectors.toList());
            applyDO.setPayway(JsonUtil.encode(payWayList));
        }
        return applyDO;
    }

    /**
     * 填充操作信息
     *
     * @param request
     * @param applyDO
     */
    private void fullProcessInfo(ApplyActivityRequest request, ActivityApplyEntity applyDO) {
        List<ApplyProcessInfo> processList = new ArrayList<>();
        ApplyProcessInfo processInfo = new ApplyProcessInfo();
        if (StringUtils.isNotBlank(request.getOperator())) {
            processInfo.setOperator(request.getOperator());
        }
        processInfo.setTime(System.currentTimeMillis());
        if (StringUtils.isNotBlank(request.getRemark())) {
            processInfo.setInfo(request.getRemark());
        }
        processInfo.setStatus(ActivityConstants.HANDLE);
        processList.add(processInfo);

        //增加来源
        AuditInstanceCreateEvent event = request.getAuditInstanceEvent();
        if (Objects.nonNull(event)) {
            String infoMsg = String.format("审批：%s", event.getAuditSn());
            if (AuditGetParamUtil.isApplyActivityCrmMerchantInfo(event.getBusinessMap())) {
                infoMsg = "报名来源：CRM商户详情";
            }
            ApplyProcessInfo processInfoCrm = new ApplyProcessInfo();
            processInfoCrm.setOperator(processInfo.getOperator());
            processInfoCrm.setStatus(processInfo.getStatus());
            processInfoCrm.setTime(processInfo.getTime());
            processInfoCrm.setInfo(infoMsg);
            processList.add(processInfoCrm);
        }
        applyDO.setProcess(JsonUtil.encode(processList));
    }

    public ActivityApplyQueryDalParam buildActivityApplyQueryDalParam(ApplyConditionQueryRequest request) {

        ActivityApplyQueryDalParam queryParam = new ActivityApplyQueryDalParam();
        if (request.getStatus() != null) {
            //映射对应的code
            queryParam.setInStatus(getQueryStatus(request.getStatus()));
        }
        // 添加活动ID筛选条件
        if (request.getActivityId() != null) {
            queryParam.setActivityIds(ImmutableList.of(request.getActivityId()));
        }
        if (StringUtils.isNotBlank(request.getActivityName())) {
            queryParam.setActivityName(request.getActivityName());
        }
        if (request.getActivityType() != null) {
            queryParam.setActivityType(request.getActivityType());
        }
        if (StringUtils.isNotBlank(request.getSn())) {
            queryParam.setSn(request.getSn());
        }

        PageInfo pageInfo = PageInfoUtil.extractPageInfoCreateAt(request);
        queryParam.setLimit(pageInfo.getPageSize());
        queryParam.setStart(pageInfo.getPageStart());
        queryParam.setOrderBy(pageInfo.getOrderBy());
        if (pageInfo.getDateStart() != null) {
            queryParam.setStartTime(DateTimeUtils.getFormatDateByTimestamp(pageInfo.getDateStart(), "yyyy-MM-dd HH:mm:ss"));
        }
        if (pageInfo.getDateEnd() != null) {
            queryParam.setEndTime(DateTimeUtils.getFormatDateByTimestamp(pageInfo.getDateEnd(), "yyyy-MM-dd HH:mm:ss"));
        }

        if (Objects.nonNull(request.getComboId())) {
            queryParam.setComboId(request.getComboId());
        }
        if (StringUtils.isNotBlank(request.getComboName())) {
            queryParam.setComboName(request.getComboName());
        }
        if (StringUtils.isNotBlank(request.getSubStatusName())) {
            queryParam.setSubStatusName(request.getSubStatusName());
        }
        if (CollectionUtils.isNotEmpty(request.getActivityIdList())) {
            queryParam.setActivityIds(request.getActivityIdList());
        }
        if (Objects.nonNull(request.getRiskActivityProcessType())) {
            List<Long> activityIds = commonApolloConfig.getRiskActivityIds(request.getRiskActivityProcessType());
            queryParam.setActivityIds(activityIds);
        }
        return queryParam;

    }

    public List<ApplyProcessInfo> buildProcess(ActivityApplyEntity apply, int status, String info) {
        return buildProcess(apply, status, "system", info);
    }

    public List<ApplyProcessInfo> buildProcess(ActivityApplyEntity apply, int status, String operator, String info) {
        List<ApplyProcessInfo> processInfos = apply.buildProcess();
        if (CollectionUtils.isEmpty(processInfos)) {
            processInfos = new ArrayList<>();
        }
        ApplyProcessInfo process = new ApplyProcessInfo();
        process.setStatus(status);
        process.setOperator(operator);
        process.setTime(new Date().getTime());
        if (StringUtils.isNotBlank(info)) {
            process.setInfo(info);
        }
        processInfos.add(process);

        return processInfos;
    }

    /**
     * SP 批量开通 参数
     *
     * @param request
     * @param activityId
     * @param quotaRecordId
     * @return
     */
    public ApplyActivityRequest buildApplyRequestFromSP(SPBulkApplyRequest request, Long activityId, Long quotaRecordId,
                                                        BulkAddActivityRequest bulkRequest) {
        String auditSn = bulkRequest.getAddDescribe().getAuditSn();
        if (StringUtils.isEmpty(auditSn)) {
            auditSn = "SPA批量开通";
        }
        ApplyActivityRequest apply = new ApplyActivityRequest();
        apply.setMerchantSn(request.getMerchantSn());
        apply.setActivityId(activityId);
        apply.setAuditSn(auditSn);

        apply.setSubStatusDetailId(bulkRequest.getAddActivityByFile().getSubStatusDetailId());
        apply.setComboId(bulkRequest.getAddActivityByFile().getComboId());
        if (StringUtils.isNotBlank(request.getQuotaAmount())) {
            apply.setDiscountQuota(new BigDecimal(request.getQuotaAmount()).doubleValue());
        }
        if (quotaRecordId != null) {
            apply.setDiscountQuotaRecordId(quotaRecordId);
        }
        if (StringUtils.isNotBlank(request.getQuotaFeeRate())) {
            apply.setDiscountQuotaFeeRate(request.getQuotaFeeRate());
        }
        apply.setOperator(bulkRequest.getAddDescribe().getOperatorName());
        apply.setRemark(bulkRequest.getAddDescribe().getReason());
        apply.setAuditTemplateId(bulkRequest.getAuditTemplateId());
        return apply;
    }


    /**
     * 构建 优惠额度记录
     *
     * @param quotaRuleId
     * @param request
     * @return
     */
    public DiscountQuotaRecordEntity buildQuotaRecordShare(Long quotaRuleId, SPBulkApplyRequest request) {
        DiscountQuotaRuleEntity ruleDO = quotaRuleMapper.selectByPrimaryKey(quotaRuleId);
        DiscountQuotaRecordEntity quotaRecordDO = new DiscountQuotaRecordEntity();

        if (request.getQuotaAmount() != null) {
            double quota = new BigDecimal(request.getQuotaAmount()).doubleValue();
            quotaRecordDO.setDiscount_quota((long) (quota * 100));
            quotaRecordDO.setDiscount_quota_usable(quotaRecordDO.getDiscount_quota());
        } else {
            quotaRecordDO.setDiscount_quota(ruleDO.getQuota_min());
            quotaRecordDO.setDiscount_quota_usable(ruleDO.getQuota_min());
        }
        if (StringUtils.isNotBlank(request.getQuotaFeeRate())) {
            quotaRecordDO.setDiscount_quota_fee_rate(request.getQuotaFeeRate());
        } else {
            quotaRecordDO.setDiscount_quota_fee_rate(ruleDO.getQuota_fee_rate_min());
        }
        quotaRecordDO.setActivity_id(ruleDO.getActivity_id());
        return quotaRecordDO;
    }


    /**
     * @param merchantSn
     * @param applyPayFeeRates
     * @param bulkRequest
     * @return
     */
    public ApplyActivityRequest buildApplyRequestFromAudit(String merchantSn, List<ApplyActivityRequest.ApplyPayFeeRate> applyPayFeeRates,
                                                           BulkAddActivityRequest bulkRequest) {

        BulkAddActivityRequest.AddActivityByFile byFile = bulkRequest.getAddActivityByFile();
        ApplyActivityRequest apply = new ApplyActivityRequest();
        apply.setActivityId(byFile.getActivityId());
        apply.setMerchantSn(merchantSn);
        apply.setApplyPayFeeRates(applyPayFeeRates);
        apply.setSubStatusDetailId(bulkRequest.getAddActivityByFile().getSubStatusDetailId());
        apply.setComboId(bulkRequest.getAddActivityByFile().getComboId());
        apply.setAuditSn(bulkRequest.getAddDescribe().getAuditSn());
        apply.setAuditId(bulkRequest.getAddDescribe().getAuditId());
        if (byFile.getDiscountQuota() != null) {
            apply.setDiscountQuota(byFile.getDiscountQuota());
        }
        if (StringUtils.isNotBlank(byFile.getDiscountQuotaFeeRate())) {
            apply.setDiscountQuotaFeeRate(byFile.getDiscountQuotaFeeRate());
        }
        apply.setOperator(bulkRequest.getAddDescribe().getOperatorName());
        apply.setRemark(bulkRequest.getAddDescribe().getReason());
        apply.setAuditTemplateId(bulkRequest.getAuditTemplateId());
        return apply;
    }

    public List<Integer> getQueryStatus(Integer status) {
        List<Integer> res = new ArrayList<>();
        res.add(status);
        if (ActivityConstants.APPLY_ING == status) {
            res.addAll(Arrays.asList(ActivityConstants.HANDLE, ActivityConstants.PRE_HANDLE_END));
        } else if (ActivityConstants.EFFECT == status) {
            res.add(ActivityConstants.CANCEL_ING);
        }
        return res;
    }

    public CancelActivityApplyRequest buildCancelFromBulkCancel(Long applyId, List<Integer> payWays, CancelActivityApplyRequest.CancelDescribe describe) {
        CancelActivityApplyRequest request = new CancelActivityApplyRequest();
        request.setCancelDescribe(describe);
        CancelActivityApplyRequest.CancelByActivityApply byId = new CancelActivityApplyRequest.CancelByActivityApply();
        byId.setActivityApplyId(applyId);
        if (CollectionUtils.isNotEmpty(payWays)) {
            byId.setPayways(payWays);
        }
        CancelActivityApplyRequest.CancelEntity cancelEntity = new CancelActivityApplyRequest.CancelEntity();
        cancelEntity.setCancelByActivityApply(byId);
        request.setCancelEntity(cancelEntity);
        return request;
    }

    public CancelActivityApplyRequest buildCancelFromSystem(Long applyId, String msg) {
        CancelActivityApplyRequest request = new CancelActivityApplyRequest();
        CancelActivityApplyRequest.CancelDescribe describe = new CancelActivityApplyRequest.CancelDescribe();

        describe.setReason(msg);
        describe.setOperatorName("system");
        describe.setOperator("system");


        CancelActivityApplyRequest.CancelByActivityApply byId = new CancelActivityApplyRequest.CancelByActivityApply();
        byId.setActivityApplyId(applyId);

        CancelActivityApplyRequest.CancelEntity cancelEntity = new CancelActivityApplyRequest.CancelEntity();
        cancelEntity.setCancelByActivityApply(byId);

        request.setCancelDescribe(describe);
        request.setCancelEntity(cancelEntity);
        return request;
    }

    /**
     * 解析审批活动配置
     * 活动ID:子状态ID
     *
     * @param businessMap
     * @return
     */
    public Pair<Long, Long> analyzeAuditActivity0(Map businessMap) {
        String activityStr = MapUtils.getString(businessMap, "activity_info");
        // 兼容处理
        if (StringUtils.isBlank(activityStr)) {
            activityStr = MapUtils.getString(businessMap, "activity_id");
        }
        if (StringUtils.isBlank(activityStr)) {
            return Pair.of(null, null);
        }
        String[] array = StringUtils.splitPreserveAllTokens(activityStr, ":");
        return Pair.of(CommonUtil.toLong(CommonUtil.getArrayValue(array, 0)), CommonUtil.toLong(CommonUtil.getArrayValue(array, 1)));
    }

    /**
     * 解析支付源取消审批
     *
     * @param businessMap
     */
    public Pair<Long, String> analyzeAuditPaySourceCancel(Map businessMap) {
        Long activityId = WosaiMapUtils.getLong(businessMap, "activity_id");
        String merchantSn = getMerchantSn(businessMap);
        return Pair.of(activityId, merchantSn);
    }

    /**
     * 查找活动申请子状态记录
     *
     * @param activityId
     * @param subStatusDetailId
     * @return
     */
    public ActivitySubStatusDetailEntity findApplySubStatusDetailDo(Long activityId, Long subStatusDetailId) {
        if (Objects.nonNull(subStatusDetailId)) {
            return activitySubStatusDetailMapper.selectByPrimaryKey(subStatusDetailId);
        }
        List<ActivitySubStatusDetailEntity> subStatusDetailDos = activitySubStatusDetailMapper.selectByActivityId(activityId);
        subStatusDetailDos = subStatusDetailDos.stream().filter(detail -> StringUtils.isBlank(detail.getName())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(subStatusDetailDos)) {
            throw TradeManageBizException.createExc("当活动无默认子状态. activityId:" + activityId);
        }
        if (subStatusDetailDos.size() != NumberUtils.INTEGER_ONE) {
            throw TradeManageBizException.createExc("当活动无默认子状态存在多个. activityId:" + activityId);
        }
        return subStatusDetailDos.get(0);
    }

    public List<ApplyActivityRequest.ApplyPayFeeRate> buildApplyActivityFixOrRange(Map businessMap) {
        //费率明细
        List feetRateList = (List) BeanUtil.getProperty(businessMap, AuditConstant.FIELD_FEET_RATE_LIST);
        //阶梯费率
        List ladderList = (List) BeanUtil.getProperty(businessMap, AuditConstant.FIELD_LADDER_LIST);
        //固定区间费率
        String feeRateVal = BeanUtil.getPropString(businessMap, AuditConstant.FIELD_FEE_RATE);
        if (CollectionUtils.isNotEmpty(feetRateList)) {
            List<ApplyActivityRequest.ApplyPayFeeRate> applyPayFeeRates = new ArrayList<>();
            for (Object feeRate : feetRateList) {
                Map feeRateInfo = (Map) feeRate;
                List<String> payList = (List<String>) BeanUtil.getProperty(feeRateInfo, AuditConstant.FIELD_PAY_WAY_LIST);
                if (CollectionUtils.isEmpty(payList)) {
                    return null;
                }
                for (String payWayValue : payList) {
                    ApplyActivityRequest.ApplyPayFeeRate applyPayFeeRate = new ApplyActivityRequest.ApplyPayFeeRate();
                    applyPayFeeRate.setPayWay(PayWayEnum.getCodeByName(payWayValue));
                    String rate = WosaiMapUtils.getString(feeRateInfo, AuditConstant.FIELD_RATE);
                    if (StringUtils.isNotBlank(rate)) {
                        applyPayFeeRate.setFeeRate(rate);
                    }
                    applyPayFeeRates.add(applyPayFeeRate);
                }
            }
            return applyPayFeeRates;
        } else if (CollectionUtils.isNotEmpty(ladderList)) {
            return generateApplyLadderFeeRate(businessMap);
        } else if (StringUtils.isNotBlank(feeRateVal)) {
            Pair<Long, Long> activityPair = analyzeAuditActivity0(businessMap);
            ActivityEntity activityDo = activityDOMapper.selectByPrimaryKey(activityPair.getLeft());
            // 支付源处理
            if (!ActivityTypeEnum.payWayActivities.contains(activityDo.getType())) {
                throw TradeManageBizException.createExc("费率配置key：fee_rate只处理支付源活动");
            }
            ActivitySubStatusDetailEntity subStatusDetail = findApplySubStatusDetailDo(activityPair.getLeft(), activityPair.getRight());
            List<TradeComboDetailEntity> comboDetailDos = tradeComboDetailDao.selectList(TradeComboDetailQueryDalParam.builder()
                    .comboId(subStatusDetail.getComboId()).build());
            return comboDetailDos.stream().map(comboDetailEntity -> {
                if (StringUtils.isNotBlank(feeRateVal)) {
                    FixedFeeRate fixedFeeRate = comboDetailEntity.buildFixedFeeRate();
                    FeeRateUtils.verifyRangeValue("费率", feeRateVal, fixedFeeRate != null ? fixedFeeRate.getMin() : null, fixedFeeRate != null ? fixedFeeRate.getMax() : null);
                }
                return ApplyActivityRequest.ApplyPayFeeRate.builder()
                        .payWay(comboDetailEntity.getPayway())
                        .feeRate(feeRateVal)
                        .build();
            }).collect(Collectors.toList());
        }
        return null;
    }

    /**
     * 构建刷卡活动报名资金渠道费率
     *
     * @param businessMap
     * @return
     */
    public List<ApplyActivityRequest.ApplyPayFeeRate> buildApplyActivityByChannelFeeRate(Map businessMap) {
        //获取套餐明细
        Long activityId = MapUtils.getLong(businessMap, AuditConstant.FIELD_ACTIVITY_INFO);
        ValidationUtils.notNull(activityId, "活动ID不存在");
        ActivityEntity activityDo = activityDOMapper.selectByPrimaryKey(activityId);
        ValidationUtils.notNull(activityDo, "活动不存在. activityId:" + activityId);
        ValidationUtils.check(Objects.equals(activityDo.getStatus(), ActivityConstants.USABLE), "活动不可用");
        List<TradeComboDetailEntity> comboDetailList = tradeComboDetailDao.selectList(TradeComboDetailQueryDalParam.builder()
                .comboId(activityDo.getCombo_id())
                .payWay(PayWayEnum.BANK_CARD.getCode()) //只针对刷卡费率
                .build());
        ValidationUtils.notEmpty(comboDetailList, "刷卡活动套餐不存在. activityId:" + activityId);
        TradeComboDetailEntity comboDetail = comboDetailList.get(0);
        List<TradeComboDetailEntity.ChannelFeeRate> channelFeeRateList = comboDetail.buildChannelFeeRates();
        //构建资金渠道费率
        List<ApplyChannelFeeRate.ApplyChannelFeeRateValue> applyChannelFeeRateList = channelFeeRateList.stream().map(channelFeeRate -> {
            //费率类型: debit,credit,dcc,edc
            String type = channelFeeRate.getType();
            String feeRate = MapUtils.getString(businessMap, String.format("%s_%s", type, AuditConstant.FIELD_FEE_RATE));
            String max = MapUtils.getString(businessMap, String.format("%s_%s", type, AuditConstant.FIELD_MAX));
            return buildApplyChannelFeeRateValue(channelFeeRate, feeRate, max);
        }).filter(Objects::nonNull).collect(Collectors.toList());
        ApplyChannelFeeRate applyFeeRate = new ApplyChannelFeeRate();
        applyFeeRate.setFeeRateType(FeeRateTypeEnum.CHANNEL.getType());
        applyFeeRate.setValue(applyChannelFeeRateList);
        String feeRateStr = JsonUtil.encode(applyFeeRate);
        //资金渠道费率校验
        TradeComboEntity tradeCombo = tradeComboDao.selectById(activityDo.getCombo_id());
        ApplyFeeRateChainExtra extra = new ApplyFeeRateChainExtra(ImmutableMap.of(comboDetail.getPayway() + "", feeRateStr));
        ChannelFeeRateFilter channelFeeRateFilter = SpringBeanUtil.getBean(ChannelFeeRateFilter.class);
        channelFeeRateFilter.doFilter(tradeCombo, null, System.currentTimeMillis(), extra);
        //构建请求入参
        ApplyActivityRequest.ApplyPayFeeRate applyPayFeeRate = ApplyActivityRequest.ApplyPayFeeRate.builder()
                .payWay(comboDetail.getPayway())
                .feeRate(feeRateStr)
                .build();
        return ImmutableList.of(applyPayFeeRate);
    }

    /**
     * 构建资金渠道费率值
     *
     * @param channelFeeRate
     * @param feeRate
     * @param max
     * @return
     */
    private ApplyChannelFeeRate.ApplyChannelFeeRateValue buildApplyChannelFeeRateValue(TradeComboDetailEntity.ChannelFeeRate channelFeeRate, String feeRate, String max) {
        if (StringUtil.isEmpty(feeRate)) {
            return null;
        }
        ApplyChannelFeeRate.ApplyChannelFeeRateValue value = new ApplyChannelFeeRate.ApplyChannelFeeRateValue();
        value.setType(channelFeeRate.getType());
        value.setFeeRate(feeRate);
        value.setMax(max);
        return value;
    }

    /**
     * 构建审批入参阶梯费率
     *
     * @param comboDetail
     * @param ladderInfo
     * @return
     */
    public List<ApplyLadderFeeRate.LadderFeeRate> buildAuditLadderFeeRate(TradeComboDetailEntity comboDetail, Map<String, Object> ladderInfo) {
        Map<String, ApplyLadderFeeRate.LadderFeeRate> ladderMap = buildAuditLadderFeeRateMap(ladderInfo);
        List<TradeComboDetailEntity.LadderFeeRate> ladderFeeRateList = comboDetail.buildLadderFeeRates();
        if (CollectionUtils.isEmpty(ladderFeeRateList)) {
            throw TradeManageBizException.createExc("当前套餐明细非阶梯费率. comboDetailId:" + comboDetail.getId());
        }

        return ladderFeeRateList.stream().map(feeRateConfig -> {
            String ladderKey = feeRateConfig.getMin() + "-" + feeRateConfig.getMax();
            ApplyLadderFeeRate.LadderFeeRate ladderFeeRateVal = (ApplyLadderFeeRate.LadderFeeRate) MapUtils.getObject(ladderMap, ladderKey);
            if (Objects.isNull(ladderFeeRateVal)) {
                throw TradeManageBizException.createExc("缺少阶梯区间. [" + ladderKey + "]");
            }
            FeeRateUtils.verifyRangeValue("阶梯费率", ladderFeeRateVal.getFeeRate(), feeRateConfig.getFeeRateMin(), feeRateConfig.getFeeRateMax());
            ApplyLadderFeeRate.LadderFeeRate ladderFeeRate = new ApplyLadderFeeRate.LadderFeeRate();
            ladderFeeRate.setMin(feeRateConfig.getMin());
            ladderFeeRate.setMax(feeRateConfig.getMax());
            ladderFeeRate.setFeeRate(ladderFeeRateVal.getFeeRate());
            return ladderFeeRate;
        }).collect(Collectors.toList());
    }

    /**
     * 生成阶梯费率－活动申请
     *
     * @param businessMap
     * @return
     */
    private List<ApplyActivityRequest.ApplyPayFeeRate> generateApplyLadderFeeRate(Map businessMap) {
        //获取套餐明细列表
        Pair<Long, Long> activityPair = analyzeAuditActivity0(businessMap);
        ActivitySubStatusDetailEntity subStatusDetail = findApplySubStatusDetailDo(activityPair.getLeft(), activityPair.getRight());
        // payWay->detail
        Map<Integer, TradeComboDetailEntity> comboDetailMap = tradeComboDetailDao.selectList(TradeComboDetailQueryDalParam.builder()
                .comboId(subStatusDetail.getComboId()).build()).stream().collect(Collectors.toMap(TradeComboDetailEntity::getPayway, Function.identity()));

        List<Map<String, Object>> ladderList = (List<Map<String, Object>>) BeanUtil.getProperty(businessMap, "ladder_list");
        List<ApplyActivityRequest.ApplyPayFeeRate> applyPayFeeRates = new ArrayList<>();
        for (Map<String, Object> ladderInfo : ladderList) {
            List<String> payWayList = (List<String>) BeanUtil.getProperty(ladderInfo, "pay_way_list");
            if (CollectionUtils.isEmpty(payWayList)) {
                return null;
            }
            Map<String, ApplyLadderFeeRate.LadderFeeRate> ladderMap = buildAuditLadderFeeRateMap(ladderInfo);
            for (String payWayName : payWayList) {
                Integer payWayCode = PayWayEnum.getCodeByName(payWayName);
                List<ApplyLadderFeeRate.LadderFeeRate> feeRates = buildPayWayApplyFeeRate(comboDetailMap, ladderMap, payWayName);
                ApplyLadderFeeRate ladderFeeRate = new ApplyLadderFeeRate();
                ladderFeeRate.setFeeRateType(StringUtils.lowerCase(FeeRateTypeEnum.LADDER.name()));
                ladderFeeRate.setValue(feeRates);
                ApplyActivityRequest.ApplyPayFeeRate applyPayFeeRate = new ApplyActivityRequest.ApplyPayFeeRate();
                applyPayFeeRate.setPayWay(payWayCode);
                applyPayFeeRate.setFeeRate(JsonUtil.encode(ladderFeeRate));
                applyPayFeeRates.add(applyPayFeeRate);
            }
        }
        return applyPayFeeRates;
    }

    /**
     * 生成阶梯费率－套餐切换或费率变更
     *
     * @param businessMap
     * @return
     */
    public ApplyLadderFeeRate generateModifyLadderFeeRate(Integer payWayCode, Map<Integer, TradeComboDetailEntity> comboDetailMap, Map businessMap) {
        List<Map<String, Object>> ladderList = (List<Map<String, Object>>) BeanUtil.getProperty(businessMap, "ladder_list");
        ValidationUtils.check(CollectionUtils.isNotEmpty(ladderList), "阶梯费率列表为空");
        ValidationUtils.check(NumberUtils.INTEGER_ONE.equals(ladderList.size()), "阶梯费率列表不唯一");
        String payWayName = PayWayEnum.getNameByCode(payWayCode);
        Map<String, ApplyLadderFeeRate.LadderFeeRate> ladderMap = buildAuditLadderFeeRateMap(ladderList.get(0));
        List<ApplyLadderFeeRate.LadderFeeRate> feeRateList = buildPayWayApplyFeeRate(comboDetailMap, ladderMap, payWayName);
        ApplyLadderFeeRate ladderFeeRate = new ApplyLadderFeeRate();
        ladderFeeRate.setFeeRateType(FeeRateTypeEnum.LADDER.name().toLowerCase());
        ladderFeeRate.setValue(feeRateList);
        return ladderFeeRate;
    }

    /**
     * 构建审批内的阶梯map
     *
     * @param feeRateInfo
     * @return
     */
    private Map<String, ApplyLadderFeeRate.LadderFeeRate> buildAuditLadderFeeRateMap(Map<String, Object> feeRateInfo) {
        Map<String, ApplyLadderFeeRate.LadderFeeRate> ladderFeeRateMap = Maps.newHashMap();
        for (Map.Entry<String, Object> entry : feeRateInfo.entrySet()) {
            String key = entry.getKey();
            //数据格式为: ladder:1-100
            if (!StringUtils.startsWith(key, "ladder")) {
                continue;
            }
            String[] array = StringUtils.split(key, ":");
            if (array.length != 2) {
                continue;
            }
            String[] amountRange = StringUtils.splitPreserveAllTokens(array[1], "-");
            if (amountRange.length != 2) {
                continue;
            }
            Double max = null;
            if (StringUtils.isNotBlank(amountRange[1])) {
                max = Double.valueOf(StringUtils.trim(amountRange[1]));
                if (max >= TradeComboConstants.ONE_BILLION) {
                    max = null;
                }
            }
            ValidationUtils.check(Objects.nonNull(entry.getValue()), "费率值不可为空. key:" + key);
            ApplyLadderFeeRate.LadderFeeRate ladderFeeRate = new ApplyLadderFeeRate.LadderFeeRate();
            ladderFeeRate.setMin(Double.valueOf(StringUtils.trim(amountRange[0])));
            ladderFeeRate.setMax(max);
            ladderFeeRate.setFeeRate(entry.getValue().toString());
            ladderFeeRateMap.put(ladderFeeRate.generateKey(), ladderFeeRate);
        }
        return ladderFeeRateMap;
    }

    /**
     * 构建指定payWay阶梯费率
     *
     * @param comboDetailMap
     * @param ladderMap
     * @param payWayName
     * @return
     */
    private List<ApplyLadderFeeRate.LadderFeeRate> buildPayWayApplyFeeRate(Map<Integer, TradeComboDetailEntity> comboDetailMap,
                                                                           Map<String, ApplyLadderFeeRate.LadderFeeRate> ladderMap,
                                                                           String payWayName) {
        Integer payWayCode = PayWayEnum.getCodeByName(payWayName);
        TradeComboDetailEntity comboDetail = comboDetailMap.get(payWayCode);
        if (Objects.isNull(comboDetail)) {
            throw TradeManageBizException.createExc("支付源不存在. payWayName:" + payWayName);
        }
        List<TradeComboDetailEntity.LadderFeeRate> ladderFeeRateList = comboDetail.buildLadderFeeRates();
        if (CollectionUtils.isEmpty(ladderFeeRateList)) {
            throw TradeManageBizException.createExc("当前套餐明细非阶梯费率. payWayName:" + payWayName);
        }
        return ladderFeeRateList.stream().map(feeRateConfig -> {
            String ladderKey = feeRateConfig.getMin() + "-" + feeRateConfig.getMax();
            ApplyLadderFeeRate.LadderFeeRate ladderFeeRateVal = (ApplyLadderFeeRate.LadderFeeRate) MapUtils.getObject(ladderMap, ladderKey);
            if (Objects.isNull(ladderFeeRateVal)) {
                throw TradeManageBizException.createExc("缺少阶梯区间. [" + ladderKey + "]");
            }
            FeeRateUtils.verifyRangeValue("阶梯费率", ladderFeeRateVal.getFeeRate(), feeRateConfig.getFeeRateMin(), feeRateConfig.getFeeRateMax());
            ApplyLadderFeeRate.LadderFeeRate ladderFeeRate = new ApplyLadderFeeRate.LadderFeeRate();
            ladderFeeRate.setMin(feeRateConfig.getMin());
            ladderFeeRate.setMax(feeRateConfig.getMax());
            ladderFeeRate.setFeeRate(ladderFeeRateVal.getFeeRate());
            return ladderFeeRate;
        }).collect(Collectors.toList());
    }

    /**
     * 附加子状态相关信息到请求body内
     * 　@link https://confluence.wosai-inc.com/pages/viewpage.action?pageId=729219587
     *
     * @param activityDo
     * @param request
     * @return
     */
    public void attachActivitySubStatusInfoToRequest(ApplyActivityRequest request, ActivityEntity activityDo) {
        //非支付源活动处理
        if (!ActivityTypeEnum.payWayActivities.contains(activityDo.getType())) {
            ActivitySubStatusDetailEntity subDetail = findApplySubStatusDetailDo(request.getActivityId(), request.getSubStatusDetailId());
            request.setSubStatusDetailId(subDetail.getId());
            request.setComboId(subDetail.getComboId());
            return;
        }
        // 主体挂靠
        attachMasterInfo(request, activityDo);
        //支付源活动处理
        ActivityApplyEntity historyApplyDo = activityApplyDOMapper.getApplyCanceledByLastUpdateTime(request.getMerchantSn(), ImmutableList.of(activityDo.getId()));
        if (Objects.isNull(historyApplyDo)) {
            ActivitySubStatusDetailEntity subDetail = findApplySubStatusDetailDo(request.getActivityId(), request.getSubStatusDetailId());
            request.setSubStatusDetailId(subDetail.getId());
            request.setComboId(subDetail.getComboId());
            return;
        }
        //是否带风控取消标识
        boolean riskFlag = false;
        ApplyExtraParam extra = historyApplyDo.buildJsonExtra();
        if (Objects.nonNull(extra)) {
            riskFlag = Objects.equals(CancelActivityApplyRequest.OCCUR_SOURCE_RISK, extra.getOccurSource());
        }
        //带风控取消标识，默认处理
        if (riskFlag) {
            ActivitySubStatusDetailEntity subDetail = findApplySubStatusDetailDo(request.getActivityId(), request.getSubStatusDetailId());
            request.setSubStatusDetailId(subDetail.getId());
            request.setComboId(subDetail.getComboId());
            return;
        }
        List<ApplyActivityRequest.ApplyPayFeeRate> feeRateList = null;
        Map<String, String> feeRateMap = historyApplyDo.buildFeeRate();
        //恢复主动取消相关参数，先取activity_apply
        if (MapUtils.isNotEmpty(feeRateMap)) {
            feeRateList = feeRateMap.entrySet().stream()
                    .filter(feeRate -> StringUtils.isNotEmpty(feeRate.getValue()))
                    .map(feeRate -> ApplyActivityRequest.ApplyPayFeeRate.builder()
                            .payWay(Integer.valueOf(feeRate.getKey()))
                            .feeRate(feeRate.getValue())
                            .build()
                    ).collect(Collectors.toList());
        }
        //再从merchant_fee_rate里查找
        if (CollectionUtils.isEmpty(feeRateList)) {
            MerchantFeeRateEntity feeRateDo = merchantFeeRateDao.selectLastDisabledByCombo(request.getMerchantSn(), historyApplyDo.getCombo_id());
            //历史被归档数据、刚报名就被取消兼容处理
            if (Objects.isNull(feeRateDo)) {
                ActivitySubStatusDetailEntity subDetail = findApplySubStatusDetailDo(request.getActivityId(), request.getSubStatusDetailId());
                request.setSubStatusDetailId(subDetail.getId());
                request.setComboId(subDetail.getComboId());
                return;
            }
            feeRateList = ImmutableList.of(ApplyActivityRequest.ApplyPayFeeRate.builder()
                    .payWay(feeRateDo.getPayWay())
                    .feeRate(feeRateDo.getFeeRate())
                    .build());
        }
        ValidationUtils.check(CollectionUtils.isNotEmpty(feeRateList), "待恢复费率为空");
        ApplyExtraParam extraParam = new ApplyExtraParam();
        extraParam.setSourceApplyId(historyApplyDo.getId());
        request.setSubStatusDetailId(historyApplyDo.getActivity_sub_status_id());
        request.setComboId(historyApplyDo.getCombo_id());
        request.setSourceApplyId(historyApplyDo.getId());
        request.setApplyPayFeeRates(feeRateList);
    }

    private void attachMasterInfo(ApplyActivityRequest request, ActivityEntity activityDo) {
        ApplyActivityRequest.MasterInfo masterInfo = request.getMasterInfo();
        if (Objects.isNull(masterInfo)) {
           return;
        }
        String parentMerchantSn = masterInfo.getMerchantSn();
        ValidationUtils.notNull(masterInfo.getApplyId(), "支付源活动报名主体商户sn不能为空");
        ActivityApplyEntity masterApplyDo = activityApplyDOMapper.selectByPrimaryKey(masterInfo.getApplyId());
        ValidationUtils.check(Objects.nonNull(masterApplyDo) && Objects.equals(masterApplyDo.getStatus(), ActivityConstants.EFFECT),
                "支付源活动报名主体商户无生效中报名记录");
        //附加主体商户applyId
        masterInfo.setApplyId(masterApplyDo.getId());
        request.setSubStatusDetailId(masterApplyDo.getActivity_sub_status_id());
        request.setComboId(masterApplyDo.getCombo_id());
        Map<String, String> feeRateMap = masterApplyDo.buildFeeRate();
        ValidationUtils.check(MapUtils.isNotEmpty(feeRateMap), "主体商户费率为空");
        request.setRemark(request.getRemark() + "，挂靠主体商户sn：" + parentMerchantSn);
        request.setApplyPayFeeRates(ImmutableList.of(ApplyActivityRequest.ApplyPayFeeRate.builder()
                .payWay(activityDo.getType()) // type==payway
                .feeRate(feeRateMap.get(activityDo.getType() + ""))
                .build()));
    }

    /**
     * 获取本次待生效payWay
     *
     * @param request
     * @return
     */
    public List<Integer> getApplyPayWayList(ActivityEntity activityEntity, ApplyActivityRequest request) {
        //本次活动报名的支付方式
        if (CollectionUtils.isNotEmpty(request.getApplyPayFeeRates())) {
            return request.getApplyPayFeeRates().stream().map(ApplyActivityRequest.ApplyPayFeeRate::getPayWay).collect(Collectors.toList());
        }
        //未填写payWay则表示全部生效，那么从套餐明细里读取
        Long comboId = request.getComboId();
        if (Objects.isNull(comboId)) {
            comboId = activityEntity.getCombo_id();
        }
        return tradeComboDetailDao.selectList(TradeComboDetailQueryDalParam.builder().comboId(comboId).build())
                .stream().map(TradeComboDetailEntity::getPayway).collect(Collectors.toList());
    }

    public String buildRestoreMessage(ApplyActivityRequest request) {
        if (Objects.isNull(request.getSourceApplyId())) {
            return StringUtils.EMPTY;
        }
        try {
            ActivitySubStatusDetailEntity subStatus = activitySubStatusDetailMapper.selectByPrimaryKey(request.getSubStatusDetailId());
            return String.format("，已恢复活动状态：%s，生效费率：%s。", subStatus.getName(), request.getApplyPayFeeRates().get(0).getFeeRate());
        } catch (Exception e) {
            log.error("buildRestoreMessage failed. request:{}", JsonUtil.encode(request));
        }
        return StringUtils.EMPTY;
    }
}

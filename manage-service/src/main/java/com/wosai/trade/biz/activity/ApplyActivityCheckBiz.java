package com.wosai.trade.biz.activity;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.trade.biz.audit.filter.MutexFilter;
import com.wosai.trade.biz.fee.validator.FeeRateValidator;
import com.wosai.trade.constant.enums.ApplyActivityCheckSceneEnum;
import com.wosai.trade.impl.TradeCommonService;
import com.wosai.trade.model.apollo.VerifyApplyActivityConfig;
import com.wosai.trade.model.biz.ApplyFeeRateChainExtra;
import com.wosai.trade.model.enums.PayWayEnum;
import com.wosai.trade.repository.ActivityApplyRepository;
import com.wosai.trade.repository.FeeRateRepository;
import com.wosai.trade.repository.dao.*;
import com.wosai.trade.repository.dao.entity.ActivityApplyEntity;
import com.wosai.trade.repository.dao.entity.ActivityEntity;
import com.wosai.trade.repository.dao.entity.TradeAppEntity;
import com.wosai.trade.repository.dao.entity.TradeComboEntity;
import com.wosai.trade.service.TradeComboDetailService;
import com.wosai.trade.service.activity.constant.ActivityConstants;
import com.wosai.trade.service.activity.enums.ApplyActivityPlatformEnum;
import com.wosai.trade.service.activity.enums.TradeAssessmentTypeEnum;
import com.wosai.trade.service.activity.request.*;
import com.wosai.trade.service.activity.request.BulkAddActivityRequest.AddActivityByFile;
import com.wosai.trade.service.activity.request.CancelActivityApplyRequest.CancelByActivityApply;
import com.wosai.trade.service.activity.request.CancelActivityApplyRequest.CancelByFile;
import com.wosai.trade.service.activity.request.CancelActivityApplyRequest.CancelEntity;
import com.wosai.trade.service.enums.ComboConfigLevelEnum;
import com.wosai.trade.service.exception.TradeManageBizException;
import com.wosai.trade.service.result.TradeComboDetailResult;
import com.wosai.trade.util.DateTimeUtils;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.service.AcquirerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.wosai.trade.service.activity.constant.ActivityConstants.PROCESSING_STATUS;
import static com.wosai.trade.service.activity.enums.ActivityTypeEnum.payWayActivities;

/**
 * @Description: 申请活动校验
 * <AUTHOR>
 * @Date: 2022/5/25 10:52 上午
 */
@Component
@Slf4j
public class ApplyActivityCheckBiz {
    @Autowired
    private ActivityApplyDOMapper activityApplyDOMapper;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private FeeRateValidator feeRateValidator;
    @Autowired
    private TradeComboDetailService comboDetailService;
    @Autowired
    private AcquirerService acquirerService;
    @Autowired
    private MutexFilter mutexFilter;
    @Autowired
    private TradeComboDao tradeComboDao;
    @Autowired
    private ActivityDOMapper activityDOMapper;
    @Autowired
    private TradeCommonService tradeCommonService;
    @Autowired
    private FeeRateRepository feeRateRepository;
    @Autowired
    private GlobalActivityRuleMapper globalActivityRuleMapper;
    @Autowired
    private MerchantFeeRateDao merchantFeeRateDao;
    @Autowired
    private TradeAppDao tradeAppDao;
    @Autowired
    private ActivityApplyRepository activityApplyRepository;
    @Autowired
    private UploadActivityCrossCategoryFixBiz uploadActivityCrossCategoryFixBiz;

    /**
     * 申请活动基础校验
     *
     * @param request
     */
    public void applyBaseCheck(ApplyActivityRequest request) {
        if (StringUtils.isBlank(request.getMerchantSn()) && StringUtils.isBlank(request.getMerchantId())) {
            TradeManageBizException.createExc("申请活动 商户SN和商户ID 不能同时为空");
        }

        if (StringUtils.isBlank(request.getMerchantId())) {
            //商户ID为空，根据SN查询ID
            Map merchant = merchantService.getMerchantBySn(request.getMerchantSn());
            if (WosaiMapUtils.isEmpty(merchant)) {
                TradeManageBizException.createExc("商户不存在");
            }
            request.setMerchantId(WosaiMapUtils.getString(merchant, DaoConstants.ID));
        } else {
            //商户ID不为空，根据ID查询SN
            Map merchant = merchantService.getMerchant(request.getMerchantId());
            if (WosaiMapUtils.isEmpty(merchant)) {
                TradeManageBizException.createExc("商户不存在");
            }
            request.setMerchantSn(WosaiMapUtils.getString(merchant, Merchant.SN));
        }

        if (StringUtils.isBlank(request.getSn())) {
            request.setSn(request.getMerchantSn());
        }


    }

    /**
     * 全局规则校验
     * @param request
     */
    public void globalRuleCheck(ApplyActivityRequest request) {
        List<ApplyActivityRequest.ApplyPayFeeRate> applyPayFeeRates = request.getApplyPayFeeRates();
        if (CollectionUtils.isEmpty(applyPayFeeRates)) {
            return;
        }
        List<Integer> payWays = applyPayFeeRates.stream()
                .map(ApplyActivityRequest.ApplyPayFeeRate::getPayWay)
                .collect(Collectors.toList());
        globalRuleCheck0(request.getMerchantSn(), request.getActivityId(), payWays);
    }

    protected void globalRuleCheck0(String merchantSn, Long activityId, List<Integer> payWays) {
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        String industry = MapUtils.getString(merchant, Merchant.INDUSTRY);
        if (Objects.isNull(industry)) {
            return;
        }
        //根据行业查询全局规则
        List<Map> activityRules = globalActivityRuleMapper.queryRuleByIndustry(industry);
        if (CollectionUtils.isEmpty(activityRules)) {
            return;
        }
        Set<Long> ids = new LinkedHashSet<>();
        //遍历全局规则rule，根据payway 来检验
        boolean contains = false;
        for (Map rule : activityRules) {
            if (contains) {
                break;
            }
            Map<String, Object> ruleMap = (Map) rule.get("rule");
            for (int payway : payWays) {
                List ruleList = ((List) ruleMap.get(payway + ""));
                if (Objects.isNull(ruleList)) { //payWay不存在时不处理
                    continue;
                }
                List activityIds = (List) ruleList.stream().map(s -> NumberUtils.toLong(s + "")).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(activityIds)) {
                    continue;
                }
                //只要有一个规则通过，直接break
                if (activityIds.contains(activityId)) {
                    contains = true;
                    break;
                } else {
                    //校验不通过，需要对应的行业id
                    Long id = MapUtils.getLong(rule, DaoConstants.ID);
                    ids.add(id);
                }

            }
        }

        if (!contains && !CollectionUtils.isEmpty(ids)) {
            TradeManageBizException.createExc("该商户行业为特殊结算底价行业，不允许申请该活动，对应全局规则id为： " + JSON.toJSONString(ids));
        }

    }

    /**
     * 申请活动业务校验
     *
     * @param request
     */
    public void applyBizCheck(ApplyActivityRequest request, ActivityEntity activityEntity, ApplyActivityCheckSceneEnum sceneEnum) {
        //前置校验
        verifyPreApply0(request.getMerchantSn(), activityEntity);
        //应用方校验
        verifyTradeApp(request, activityEntity);
        //报名时幂等校验
        if (ApplyActivityCheckSceneEnum.APPLY == sceneEnum) {
            //是否存在进行中的活动
            if (payWayActivities.contains(activityEntity.getType())) {
                if (StringUtils.isBlank(request.getSubMerchantSn())) {
                    TradeManageBizException.createExc("支付源活动 子商户号不能为空");
                }
                //子商户号 是否报名
                int count = activityApplyDOMapper.countByActivityIdAndStatusAndSn(activityEntity.getId(), request.getMerchantSn(),
                        request.getSubMerchantSn(), PROCESSING_STATUS);
                if (count > 0) {
                    TradeManageBizException.createExc("申请活动已存在，不允许重复报名");
                }
                // 挂靠检测
                Long parentApplyId = request.fetchParentApplyId();
                if (Objects.nonNull(parentApplyId)) {
                    ActivityApplyEntity masterApplyDo = activityApplyDOMapper.selectByPrimaryKey(parentApplyId);
                    if (Objects.isNull(masterApplyDo)) {
                        log.info("支付源挂靠主体商户状态不是生效中. masterApplyId={}", parentApplyId);
                        throw new TradeManageBizException("支付源挂靠主体商户不存在");
                    }
                    if (!Objects.equals(masterApplyDo.getStatus(), ActivityConstants.EFFECT)) {
                        log.info("支付源挂靠主体商户状态不是生效中. masterApplyId={},masterMerchantSn={},status={}",
                                masterApplyDo.getId(), masterApplyDo.getMerchant_sn(), masterApplyDo.getStatus());
                        throw new TradeManageBizException("支付源挂靠主体商户状态不是生效中");
                    }
                }
            } else {
                // 交易考核活动校验
                TradeAssessmentTypeEnum assessmentTypeEnum = activityEntity.fetchTradeAssessmentType();
                if (Objects.nonNull(assessmentTypeEnum) && activityApplyRepository.existsAssessmentActivityApply(request.getMerchantSn())) {
                    TradeManageBizException.createExc("交易考核活动已存在，不允许重复报名");
                }
                //非支付源活动
                int count = activityApplyDOMapper.countByActivityIdAndStatusAndSn(activityEntity.getId(), request.getMerchantSn(),
                        null, PROCESSING_STATUS);
                if (count > 0) {
                    TradeManageBizException.createExc("申请活动已存在，不允许重复报名");
                }
            }
        }
        //校验活动报名费率列表
        feeRateValidator.validateApplyActivityRequest(request);
        //商户当前生效活动 与 申请活动是否互斥
        ApplyFeeRateChainExtra extra = null;
        if (CollectionUtils.isNotEmpty(request.getApplyPayFeeRates())) {
            Map<String, String> applyWays = new HashMap<>();
            for (ApplyActivityRequest.ApplyPayFeeRate apply : request.getApplyPayFeeRates()) {
                applyWays.put(String.valueOf(apply.getPayWay()), apply.getFeeRate());
            }
            extra = new ApplyFeeRateChainExtra(true, applyWays);
        }
        Map<String, Object> merchantInfo = merchantService.getMerchantBySn(request.getMerchantSn());
        TradeComboEntity tradeComboEntity = tradeComboDao.selectById(activityEntity.getCombo_id());
        mutexFilter.doFilter(tradeComboEntity, merchantInfo, System.currentTimeMillis(), extra);
    }

    /**
     * 商户报名前置校验
     *
     * @param merchantSn
     * @param activityDo
     */
    public void verifyPreApply(String merchantSn, ActivityEntity activityDo) {
        List<Integer> payWays = Lists.newArrayList();
        for (ActivityRule.TradeCombo tradeCombo : activityDo.buildChangeCombo()) {
            for (ActivityRule.TradeComboDetail tradeComboDetail : tradeCombo.getTradeComboDetails()) {
                payWays.add(tradeComboDetail.getPayWay());
            }
        }
        verifyPreApply0(merchantSn, activityDo);
        globalRuleCheck0(merchantSn, activityDo.getId(), payWays);
    }

    /**
     * 商户报名前置校验
     *
     * @param merchantSn
     * @param activityDo
     */
    private void verifyPreApply0(String merchantSn, ActivityEntity activityDo) {
        //符合的收单机构
        String acquirer = acquirerService.getMerchantAcquirer(merchantSn);
        List<String> supportAcquirers = activityDo.buildAcquirer();
        //兼容收单机构lkl lklV3
        if (supportAcquirers.contains(McConstant.ACQUIRER_LKL)) {
            supportAcquirers.add(McConstant.RULE_GROUP_LKLV3);
        }
        if (supportAcquirers.contains(McConstant.RULE_GROUP_LKLV3)) {
            supportAcquirers.add(McConstant.ACQUIRER_LKL);
        }
        if (!supportAcquirers.contains(acquirer)) {
            TradeManageBizException.createExc("商户所在收单机构 不支持此活动");
        }
        Map<String, Object> merchantInfo = merchantService.getMerchantBySn(merchantSn);
        verifyCities(merchantInfo, activityDo);
        // 校验行业
        verifyIndustry(merchantInfo, activityDo);
        verifyOrganizations(merchantInfo, activityDo);
        //报名时间校验
        verifyApplyTime(merchantInfo, activityDo);
    }

    public void bulkApplyCheck(BulkAddActivityRequest request) {
        if (request == null) {
            TradeManageBizException.createExc("请求不能为空");
        }

        BulkAddActivityRequest.AddDescribe describe = request.getAddDescribe();
        if (describe == null) {
            describe = new BulkAddActivityRequest.AddDescribe();
        }
        if (StringUtils.isBlank(describe.getOperator())) {
            describe.setOperator("system");
        }
        if (StringUtils.isBlank(describe.getOperatorName())) {
            describe.setOperatorName("system");
        }
        if (StringUtils.isBlank(describe.getReason())) {
            describe.setReason("活动批量申请");
        }
        request.setAddDescribe(describe);


        AddActivityByFile byFile = request.getAddActivityByFile();
        if (byFile == null) {
            TradeManageBizException.createExc("开通对象不能为空");
        }

        if (StringUtil.isEmpty(byFile.getOssPath())) {
            TradeManageBizException.createExc("文件地址不能为空");
        }
        if (byFile.getActivityId() == null) {
            TradeManageBizException.createExc("开通活动对象不能为空");
        }
        if (byFile.getPlatform() == null) {
            TradeManageBizException.createExc("开通平台不能为空");
        }
    }

    public void cancelSingleCheck(CancelActivityApplyRequest request) {
        if (request == null) {
            TradeManageBizException.createExc("请求不能为空");
        }
        CancelActivityApplyRequest.CancelDescribe describe = request.getCancelDescribe();
        if (request.getCancelDescribe() == null) {
            describe = new CancelActivityApplyRequest.CancelDescribe();
        }
        if (StringUtils.isBlank(describe.getOperator())) {
            describe.setOperator("system");
        }
        if (StringUtils.isBlank(describe.getOperatorName())) {
            describe.setOperator("system");
        }
        if (StringUtils.isBlank(describe.getReason())) {
            describe.setOperator("申请活动取消");
        }
        request.setCancelDescribe(describe);


        if (request.getCancelEntity() == null) {
            TradeManageBizException.createExc("开通对象不能为空");
        }
        CancelEntity cancelEntity = request.getCancelEntity();
        if (cancelEntity.getCancelByActivityApply() == null) {
            TradeManageBizException.createExc("取消对象不能为空");
        }

        CancelByActivityApply cancelByActivityApply = cancelEntity.getCancelByActivityApply();
        if (cancelByActivityApply.getActivityApplyId() == null) {
            TradeManageBizException.createExc("取消商户活动id不能为空");
        }
    }

    /**
     * 校验是否存在待取消payWay
     *
     * @param merchantSn
     * @param activityId
     * @param payList
     */
    public void verifyCancelPayWayEffectFeeRate(String merchantSn, Long activityId, List<String> payList) {
        if (CollectionUtils.isEmpty(payList)) {
            return;
        }
        List<Integer> payWayCodeList = payList.stream().map(PayWayEnum::getCodeByName).collect(Collectors.toList());
        ActivityApplyEntity activityApplyEntity = activityApplyDOMapper.getEffectIngApplyByMerchantSn(activityId, merchantSn);
        if (Objects.isNull(activityApplyEntity)) {
            TradeManageBizException.createExc("活动申请不存在");
        }
        if (activityApplyEntity.getStatus() == ActivityConstants.EFFECT_NOT_STANDARD) {
            log.info("活动状态为不达标. 可以通过取消. merchantSn={}", merchantSn);
            return;
        }
        ActivityEntity activity = activityDOMapper.selectByPrimaryKey(activityApplyEntity.getActivity_id());
        int cnt = feeRateRepository.countEffectFeeRate(activityApplyEntity.getSn(), activityApplyEntity.getCombo_id(),
                ComboConfigLevelEnum.valueOf(StringUtils.upperCase(activity.getConfig_level())),
                payWayCodeList);
        if (cnt <= 0) {
            throw TradeManageBizException.createExc("无可变更费率. payWays:" + payList);
        }
    }

    public void cancelBulkCheck(CancelActivityApplyRequest request) {
        if (request == null) {
            TradeManageBizException.createExc("请求不能为空");
        }
        CancelActivityApplyRequest.CancelDescribe describe = request.getCancelDescribe();
        if (request.getCancelDescribe() == null) {
            describe = new CancelActivityApplyRequest.CancelDescribe();
        }
        if (StringUtils.isBlank(describe.getOperator())) {
            describe.setOperator("system");
        }
        if (StringUtils.isBlank(describe.getOperatorName())) {
            describe.setOperator("system");
        }
        if (StringUtils.isBlank(describe.getReason())) {
            describe.setOperator("申请批量取消");
        }
        request.setCancelDescribe(describe);

        if (request.getCancelEntity() == null) {
            TradeManageBizException.createExc("取消对象不能为空");
        }
        CancelEntity cancelEntity = request.getCancelEntity();
        if (cancelEntity.getCancelByFile() == null) {
            TradeManageBizException.createExc("取消对象不能为空");
        }

        CancelByFile cancelByFile = cancelEntity.getCancelByFile();
        if (StringUtil.isEmpty(cancelByFile.getOssPath())) {
            TradeManageBizException.createExc("文件地址不能为空");
        }
        if (cancelByFile.getActivityId() == null) {
            TradeManageBizException.createExc("取消活动对象不能为空");
        }

    }


    /**
     * sp批量开通文件校验
     *
     * @param request
     */
    public void spBulkApplyCheck(SPBulkApplyRequest request) {
        if (StringUtils.isBlank(request.getMerchantSn())) {
            TradeManageBizException.createExc("商户号不能为空");
        }
        String isShare = request.getIsShare();
        if (StringUtils.isBlank(isShare)) {
            TradeManageBizException.createExc("额度是否共享不能为空");
        }
        if ("是".equals(isShare)) {
            if (StringUtils.isBlank(request.getQuotaSign())) {
                TradeManageBizException.createExc("共享额度标示不能为空");
            }
        } else if ("否".equals(isShare)) {
            if (StringUtils.isNotBlank(request.getQuotaSign())) {
                TradeManageBizException.createExc("该商户无共享  额度标示需为空");
            }
        } else {
            TradeManageBizException.createExc("额度是否共享 只允许 是/否");
        }


    }

    public void auditBulkApplyCheck(AuditBulkApplyRequest request) {
        if (StringUtils.isBlank(request.getMerchantSn())) {
            TradeManageBizException.createExc("商户SN不能为空");
        }

        if (StringUtils.isBlank(request.getPayWayName())) {
            TradeManageBizException.createExc("支付渠道不能为空");
        }

        if (!request.isResult()) {
            TradeManageBizException.createExc(request.getFailMessage());
        }
    }

    /**
     * 批量取消 校验
     *
     * @param request
     */
    public void bulkCancelCheck(BulkCancelRequest request, Integer platForm) {
        if (StringUtils.isBlank(request.getMerchantSn())) {
            TradeManageBizException.createExc("商户SN不能为空");
        }

        if (ApplyActivityPlatformEnum.APPROVAL.getCode() == platForm) {
            if (StringUtils.isBlank(request.getPayWayName())) {
                TradeManageBizException.createExc("支付渠道不能为空");
            }
        } else {
            if (StringUtils.isNotBlank(request.getPayWayName())) {
                TradeManageBizException.createExc("SP 后台 不允许指定支付渠道");
            }
        }
    }

    /**
     * 城市验证
     *
     * @param merchantInfo
     * @param activityEntity
     */
    private void verifyCities(Map<String, Object> merchantInfo, ActivityEntity activityEntity) {
        //城市里包含：省、市
        List<String> cities = activityEntity.buildCities();
        if (CollectionUtils.isEmpty(cities)) {
            return;
        }
        List<String> merchantCities = ImmutableList.of(Merchant.PROVINCE, Merchant.CITY, Merchant.DISTRICT)
                .stream().map(verifyKey -> WosaiMapUtils.getString(merchantInfo, verifyKey))
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        // 这里算一个交集
        merchantCities.retainAll(cities);
        if (CollectionUtils.isEmpty(merchantCities)) {
            throw TradeManageBizException.createExc("商户所在城市 不在可参与的城市内");
        }
    }

    /**
     * 组织验证
     *
     * @param merchantInfo
     * @param activityEntity
     */
    private void verifyOrganizations(Map<String, Object> merchantInfo, ActivityEntity activityEntity) {
        List<String> organizations = activityEntity.buildOrganizations();
        if (CollectionUtils.isEmpty(organizations)) {
            return;
        }
        String merchantId = MapUtils.getString(merchantInfo, DaoConstants.ID);
        String path = tradeCommonService.findCustomerRelationOriginPath(merchantId);
        List<String> originCodes = StrUtil.splitTrim(path, ",");
        if (CollectionUtils.isEmpty(originCodes)) {
            throw TradeManageBizException.createExc("商户所在组织 不在可参与的组织内");
        }
        //无交集，报错
        organizations.retainAll(originCodes);
        if (CollectionUtils.isEmpty(organizations)) {
            throw TradeManageBizException.createExc("商户所在组织 不在可参与的组织内");
        }
    }

    /**
     * 行业验证
     *
     * @param merchantInfo 商户信息
     * @param activityEntity 活动实体
     * @throws TradeManageBizException 当行业验证不通过时抛出
     */
    private void verifyIndustry(Map<String, Object> merchantInfo, ActivityEntity activityEntity) {
        final String TRAINING_INDUSTRY_APPEAL_MSG = "1、若不是教培行业，可以申辩为非教培。"
                + "2、若是线下教培行业，请修改所属行业后再提交审批报名活动费率。"
                + "3、若是线上教培行业，请修改所属行业，且不支持报名教培活动费率。";
        final String NON_TRAINING_INDUSTRY_MSG = "非教培行业不支持报名教培活动费率";

        String merchantSn = MapUtils.getString(merchantInfo, Merchant.SN);
        Long activityId = activityEntity.getId();

        try {
            verifyIndustryRule(merchantInfo, activityEntity);
        } catch (TradeManageBizException e) {
            Pair<Boolean, Boolean> result = uploadActivityCrossCategoryFixBiz.checkMerchantInRectificationList(activityId, merchantSn);
            // 是否为整改名单活动
            boolean isRectificationActivity = result.getLeft();
            // 商户是否在该活动类型的整改名单中
            boolean isMerchantInList = result.getRight();
            if (isRectificationActivity) {
                // 以下是整改名单活动的处理逻辑
                String errorMsg = isMerchantInList ? TRAINING_INDUSTRY_APPEAL_MSG : NON_TRAINING_INDUSTRY_MSG;
                throw TradeManageBizException.createExc(errorMsg);
            }
            // 非整改名单活动，直接抛出原异常
            throw e;
        }
    }

    private void verifyIndustryRule(Map<String, Object> merchantInfo, ActivityEntity activityEntity) {
        ActivityRule.IndustryRule industryRule = activityEntity.buildIndustryRule();
        if (Objects.isNull(industryRule)) {
            return;
        }
        String industryId = WosaiMapUtils.getString(merchantInfo, Merchant.INDUSTRY);
        if (StringUtils.isBlank(industryId)) {
            throw TradeManageBizException.createExc("商户行业为空");
        }
        if (CollectionUtils.isNotEmpty(industryRule.getAllowList())
                && !industryRule.getAllowList().contains(industryId)) {
            throw TradeManageBizException.createExc("商户所在行业 不在可参与的行业白名单内");
        }
        if (CollectionUtils.isNotEmpty(industryRule.getDenyList())
                && industryRule.getDenyList().contains(industryId)) {
            throw TradeManageBizException.createExc("商户所在行业 在可参与的行业黑名单内");
        }
    }

    /**
     * 报名时间验证
     *
     * @param merchantInfo
     * @param activityEntity
     */
    private void verifyApplyTime(Map<String, Object> merchantInfo, ActivityEntity activityEntity) {
        ActivityRule.ApplyTime applyTime = activityEntity.buildApplyTime();
        //兼容历史数据
        if (Objects.isNull(applyTime)) {
            return;
        }
        Date now = new Date();
        if (!applyTime.isRange(now)) {
            throw TradeManageBizException.createExc(String.format("不在活动报名时间单范围内. %s~%s",
                    DateTimeUtils.getFormatDateByDate(applyTime.getBegin(), DateTimeUtils.format),
                    DateTimeUtils.getFormatDateByDate(applyTime.getEnd(), DateTimeUtils.format)
            ));
        }
    }

    /**
     * 活动申请应用方校验
     *
     * @param request
     * @param activityEntity
     */
    protected void verifyTradeApp(ApplyActivityRequest request, ActivityEntity activityEntity) {
        Long appId = activityEntity.getBiz_id();
        String merchantSn = request.getMerchantSn();
        //当前活动业务方是否参与报名校验拦截
        TradeAppEntity appDo = tradeAppDao.queryTradeAppById(appId);
        if (!hasVerifyTradeApp(appDo)) {
            return;
        }
        List<Integer> payWayList;
        //没有指定payWay说明全部参与报名
        if (CollectionUtils.isEmpty(request.getApplyPayFeeRates())) {
            List<TradeComboDetailResult> comboDetails = comboDetailService.listByComboId(request.getComboId());
            payWayList = comboDetails.stream().map(TradeComboDetailResult::getPayway).collect(Collectors.toList());
        } else {
            payWayList = request.getApplyPayFeeRates().stream().map(ApplyActivityRequest.ApplyPayFeeRate::getPayWay).collect(Collectors.toList());
        }
        //已开通多业务的payWay列表
        List<Integer> openPayWayList = merchantFeeRateDao.findOpenTradeAppPayWayList(appId, merchantSn, payWayList);
        payWayList.removeAll(openPayWayList);
        if (CollectionUtils.isNotEmpty(payWayList)) {
            List<String> payWayNameList = payWayList.stream().map(PayWayEnum::getNameByCode).collect(Collectors.toList());
            String message = String.format("业务处理失败，%s应用%s未开通，不能申请该应用下的活动费率", appDo.getName(), payWayNameList);
            throw TradeManageBizException.createExc(message);
        }
    }

    /**
     * 当前活动业务方是否参与报名校验拦截
     *
     * @param appDo
     * @return
     */
    private boolean hasVerifyTradeApp(TradeAppEntity appDo) {
        //多业务全部参与校验拦截
        if (appDo.isPayApp()) {
            return true;
        }
        //基础业务方配置项
        return !VerifyApplyActivityConfig.disableVerifyBaseApp(appDo.getId());
    }
}

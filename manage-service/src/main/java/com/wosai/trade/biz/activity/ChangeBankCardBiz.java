package com.wosai.trade.biz.activity;

import com.google.common.collect.ImmutableList;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.util.CollectionUtil;
import com.wosai.middleware.hera.toolkit.trace.Trace;
import com.wosai.service.IMerchantGrayService;
import com.wosai.service.enumeration.AccessTokenType;
import com.wosai.trade.biz.audit.AuditBiz;
import com.wosai.trade.biz.businesslog.BusinessOpLogBiz;
import com.wosai.trade.biz.businesslog.TemplateCodeEnum;
import com.wosai.trade.config.apollo.CommonApolloConfig;
import com.wosai.trade.impl.TradeCommonService;
import com.wosai.trade.repository.dao.QuotaActivityApplyEntityMapper;
import com.wosai.trade.repository.dao.entity.QuotaActivityApplyEntity;
import com.wosai.trade.repository.dao.entity.QuotaActivityEntity;
import com.wosai.trade.service.QuotaActivityApplyService;
import com.wosai.trade.service.activity.constant.ActivityConstants;
import com.wosai.trade.service.activity.request.QuotaActivityCancelRequest;
import com.wosai.trade.service.enums.QuotaActivityTypeEnum;
import com.wosai.trade.util.ConstantUtil;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.service.MerchantService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @Description:
 * <AUTHOR>
 * @Date: 2022/9/6 3:23 下午
 */
@Component
@Slf4j
public class ChangeBankCardBiz {
    @Autowired
    private QuotaActivityApplyEntityMapper quotaApplyMapper;
    @Autowired
    private QuotaActivityApplyService quotaApplyService;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private IMerchantGrayService iMerchantGrayService;

    @Autowired
    private TradeCommonService tradeCommonService;

    @Autowired
    private BusinessOpLogBiz businessOpLogBiz;

    @Autowired
    private CommonApolloConfig commonApolloConfig;

    private static final String CANCEL_DO_NOTIFY_TEMPLATE = "XLLU0JYCEKDY";

    private static final Integer bankCooperationD0Merchant = 23;


    /**
     * 切换银行卡业务处理
     *
     * @param merchantSn
     */
    @Trace
    public void changeCard(String merchantSn, String merchantId) {
        try {
            //额度减免
            quotaActivity(merchantSn);
        } catch (Exception e) {
            log.error("切换银行卡业务处理-额度减免失败. merchantSn={}", merchantSn, e);
        }
        //换卡 取消银行d0并发aop通知
        d0WithdrawHandle(merchantId);

    }

    public void d0WithdrawHandle(String merchantId) {
        Map<String, Object> updateMap = new HashMap<>();
        updateMap.put("merchant_id", merchantId);
        updateMap.put("type", AccessTokenType.CUSTOMIZE_OFFSET_HOUR_MERCHANT_D0.getCode());
        Map<String, Object> query = iMerchantGrayService.query(updateMap);

        Integer value = MapUtils.getInteger(query, "value");
        //存在配置 ，删除 && 发取消d0 aop 通知
        if (Objects.equals(value, bankCooperationD0Merchant)) {
            iMerchantGrayService.delete(updateMap);
            //删除对账周期
            updateMap.put("type", AccessTokenType.OFFSET_HOUR.getCode());
            iMerchantGrayService.delete(updateMap);
            tradeCommonService.sendAopNotice(merchantId, ImmutableList.of("TERMINALAPP"), commonApolloConfig.getSendMerchantD0AopNotifyDevCode(), CANCEL_DO_NOTIFY_TEMPLATE, null, null);
            Map before = CollectionUtil.hashMap("status", AuditBiz.OPEN);
            Map after = CollectionUtil.hashMap("status", AuditBiz.CLOSE);
            businessOpLogBiz.sendByTemplateCodeBusinessLog(TemplateCodeEnum.D0_WITHDRAW, merchantId, ConstantUtil.SYSTEM_NAME, ConstantUtil.SYSTEM_NAME, "商户换绑银行卡，取消d0提现，转为自动提现", before, after);
        }

    }


    /**
     * 额度减免
     *
     * @param merchantSn
     */
    private void quotaActivity(String merchantSn) {
        List<QuotaActivityApplyEntity> cancelList = quotaApplyMapper.getApplyCanCancelByMerchantSnWhenMerchantInfoChange(merchantSn);
        if (CollectionUtils.isEmpty(cancelList)) {
            return;
        }
        List<Long> needCancelIds = new ArrayList<>();
        for (QuotaActivityApplyEntity apply : cancelList) {
            //校验是否取消
            QuotaActivityEntity entity = apply.buildActivityInfo();
            boolean hasCancel = hasCancel(entity, apply.getMerchant_id());
            if (hasCancel) {
                log.info("额度减免换卡,满足取消条件. merchantSn={},applyId={},activityId={},activityType={}.",
                        apply.getMerchant_sn(), apply.getId(), apply.getActivity_id(), entity.getType());
                needCancelIds.add(apply.getId());
            }
        }
        log.info("额度减免换卡. merchantSn={}, needCancelIds={}", merchantSn, needCancelIds);
        if (CollectionUtils.isEmpty(needCancelIds)) {
            return;
        }

        //取消
        for (Long applyId : needCancelIds) {
            QuotaActivityCancelRequest request = new QuotaActivityCancelRequest();
            request.setApplyId(applyId);
            request.setMsg("同行换卡");
            request.setCancelType(ActivityConstants.BANK_CARD_CHANGE);
            quotaApplyService.cancelByApplyId(request);
        }
    }


    /**
     * 判断银行卡是否符合取消条件
     *
     * @param entity
     * @return true 银行卡符合条件
     * false 银行卡不符合条件
     */
    public boolean hasCancel(QuotaActivityEntity entity, String merchantId) {
        QuotaActivityTypeEnum type = QuotaActivityTypeEnum.of(entity.getType());
        // 银行类型直接返回true
        if (Objects.equals(type, QuotaActivityTypeEnum.BRANCH)
                || Objects.equals(type, QuotaActivityTypeEnum.TOTAL_BRANCH)) {
            return true;
        }
        // 存量活动类型处理
        if (Objects.equals(type, QuotaActivityTypeEnum.BASE)) {
            //银行卡
            if (StringUtils.isBlank(entity.getBank_name())) {
                return false;
            }
            Map bankInfo = merchantService.getMerchantBankAccountByMerchantId(merchantId);
            String bankName = WosaiMapUtils.getString(bankInfo, MerchantBankAccount.BANK_NAME);
            log.info("银行卡={},活动银行卡={}", bankName, entity.getBank_name());
            return !entity.getBank_name().equals(bankName);
        }
        //其它不处理
        return false;
    }

}

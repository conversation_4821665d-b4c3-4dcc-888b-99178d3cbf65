package com.wosai.trade.biz.servicefee;

import com.google.common.collect.ImmutableList;
import com.wosai.middleware.hera.toolkit.trace.TraceContext;
import com.wosai.profit.sharing.model.request.BusinessCloseRequest;
import com.wosai.profit.sharing.model.request.BusinessOpenRequest;
import com.wosai.profit.sharing.model.request.ChangeBusinessRuleRequest;
import com.wosai.profit.sharing.service.BusinessOpenService;
import com.wosai.trade.config.ActivityMonitorNoticeConfig;
import com.wosai.trade.config.apollo.CommonApolloConfig;
import com.wosai.trade.impl.TradeCommonService;
import com.wosai.trade.model.apollo.ProfitShareMetricsConfig;
import com.wosai.trade.model.dal.ServiceFeeEffectiveQueryParam;
import com.wosai.trade.repository.dao.ServiceFeeEffectiveMapper;
import com.wosai.trade.repository.dao.ServiceFeeMapper;
import com.wosai.trade.repository.dao.entity.ServiceFeeEffectiveEntity;
import com.wosai.trade.repository.dao.entity.ServiceFeeEntity;
import com.wosai.trade.service.enums.ChargeModeEnum;
import com.wosai.trade.service.enums.ServiceFeeEffectiveStatusEnum;
import com.wosai.trade.service.exception.TradeManageBizException;
import com.wosai.trade.service.servicefee.model.ProfitShareMetricsResponse;
import com.wosai.trade.util.JsonUtil;
import com.wosai.trade.util.RedisLockUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Description: 开通分账
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023/8/11
 */
@Slf4j
@Component
public class OpenProfitSharingBiz {
    @Resource
    private BusinessOpenService businessOpenService;
    @Resource
    private CommonApolloConfig commonApolloConfig;
    @Resource
    private ServiceFeeMapper serviceFeeMapper;
    @Resource
    private ServiceFeeEffectiveMapper serviceFeeEffectiveMapper;
    @Resource
    private TradeCommonService tradeCommonService;
    @Resource
    private ActivityMonitorNoticeConfig activityMonitorNoticeConfig;
    @Value("${lark.saasUrl}")
    private String larkUrl;


    /**
     * 　门店服务费规则取消分账处理
     *
     * @param cancelEffectiveDo 当前取消规则记录
     */
    public void cancelStore(ServiceFeeEffectiveEntity cancelEffectiveDo) {
        //只处理门店层级
        if (!cancelEffectiveDo.isStoreLevel()) {
            return;
        }
        if (!commonApolloConfig.getSaasSharingModeConfig().profitOpen(cancelEffectiveDo.getTradeAppId())) {
            log.info("无需取消规则记录. tradeAppId:{}", cancelEffectiveDo.getTradeAppId());
            return;
        }
        //判断是否有兜底生效中服务规则
        List<ServiceFeeEffectiveEntity> list = serviceFeeEffectiveMapper.queryByCondition(ServiceFeeEffectiveQueryParam.builder()
                .merchantSn(cancelEffectiveDo.getMerchantSn())
                .tradeAppId(cancelEffectiveDo.getTradeAppId())
                .statusList(ImmutableList.of(ServiceFeeEffectiveStatusEnum.ACTIVE.getStatusValue()))
                .build());
        if (CollectionUtils.isEmpty(list)) {
            throw TradeManageBizException.createExc(String.format("当前门店无兜底生效中服务规则. merchantSn:%s,id:%d",
                    cancelEffectiveDo.getMerchantSn(), cancelEffectiveDo.getId()));
        }
        List<ServiceFeeEffectiveEntity> effectiveEntities = serviceFeeEffectiveMapper.queryByCondition(ServiceFeeEffectiveQueryParam.builder()
                .merchantSn(cancelEffectiveDo.getMerchantSn())
                .sn(cancelEffectiveDo.getSn())
                .tradeAppId(cancelEffectiveDo.getTradeAppId())
                .statusList(ImmutableList.of(ServiceFeeEffectiveStatusEnum.ACTIVE.getStatusValue()))
                .build());
        log.info("取消后重新生效分账 cancelEffectiveId:{},merchantSn:{},size:{}", cancelEffectiveDo.getId(), cancelEffectiveDo.getMerchantSn(), effectiveEntities.size());
        //只处理门店层级下已存在的生效记录，重新开通
        if (CollectionUtils.isEmpty(effectiveEntities)) {
            //若当前门店下无生效服务费规则，则走关闭逻辑
            close(cancelEffectiveDo);
            return;
        }
        RedisLockUtil.tryLock(generatorLockName(cancelEffectiveDo), 120L, () -> {
            BusinessOpenRequest request = null;
            try {
                request = buildCancelRequestAfter(effectiveEntities);
                log.info("取消后重新生效分账 merchantSn:{},request:{}", cancelEffectiveDo.getMerchantSn(), JsonUtil.encode(request));
                businessOpenService.open(request);
            } catch (Exception e) {
                log.error("取消后重新生效分账失败.cancelEffectiveId:{}, request:{}", cancelEffectiveDo.getId(), JsonUtil.encode(request), e);
                //添加告警
                alarmEvent(cancelEffectiveDo, e);
                throw e;
            }
            return null;
        });
    }

    /**
     * 　门店服务费规则取消分账处理
     *
     * @param cancelEffectiveDo 当前取消规则记录
     */
    public void cancel(ServiceFeeEffectiveEntity cancelEffectiveDo) {
        List<ServiceFeeEffectiveEntity> effectiveEntities = serviceFeeEffectiveMapper.queryByCondition(ServiceFeeEffectiveQueryParam.builder()
                .merchantSn(cancelEffectiveDo.getMerchantSn())
                .sn(cancelEffectiveDo.getSn())
                .tradeAppId(cancelEffectiveDo.getTradeAppId())
                .scenesType(cancelEffectiveDo.getScenesType())
                .statusList(ImmutableList.of(ServiceFeeEffectiveStatusEnum.ACTIVE.getStatusValue()))
                .build());
        log.info("取消后重新生效分账 cancelEffectiveId:{},merchantSn:{},size:{}", cancelEffectiveDo.getId(), cancelEffectiveDo.getMerchantSn(), effectiveEntities.size());
        //当前门店/商户无生效规则，关闭分账
        if (CollectionUtils.isEmpty(effectiveEntities)) {
            close(cancelEffectiveDo);
            return;
        }
        //当前门店/商户下存在生效规则重新生效分账。如：校园，就是重新生效。
        RedisLockUtil.tryLock(generatorLockName(cancelEffectiveDo), 120L, () -> {
            BusinessOpenRequest request = null;
            try {
                request = buildCancelRequestAfter(effectiveEntities);
                log.info("当前门店/商户下存在生效规则重新生效分账 merchantSn:{},request:{}", cancelEffectiveDo.getMerchantSn(), JsonUtil.encode(request));
                businessOpenService.open(request);
            } catch (Exception e) {
                log.error("当前门店/商户下存在生效规则重新生效分账失败.cancelEffectiveId:{}, request:{}", cancelEffectiveDo.getId(), JsonUtil.encode(request), e);
                //添加告警
                alarmEvent(cancelEffectiveDo, e);
                throw e;
            }
            return null;
        });
    }

    private void close(ServiceFeeEffectiveEntity cancelEffectiveDo) {
        ServiceFeeEffectiveEntity.Extra effectiveExtra = cancelEffectiveDo.buildExtra();
        BusinessCloseRequest request = new BusinessCloseRequest();
        request.setTradeAppId(cancelEffectiveDo.getTradeAppId() + "");
        request.setStoreId(effectiveExtra.getStoreId());
        request.setMerchantId(effectiveExtra.getMerchantId());
        request.setScenesType(cancelEffectiveDo.getScenesType());
        try {
            log.info("关闭分账 merchantSn:{},request:{}", cancelEffectiveDo.getMerchantSn(), JsonUtil.encode(request));
            businessOpenService.close(request);
        } catch (Exception e) {
            log.error("关闭门店分账失败.cancelEffectiveId:{}, request:{}", cancelEffectiveDo.getId(), JsonUtil.encode(request), e);
            //添加告警
            alarmCloseEvent(cancelEffectiveDo, e);
            throw e;
        }
    }

    /**
     * 业务方收费规则分账基数变更
     *
     * @param serviceFee
     */
    public void changeBusinessRule(ServiceFeeEntity serviceFee) {
        if (!commonApolloConfig.getSaasSharingModeConfig().profitOpen(serviceFee.getTradeAppId())) {
            log.info("无需业务方收费规则分账基数变更. tradeAppId:{}", serviceFee.getTradeAppId());
            return;
        }
        ChangeBusinessRuleRequest request = new ChangeBusinessRuleRequest();
        request.setServiceFeeId(serviceFee.getId() + "");
        request.setBaseMetrics(buildBaseMetrics(serviceFee));
        log.info("changeBusinessRule request:{}", JsonUtil.encode(request));
        businessOpenService.changeBusinessRule(request);
    }

    /**
     * 分账开通生效
     *
     * @param effectiveDo
     * @param serviceFeeDo
     * @param endDate
     */
    public void openExecute(ServiceFeeEffectiveEntity effectiveDo, ServiceFeeEntity serviceFeeDo, Date endDate) {
        if (!commonApolloConfig.getSaasSharingModeConfig().profitOpen(serviceFeeDo.getTradeAppId())) {
            log.info("无需开通分账服务. tradeAppId:{}", serviceFeeDo.getTradeAppId());
            return;
        }
        RedisLockUtil.tryLock(generatorLockName(effectiveDo), 120L, () -> {
            BusinessOpenRequest request = null;
            try {
                request = buildBusinessOpenRequest(effectiveDo, serviceFeeDo, endDate);
                log.info("开通分账 merchantSn:{},request:{}", effectiveDo.getMerchantSn(), JsonUtil.encode(request));
                retryOpen(request);
                EnableServiceFeeRetryContextHolder.setRetry(effectiveDo.getId(), false, null);
            } catch (Exception e) {
                //设置重试
                EnableServiceFeeRetryContextHolder.setRetry(effectiveDo.getId(), true, e.getMessage());
                log.error("开通分账失败.effectiveId:{},request:{}", effectiveDo.getId(), JsonUtil.encode(request), e);
                if (!hasSaasProfitFailMessage(e.getMessage())) {
                    //添加告警
                    alarmEvent(effectiveDo, e);
                }
                throw e;
            }
            return null;
        });
    }

    private void retryOpen(BusinessOpenRequest request) throws Exception {
        Exception exception = null;
        for (int retryCount = 0; retryCount < 3; retryCount++) {
            try {
                businessOpenService.open(request);
                //成功结束重试
                exception = null;
                break;
            } catch (Exception e) {
                exception = e;
                //出现以下message时不重试
                if (hasSaasProfitFailMessage(e.getMessage())) {
                    break;
                }
            }
        }
        if (Objects.nonNull(exception)) {
            throw exception;
        }
    }

    private BusinessOpenRequest buildBusinessOpenRequest(ServiceFeeEffectiveEntity effectiveDo, ServiceFeeEntity serviceFeeDo, Date endDate) {
        BusinessOpenRequest request = buildBaseBusinessOpenRequest(effectiveDo);
        //构建服务费列表
        List<BusinessOpenRequest.ServiceFee> serviceFeeReqList = request.getServiceFees();
        serviceFeeReqList.add(buildServiceFeeReq(effectiveDo, serviceFeeDo, endDate));
        //门店层级下可能会有校园类型，故会存在多条生效记录需合并，新增同商户下多个应用场景如payway
        if (effectiveDo.isStoreLevel() || Objects.nonNull(effectiveDo.getScenesType())) {
            List<ServiceFeeEffectiveEntity> effectiveEntities = serviceFeeEffectiveMapper.queryByCondition(ServiceFeeEffectiveQueryParam.builder()
                    .merchantSn(effectiveDo.getMerchantSn())
                    .sn(effectiveDo.getSn())
                    .tradeAppId(effectiveDo.getTradeAppId())
                    .statusList(ImmutableList.of(ServiceFeeEffectiveStatusEnum.ACTIVE.getStatusValue()))
                    .build());
            List<BusinessOpenRequest.ServiceFee> reqList = effectiveEntities.stream()
                    //同维度不上送，后面会走取消逻辑
                    .filter(currentDo -> !Objects.equals(currentDo.getDimensionNo(), effectiveDo.getDimensionNo()))
                    .map(currentDo -> {
                        ServiceFeeEntity currentFeeDo = serviceFeeMapper.selectByPrimaryKey(currentDo.getFeeId());
                        return buildServiceFeeReq(currentDo, currentFeeDo, currentDo.getEndDate());
                    }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(reqList)) {
                serviceFeeReqList.addAll(reqList);
            }
        }
        request.setServiceFees(serviceFeeReqList);
        return request;
    }

    /**
     * 取消后重新构建分账参数
     *
     * @param effectiveEntities
     */
    private BusinessOpenRequest buildCancelRequestAfter(List<ServiceFeeEffectiveEntity> effectiveEntities) {
        BusinessOpenRequest request = buildBaseBusinessOpenRequest(effectiveEntities.get(0));
        //构建服务费列表
        List<BusinessOpenRequest.ServiceFee> serviceFeeReqList = request.getServiceFees();
        List<BusinessOpenRequest.ServiceFee> reqList = effectiveEntities.stream()
                .map(currentDo -> {
                    ServiceFeeEntity currentFeeDo = serviceFeeMapper.selectByPrimaryKey(currentDo.getFeeId());
                    return buildServiceFeeReq(currentDo, currentFeeDo, currentDo.getEndDate());
                }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(reqList)) {
            serviceFeeReqList.addAll(reqList);
        }
        request.setServiceFees(serviceFeeReqList);
        return request;
    }

    /**
     * 构建基础参数
     *
     * @param effectiveDo
     * @return
     */
    private BusinessOpenRequest buildBaseBusinessOpenRequest(ServiceFeeEffectiveEntity effectiveDo) {
        ServiceFeeEffectiveEntity.Extra effectiveExtra = effectiveDo.buildExtra();
        BusinessOpenRequest request = new BusinessOpenRequest();
        request.setTradeAppId(effectiveDo.getTradeAppId() + "");
        request.setMerchantId(effectiveExtra.getMerchantId());
        request.setStoreId(effectiveExtra.getStoreId());
        request.setServiceFees(Lists.newArrayList());
        return request;
    }

    private BusinessOpenRequest.ServiceFee buildServiceFeeReq(ServiceFeeEffectiveEntity effectiveDo, ServiceFeeEntity serviceFeeDo, Date endDate) {
        BusinessOpenRequest.ServiceFee serviceFee;
        ChargeModeEnum chargeMode = serviceFeeDo.buildChargeMode();
        if (ChargeModeEnum.USAGE.equals(chargeMode)) {
            serviceFee = buildUsageServiceFeeReq(effectiveDo, serviceFeeDo);
        } else if (ChargeModeEnum.PERIOD.equals(chargeMode)) {
            serviceFee = buildPeriodServiceFeeReq(effectiveDo, serviceFeeDo, endDate);
        } else if (ChargeModeEnum.ALL.equals(chargeMode)) {
            serviceFee = buildAllServiceFeeReq(effectiveDo, serviceFeeDo, endDate);
        } else {
            throw TradeManageBizException.createExc(String.format("未知的续费规则模型. mode:%s", chargeMode));
        }
        serviceFee.setScenesType(serviceFeeDo.getScenesType());
        return serviceFee;
    }

    private BusinessOpenRequest.ServiceFee buildUsageServiceFeeReq(ServiceFeeEffectiveEntity effectiveDo, ServiceFeeEntity serviceFeeDo) {
        ServiceFeeEffectiveEntity.Extra extra = effectiveDo.buildExtra();
        BusinessOpenRequest.ServiceFee serviceFeeReq = buildBaseServiceFee(effectiveDo, serviceFeeDo);
        serviceFeeReq.setRatio(generatorRatio(effectiveDo.getProfitShareRatio()));
        serviceFeeReq.setMinSharingAmount(Optional.ofNullable(effectiveDo.getMinCharge()).map(Long::intValue).orElse(null));
        if (Objects.nonNull(extra) && Objects.nonNull(extra.getCommission())) {
            serviceFeeReq.setCommission(extra.getCommission().intValue());
        }
        return serviceFeeReq;
    }

    private BusinessOpenRequest.ServiceFee buildPeriodServiceFeeReq(ServiceFeeEffectiveEntity effectiveDo, ServiceFeeEntity serviceFeeDo, Date endDate) {
        BusinessOpenRequest.ServiceFee serviceFeeReq = buildBaseServiceFee(effectiveDo, serviceFeeDo);
        serviceFeeReq.setRatioInvalidDate(Optional.ofNullable(endDate).map(Date::getTime).orElse(null));
        ServiceFeeEffectiveEntity.Extra effectiveExtra = effectiveDo.buildExtra();
        //设置兜底分账比例、保底收费
        if (Objects.nonNull(effectiveExtra.getUnrenewedId())) {
            Integer minSharingAmount = null;
            if (Objects.nonNull(effectiveExtra.getUnrenewedMinCharge())) {
                minSharingAmount = effectiveExtra.getUnrenewedMinCharge().intValue();
            }
            serviceFeeReq.setRatio(generatorRatio(effectiveExtra.getUnrenewedProfitShareRatio()));
            serviceFeeReq.setCommission(Optional.ofNullable(effectiveExtra.getUnrenewedCommission()).map(Long::intValue).orElse(null));
            serviceFeeReq.setMinSharingAmount(minSharingAmount);
        }
        return serviceFeeReq;
    }

    private BusinessOpenRequest.ServiceFee buildBaseServiceFee(ServiceFeeEffectiveEntity effectiveDo, ServiceFeeEntity serviceFeeDo) {
        BusinessOpenRequest.ServiceFee serviceFeeReq = new BusinessOpenRequest.ServiceFee();
        serviceFeeReq.setServiceFeeId(effectiveDo.getFeeId() + "");
        serviceFeeReq.setCampuId(effectiveDo.getScenesCode());
        serviceFeeReq.setScenesCode(effectiveDo.getScenesCode());
        serviceFeeReq.setAccountBusinessType(serviceFeeDo.fetchReceiverAccountBusinessType());
        serviceFeeReq.setBaseMetrics(buildBaseMetrics(serviceFeeDo));
        return serviceFeeReq;
    }

    private BusinessOpenRequest.ServiceFee buildAllServiceFeeReq(ServiceFeeEffectiveEntity effectiveDo, ServiceFeeEntity serviceFeeDo, Date endDate) {
        BusinessOpenRequest.ServiceFee serviceFeeReq = buildBaseServiceFee(effectiveDo, serviceFeeDo);
        serviceFeeReq.setBaseMetrics(buildBaseMetrics(serviceFeeDo));
        serviceFeeReq.setRatio(generatorRatio(effectiveDo.getProfitShareRatio()));
        serviceFeeReq.setMinSharingAmount(Optional.ofNullable(effectiveDo.getMinCharge()).map(Long::intValue).orElse(null));
        ServiceFeeEffectiveEntity.Extra extra = effectiveDo.buildExtra();
        if (Objects.nonNull(extra) && Objects.nonNull(extra.getCommission())) {
            serviceFeeReq.setCommission(extra.getCommission().intValue());
        }
        // 设置收款账号
        serviceFeeReq.setAccountBusinessType(serviceFeeDo.fetchReceiverAccountBusinessType());
        return serviceFeeReq;
    }

    private String buildBaseMetrics(ServiceFeeEntity serviceFeeDo) {
        List<ProfitShareMetricsResponse> shareMetricsList;
        List<String> metrics = serviceFeeDo.buildProfitShareBaseMetrics();
        List<ProfitShareMetricsResponse> shareMetricsConfigs = commonApolloConfig.getProfitShareMetrics();
        if (CollectionUtils.isEmpty(metrics)) {
            shareMetricsList = shareMetricsConfigs.stream().filter(s -> Objects.equals(ProfitShareMetricsConfig.DEFAULT, s.getKey())).collect(Collectors.toList());
        } else {
            shareMetricsList = shareMetricsConfigs.stream().filter(shareMetricsResponse -> metrics.contains(shareMetricsResponse.getKey())).collect(Collectors.toList());
        }

        StringBuilder sb = new StringBuilder();
        for (ProfitShareMetricsResponse shareMetrics : shareMetricsList) {
            sb.append(shareMetrics.getSymbol()).append(shareMetrics.getKey());
        }
        sb.deleteCharAt(0);
        return sb.toString();
    }

    private String generatorRatio(String ratio) {
        if (StringUtils.isNotEmpty(ratio)) {
            ratio += "%";
        }
        return ratio;
    }

    /**
     * 生成锁名称
     *
     * @param effectiveDo
     * @return
     */
    private String generatorLockName(ServiceFeeEffectiveEntity effectiveDo) {
        return String.format("openProfitSharing:%s:%s:%s",
                effectiveDo.getLevel(),
                effectiveDo.getMerchantSn(),
                StringUtils.defaultIfEmpty(effectiveDo.getSn(), "-"));
    }

    /**
     * 告警
     *
     * @param effectiveDo
     */
    private void alarmEvent(ServiceFeeEffectiveEntity effectiveDo, Exception exception) {
        try {
            final String TEMPLATE =
                    "触发事件: trade-manage开通分账失败\n" +
                            "商户sn: %s\n" +
                            "服务规则ID: %d\n" +
                            "生效记录ID: %d\n" +
                            "log traceID: %s\n" +
                            "exceptionMsg: %s\n";
            String message = activityMonitorNoticeConfig.appendActiveProfile(String.format(TEMPLATE,
                    effectiveDo.getMerchantSn(), effectiveDo.getFeeId(), effectiveDo.getId(),
                    TraceContext.traceId(), exception.getMessage()
            ));
            tradeCommonService.sendLarkText(larkUrl, message);
        } catch (Exception e) {
            log.error("alarmEvent failed.", e);
        }
    }

    /**
     * 告警
     *
     * @param effectiveDo
     */
    private void alarmCloseEvent(ServiceFeeEffectiveEntity effectiveDo, Exception exception) {
        try {
            final String TEMPLATE =
                    "触发事件: trade-manage关闭分账失败\n" +
                            "商户sn: %s\n" +
                            "服务规则ID: %d\n" +
                            "生效记录ID: %d\n" +
                            "log traceID: %s\n" +
                            "exceptionMsg: %s\n";
            String message = activityMonitorNoticeConfig.appendActiveProfile(String.format(TEMPLATE,
                    effectiveDo.getMerchantSn(), effectiveDo.getFeeId(), effectiveDo.getId(),
                    TraceContext.traceId(), exception.getMessage()
            ));
            tradeCommonService.sendLarkText(larkUrl, message);
        } catch (Exception e) {
            log.error("alarmEvent failed.", e);
        }
    }

    /**
     * 明确为失败消息
     *
     * @param message
     * @return
     */
    private boolean hasSaasProfitFailMessage(String message) {
        List<String> list = commonApolloConfig.saasProfitFailMessageList();
        return list.stream().anyMatch(errorMsg -> Pattern.compile(errorMsg).matcher(message).matches());
    }

}

package com.wosai.trade.producer;

import com.wosai.trade.model.dal.BankRecommendPayMsg;
import io.confluent.kafka.serializers.KafkaAvroSerializerConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/3/18、14:54
 **/

@Slf4j
@Component
public class BankRecommendQuotaSyncProducer {

    @Value("${spring.kafka.bank-recommend.bootstrap-servers}")
    private String bootstrap;
    @Value("${spring.kafka.bank-recommend.schema-registry}")
    private String registryUrl;

    @Value("${spring.kafka.bank-recommend.topic}")
    private String topic = "events_CUA_merchant-pay-amount";


    @Autowired
    private KafkaProperties kafkaProperties;

    private KafkaProducer<byte[], byte[]> producer;


    @PostConstruct
    public void startProduce() {
        producer = createProducer(bootstrap, registryUrl);
    }

    private KafkaProducer<byte[], byte[]> createProducer(String broker, String registryUrl) {
        Map<String, Object> props = kafkaProperties.buildProducerProperties();
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, org.apache.kafka.common.serialization.StringSerializer.class);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, io.confluent.kafka.serializers.KafkaAvroSerializer.class);
        props.put(KafkaAvroSerializerConfig.SCHEMA_REGISTRY_URL_CONFIG, registryUrl);
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, broker);
        return new KafkaProducer<>(props);
    }

    public void sendQuota(BankRecommendPayMsg bankRecommendPayMsg) {
        log.info("quotaInfo : " +  bankRecommendPayMsg.getMerchantId() + " " +  bankRecommendPayMsg.getBizValue());
        ProducerRecord record = new ProducerRecord(topic, null, bankRecommendPayMsg);
        producer.send(record, new Callback() {

            @Override
            public void onCompletion(RecordMetadata metadata, Exception exception) {
                if (null != exception) {
                    log.error("failed to send  BankRecommendQuota message: " + topic + " merchant_id " + bankRecommendPayMsg.getMerchantId() , exception);
                }
            }
        });
    }

}

package com.wosai.trade.producer;

import com.wosai.databus.avro.AvroEventEntry;
import com.wosai.databus.event.AbstractEvent;
import com.wosai.databus.event.pay.TradeManageEvent;
import com.wosai.databus.jackson.EventAwareJackson2PersistenceHelper;
import com.wosai.middleware.hera.toolkit.trace.TraceContext;
import io.confluent.kafka.serializers.KafkaAvroSerializer;
import io.confluent.kafka.serializers.KafkaAvroSerializerConfig;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.serialization.StringSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.nio.ByteBuffer;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;

/**
 * Created by wujianwei on 2021/12/2.
 */
@Component
public class TradeManageKafkaProducer {
    public static final Logger logger = LoggerFactory.getLogger(TradeManageKafkaProducer.class);
    public static final EventAwareJackson2PersistenceHelper persistenceHelper = new EventAwareJackson2PersistenceHelper();
    public static final String topic = "databus.event.pay.manage.allin";


    @Autowired
    private KafkaProperties kafkaProperties;
    private KafkaProducer<String, AbstractEvent> producer;

    @Value("${spring.kafka.bootstrap-servers}")
    private String bootstrap;
    @Value("${spring.kafka.properties.schema.registry.url}")
    private String registryUrl;


    @PostConstruct
    public void startProduce() {
        producer = createProducer(bootstrap, registryUrl);
    }

    private KafkaProducer<String, AbstractEvent> createProducer(String broker, String registryUrl) {
        Map<String, Object> props = kafkaProperties.buildProducerProperties();
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, KafkaAvroSerializer.class);
        props.put(KafkaAvroSerializerConfig.SCHEMA_REGISTRY_URL_CONFIG, registryUrl);
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, broker);
        return new KafkaProducer<>(props);
    }

    /**
     * 写入kafka
     * @param event
     */
    public void sendEvent(TradeManageEvent event) {
        doSendEvent(event, exception -> {
        });
    }

    /**
     * 写入kafka
     * @param event
     */
    public void doSendEvent(TradeManageEvent event, Consumer<Exception> callable) {
        long now = System.currentTimeMillis();
        String tid = TraceContext.traceId();
        producer.send(new ProducerRecord(topic, event.getMerchantSn(), new AvroEventEntry(now, now, ByteBuffer.wrap(persistenceHelper.toJsonBytes(event)))), (metadata, exception) -> {
            if (exception != null) {
                logger.error("send to kafka error, traceId={} event={}", tid, persistenceHelper.toJsonString(event), exception);
                if (Objects.nonNull(callable)) {
                    callable.accept(exception);
                }
            }
        });
    }

}

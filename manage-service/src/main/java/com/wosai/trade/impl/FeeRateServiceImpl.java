package com.wosai.trade.impl;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.aop.gateway.model.ClientSideNoticeSendModel;
import com.wosai.aop.gateway.service.ClientSideNoticeService;
import com.wosai.bsm.creditpaybackend.service.UserInfoService;
import com.wosai.data.bean.BeanUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.trade.biz.activity.ApplyActivityBiz;
import com.wosai.trade.biz.audit.filter.ApplyFeeRateFilterChain;
import com.wosai.trade.biz.audit.model.ApplyLadderFeeRate;
import com.wosai.trade.biz.businesslog.BusinessOpLogBiz;
import com.wosai.trade.biz.fee.MerchantBasicAgreementFeeRateBiz;
import com.wosai.trade.biz.fee.converter.FeeRateBuildParams;
import com.wosai.trade.config.apollo.ApplyRateNotifyConfig;
import com.wosai.trade.config.apollo.CommonApolloConfig;
import com.wosai.trade.config.apollo.TradeTagConfig;
import com.wosai.trade.constant.FeeRateStatusConst;
import com.wosai.trade.constant.LockConst;
import com.wosai.trade.constant.TradeComboConstants;
import com.wosai.trade.model.apollo.ApplyRateNotifyModel;
import com.wosai.trade.model.biz.ActivityFeeRateParam;
import com.wosai.trade.model.biz.ApplyFeeRateChainExtra;
import com.wosai.trade.model.biz.ApplyFeeRateUpsertDalParam;
import com.wosai.trade.model.biz.UpdateFeeRateComboParam;
import com.wosai.trade.model.constant.OpLogConstant;
import com.wosai.trade.model.dal.*;
import com.wosai.trade.model.enums.PayWayEnum;
import com.wosai.trade.repository.dao.*;
import com.wosai.trade.repository.dao.entity.*;
import com.wosai.trade.repository.dao.entity.TradeComboDetailEntity.FeeRate;
import com.wosai.trade.repository.dao.entity.TradeComboDetailEntity.FixedFeeRate;
import com.wosai.trade.service.FeeRateService;
import com.wosai.trade.service.TradeComboDetailService;
import com.wosai.trade.service.activity.constant.ActivityConstants;
import com.wosai.trade.service.activity.request.CancelActivityApplyRequest;
import com.wosai.trade.service.constant.CommonConstant;
import com.wosai.trade.service.enums.ChangeActivityFeeRateEnum;
import com.wosai.trade.service.enums.ComboConfigLevelEnum;
import com.wosai.trade.service.enums.FeeRateTypeEnum;
import com.wosai.trade.service.enums.TradeComboStatusEnum;
import com.wosai.trade.service.exception.TradeManageBizException;
import com.wosai.trade.service.request.*;
import com.wosai.trade.service.result.*;
import com.wosai.trade.service.result.ListMchFeeRateResult.LadderFeeRate;
import com.wosai.trade.util.*;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.meta.Payway;
import com.wosai.upay.core.meta.SubPayway;
import com.wosai.upay.core.model.*;
import com.wosai.upay.core.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.joda.time.LocalDate;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 *
 */
@Slf4j
@Service
@AutoJsonRpcServiceImpl
public class FeeRateServiceImpl implements FeeRateService, ApplicationContextAware {

    private ApplicationContext applicationContext;

    @Autowired
    private MerchantFeeRateDao merchantFeeRateDao;
    @Autowired
    private TerminalFeeRateDao terminalFeeRateDao;
    @Autowired
    private StoreFeeRateDao storeFeeRateDao;
    @Autowired
    private TradeAppDao tradeAppDao;
    @Autowired
    private TradeComboDao tradeComboDao;
    @Autowired
    private TradeComboDetailDao tradeComboDetailDao;
    @Autowired
    private MerchantFeeRateApplyLogDao merchantFeeRateApplyLogDao;
    @Autowired
    private TradeConfigService tradeConfigService;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private BusinssCommonService businssCommonService;
    @Autowired
    private StoreService storeService;
    @Autowired
    private TerminalService terminalService;
    @Autowired
    private SupportService supportService;
    @Autowired
    private ApplyFeeRateFilterChain applyFeeRateFilterChain;
    @Autowired
    private UserInfoService creditPayUserInfoService;
    @Autowired
    private ClientSideNoticeService clientSideNoticeService;
    @Autowired
    private ApplyRateNotifyConfig applyRateNotifyConfig;
    @Autowired
    private CommonApolloConfig commonApolloConfig;
    @Autowired
    private TradeCommonService tradeCommonService;
    @Autowired
    private CommonActivityService commonActivityService;
    @Autowired
    private TradeComboDetailService tradeComboDetailService;

    @Autowired
    private ActivityApplyDOMapper activityApplyDOMapper;
    @Autowired
    @Lazy
    private ApplyActivityBiz applyBiz;
    @Autowired
    private FeeRateCommonService feeRateCommonService;
    @Autowired
    private FeeRateBuildParams feeRateBuildParams;
    @Autowired
    private MerchantBasicAgreementFeeRateBiz merchantBasicAgreementFeeRateBiz;

    @Resource
    private BusinessOpLogBiz businessOpLogBiz;


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Override
    public List<BulkApplyFeeRateResult> bulkApplyFeeRate(List<BulkApplyFeeRateRequest> params) {
        return params.stream()
                .map(param -> {
                    String merchantSn = param.getMerchantSn();
                    Long tradeComboId = param.getTradeComboId();
                    String terminalSn = param.getTerminalSn();
                    boolean result = false;
                    String message = null;
                    try {
                        ApplyFeeRateRequest applyFeeRateRequest = new ApplyFeeRateRequest()
                                .setMerchantSn(merchantSn)
                                .setStoreSn(param.getStoreSn())
                                .setTerminalSn(terminalSn)
                                .setOperator("系统批量申请")
                                .setOperatorName("系统批量申请")
                                .setTradeComboId(tradeComboId)
                                .setAuditSn("系统批量申请");
                        applyFeeRateOne(applyFeeRateRequest);

                        result = true;
                    } catch (Exception e) {
                        message = e.getMessage();
                        if (message != null && message.length() > 200) {
                            message = message.substring(0, 200);
                        }
                        log.error("商户号 => {} 套餐费率id => {} 费率申请失败：", merchantSn, tradeComboId, e);
                    }

                    return new BulkApplyFeeRateResult()
                            .setMerchantSn(merchantSn)
                            .setResult(result)
                            .setMessage(message);
                })
                .collect(Collectors.toList());
    }

    @Override
    public void applyFeeRateOne(ApplyFeeRateRequest applyFeeRateRequest) {
        String merchantSn = applyFeeRateRequest.getMerchantSn();
        String storeSn = applyFeeRateRequest.getStoreSn();
        String terminalSn = applyFeeRateRequest.getTerminalSn();
        String platform = applyFeeRateRequest.getPlatform();
        String operator = applyFeeRateRequest.getOperator();
        String operatorName = applyFeeRateRequest.getOperatorName();
        Long tradeComboId = applyFeeRateRequest.getTradeComboId();
        // 处理是否只生效部分的支付通道费率
        boolean applyPartialPayway = applyFeeRateRequest.isApplyPartialPayway();
        Map<String, String> applyFeeRateMp = applyFeeRateRequest.getApplyFeeRateMap();
        String auditSn = applyFeeRateRequest.getAuditSn();
        Long applyTimeMillis = applyFeeRateRequest.getApplyTimeMillis();
        Boolean check = applyFeeRateRequest.getCheck();

        Map<String, Object> merchantInfo = merchantService.getMerchantBySn(merchantSn);
        if (MapUtils.isEmpty(merchantInfo)) {
            throw TradeManageBizException.createExc("商户不存在");
        }
        String merchantId = MapUtils.getString(merchantInfo, DaoConstants.ID);
        TradeComboEntity tradeComboEntity = tradeComboDao.selectById(tradeComboId);

        // 审批的套餐
        if (tradeComboEntity == null || tradeComboEntity.getId() == null) {
            throw TradeManageBizException.createExc("套餐不存在");
        }
        if (TradeComboStatusEnum.ENABLE.getCode() != tradeComboEntity.getStatus()) {
            throw TradeManageBizException.createExc("套餐不可用");
        }
        // 套餐是活动套餐时，需要做payWay校验
        ActivityFeeRateParam activityInfo = feeRateBuildParams.buildActivityFeeRateParam(ActivityFeeRateParam.ActivityFeeRateRequest.builder()
                .applyId(applyFeeRateRequest.getApplyId())
                .activitySubStatusId(applyFeeRateRequest.getSubStatusId())
                .comboId(tradeComboId)
                .merchantSn(applyFeeRateRequest.getMerchantSn())
                .storeSn(applyFeeRateRequest.getStoreSn())
                .terminalSn(applyFeeRateRequest.getTerminalSn())
                .changeActivityFeeRateEnum(applyFeeRateRequest.getChangeActivityFeeRateEnum())
                .applyFeeRateMap(applyFeeRateMp)
                .remark(applyFeeRateRequest.getRemark())
                .operator(operator)
                .operatorName(operatorName)
                .auditSn(auditSn)
                .subStatusId(applyFeeRateRequest.getSubStatusId())
                .tradeComboEntity(tradeComboEntity)
                .activityApplyLog(applyFeeRateRequest.getActivityApplyLog())
                .businessOpLog(applyFeeRateRequest.getBusinessOpLog())
                .build());
        // 校验
        if (Objects.equals(check, true)) {
            ApplyFeeRateChainExtra extra = new ApplyFeeRateChainExtra(applyPartialPayway, applyFeeRateMp, activityInfo, applyFeeRateRequest.getAuditTemplateId());
            applyFeeRateFilterChain.filter(tradeComboEntity, merchantInfo, applyTimeMillis, extra);
        }

        // 审批的套餐明细列表
        TradeComboDetailQueryDalParam tradeComboDetailQueryDalParam = TradeComboDetailQueryDalParam.builder().comboId(tradeComboId).build();

        List<TradeComboDetailEntity> tradeComboDetailEntityList = tradeComboDetailDao.selectList(tradeComboDetailQueryDalParam);
        if (applyPartialPayway) {
            final Map<String, String> finalApplyFeeRateMp = applyFeeRateMp;
            tradeComboDetailEntityList = tradeComboDetailEntityList.stream().filter(detail -> finalApplyFeeRateMp.keySet().contains(detail.getPayway().toString())).collect(Collectors.toList());
        }
        if (commonApolloConfig.getMicroPuhuiComboIds().contains(tradeComboId)) {
            //普惠套餐，写kafka神策事件
            applicationContext.getBean(MicroPuHuiService.class).sendPuHuiEffectEvent(merchantSn, tradeComboId);
        }
        //通用活动处理
        commonActivityService.processEffectCombo(merchantSn, auditSn, tradeComboId);

        // 商户级别
        if (ComboConfigLevelEnum.MERCHANT.name().equalsIgnoreCase(tradeComboEntity.getComboConfigLevel())) {
            applyMerchantFeeRate(merchantId, merchantSn, platform, operator, operatorName, auditSn, tradeComboEntity, tradeComboDetailEntityList, applyFeeRateMp, activityInfo);
        }
        // 门店级别
        if (ComboConfigLevelEnum.STORE.name().equalsIgnoreCase(tradeComboEntity.getComboConfigLevel())) {
            applyStoreFeeRate(merchantId, merchantSn, storeSn, platform, operator, operatorName, auditSn,
                    tradeComboEntity, tradeComboDetailEntityList, applyFeeRateMp, activityInfo);
        }
        // 终端级别
        if (ComboConfigLevelEnum.TERMINAL.name().equalsIgnoreCase(tradeComboEntity.getComboConfigLevel())) {
            applyTerminalFeeRate(merchantId, merchantSn, terminalSn, platform, operator, operatorName, auditSn,
                    tradeComboEntity, tradeComboDetailEntityList, applyFeeRateMp, activityInfo);
        }
        // 活动优惠套餐处理
        effectOrInvalidActivity(activityInfo, true, operator, auditSn);
        //后置处理
        commonActivityService.processEffectComboAfterHandle(merchantSn, auditSn, tradeComboId);

    }

    @Override
    public void preApplyFeeRate(String merchantSn, Map<String, Object> tradeComboEntityAndList,Long applyTimeMillis) {
        preApplyFeeRate(merchantSn, tradeComboEntityAndList, applyTimeMillis, null, null);
    }

    @Override
    public void preApplyFeeRate(String merchantSn, Map<String, Object> tradeComboEntityAndList, Long applyTimeMillis, Map<String, String> applyFeeRateMap) {
        preApplyFeeRate(merchantSn, tradeComboEntityAndList, applyTimeMillis, applyFeeRateMap, null);
    }

    @Override
    public void preApplyFeeRate(String merchantSn, Map<String, Object> tradeComboEntityAndList, Long applyTimeMillis, Map<String, String> applyFeeRateMap, String auditTemplateId) {
        TradeComboEntity tradeComboEntity = (TradeComboEntity)tradeComboEntityAndList.get("tradeComboEntity");
        List<TradeComboDetailEntity> tradeComboDetailEntityList = new ArrayList<>();
        tradeComboDetailEntityList.addAll ((Collection<? extends TradeComboDetailEntity>) tradeComboEntityAndList.get("tradeComboDetailEntityList"));
        if (Objects.isNull(tradeComboEntity) || CollectionUtils.isEmpty(tradeComboDetailEntityList)){
            throw TradeManageBizException.createExc("商户"+merchantSn+"传入的费率套餐信息错误");
        }
        Map<String, Object> merchantInfo = merchantService.getMerchantBySn(merchantSn);
        if (MapUtils.isEmpty(merchantInfo)) {
            throw TradeManageBizException.createExc("商户"+merchantSn+"不存在");
        }
        ApplyFeeRateChainExtra extra = new ApplyFeeRateChainExtra(applyFeeRateMap != null, applyFeeRateMap, null, auditTemplateId);
        //商户注册时间等的校验
        applyFeeRateFilterChain.filter(tradeComboEntity, merchantInfo, applyTimeMillis, extra);
    }

    /**
     * 生效商户级别费率
     *
     * @param merchantId                 商户id
     * @param merchantSn                 商户sn
     * @param platform                   操作来源
     * @param operator                   操作人
     * @param operatorName               操作人名称
     * @param auditSn                    审批编号
     * @param applyFeeRateMap            申请固定费率套餐时使用的费率map
     * @param tradeComboEntity           费率套餐
     * @param tradeComboDetailEntityList 费率套餐明细列表
     * @param activityInfo               活动明细
     */
    private void applyMerchantFeeRate(String merchantId, String merchantSn,
                                      String platform, String operator, String operatorName, String auditSn,
                                      TradeComboEntity tradeComboEntity, List<TradeComboDetailEntity> tradeComboDetailEntityList,Map<String, String> applyFeeRateMap,
                                      ActivityFeeRateParam activityInfo) {
        applyMerchantFeeRateLock(merchantId, merchantSn, platform, operator, operatorName, auditSn,
                tradeComboEntity, tradeComboDetailEntityList, applyFeeRateMap, CommonConstant.FEE_RATE_ACTION_APPLY, activityInfo);
        Long tradeComboId = tradeComboEntity.getId();

        // 发送aop推送
        ApplyRateNotifyModel.NotifyConfig notifyConfig = applyRateNotifyConfig.getNotifyConfig(tradeComboId);
        if(notifyConfig != null){
            ClientSideNoticeSendModel sendModel = new ClientSideNoticeSendModel();
            sendModel.setAccountId(merchantId);
            sendModel.setClientSides(Collections.singletonList("TERMINALAPP"));
            sendModel.setDevCode(notifyConfig.getDevCode());
            sendModel.setTemplateCode(notifyConfig.getTemplateCode());
            sendModel.setTimestamp(System.currentTimeMillis());
            sendModel.setRole("super_admin");
            sendModel.setThumbnailUrl(notifyConfig.getThumbnailUrl());
            try {
                clientSideNoticeService.send(sendModel);
            } catch (Exception e) {
                log.error("send aop error" + e.getMessage(), e);
            }
        }
    }

    private void applyMerchantFeeRateLock(String merchantId, String merchantSn, String platform, String operator,
                                          String operatorName, String auditSn,
                                          TradeComboEntity tradeComboEntity, List<TradeComboDetailEntity> tradeComboDetailEntityList,
                                          Map<String, String> applyFeeRateMap, String actionType,ActivityFeeRateParam activityInfo) {
        RedisLockUtil.tryLock(LockConst.APPLY_MERCHANT_FEE_RATE_LOCK_PREFIX + merchantSn, 10L, () -> {
            applyMerchantFeeRate0(merchantId, merchantSn, platform, operator, operatorName, auditSn,
                    tradeComboEntity, tradeComboDetailEntityList, applyFeeRateMap, actionType, activityInfo);
            return null;
        });
    }

    private void applyMerchantFeeRate0(String merchantId, String merchantSn, String platform, String operator,
                                       String operatorName, String auditSn,
                                       TradeComboEntity tradeComboEntity, List<TradeComboDetailEntity> tradeComboDetailEntityList,
                                       Map<String, String> applyFeeRateMap, String actionType, ActivityFeeRateParam activityInfo) {
        Long tradeComboId = tradeComboEntity.getId();
        Long tradeAppId = tradeComboEntity.getTradeAppId();
        TradeAppEntity tradeAppEntity = tradeAppDao.queryTradeAppById(tradeAppId);

        Iterator<TradeComboDetailEntity> iterator = tradeComboDetailEntityList.iterator();
        while (iterator.hasNext()) {
            TradeComboDetailEntity tradeComboDetailEntity = iterator.next();
            int payWay = tradeComboDetailEntity.getPayway();
            // 遍历当前支付方式生效的费率列表
            MerchantFeeRateQueryDalParam merchantFeeRateQueryDalParam = MerchantFeeRateQueryDalParam.builder()
                    .merchantSn(merchantSn)
                    .payWay(payWay)
                    .status(FeeRateStatusConst.IN_EFFECT)
                    .build();
            List<MerchantFeeRateEntity> effectFeeRateList = merchantFeeRateDao.selectList(merchantFeeRateQueryDalParam);
            for (MerchantFeeRateEntity merchantFeeRateEntity : effectFeeRateList) {
                TradeAppEntity currentTradeAppEntity = tradeAppDao.queryTradeAppById(merchantFeeRateEntity.getAppId());
                if (currentTradeAppEntity != null) {
                    if (!Objects.equals(currentTradeAppEntity.isPayApp(), tradeAppEntity.isPayApp())){
                        // 非同类业务时，不失效套餐（只失效同类业务的套餐）
                        continue;
                    } else if (currentTradeAppEntity.isPayApp() && !Objects.equals(merchantFeeRateEntity.getAppId(), tradeAppId)) {
                        // 智慧门店业务允许子业务方单独存在一个payway配置
                        continue;
                    }
                }
                MerchantFeeRateUpsertDalParam updateDalParam = buildFeeRateUpdateDalParam(merchantFeeRateEntity, tradeComboDetailEntity).buildMerchantFeeRateUpsertDalParam();
                merchantFeeRateDao.updateById(updateDalParam);
                //费率生效动作
                if (Objects.equals(CommonConstant.FEE_RATE_ACTION_APPLY, actionType)) {
                    int total = getMerchantComboEffectCount(merchantSn, merchantFeeRateEntity.getTradeComboId());
                    if (total == 0) {
                        // 如果当前支付方式所在套餐中所有支付方式费率都不使用，则打费率取消标签
                        TradeComboEntity.MarkLabelExtra markLabelExtra = tradeComboDao.selectById(merchantFeeRateEntity.getTradeComboId()).buildMarkLabelExtra();
                        String tagId = markLabelExtra.getLabel_id();
                        String tagValue = markLabelExtra.getCancel_label_value();
                        tradeCommonService.tag(merchantId, tagId, tagValue);
                        // 如果被终止则参与活动，自动结束
                        if (!Objects.equals(tradeComboId, merchantFeeRateEntity.getTradeComboId())) {
                            commonActivityService.processEndCombo(merchantSn, auditSn, Pair.of(merchantFeeRateEntity.getTradeComboId(), tradeComboId));
                        }
                    }
                }
            }
        }

        if(tradeComboDetailEntityList.size() > 0){
            // 生效套餐
            effectMchFeeRate(merchantId, merchantSn, platform, operator, operatorName, auditSn, tradeComboEntity, tradeComboDetailEntityList,
                    applyFeeRateMap, actionType, tradeAppEntity, activityInfo);
            // 打生效标签
            tagEffect(merchantId, tradeComboEntity);
            //设置花呗分期初始金额
            setHuabeiInitAmountByCombo(merchantId,tradeComboId,FeeRateStatusConst.IN_EFFECT);
            // 清缓存
            supportService.removeCachedParams(merchantSn);
        }
    }

    private void effectMchFeeRate(String merchantId, String merchantSn, String platform, String operator,
            String operatorName, String auditSn,
            TradeComboEntity tradeComboEntity, List<TradeComboDetailEntity> tradeComboDetailEntityList,
            Map<String, String> applyFeeRateMap, String actionType, TradeAppEntity tradeAppEntity, ActivityFeeRateParam activityInfo) {
        TradeComboEntity.ExpirationRuleExtra expirationRuleExtra = tradeComboEntity.buildExpirationRuleExtra();
        LocalDate beginDate = LocalDate.now();
        LocalDate endDate = FeeRateUtils.calculateEndDate(expirationRuleExtra);
        // 获取商户交易参数
        Function<Integer, Map<String, Object>> getMerchantConfigFn = (payway) -> {
            if (!tradeAppEntity.isPayApp()) {
                // 支付业务，参数配置到merchant_config 中
                return tradeConfigService.getAnalyzedMerchantConfigByMerchantIdAndPayway(merchantId, payway);
            } else {
                // 非支付业务，参数配置到merchant_app_config 中
                return tradeConfigService.getMerchantAppConfigByMerchantIdAndPaywayAndApp(merchantId, payway, tradeAppEntity.getId() + "");
            }
        };
        // 费率变更
        BiConsumer<Integer, Map<String, Object>> mcUpdateBc = (payway, updateMerchantConfig) -> {
            if (!tradeAppEntity.isPayApp()) {
                // 支付业务，参数配置到merchant_config 中
                tradeConfigService.updateMerchantConfigStatusAndFeeRate(merchantId, updateMerchantConfig);
            } else {
                // 非支付业务，参数配置到merchant_app_config 中
                Map<String, Object> merchantAppConfig = getMerchantConfigFn.apply(payway);
                if (merchantAppConfig == null) {
                    merchantAppConfig = tradeConfigService.createMerchantAppConfig(MapUtil.hashMap(MerchantAppConfig.MERCHANT_ID, merchantId,
                            MerchantAppConfig.PAYWAY, payway,
                            MerchantAppConfig.APP_ID, tradeAppEntity.getId() + ""));
                }
                Map<String, Object> merchantAppConfigUpdatePart = MapUtil.copyInclusive(merchantAppConfig, DaoConstants.ID);
                Map<String, Object> params = Optional.ofNullable((Map)merchantAppConfig.get(MerchantAppConfig.PARAMS)).orElse(new HashMap<>());
                Map<String, Object> updateParams = Optional.ofNullable((Map)updateMerchantConfig.get(MerchantAppConfig.PARAMS)).orElse(new HashMap<>());
                params.putAll(updateParams);

                merchantAppConfigUpdatePart.putAll(updateMerchantConfig);
                merchantAppConfigUpdatePart.put(MerchantAppConfig.PARAMS, params);
                tradeConfigService.updateMerchantAppConfig(merchantAppConfigUpdatePart);
            }
        };
        // 按支付方式级别追个生效二级费率
        for (TradeComboDetailEntity entity : tradeComboDetailEntityList) {
            int payWay = entity.getPayway();
            //获取费率类型
            FeeRateTypeEnum feeRateTypeEnum = getFeeRateTypeEnumByTradeComboDetailEntity(entity);
            String feeRateText = feeRateBuildParams.getEffectFeeRate(entity, applyFeeRateMap);
            ApplyFeeRateUpsertDalParam applyFeeRateUpsertDalParam = buildFeeRateInsertDalParam(merchantSn, auditSn, beginDate, endDate, feeRateText, tradeComboEntity, entity);
            merchantFeeRateDao.insert(applyFeeRateUpsertDalParam.buildMerchantFeeRateUpsertDalParam());
            // 记录到商户费率审批 log 表
            MchFeeRateApplyLogUpsertDalParam logInsertDalParam = buildMchFeeRateApplyLogUpsertDalParam(merchantSn, auditSn, beginDate, endDate, feeRateText, tradeAppEntity, tradeComboEntity, entity);
            merchantFeeRateApplyLogDao.insert(logInsertDalParam);

            // 先获取更新前交易配置
            Map<String, Object> before = getMerchantConfigFn.apply(payWay);
            Map<String, Object> updateValue = null;
            // 调用 core-b 接口，更新费率
            // 如果是，固定费率
            if (FeeRateTypeEnum.FIXED.equals(feeRateTypeEnum)) {
                updateValue = buildUpdateConfigWhenFix(entity, feeRateText);
            }
            // 资金通道费率费率
            else if (FeeRateTypeEnum.CHANNEL.equals(feeRateTypeEnum)) {
                updateValue = buildUpdateConfigWhenChannel(entity, feeRateText);
            }
            // 资金通道阶梯费率费率
            else if (FeeRateTypeEnum.CHANNEL_LADDER.equals(feeRateTypeEnum)) {
                updateValue = buildUpdateConfigWhenChannelLadder(entity, feeRateText);
            }
            // 否则为阶梯费率
            else {
                updateValue = buildUpdateConfigWhenLadder(entity, feeRateText);
            }
            mcUpdateBc.accept(payWay, updateValue);

            // 获取更新后配置
            Map<String, Object> after = getMerchantConfigFn.apply(payWay);
            String effectiveLabelValue = tradeComboEntity.buildMarkLabelExtra().getEffective_label_value();
            String remark = String.format("审批编号：%s %s", auditSn, effectiveLabelValue != null ? effectiveLabelValue : "");
            if (Objects.nonNull(activityInfo) && StringUtils.isNotEmpty(activityInfo.fetchBusinessOpLog())) {
                remark = activityInfo.fetchBusinessOpLog();
            }
            feeRateCommonService.sendBusinessLog(merchantId, platform, operator, operatorName, remark, before, after);
        }
    }
    
    /**
     * 生效终端级别费率
     *
     * @param merchantId                 商户id
     * @param merchantSn                 商户sn
     * @param storeSn                    门店sn
     * @param platform                   操作来源
     * @param operator                   操作人
     * @param operatorName               操作人名称
     * @param auditSn                    审批编号
     * @param applyFeeRateMap            申请固定费率套餐时使用的费率
     * @param tradeComboEntity           费率套餐
     * @param tradeComboDetailEntityList 费率套餐明细列表
     */
    private void applyStoreFeeRate(String merchantId, String merchantSn, String storeSn,
                                      String platform, String operator, String operatorName, String auditSn,
                                      TradeComboEntity tradeComboEntity, List<TradeComboDetailEntity> tradeComboDetailEntityList,Map<String, String> applyFeeRateMap,
                                   ActivityFeeRateParam activityInfo) {
        Long tradeComboId = tradeComboEntity.getId();
        Long tradeAppId = tradeComboEntity.getTradeAppId();
        TradeAppEntity tradeAppEntity = tradeAppDao.queryTradeAppById(tradeAppId);
        String tradeAppName = tradeAppEntity.getName();
        if(!tradeAppEntity.isPayApp()){
            throw TradeManageBizException.createExc("门店不支持非支付应用的套餐");
        }
        if (StringUtils.isEmpty(storeSn)) {
            throw TradeManageBizException.createExc("申请门店级别费率套餐时：门店号必填");
        }
        // 获取门店
        Map<String, Object> storeInfo = storeService.getStoreByStoreSn(storeSn);
        if (MapUtils.isEmpty(storeInfo)) {
            throw TradeManageBizException.createExc("门店不存在");
        }
        if (!Objects.equals(merchantId, MapUtils.getString(storeInfo, Store.MERCHANT_ID))) {
            throw TradeManageBizException.createExc("该门店不属于该商户");
        }
        String storeId = MapUtils.getString(storeInfo, DaoConstants.ID);

        Iterator<TradeComboDetailEntity> iterator = tradeComboDetailEntityList.iterator();
        while (iterator.hasNext()) {
            TradeComboDetailEntity tradeComboDetailEntity = iterator.next();
            int payWay = tradeComboDetailEntity.getPayway();
            // 遍历当前支付方式生效的费率列表
            StoreFeeRateQueryDalParam merchantFeeRateQueryDalParam = StoreFeeRateQueryDalParam.builder()
                    .merchantSn(merchantSn)
                    .storeSn(storeSn)
                    .payWay(payWay)
                    .status(FeeRateStatusConst.IN_EFFECT)
                    .build();
            storeFeeRateDao.selectList(merchantFeeRateQueryDalParam)
                    .forEach(feeRateEntity -> {
                        if (!Objects.equals(feeRateEntity.getAppId(), tradeAppId)) {
                            // 非支付应用允许子业务方单独存在一个payway配置
                            return;
                        }
                        StoreFeeRateUpsertDalParam updateDalParam = buildFeeRateUpdateDalParam(feeRateEntity, tradeComboDetailEntity).buildStoreFeeRateUpsertDalParam();
                        updateDalParam.setStoreSn(storeSn);
                        storeFeeRateDao.updateById(updateDalParam);
                        // 如果当前支付方式所在套餐中所有支付方式费率都不使用，则打费率取消标签
                        if (getStoreComboEffectCount(merchantSn, storeSn, feeRateEntity.getTradeComboId()) == 0) {
                            TradeComboEntity.MarkLabelExtra markLabelExtra = tradeComboDao.selectById(feeRateEntity.getTradeComboId()).buildMarkLabelExtra();
                            String tagId = markLabelExtra.getLabel_id();
                            String tagValue = markLabelExtra.getCancel_label_value();
                            tradeCommonService.tag(merchantId, tagId, tagValue);
                            // 如果被终止则参与活动，自动结束
                            if (tradeComboId != feeRateEntity.getTradeComboId()) {
                                commonActivityService.processEndCombo(merchantSn, auditSn, Pair.of(feeRateEntity.getTradeComboId(), tradeComboId));
                            }
                        }
                    });
        }

        TradeComboEntity.ExpirationRuleExtra expirationRuleExtra = tradeComboEntity.buildExpirationRuleExtra();
        LocalDate beginDate = LocalDate.now();
        LocalDate endDate = FeeRateUtils.calculateEndDate(expirationRuleExtra);
        // 按支付方式级别追个生效二级费率
        for (TradeComboDetailEntity entity : tradeComboDetailEntityList) {
            int payWay = entity.getPayway();
            // 获取费率类型
            FeeRateTypeEnum feeRateTypeEnum = getFeeRateTypeEnumByTradeComboDetailEntity(entity);
            String feeRateText = feeRateBuildParams.getEffectFeeRate(entity, applyFeeRateMap);
            // 写入数据库
            ApplyFeeRateUpsertDalParam applyFeeRateUpsertDalParam = buildFeeRateInsertDalParam(merchantSn, auditSn, beginDate, endDate, feeRateText, tradeComboEntity, entity);
            StoreFeeRateUpsertDalParam insertDalParam = applyFeeRateUpsertDalParam.buildStoreFeeRateUpsertDalParam();
            insertDalParam.setStoreSn(storeSn);
            storeFeeRateDao.insert(insertDalParam);

            // 记录到商户费率审批 log 表
            MchFeeRateApplyLogUpsertDalParam logInsertDalParam = buildMchFeeRateApplyLogUpsertDalParam(merchantSn, auditSn, beginDate, endDate, feeRateText, tradeAppEntity, tradeComboEntity, entity);
            merchantFeeRateApplyLogDao.insert(logInsertDalParam);

            // 先获取更新前交易配置
            Map<String, Object> before = tradeConfigService.getStoreAppConfigByStoreIdAndPaywayAndApp(storeId, payWay, tradeAppId + "");
            Map<String, Object> cloneBefore = CommonUtil.cloneMap(before);
            // 调用 core-b 接口，更新费率
            Map<String, Object> updateConfig = Maps.newTreeMap();
            
            // 配置参数
            Map storeConfigParams = Optional.ofNullable(MapUtils.getMap(before, StoreConfig.PARAMS)).orElse(new TreeMap<>());
            // 如果是，固定费率
            if (FeeRateTypeEnum.FIXED.equals(feeRateTypeEnum)) {
                updateConfig = buildUpdateConfigWhenFix(entity, feeRateText);
            }
            // 资金渠道费率
            else if (FeeRateTypeEnum.CHANNEL.equals(feeRateTypeEnum)) {
                updateConfig = buildUpdateConfigWhenChannel(entity, feeRateText);
            }
            // 资金渠道阶梯费率
            else if (FeeRateTypeEnum.CHANNEL_LADDER.equals(feeRateTypeEnum)) {
                updateConfig = buildUpdateConfigWhenChannelLadder(entity, feeRateText);
            }
            // 否则为阶梯费率
            else {
                updateConfig = buildUpdateConfigWhenLadder(entity, feeRateText);
            }
            // 移除其他业务配置
            storeConfigParams.remove(MerchantConfig.CHANNEL_STATUS);
            storeConfigParams.remove(TransactionParam.PARAMS_BANKCARD_FEE);
            storeConfigParams.remove(TransactionParam.CHANNEL_FEE_RATE);
            storeConfigParams.remove(TransactionParam.CHANNEL_FEE_RATE_TAG);
            storeConfigParams.remove(MerchantConfig.LADDER_STATUS);
            storeConfigParams.remove(TransactionParam.LADDER_FEE_RATE_TAG);

            Map updateStoreConfig = Optional.ofNullable(MapUtils.getMap(updateConfig, StoreConfig.PARAMS)).orElse(new TreeMap<>());
            storeConfigParams.putAll(updateStoreConfig);
            updateConfig.put(StoreConfig.PARAMS, storeConfigParams);

            // 如果存在，则更新
            String storeConfigId = MapUtils.getString(before, DaoConstants.ID);
            if (MapUtils.isNotEmpty(before) && StringUtils.isNotEmpty(storeConfigId)) {
                updateConfig.put(DaoConstants.ID, storeConfigId);
                tradeConfigService.updateStoreAppConfig(updateConfig);
            }
            // 如果不存在，则新增
            else {
                updateConfig.put(StoreAppConfig.APP_ID, tradeAppId + "");
                updateConfig.put(StoreConfig.STORE_ID, storeId);
                updateConfig.put(StoreConfig.PAYWAY, payWay);
                tradeConfigService.createStoreAppConfig(updateConfig);
            }

            // 获取更新后配置
            Map after =  tradeConfigService.getStoreAppConfigByStoreIdAndPaywayAndApp(storeId, payWay, tradeAppId + "");
            String labelValue = tradeComboEntity.buildMarkLabelExtra().getEffective_label_value();
            String remark = String.format("审批编号：%s 门店号 %s %s 应用 %s", auditSn, storeSn, labelValue != null ? labelValue : "", tradeAppName);
            feeRateCommonService.sendBusinessLog(merchantId, platform, operator, operatorName, remark, cloneProcessLadderForLog(cloneBefore), cloneProcessLadderForLog(after));
        }

        if(tradeComboDetailEntityList.size() > 0){
            // 清缓存
            supportService.removeCachedParams(merchantSn);
            // 打生效标签
            tagEffect(merchantId, tradeComboEntity);
        }
    }




    /**
     * 生效终端级别费率
     *
     * @param merchantId                 商户id
     * @param merchantSn                 商户sn
     * @param terminalSn                 终端sn
     * @param platform                   操作来源
     * @param operator                   操作人
     * @param operatorName               操作人名称
     * @param auditSn                    审批编号
     * @param applyFeeRateMap            申请固定费率套餐时使用的费率
     * @param tradeComboEntity           费率套餐
     * @param tradeComboDetailEntityList 费率套餐明细列表
     */
    private void applyTerminalFeeRate(String merchantId, String merchantSn, String terminalSn,
                                      String platform, String operator, String operatorName, String auditSn,
                                      TradeComboEntity tradeComboEntity, List<TradeComboDetailEntity> tradeComboDetailEntityList,Map<String, String> applyFeeRateMap,
                                      ActivityFeeRateParam activityInfo) {
        Long tradeComboId = tradeComboEntity.getId();
        Long tradeAppId = tradeComboEntity.getTradeAppId();
        TradeAppEntity tradeAppEntity = tradeAppDao.queryTradeAppById(tradeAppId);
        if (StringUtils.isEmpty(terminalSn)) {
            throw TradeManageBizException.createExc("申请终端级别费率套餐时：终端号必填");
        }
        // 获取终端
        Map<String, Object> terminalInfo = terminalService.getTerminalBySn(terminalSn);
        if (MapUtils.isEmpty(terminalInfo)) {
            throw TradeManageBizException.createExc("终端不存在");
        }
        if (!Objects.equals(merchantId, MapUtils.getString(terminalInfo, Terminal.MERCHANT_ID))) {
            throw TradeManageBizException.createExc("该终端不属于该商户");
        }
        String terminalId = MapUtils.getString(terminalInfo, DaoConstants.ID);

        Iterator<TradeComboDetailEntity> iterator = tradeComboDetailEntityList.iterator();
        while (iterator.hasNext()) {
            TradeComboDetailEntity tradeComboDetailEntity = iterator.next();
            int payWay = tradeComboDetailEntity.getPayway();
            // 遍历当前支付方式生效的费率列表
            TerminalFeeRateQueryDalParam merchantFeeRateQueryDalParam = TerminalFeeRateQueryDalParam.builder()
                    .merchantSn(merchantSn)
                    .terminalSn(terminalSn)
                    .payWay(payWay)
                    .status(FeeRateStatusConst.IN_EFFECT)
                    .build();
            terminalFeeRateDao.selectList(merchantFeeRateQueryDalParam)
                    .forEach(feeRateEntity -> {
                        TerminalFeeRateUpsertDalParam updateDalParam = buildFeeRateUpdateDalParam(feeRateEntity, tradeComboDetailEntity).buildTerminalFeeRateUpsertDalParam();
                        updateDalParam.setTerminalSn(terminalSn);
                        terminalFeeRateDao.updateById(updateDalParam);
                        // 如果当前支付方式所在套餐中所有支付方式费率都不使用，则打费率取消标签
                        if (getTerminalComboEffectCount(merchantSn, terminalSn, feeRateEntity.getTradeComboId()) == 0) {
                            TradeComboEntity.MarkLabelExtra markLabelExtra = tradeComboDao.selectById(feeRateEntity.getTradeComboId()).buildMarkLabelExtra();
                            String tagId = markLabelExtra.getLabel_id();
                            String tagValue = markLabelExtra.getCancel_label_value();
                            tradeCommonService.tag(merchantId, tagId, tagValue);
                            // 如果被终止则参与活动，自动结束
                            if (tradeComboId != feeRateEntity.getTradeComboId()) {
                                commonActivityService.processEndCombo(merchantSn, auditSn, Pair.of(feeRateEntity.getTradeComboId(), tradeComboId));
                            }
                        }
                    });
        }

        TradeComboEntity.ExpirationRuleExtra expirationRuleExtra = tradeComboEntity.buildExpirationRuleExtra();
        LocalDate beginDate = LocalDate.now();
        LocalDate endDate = FeeRateUtils.calculateEndDate(expirationRuleExtra);

        // 按支付方式级别追个生效二级费率
        for (TradeComboDetailEntity entity : tradeComboDetailEntityList) {
            int payWay = entity.getPayway();
            // 获取费率类型
            FeeRateTypeEnum feeRateTypeEnum = getFeeRateTypeEnumByTradeComboDetailEntity(entity);
            String feeRateText = feeRateBuildParams.getEffectFeeRate(entity, applyFeeRateMap);
            // 写入数据库
            ApplyFeeRateUpsertDalParam applyFeeRateUpsertDalParam = buildFeeRateInsertDalParam(merchantSn, auditSn, beginDate, endDate, feeRateText, tradeComboEntity, entity);
            TerminalFeeRateUpsertDalParam insertDalParam = applyFeeRateUpsertDalParam.buildTerminalFeeRateUpsertDalParam();
            insertDalParam.setTerminalSn(terminalSn);
            terminalFeeRateDao.insert(insertDalParam);

            // 记录到商户费率审批 log 表
            MchFeeRateApplyLogUpsertDalParam logInsertDalParam = buildMchFeeRateApplyLogUpsertDalParam(merchantSn, auditSn, beginDate, endDate, feeRateText, tradeAppEntity, tradeComboEntity, entity);
            merchantFeeRateApplyLogDao.insert(logInsertDalParam);

            // 先获取更新前交易配置
            List<Map<String, Object>> beforeList = tradeConfigService.getAnalyzedTerminalConfigsByPayWayList(terminalId, new int[]{payWay});
            Map<String, Object> before = null;
            if (CollectionUtils.isNotEmpty(beforeList)) {
                before = beforeList.get(0);
            }
            Map<String, Object> cloneBefore = CommonUtil.cloneMap(before);

            // 调用 core-b 接口，更新费率
            Map<String, Object> updateConfig = Maps.newTreeMap();
            // 配置参数
            Map terminalConfigParams = Optional.ofNullable(MapUtils.getMap(before, TerminalConfig.PARAMS)).orElse(new TreeMap<>());
            // 如果是，固定费率
            if (FeeRateTypeEnum.FIXED.equals(feeRateTypeEnum)) {
                updateConfig = buildUpdateConfigWhenFix(entity, feeRateText);
            }
            // 资金渠道
            else if (FeeRateTypeEnum.CHANNEL.equals(feeRateTypeEnum)) {
                updateConfig = buildUpdateConfigWhenChannel(entity, feeRateText);
            }
            // 资金通道阶梯
            else if (FeeRateTypeEnum.CHANNEL_LADDER.equals(feeRateTypeEnum)) {
                updateConfig = buildUpdateConfigWhenChannelLadder(entity, feeRateText);
            }
            // 否则为阶梯费率
            else {
                updateConfig = buildUpdateConfigWhenLadder(entity, feeRateText);
            }
            // 移除其他业务配置
            terminalConfigParams.remove(MerchantConfig.CHANNEL_STATUS);
            terminalConfigParams.remove(TransactionParam.PARAMS_BANKCARD_FEE);
            terminalConfigParams.remove(TransactionParam.CHANNEL_FEE_RATE);
            terminalConfigParams.remove(TransactionParam.CHANNEL_FEE_RATE_TAG);
            terminalConfigParams.remove(MerchantConfig.LADDER_STATUS);
            terminalConfigParams.remove(TransactionParam.LADDER_FEE_RATE_TAG);

            Map updateTerminalConfig = Optional.ofNullable(MapUtils.getMap(updateConfig, StoreConfig.PARAMS)).orElse(new TreeMap<>());
            terminalConfigParams.putAll(updateTerminalConfig);
            updateConfig.put(TerminalConfig.PARAMS, terminalConfigParams);

            // 如果存在，则更新
            String terminalConfigId = MapUtils.getString(before, DaoConstants.ID);
            if (MapUtils.isNotEmpty(before) && StringUtils.isNotEmpty(terminalConfigId)) {
                updateConfig.put(DaoConstants.ID, terminalConfigId);
                tradeConfigService.updateTerminalConfig(updateConfig);
            }
            // 如果不存在，则新增
            else {
                updateConfig.put(TerminalConfig.TERMINAL_ID, terminalId);
                updateConfig.put(TerminalConfig.PAYWAY, payWay);
                tradeConfigService.createTerminalConfig(updateConfig);
            }

            // 获取更新后配置
            Map after = (Map) tradeConfigService.getAnalyzedTerminalConfigsByPayWayList(terminalId, new int[]{payWay}).get(0);
            String labelValue = tradeComboEntity.buildMarkLabelExtra().getEffective_label_value();
            String remark = String.format("审批编号：%s 终端号 %s %s", auditSn, terminalSn, labelValue != null ? labelValue : "");
            feeRateCommonService.sendBusinessLog(merchantId, platform, operator, operatorName, remark, cloneBefore, after);
        }

        if(tradeComboDetailEntityList.size() > 0){
            // 清缓存
            supportService.removeCachedParams(merchantSn);
            // 打生效标签
            tagEffect(merchantId, tradeComboEntity);
        }
    }

    /**
     * 构建生效固定费率时候的配置
     * @param entity
     * @param feeRateText
     * @return
     */
    private Map<String,Object> buildUpdateConfigWhenFix(TradeComboDetailEntity entity, String feeRateText){
        TradeComboDetailEntity.SubPaywayStatus subPayWayStatus = entity.buildSubPaywayStatus();
        long tradeComboId = entity.getComboId();
        Map<String, Object> config = Maps.newTreeMap();
        // 业务方标识
        String productFlagTag = TradeTagConfig.getTradeComboProductFlagMap().get(tradeComboId);
        String tag = FeeRateUtils.getFeeRateTag(productFlagTag, tradeComboId);
        config.put(MerchantConfig.PAYWAY, entity.getPayway());
        Map<Integer, String> feeRateTag = Maps.newTreeMap();
        if (subPayWayStatus.getB2c_status() == 1) {
            config.put(MerchantConfig.B2C_FEE_RATE, feeRateText);
            feeRateTag.put(TradeConfigService.SUB_PAYWAY_BARCODE, tag);
        }
        if (subPayWayStatus.getC2b_status() == 1) {
            config.put(MerchantConfig.C2B_FEE_RATE, feeRateText);
            feeRateTag.put(TradeConfigService.SUB_PAYWAY_QRCODE, tag);
        }
        if (subPayWayStatus.getWap_status() == 1) {
            config.put(MerchantConfig.WAP_FEE_RATE, feeRateText);
            feeRateTag.put(TradeConfigService.SUB_PAYWAY_WAP, tag);
        }
        if (subPayWayStatus.getMini_status() == 1) {
            config.put(MerchantConfig.MINI_FEE_RATE, feeRateText);
            feeRateTag.put(TradeConfigService.SUB_PAYWAY_MINI, tag);
        }
        // 直连特有的二级支付方式：APP + H5
        if (subPayWayStatus.getApp_status() == 1) {
            config.put(MerchantConfig.APP_FEE_RATE, feeRateText);
            feeRateTag.put(TradeConfigService.SUB_PAYWAY_APP, tag);
        }
        if (subPayWayStatus.getH5_status() == 1) {
            config.put(MerchantConfig.H5_FEE_RATE, feeRateText);
            feeRateTag.put(TradeConfigService.SUB_PAYWAY_H5, tag);
        }
        // 更新普通/固定费率
        Map<String, Object> merchantConfigParams = Maps.newHashMap();
        merchantConfigParams.put(TransactionParam.FEE_RATE_TAG, feeRateTag);
        merchantConfigParams.put(TransactionParam.FEE_RATE_TYPE, FeeRateTypeEnum.FIXED.name().toLowerCase());
        config.put(MerchantConfig.PARAMS, merchantConfigParams);
        return config;
    }

    /**
     * 生效通道费率时候的配置
     *
     * @param entity
     * @param feeRateText
     */
    private Map<String, Object> buildUpdateConfigWhenChannel(TradeComboDetailEntity entity, String feeRateText){
        TradeComboDetailEntity.SubPaywayStatus subPayWayStatus = entity.buildSubPaywayStatus();
        long tradeComboId = entity.getComboId();

        // 业务方标识
        String productFlagTag = TradeTagConfig.getTradeComboProductFlagMap().get(tradeComboId);
        String tag = FeeRateUtils.getFeeRateTag(productFlagTag, tradeComboId);
        Map<Integer, String> feeRateTag = Maps.newHashMap();
        if (subPayWayStatus.getB2c_status() == 1) {
            feeRateTag.put(TradeConfigService.SUB_PAYWAY_BARCODE, tag);
        }
        if (subPayWayStatus.getC2b_status() == 1) {
            feeRateTag.put(TradeConfigService.SUB_PAYWAY_QRCODE, tag);
        }
        if (subPayWayStatus.getWap_status() == 1) {
            feeRateTag.put(TradeConfigService.SUB_PAYWAY_WAP, tag);
        }
        if (subPayWayStatus.getMini_status() == 1) {
            feeRateTag.put(TradeConfigService.SUB_PAYWAY_MINI, tag);
        }
        // 直连特有的二级支付方式：APP + H5
        if (subPayWayStatus.getApp_status() == 1) {
            feeRateTag.put(TradeConfigService.SUB_PAYWAY_APP, tag);
        }
        if (subPayWayStatus.getH5_status() == 1) {
            feeRateTag.put(TradeConfigService.SUB_PAYWAY_H5, tag);
        }

        Map<String, Object> merchantConfig = Maps.newHashMap();
        Map<String, Object> merchantConfigParams = Maps.newHashMap();
        merchantConfig.put(MerchantConfig.PAYWAY, entity.getPayway());
        //银行卡业务处理
        List<MerchantFeeRateEntity.ChannelFeeRate> applyChannelFeeRates = JsonUtil
                .decode(feeRateText, new TypeReference<List<MerchantFeeRateEntity.ChannelFeeRate>>() {});
        Map<String, Map<String, Object>> paramsBankCardMap = Maps.newHashMap();
        applyChannelFeeRates.forEach(value -> {
            Map<String, Object> map = MapUtil.hashMap(TransactionParam.FEE, StringUtil.isNotEmpty(value.getFeeRate()) ? value.getFeeRate() : value.getFee());
            if (StringUtils.isNotEmpty(value.getMax())) {
                // 元转分
                map.put(TransactionParam.PARAMS_BANKCARD_FEE_MAX, com.wosai.mpay.util.StringUtils.yuan2cents(value.getMax()));
            }
            paramsBankCardMap.put(value.getType(), map);
        });
        // 更新普通/固定费率
        merchantConfigParams.put(TransactionParam.FEE_RATE_TAG, feeRateTag);
        //设置银行卡费率
        merchantConfigParams.put(TransactionParam.PARAMS_BANKCARD_FEE, paramsBankCardMap);
        merchantConfigParams.put(TransactionParam.FEE_RATE_TYPE, FeeRateTypeEnum.CHANNEL.name().toLowerCase());
        merchantConfig.put(MerchantConfig.PARAMS, merchantConfigParams);
        return merchantConfig;
    }

    /**
     * 生效资金渠道阶梯费率
     *
     * @param entity
     * @param feeRateText
     */
    private Map<String, Object> buildUpdateConfigWhenChannelLadder(TradeComboDetailEntity entity, String feeRateText){
        TradeComboDetailEntity.SubPaywayStatus subPayWayStatus = entity.buildSubPaywayStatus();
        long tradeComboId = entity.getComboId();

        // 业务方标识
        String productFlagTag = TradeTagConfig.getTradeComboProductFlagMap().get(tradeComboId);
        String tag = FeeRateUtils.getFeeRateTag(productFlagTag, tradeComboId);
        Map<String, Object> merchantConfig = Maps.newHashMap();
        Map<String, Object> merchantConfigParams = Maps.newHashMap();
        merchantConfig.put(MerchantConfig.PAYWAY, entity.getPayway());
        Map<Integer, String> ladderFeeRateTag = Maps.newTreeMap();
        //资金渠道阶梯费率
        List<MerchantFeeRateEntity.ChannelLadderFeeRate> applyChannelLadderFeeRates = JsonUtil
                .decode(feeRateText, new TypeReference<List<MerchantFeeRateEntity.ChannelLadderFeeRate>>() {});
        Map<String, Object> channelLadderFeeMap = Maps.newHashMap();
        applyChannelLadderFeeRates.forEach(value -> {
            List<Map<String, Object>> ladderFeeRatesMapList = value.getLadderFeeRates().stream()
                    .map(ladderFeeRate -> new TreeMap<String, Object>() {{
                        // 当最大金额大于等于10亿时，表示正无穷，填空值
                        Double max = ladderFeeRate.getMax() != null && ladderFeeRate.getMax() < TradeComboConstants.ONE_BILLION
                                ? ladderFeeRate.getMax() : null;
                        put("min", ladderFeeRate.getMin());
                        put("max", max);
                        String feeRate = ladderFeeRate.getFee_rate();
                        if (subPayWayStatus.getB2c_status() == 1) {
                            put(MerchantConfig.B2C_FEE_RATE, feeRate);
                            ladderFeeRateTag.put(TradeConfigService.SUB_PAYWAY_BARCODE, tag);
                        }
                        if (subPayWayStatus.getC2b_status() == 1) {
                            put(MerchantConfig.C2B_FEE_RATE, feeRate);
                            ladderFeeRateTag.put(TradeConfigService.SUB_PAYWAY_QRCODE, tag);
                        }
                        if (subPayWayStatus.getWap_status() == 1) {
                            put(MerchantConfig.WAP_FEE_RATE, feeRate);
                            ladderFeeRateTag.put(TradeConfigService.SUB_PAYWAY_WAP, tag);
                        }
                        if (subPayWayStatus.getMini_status() == 1) {
                            put(MerchantConfig.MINI_FEE_RATE, feeRate);
                            ladderFeeRateTag.put(TradeConfigService.SUB_PAYWAY_MINI, tag);
                        }
                        // 直连特有的二级支付方式：APP + H5
                        if (subPayWayStatus.getApp_status() == 1) {
                            put(MerchantConfig.APP_FEE_RATE, feeRate);
                            ladderFeeRateTag.put(TradeConfigService.SUB_PAYWAY_APP, tag);
                        }
                        if (subPayWayStatus.getH5_status() == 1) {
                            put(MerchantConfig.H5_FEE_RATE, feeRate);
                            ladderFeeRateTag.put(TradeConfigService.SUB_PAYWAY_H5, tag);
                        }
                    }})
                    .collect(Collectors.toList());
            channelLadderFeeMap.put(value.getType(), ladderFeeRatesMapList);
        });
        // 更新普通/固定费率
        merchantConfigParams.put(TransactionParam.FEE_RATE_TAG, ladderFeeRateTag);
        //开通资金渠道费率
        merchantConfigParams.put(TransactionParam.FEE_RATE_TYPE, FeeRateTypeEnum.CHANNEL_LADDER.name().toLowerCase());
        //设置银行卡费率
        merchantConfigParams.put(TransactionParam.CHANNEL_LADDER_FEE_RATES, channelLadderFeeMap);
        merchantConfig.put(MerchantConfig.PARAMS, merchantConfigParams);
        return merchantConfig;
    }

    /**
     * 构建生效阶梯费率时候的配置
     * @param entity
     * @return
     */
    private Map<String,Object> buildUpdateConfigWhenLadder(TradeComboDetailEntity entity, String feeRateText) {
        TradeComboDetailEntity.SubPaywayStatus subPayWayStatus = entity.buildSubPaywayStatus();
        long tradeComboId = entity.getComboId();
        String productFlagTag = TradeTagConfig.getTradeComboProductFlagMap().get(tradeComboId);
        String tag = FeeRateUtils.getFeeRateTag(productFlagTag, tradeComboId);
        Map<Integer, String> ladderFeeRateTag = Maps.newTreeMap();
        List<MerchantFeeRateEntity.LadderFeeRate> ladderFeeRateList = JsonUtil.decode(feeRateText, new TypeReference<List<MerchantFeeRateEntity.LadderFeeRate>>() {});
        if (Objects.isNull(ladderFeeRateList)) {
            log.error("阶梯费率为空. feeRateText:{}", feeRateText);
            throw TradeManageBizException.createExc("阶梯费率为空");
        }
        Map<String, Object> merchantConfig = Maps.newHashMap();
        Map<String, Object> merchantConfigParams = Maps.newHashMap();
        merchantConfig.put(MerchantConfig.PAYWAY, entity.getPayway());

        List<Map<String, Object>> ladderFeeRatesMapList = ladderFeeRateList.stream()
                .map(ladderFeeRate -> new TreeMap<String, Object>() {{
                    // 当最大金额大于等于10亿时，表示正无穷，填空值
                    Double max = ladderFeeRate.getMax() != null && ladderFeeRate.getMax() < TradeComboConstants.ONE_BILLION
                            ? ladderFeeRate.getMax() : null;
                    put("min", ladderFeeRate.getMin());
                    put("max", max);
                    String feeRate = ladderFeeRate.getFee_rate();
                    if (subPayWayStatus.getB2c_status() == 1) {
                        put(MerchantConfig.B2C_FEE_RATE, feeRate);
                        ladderFeeRateTag.put(TradeConfigService.SUB_PAYWAY_BARCODE, tag);
                    }
                    if (subPayWayStatus.getC2b_status() == 1) {
                        put(MerchantConfig.C2B_FEE_RATE, feeRate);
                        ladderFeeRateTag.put(TradeConfigService.SUB_PAYWAY_QRCODE, tag);
                    }
                    if (subPayWayStatus.getWap_status() == 1) {
                        put(MerchantConfig.WAP_FEE_RATE, feeRate);
                        ladderFeeRateTag.put(TradeConfigService.SUB_PAYWAY_WAP, tag);
                    }
                    if (subPayWayStatus.getMini_status() == 1) {
                        put(MerchantConfig.MINI_FEE_RATE, feeRate);
                        ladderFeeRateTag.put(TradeConfigService.SUB_PAYWAY_MINI, tag);
                    }
                    // 直连特有的二级支付方式：APP + H5
                    if (subPayWayStatus.getApp_status() == 1) {
                        put(MerchantConfig.APP_FEE_RATE, feeRate);
                        ladderFeeRateTag.put(TradeConfigService.SUB_PAYWAY_APP, tag);
                    }
                    if (subPayWayStatus.getH5_status() == 1) {
                        put(MerchantConfig.H5_FEE_RATE, feeRate);
                        ladderFeeRateTag.put(TradeConfigService.SUB_PAYWAY_H5, tag);
                    }
                }})
                .collect(Collectors.toList());
        merchantConfigParams.put(MerchantConfig.LADDER_STATUS, MerchantConfig.STATUS_OPENED);
        merchantConfigParams.put(TransactionParam.FEE_RATE_TYPE, FeeRateTypeEnum.LADDER.name().toLowerCase());
        merchantConfigParams.put(TransactionParam.FEE_RATE_TAG, ladderFeeRateTag);
        merchantConfigParams.put(MerchantConfig.LADDER_FEE_RATES, ladderFeeRatesMapList);
        merchantConfig.put(MerchantConfig.PARAMS, merchantConfigParams);
        return merchantConfig;
    }

    /**
     * 生效套餐时，获取某支付方式旧生效费率的变更数据
     * @param entity
     * @param detailEntity
     * @return
     */
    private ApplyFeeRateUpsertDalParam buildFeeRateUpdateDalParam(FeeRateEntity entity, TradeComboDetailEntity detailEntity){
        TradeComboDetailEntity.SubPaywayStatus subPayWayStatus = detailEntity.buildSubPaywayStatus();
        int b2cInUse = entity.getB2cInUse() == 1 && subPayWayStatus.getB2c_status() == 0 ? 1 : 0;
        int c2bInUse = entity.getC2bInUse() == 1 && subPayWayStatus.getC2b_status() == 0 ? 1 : 0;
        int appInUse = entity.getAppInUse() == 1 && subPayWayStatus.getApp_status() == 0 ? 1 : 0;
        int miniInUse = entity.getMiniInUse() == 1 && subPayWayStatus.getMini_status() == 0 ? 1 : 0;
        int h5InUse = entity.getH5InUse() == 1 && subPayWayStatus.getH5_status() == 0 ? 1 : 0;
        int wapInUse = entity.getWapInUse() == 1 && subPayWayStatus.getWap_status() == 0 ? 1 : 0;
        int afterMchFeeRateStatus = (b2cInUse | c2bInUse | appInUse | miniInUse | h5InUse | wapInUse) == 1
                ? FeeRateStatusConst.IN_EFFECT : FeeRateStatusConst.DELETED;

        ApplyFeeRateUpsertDalParam updateDalParam = ApplyFeeRateUpsertDalParam.builder()
                .id(entity.getId())
                .b2cInUse(b2cInUse)
                .c2bInUse(c2bInUse)
                .appInUse(appInUse)
                .miniInUse(miniInUse)
                .h5InUse(h5InUse)
                .wapInUse(wapInUse)
                .status(afterMchFeeRateStatus)
                .build();
        return updateDalParam;
    }

    private ApplyFeeRateUpsertDalParam buildFeeRateInsertDalParam(String merchantSn, String auditSn, LocalDate beginDate, LocalDate endDate, String feeRateText, TradeComboEntity comboEntity, TradeComboDetailEntity detailEntity){
        Long tradeComboId = comboEntity.getId();
        Long tradeAppId = comboEntity.getTradeAppId();
        TradeComboDetailEntity.SubPaywayStatus subPayWayStatus = detailEntity.buildSubPaywayStatus();
        FeeRateTypeEnum feeRateTypeEnum = getFeeRateTypeEnumByTradeComboDetailEntity(detailEntity);
        // 写入数据库
        ApplyFeeRateUpsertDalParam insertDalParam = ApplyFeeRateUpsertDalParam.builder()
                .appId(tradeAppId)
                .merchantSn(merchantSn)
                .tradeComboId(tradeComboId)
                .payWay(detailEntity.getPayway())
                .beginDate(beginDate.toString())
                .endDate(endDate.toString())
                .b2cInUse(subPayWayStatus.getB2c_status())
                .c2bInUse(subPayWayStatus.getC2b_status())
                .appInUse(subPayWayStatus.getApp_status())
                .miniInUse(subPayWayStatus.getMini_status())
                .h5InUse(subPayWayStatus.getH5_status())
                .wapInUse(subPayWayStatus.getWap_status())
                .status(FeeRateStatusConst.IN_EFFECT)
                .feeRateType(feeRateTypeEnum.name().toLowerCase())
                .feeRate(feeRateText)
                .auditSn(auditSn)
                .status(FeeRateStatusConst.IN_EFFECT)
                .build();
        return insertDalParam;
    }

    private MchFeeRateApplyLogUpsertDalParam buildMchFeeRateApplyLogUpsertDalParam(String merchantSn, String auditSn, LocalDate beginDate, LocalDate endDate, String feeRateText, TradeAppEntity appEntity, TradeComboEntity comboEntity, TradeComboDetailEntity detailEntity){
        FeeRateTypeEnum feeRateTypeEnum = getFeeRateTypeEnumByTradeComboDetailEntity(detailEntity);
        // 记录到商户费率审批 log 表
        MchFeeRateApplyLogUpsertDalParam logInsertDalParam = MchFeeRateApplyLogUpsertDalParam.builder()
                .merchantSn(merchantSn)
                .auditSn(auditSn)
                .payWay(detailEntity.getPayway())
                .tradeAppName(appEntity.getName())
                .tradeComboId(comboEntity.getId())
                .tradeComboShortName(comboEntity.getShortName())
                .description(comboEntity.getDescription())
                .beginDate(beginDate.toString())
                .endDate(endDate.toString())
                .feeRateType(feeRateTypeEnum.name().toLowerCase())
                .feeRate(feeRateText)
                .build();
        return logInsertDalParam;
    }

    private FeeRateTypeEnum getFeeRateTypeEnumByTradeComboDetailEntity(TradeComboDetailEntity detailEntity){
        FeeRate fee = detailEntity.buildFeeRate();
        if (fee != null) {
            return FeeRateTypeEnum.valueOf(detailEntity.buildFeeRate().getFeeRateType().toUpperCase());
        } else {
            // TODO 套餐旧版本实现逻辑，等所有套餐存储数据格式都迁移后，移除代码
            FeeRateTypeEnum feeRateTypeEnum;
            // 资金通道费率
            if (StringUtils.isNotEmpty(detailEntity.getChannelFeeRates())) {
                feeRateTypeEnum = FeeRateTypeEnum.CHANNEL;
            }
            // 固定费率
            else if (StringUtils.isNotEmpty(detailEntity.getFeeRateMin())) {
                feeRateTypeEnum = FeeRateTypeEnum.FIXED;
            }
            // 阶梯费率
            else {
                feeRateTypeEnum = FeeRateTypeEnum.LADDER;
            }
            return feeRateTypeEnum;
        }
    }

    /**
     * 取消恢复
     *
     * @param request
     */
    @Override
    public void cancelFeeRate(CancelFeeRateRequest request) {
        String merchantSn = request.getMerchantSn();
        Long tradeComboId = request.getTradeComboId();
        String auditSn = request.getAuditSn();
        String operator = request.getOperator();
        String operatorName = request.getOperatorName();
        List<Integer> payWays = request.getPayWays();

        Map<String, Object> merchantInfo = merchantService.getMerchantBySn(merchantSn);
        if (MapUtils.isEmpty(merchantInfo)) {
            throw TradeManageBizException.createExc("商户不存在");
        }
        String merchantId = MapUtils.getString(merchantInfo, DaoConstants.ID);

        TradeComboEntity tradeComboEntity = tradeComboDao.selectById(tradeComboId);
        if (tradeComboEntity == null || tradeComboEntity.getId() == null) {
            throw TradeManageBizException.createExc("费率套餐不存在");
        }
        // 套餐是活动套餐时，商户不是按照payway
        ActivityFeeRateParam activityInfo = feeRateBuildParams.buildActivityFeeRateParam(ActivityFeeRateParam.ActivityFeeRateRequest.builder()
                .applyId(request.getApplyId())
                .applyStatus(request.getApplyStatus())
                .changeActivityFeeRateEnum(request.getChangeActivityFeeRateEnum())
                .comboId(tradeComboId)
                .merchantSn(merchantSn)
                .storeSn(request.getStoreSn())
                .terminalSn(request.getTerminalSn())
                .auditSn(auditSn)
                .operator(operator)
                .operatorName(operatorName)
                .tradeComboEntity(tradeComboEntity)
                .applyPuHuiHint(request.isApplyPuHuiHint())
                .systemCancel(request.isSystemCancel())
                .merchantId(merchantId)
                .occurSource(request.getOccurSource())
                .businessOpLog(request.getBusinessOpLog())
                .activityApplyLog(request.getActivityApplyLog())
                .build());

        String comboConfigLevel = tradeComboEntity.getComboConfigLevel();
        // 商户级别取消
        if (ComboConfigLevelEnum.MERCHANT.name().equalsIgnoreCase(comboConfigLevel)) {
            cancelMerchantFeeRate(merchantId, merchantSn, tradeComboEntity, auditSn, operator, operatorName, payWays, activityInfo);
        }
        // 门店级别取消
        if (ComboConfigLevelEnum.STORE.name().equalsIgnoreCase(comboConfigLevel)) {
            String storeSn = request.getStoreSn();
            if (StringUtils.isEmpty(storeSn)) {
                throw TradeManageBizException.createExc("门店级别取消费率时：门店号不能为空");
            }
            cancelStoreFeeRate(merchantId, merchantSn, storeSn, tradeComboEntity, auditSn, operator, operatorName, payWays, activityInfo);
        }
        // 终端级别取消
        if (ComboConfigLevelEnum.TERMINAL.name().equalsIgnoreCase(comboConfigLevel)) {
            String terminalSn = request.getTerminalSn();
            if (StringUtils.isEmpty(terminalSn)) {
                throw TradeManageBizException.createExc("终端级别取消费率时：终端号不能为空");
            }
            cancelTerminalFeeRate(merchantId, merchantSn, terminalSn, tradeComboEntity, auditSn, operator, operatorName, payWays, activityInfo);
        }
        //取消套餐后置事件操作，引发活动事件处理等
        processCancelComboAfter(activityInfo);
    }

    /**
     * 取消商户级别费率套餐
     */
    private void cancelMerchantFeeRate(String merchantId, String merchantSn, TradeComboEntity tradeComboEntity,
                                       String auditSn, String operator, String operatorName, List<Integer> payWays,
                                       ActivityFeeRateParam activityInfo) {
        Long tradeComboId = tradeComboEntity.getId();

        MerchantFeeRateCountDalParam countDalParam = MerchantFeeRateCountDalParam.builder()
                .merchantSn(merchantSn)
                .tradeComboId(tradeComboId)
                .status(FeeRateStatusConst.IN_EFFECT)
                .build();
        if (merchantFeeRateDao.count(countDalParam) == 0) {
            if (tradeComboId == commonApolloConfig.getBindBankcardComboId()) {
                log.debug("商户当前没有生效绑卡套餐，默认当做成功处理");
                return;
            }
            throw TradeManageBizException.createExc("没有可以取消的费率");
        }

        restoreMerchantFeeRate(merchantId, merchantSn,
                tradeComboEntity, auditSn, operator, operatorName, payWays, activityInfo);
    }

    /**
     * 恢复商户级别默认费率
     */
    private void restoreMerchantFeeRate(String merchantId, String merchantSn, TradeComboEntity tradeComboEntity,
                                              String auditSn, String operator, String operatorName,
                                              List<Integer> payWays, ActivityFeeRateParam activityInfo) {
        Long tradeComboId = tradeComboEntity.getId();
        // 获取费率列表，逐个取消并恢复费率
        MerchantFeeRateQueryDalParam queryDalParam = MerchantFeeRateQueryDalParam.builder()
                .merchantSn(merchantSn)
                .tradeComboId(tradeComboId)
                .status(FeeRateStatusConst.IN_EFFECT)
                .build();
        List<MerchantFeeRateEntity> merchantFeeRateEntities = Lists.newArrayList();
        merchantFeeRateDao.selectList(queryDalParam).forEach(merchantFeeRateEntity -> {
            //如果不在指定的支付通道里面，那么不取消
            if (payWays != null && !payWays.isEmpty() && !payWays.contains(merchantFeeRateEntity.getPayWay())) {
                return;
            }
            //恢复商户级别默认sub_payway
            restoreMerchantSubPayWay(merchantFeeRateEntity);
            merchantFeeRateEntities.add(merchantFeeRateEntity);
        });
        if (CollectionUtils.isEmpty(merchantFeeRateEntities)) {
            log.info("当前无待取消套餐. tradeComboId:{},payWays:{}", tradeComboId, payWays);
            return;
        }
        TradeAppEntity tradeAppEntity = tradeAppDao.queryTradeAppById(tradeComboEntity.getTradeAppId());
        if (!tradeAppEntity.isPayApp()){
            // 基础业务参数
            // 生效恢复套餐
            Map merchantInfo = merchantService.getMerchant(merchantId);
            List<ApplyFeeRateRequest> feeRateRequests = feeRateCommonService.buildRestoreApplyFeeRateRequests(merchantSn, operator, operatorName, auditSn, merchantFeeRateEntities, activityInfo);
            log.info("取消恢复套餐列表:{}", JsonUtil.encode(feeRateRequests));
            for (ApplyFeeRateRequest applyFeeRateRequest : feeRateRequests) {
                TradeComboEntity applyComboEntity = tradeComboDao.selectById(applyFeeRateRequest.getTradeComboId());
                List<TradeComboDetailEntity> tradeComboDetailEntities = tradeComboDetailDao.selectList(TradeComboDetailQueryDalParam.builder()
                        .comboId(applyFeeRateRequest.getTradeComboId()).build())
                        // 为空就是全选
                        .stream().filter(detailEntity ->
                                Objects.isNull(applyFeeRateRequest.getApplyFeeRateMap())
                                        || applyFeeRateRequest.getApplyFeeRateMap().containsKey(detailEntity.getPayway()+""))
                        .collect(Collectors.toList());
                ApplyFeeRateChainExtra extra = new ApplyFeeRateChainExtra(applyFeeRateRequest.isApplyPartialPayway(),
                        applyFeeRateRequest.getApplyFeeRateMap(), activityInfo, applyFeeRateRequest.getAuditTemplateId());
                applyFeeRateFilterChain.filter(applyComboEntity, merchantInfo, applyFeeRateRequest.getApplyTimeMillis(), extra);
                applyMerchantFeeRateLock(merchantId, merchantSn, applyFeeRateRequest.getPlatform(),
                        applyFeeRateRequest.getOperator(),
                        applyFeeRateRequest.getOperatorName(),
                        applyFeeRateRequest.getAuditSn(),
                        applyComboEntity,
                        tradeComboDetailEntities,
                        applyFeeRateRequest.getApplyFeeRateMap(),
                        CommonConstant.FEE_RATE_ACTION_RESTORE,
                        activityInfo
                );
            }
        } else {
            // 支付应用参数
            // 失效当前套餐配置
            for(MerchantFeeRateEntity merchantFeeRateEntity : merchantFeeRateEntities) {
                MerchantFeeRateUpsertDalParam updateDalParam = MerchantFeeRateUpsertDalParam.builder()
                        .id(merchantFeeRateEntity.getId())
                        .b2cInUse(0)
                        .c2bInUse(0)
                        .wapInUse(0)
                        .miniInUse(0)
                        .appInUse(0)
                        .h5InUse(0)
                        .status(FeeRateStatusConst.DELETED)
                        .build();
                merchantFeeRateDao.updateById(updateDalParam);
                // 获取更新前配置
                Map<String, Object> before = (Map<String, Object>) tradeConfigService.getMerchantAppConfigByMerchantIdAndPaywayAndApp(merchantId, merchantFeeRateEntity.getPayWay(), tradeComboEntity.getTradeAppId() +"");

                // 删除商户费率
                tradeConfigService.deleteMerchantAppConfig(MapUtils.getString(before, DaoConstants.ID));
                String labelValue = tradeComboEntity.buildMarkLabelExtra().getCancel_label_value();
                String remark = String.format("审批编号：%s 商户号：%s %s 应用 %s 删除", auditSn, merchantSn, labelValue != null ? labelValue : "", tradeAppEntity.getName());

                // 发送日志
                feeRateCommonService.sendBusinessLog(merchantId, operator, operatorName, remark, cloneProcessLadderForLog(before), Collections.emptyMap());
            }
            // 清除缓存
            supportService.removeCachedParams(merchantSn);
        }
    }

    /**
     * 恢复商户sub pay way展示问题
     */
    private void restoreMerchantSubPayWay(MerchantFeeRateEntity merchantFeeRateEntity) {
        //存在h5, app sub_payway先批量取消下，防止恢复套餐内无这两个sub_payway
        if (Objects.equals(merchantFeeRateEntity.getH5InUse(), NumberUtils.INTEGER_ONE)
                || Objects.equals(merchantFeeRateEntity.getAppInUse(), NumberUtils.INTEGER_ONE)) {
            int status = (merchantFeeRateEntity.getB2cInUse()
                    | merchantFeeRateEntity.getC2bInUse()
                    | merchantFeeRateEntity.getMiniInUse()
                    | merchantFeeRateEntity.getWapInUse()
            ) == 1 ? FeeRateStatusConst.IN_EFFECT : FeeRateStatusConst.DELETED;
            // 设置费率已删除
            MerchantFeeRateUpsertDalParam updateDalParam = MerchantFeeRateUpsertDalParam.builder()
                    .id(merchantFeeRateEntity.getId())
                    .appInUse(0)
                    .h5InUse(0)
                    .status(status)
                    .build();
            merchantFeeRateDao.updateById(updateDalParam);
        }
    }

    /**
     * 取消门店级别费率套餐
     */
    private void cancelStoreFeeRate(String merchantId, String merchantSn, String storeSn, TradeComboEntity tradeComboEntity,
                                       String auditSn, String operator, String operatorName, List<Integer> payWays,
                                    ActivityFeeRateParam activityInfo) {
        Long tradeComboId = tradeComboEntity.getId();
        TradeAppEntity tradeAppEntity = tradeAppDao.queryTradeAppById(tradeComboEntity.getTradeAppId());
        String tradeAppName = tradeAppEntity.getName();
        StoreFeeRateCountDalParam countDalParam = StoreFeeRateCountDalParam.builder()
                .merchantSn(merchantSn)
                .storeSn(storeSn)
                .tradeComboId(tradeComboId)
                .status(FeeRateStatusConst.IN_EFFECT)
                .build();
        if (storeFeeRateDao.count(countDalParam) == 0) {
            throw TradeManageBizException.createExc("没有可以取消的费率");
        }
        List<Integer> cancelPayways = new ArrayList<Integer>();
        // 获取费率列表，逐个取消并恢复费率
        StoreFeeRateQueryDalParam queryDalParam = StoreFeeRateQueryDalParam.builder()
                .merchantSn(merchantSn)
                .storeSn(storeSn)
                .tradeComboId(tradeComboId)
                .status(FeeRateStatusConst.IN_EFFECT)
                .build();
        storeFeeRateDao.selectList(queryDalParam).forEach(feeRateEntity -> {
            //如果不在指定的支付通道里面，那么不取消
            if(payWays != null && !payWays.isEmpty() && !payWays.contains(feeRateEntity.getPayWay())){
                return;
            }
            String labelValue = tradeComboEntity.buildMarkLabelExtra().getCancel_label_value();
            String remark = String.format("审批编号：%s 门店号：%s %s 应用 %s 删除", auditSn, storeSn, labelValue != null ? labelValue : "", tradeAppName);
            deleteStoreFeeRate(merchantId, merchantSn, operator, operatorName, remark, feeRateEntity);
            cancelPayways.add(feeRateEntity.getPayWay());
        });
    }

    /**
     * 取消终端级别费率套餐
     */
    private void cancelTerminalFeeRate(String merchantId, String merchantSn, String terminalSn, TradeComboEntity tradeComboEntity,
                                       String auditSn, String operator, String operatorName, List<Integer> payWays,
                                       ActivityFeeRateParam activityInfo) {
        Long tradeComboId = tradeComboEntity.getId();

        TerminalFeeRateCountDalParam countDalParam = TerminalFeeRateCountDalParam.builder()
                .merchantSn(merchantSn)
                .terminalSn(terminalSn)
                .tradeComboId(tradeComboId)
                .status(FeeRateStatusConst.IN_EFFECT)
                .build();
        if (terminalFeeRateDao.count(countDalParam) == 0) {
            throw TradeManageBizException.createExc("没有可以取消的费率");
        }

        List<Integer> cancelPayways = new ArrayList<Integer>();
        // 获取费率列表，逐个取消并恢复费率
        TerminalFeeRateQueryDalParam queryDalParam = TerminalFeeRateQueryDalParam.builder()
                .merchantSn(merchantSn)
                .terminalSn(terminalSn)
                .tradeComboId(tradeComboId)
                .status(FeeRateStatusConst.IN_EFFECT)
                .build();
        terminalFeeRateDao.selectList(queryDalParam).forEach(feeRateEntity -> {
            //如果不在指定的支付通道里面，那么不取消
            if(payWays != null && !payWays.isEmpty() && !payWays.contains(feeRateEntity.getPayWay())){
                return;
            }
            String labelValue = tradeComboEntity.buildMarkLabelExtra().getCancel_label_value();
            String remark = String.format("审批编号：%s 终端号：%s %s 删除", auditSn, terminalSn, labelValue != null ? labelValue : "");
            deleteTerminalFeeRate(merchantId, merchantSn, operator, operatorName, remark, feeRateEntity);
            cancelPayways.add(feeRateEntity.getPayWay());
        });
    }

    @Override
    public void endMerchantFeeRate(Long feeRateId) {
        MerchantFeeRateEntity merchantFeeRateEntity = merchantFeeRateDao.selectById(feeRateId);
        if (merchantFeeRateEntity == null || merchantFeeRateEntity.getStatus() == FeeRateStatusConst.DELETED) {
            return;
        }
        long tradeComboId = merchantFeeRateEntity.getTradeComboId();
        String merchantSn = merchantFeeRateEntity.getMerchantSn();

        // 获取商户信息
        Map merchantInfo = merchantService.getMerchantBySn(merchantSn);
        if (MapUtils.isEmpty(merchantInfo)) {
            throw TradeManageBizException.createExc("商户不存在");
        }
        String merchantId = MapUtils.getString(merchantInfo, DaoConstants.ID);
        TradeComboEntity tradeComboEntity = tradeComboDao.selectById(merchantFeeRateEntity.getTradeComboId());

        String labelValue = tradeComboEntity.buildMarkLabelExtra().getCancel_label_value();
        String remark = String.format("%s", labelValue != null ? labelValue : "");
        //恢复商户级别默认费率
        restoreMerchantSubPayWay(merchantFeeRateEntity);
        ActivityFeeRateParam activityInfo = getActivityByComboId(tradeComboId, merchantSn, null, null);
        List<ApplyFeeRateRequest> applyFeeRateRequests = feeRateCommonService.buildRestoreApplyFeeRateRequests(merchantSn, null, "System", remark, Lists.newArrayList(merchantFeeRateEntity), activityInfo);
        // 生效恢复套餐
        log.info("取消恢复套餐列表:{}", JsonUtil.encode(applyFeeRateRequests));
        applyFeeRateRequests.forEach(applyFeeRateRequest -> {
            TradeComboEntity applyComboEntity = tradeComboDao.selectById(applyFeeRateRequest.getTradeComboId());
            List<TradeComboDetailEntity> applyComboDetailEntity = tradeComboDetailDao.selectList(TradeComboDetailQueryDalParam.builder()
                    .comboId(applyFeeRateRequest.getTradeComboId()).build())
                    // 为空就是全选
                    .stream().filter(detailEntity -> Objects.isNull(applyFeeRateRequest.getApplyFeeRateMap()) || Objects.nonNull(applyFeeRateRequest.getApplyFeeRateMap().get(detailEntity.getPayway()+"")))
                    .collect(Collectors.toList());
            ApplyFeeRateChainExtra extra = new ApplyFeeRateChainExtra(applyFeeRateRequest.isApplyPartialPayway(),
                    applyFeeRateRequest.getApplyFeeRateMap(), activityInfo, applyFeeRateRequest.getAuditTemplateId());
            applyFeeRateFilterChain.filter(applyComboEntity, merchantInfo, applyFeeRateRequest.getApplyTimeMillis(), extra);
            applyMerchantFeeRateLock(merchantId, merchantSn, applyFeeRateRequest.getPlatform(),
                    applyFeeRateRequest.getOperator(),
                    applyFeeRateRequest.getOperatorName(),
                    applyFeeRateRequest.getAuditSn(),
                    applyComboEntity,
                    applyComboDetailEntity,
                    applyFeeRateRequest.getApplyFeeRateMap(),
                    CommonConstant.FEE_RATE_ACTION_RESTORE,
                    activityInfo
            );
        });

        MerchantFeeRateCountDalParam countDalParam = MerchantFeeRateCountDalParam.builder()
                .merchantSn(merchantSn)
                .tradeComboId(tradeComboId)
                .status(FeeRateStatusConst.IN_EFFECT)
                .build();

        //如果是银行活动费率的商户，花呗分期其实金额回调到300；
        setHuabeiInitAmountByCombo(merchantId,tradeComboId,FeeRateStatusConst.DELETED);
        if (merchantFeeRateDao.count(countDalParam) == 0) {
            // 整个费率套餐已全结束，则打结束标签
            tagEnd(merchantId, tradeComboEntity);
            commonActivityService.processCancelCombo(merchantSn, null, tradeComboId, true, true);

            // 结束活动
            effectOrInvalidActivity(activityInfo, false, ConstantUtil.SYSTEM_NAME,"取消原因：活动自动失效");
        }
    }

    @Override
    public void endStoreFeeRate(Long feeRateId) {
        StoreFeeRateEntity storeFeeRateEntity = storeFeeRateDao.selectById(feeRateId);
        if (storeFeeRateEntity == null || storeFeeRateEntity.getStatus() == FeeRateStatusConst.DELETED) {
            return;
        }
        long tradeComboId = storeFeeRateEntity.getTradeComboId();
        TradeAppEntity tradeAppEntity = tradeAppDao.queryTradeAppById(storeFeeRateEntity.getAppId());
        String tradeAppName = tradeAppEntity.getName();
        String merchantSn = storeFeeRateEntity.getMerchantSn();
        String storeSn = storeFeeRateEntity.getStoreSn();

        // 获取商户信息
        Map merchantInfo = merchantService.getMerchantBySn(merchantSn);
        if (MapUtils.isEmpty(merchantInfo)) {
            throw TradeManageBizException.createExc("商户不存在");
        }
        String merchantId = MapUtils.getString(merchantInfo, DaoConstants.ID);
        TradeComboEntity tradeComboEntity = tradeComboDao.selectById(storeFeeRateEntity.getTradeComboId());

        String labelValue = tradeComboEntity.buildMarkLabelExtra().getEnd_label_value();
        String remark = String.format("门店号：%s %s 应用 %s 删除", storeSn, labelValue != null ? labelValue : "", tradeAppName);
        // 门店费率结束，直接删除费率
        deleteStoreFeeRate(merchantId, merchantSn, null, "System", remark, storeFeeRateEntity);

        StoreFeeRateCountDalParam countDalParam = StoreFeeRateCountDalParam.builder()
                .merchantSn(merchantSn)
                .storeSn(storeSn)
                .tradeComboId(tradeComboId)
                .status(FeeRateStatusConst.IN_EFFECT)
                .build();
        if (storeFeeRateDao.count(countDalParam) == 0) {
            // 整个费率套餐已全结束，则打结束标签
            tagEnd(merchantId, tradeComboEntity);

            // 结束活动
            ActivityFeeRateParam activityInfo = getActivityByComboId(tradeComboId, null, storeSn, null);
            effectOrInvalidActivity(activityInfo, false, ConstantUtil.SYSTEM_NAME,"取消原因：活动自动失效");
        }
    }

    @Override
    public void endTerminalFeeRate(Long feeRateId) {
        TerminalFeeRateEntity terminalFeeRateEntity = terminalFeeRateDao.selectById(feeRateId);
        if (terminalFeeRateEntity == null || terminalFeeRateEntity.getStatus() == FeeRateStatusConst.DELETED) {
            return;
        }
        long tradeComboId = terminalFeeRateEntity.getTradeComboId();
        String merchantSn = terminalFeeRateEntity.getMerchantSn();
        String terminalSn = terminalFeeRateEntity.getTerminalSn();

        // 获取商户信息
        Map merchantInfo = merchantService.getMerchantBySn(merchantSn);
        if (MapUtils.isEmpty(merchantInfo)) {
            throw TradeManageBizException.createExc("商户不存在");
        }
        String merchantId = MapUtils.getString(merchantInfo, DaoConstants.ID);
        TradeComboEntity tradeComboEntity = tradeComboDao.selectById(terminalFeeRateEntity.getTradeComboId());

        String labelValue = tradeComboEntity.buildMarkLabelExtra().getEnd_label_value();
        String remark = String.format("终端号：%s %s 删除", terminalSn, labelValue != null ? labelValue : "");
        // 终端费率结束，直接删除费率
        deleteTerminalFeeRate(merchantId, merchantSn, null, "System", remark, terminalFeeRateEntity);

        TerminalFeeRateCountDalParam countDalParam = TerminalFeeRateCountDalParam.builder()
                .merchantSn(merchantSn)
                .terminalSn(terminalSn)
                .tradeComboId(tradeComboId)
                .status(FeeRateStatusConst.IN_EFFECT)
                .build();
        // 整个费率套餐已全结束，则打结束标签
        if (terminalFeeRateDao.count(countDalParam) == 0) {
            tagEnd(merchantId, tradeComboEntity);

            // 结束活动
            ActivityFeeRateParam activityInfo = getActivityByComboId(tradeComboId, null, null, terminalSn);
            effectOrInvalidActivity(activityInfo, false, ConstantUtil.SYSTEM_NAME,"取消原因：活动自动失效");
        }
    }

    /**
     * 删除终端级别费率
     */
    private void deleteTerminalFeeRate(String merchantId, String merchantSn, String operator, String operatorName, String remark,
                                       TerminalFeeRateEntity feeRateEntity) {
        long terminalFeeRateId = feeRateEntity.getId();
        int payWay = feeRateEntity.getPayWay();
        String terminalSn = feeRateEntity.getTerminalSn();
        String terminalId = MapUtils.getString(terminalService.getTerminalBySn(terminalSn), DaoConstants.ID);

        // 设置费率已删除
        TerminalFeeRateUpsertDalParam updateDalParam = TerminalFeeRateUpsertDalParam.builder()
                .id(terminalFeeRateId)
                .b2cInUse(0)
                .c2bInUse(0)
                .wapInUse(0)
                .miniInUse(0)
                .appInUse(0)
                .h5InUse(0)
                .status(FeeRateStatusConst.DELETED)
                .build();
        terminalFeeRateDao.updateById(updateDalParam);

        // 获取更新前配置
        Map<String, Object> before = (Map<String, Object>) tradeConfigService.getAnalyzedTerminalConfigsByPayWayList(terminalId, new int[]{payWay}).get(0);
        String terminalConfigId = MapUtils.getString(before, DaoConstants.ID);

        // 直接删除终端费率
        tradeConfigService.deleteTerminalConfig(terminalConfigId);
        // 清除缓存
        supportService.removeCachedParams(merchantSn);

        feeRateCommonService.sendBusinessLog(merchantId, operator, operatorName, remark, before, Collections.EMPTY_MAP);
    }

    /**
     * 删除门店级别费率
     */
    private void deleteStoreFeeRate(String merchantId, String merchantSn, String operator, String operatorName, String remark,
                                       StoreFeeRateEntity feeRateEntity) {
        long storeFeeRateId = feeRateEntity.getId();
        int payWay = feeRateEntity.getPayWay();
        Long appId = feeRateEntity.getAppId();
        String storeSn = feeRateEntity.getStoreSn();

        String storeId = businssCommonService.getStoreIdBySn(storeSn);

        // 设置费率已删除
        StoreFeeRateUpsertDalParam updateDalParam = StoreFeeRateUpsertDalParam.builder()
                .id(storeFeeRateId)
                .b2cInUse(0)
                .c2bInUse(0)
                .wapInUse(0)
                .miniInUse(0)
                .appInUse(0)
                .h5InUse(0)
                .status(FeeRateStatusConst.DELETED)
                .build();
        storeFeeRateDao.updateById(updateDalParam);

        // 获取更新前配置
        Map<String, Object> before = (Map<String, Object>) tradeConfigService.getStoreAppConfigByStoreIdAndPaywayAndApp(storeId, payWay, appId +"");
        String storeConfigId = MapUtils.getString(before, DaoConstants.ID);

        // 直接删除门店费率
        tradeConfigService.deleteStoreAppConfig(storeConfigId);

        // 清除缓存
        supportService.removeCachedParams(merchantSn);

        feeRateCommonService.sendBusinessLog(merchantId, operator, operatorName, remark, cloneProcessLadderForLog(before), Collections.emptyMap());
    }

    @Override
    public void applyFeeRate(String mchSn, Long tradeComboId) {
        ApplyFeeRateRequest request = new ApplyFeeRateRequest()
                .setMerchantSn(mchSn)
                .setTradeComboId(tradeComboId)
                .setAuditSn("接口调用申请")
                .setOperatorName("接口调用申请");
        applyFeeRateOne(request);
    }

    @Override
    public List<ListMchFeeRateResult> listMchFeeRates(String mchSn) {
        Map merchantInfo = merchantService.getMerchantBySn(mchSn);
        if (MapUtils.isEmpty(merchantInfo)) {
            throw TradeManageBizException.createExc("商户不存在");
        }
        String mchId = MapUtils.getString(merchantInfo, DaoConstants.ID);
        // 获取应用名称
        Map<Long, String> appIdAndNameMap = tradeAppDao.selectAll().stream().collect(Collectors.toMap(TradeAppEntity::getId, TradeAppEntity::getName));
        List<ListMchFeeRateResult> results = getMchEffectComboFeeRates(mchSn, appIdAndNameMap);
        // 添加基础费率
        results.addAll(getBasicFeeRates(mchId));
        // 添加商户配置应用方配置列表
        if (MapUtils.isNotEmpty(appIdAndNameMap)) {
            appIdAndNameMap.forEach(
                    (appId, appName) -> results.addAll(getScanCodeOrderFeeRates(mchId, appId, appName))
            );
        }
        return results;
    }

    @Override
    public List<ListMchFeeRateResult> listMchEffectFeeRates(String mchSn) {
        List<ListMchFeeRateResult> results = new ArrayList<>();
        Map<Long, String> appIdAndNameMap = tradeAppDao.selectAll().stream().collect(Collectors.toMap(TradeAppEntity::getId, TradeAppEntity::getName));
        results.addAll(getMchEffectComboFeeRates(mchSn, appIdAndNameMap));
        results.addAll(getMchStoreEffectComboFeeRates(mchSn, null, appIdAndNameMap));
        results.addAll(getMchTerminalEffectComboFeeRates(mchSn, null, appIdAndNameMap));
        return results;
    }

    @Override
    public List<ListMchFeeRateResult> listStoreEffectFeeRates(String mchSn, String sn) {
        Map<Long, String> appIdAndNameMap = tradeAppDao.selectAll().stream().collect(Collectors.toMap(TradeAppEntity::getId, TradeAppEntity::getName));
        return getMchStoreEffectComboFeeRates(mchSn, sn, appIdAndNameMap);
    }

    @Override
    public List<ListMchFeeRateResult> listTerminalEffectFeeRates(String mchSn, String sn) {
        Map<Long, String> appIdAndNameMap = tradeAppDao.selectAll().stream().collect(Collectors.toMap(TradeAppEntity::getId, TradeAppEntity::getName));
        return getMchTerminalEffectComboFeeRates(mchSn, sn, appIdAndNameMap);
    }

    /**
     * 获取生效中的商户套餐费率
     * @param mchSn
     * @param appIdAndNameMap
     * @return
     */
    private List<ListMchFeeRateResult> getMchEffectComboFeeRates(String mchSn, Map<Long, String> appIdAndNameMap){
        // 查询商户费率列表
        MerchantFeeRateQueryDalParam queryDalParam = MerchantFeeRateQueryDalParam.builder()
                .merchantSn(mchSn)
                .status(FeeRateStatusConst.IN_EFFECT)
                .build();
        List<MerchantFeeRateEntity> merchantFeeRateEntityList = merchantFeeRateDao.selectList(queryDalParam);
        // 获取套餐信息
        List<Long> tradeComboIds = merchantFeeRateEntityList.stream().map(MerchantFeeRateEntity::getTradeComboId).distinct().collect(Collectors.toList());
        Map<Long, TradeComboEntity> tradeComboMap = getTradeComboMap(tradeComboIds);
        String merchantId = tradeCommonService.getMerchantIdByMerchantSn(mchSn);
        List<Map<String,Object>> merchantConfigs = tradeConfigService.getMerchantConfigsByMerchantId(merchantId);
        Map<Integer, Map<String, Object>> payWayMerchantConfigs = merchantConfigs.stream().collect(Collectors.toMap(mc -> MapUtil.getInteger(mc, MerchantConfig.PAYWAY), Function.identity()));

        List<ListMchFeeRateResult> results = merchantFeeRateEntityList.stream()
                .map(merchantFeeRateEntity -> {
                    Long appId = merchantFeeRateEntity.getAppId();
                    Long tradeComboId = merchantFeeRateEntity.getTradeComboId();
                    Integer payWay = merchantFeeRateEntity.getPayWay();
                    TradeComboEntity tradeComboEntity = Optional.ofNullable(tradeComboMap.get(tradeComboId))
                            .orElse(new TradeComboEntity());
                    ListMchFeeRateResult listMchFeeRateResult = new ListMchFeeRateResult()
                            .setTradeAppName(appIdAndNameMap.get(appId))
                            .setTradeComboId(tradeComboId)
                            .setTradeComboName(tradeComboEntity.getName())
                            .setTradeComboShortName(tradeComboEntity.getShortName())
                            .setDescription(tradeComboEntity.getDescription())
                            .setLevel(ComboConfigLevelEnum.MERCHANT)
                            .setSn(merchantFeeRateEntity.getMerchantSn())
                            .setPayWay(payWay);

                    // 固定费率
                    // 固定费率可以由sp修改后，可能各个的xxx_fee_rate费率值不一样，merchant_fee_rate里面只能存一个固定的费率值，反应不了真实的费率，故需要从底层merchant_config获取真正的固定费率值
                    if (FeeRateTypeEnum.FIXED.name().equalsIgnoreCase(merchantFeeRateEntity.getFeeRateType())) {
                        Map<String, Object> payWayMerchantConfig = payWayMerchantConfigs.get(payWay);
                        TradeAppEntity tradeAppEntity = tradeAppDao.queryTradeAppById(tradeComboEntity.getTradeAppId());
                        if (tradeAppEntity != null && tradeAppEntity.isPayApp()) {
                            payWayMerchantConfig = tradeConfigService.getMerchantAppConfigByMerchantIdAndPaywayAndApp(merchantId, payWay, tradeComboEntity.getTradeAppId() + "");
                        }

                        listMchFeeRateResult.setIsLadderFeeRate(0);
                        if (merchantFeeRateEntity.getAppInUse() == 1) {
                            listMchFeeRateResult.setAppFeeRate(MapUtil.getString(payWayMerchantConfig, MerchantConfig.APP_FEE_RATE));
                        }
                        if (merchantFeeRateEntity.getB2cInUse() == 1) {
                            listMchFeeRateResult.setBscFeeRate(MapUtil.getString(payWayMerchantConfig, MerchantConfig.B2C_FEE_RATE));
                        }
                        if (merchantFeeRateEntity.getC2bInUse() == 1) {
                            listMchFeeRateResult.setCsbFeeRate(MapUtil.getString(payWayMerchantConfig, MerchantConfig.C2B_FEE_RATE));
                        }
                        if (merchantFeeRateEntity.getH5InUse() == 1) {
                            listMchFeeRateResult.setH5FeeRate(MapUtil.getString(payWayMerchantConfig, MerchantConfig.H5_FEE_RATE));
                        }
                        if (merchantFeeRateEntity.getMiniInUse() == 1) {
                            listMchFeeRateResult.setMiniFeeRate(MapUtil.getString(payWayMerchantConfig, MerchantConfig.MINI_FEE_RATE));
                        }
                        if (merchantFeeRateEntity.getWapInUse() == 1) {
                            listMchFeeRateResult.setWapFeeRate(MapUtil.getString(payWayMerchantConfig, MerchantConfig.WAP_FEE_RATE));
                        }
                    }
                    // 资金渠道费率
                    else if (FeeRateTypeEnum.CHANNEL.name().equalsIgnoreCase(merchantFeeRateEntity.getFeeRateType())) {
                        List<FeeRateEntity.ChannelFeeRate> channelFeeRates = merchantFeeRateEntity.buildChannelFeeRates();
                        List<ListMchFeeRateResult.ChannelFeeRate> channelFeeRateResult = channelFeeRates
                                .stream().map(feeRate -> {
                                    // 新旧版本中失效套餐的费率参数定义不一样，需要兼容老的费率参数
                                    String useFeeRate = StringUtil.isNotEmpty(feeRate.getFeeRate()) ? feeRate.getFeeRate() : feeRate.getFee();
                                    ListMchFeeRateResult.ChannelFeeRate channelFeeRate =  ListMchFeeRateResult.ChannelFeeRate.builder()
                                            .feeRate(useFeeRate)
                                            .max(feeRate.getMax())
                                            .type(feeRate.getType())
                                            .build();
                                    // 非银行卡需要将费率摊开到每个单独的sub_payway
                                    if (!Objects.equals(merchantFeeRateEntity.getPayWay(), Payway.BANKCARD.getCode())) {
                                        if (merchantFeeRateEntity.getAppInUse() == 1) {
                                            channelFeeRate.setAppFeeRate(useFeeRate);
                                        }
                                        if (merchantFeeRateEntity.getB2cInUse() == 1) {
                                            channelFeeRate.setBscFeeRate(useFeeRate);
                                        }
                                        if (merchantFeeRateEntity.getC2bInUse() == 1) {
                                            channelFeeRate.setCsbFeeRate(useFeeRate);
                                        }
                                        if (merchantFeeRateEntity.getH5InUse() == 1) {
                                            channelFeeRate.setH5FeeRate(useFeeRate);
                                        }
                                        if (merchantFeeRateEntity.getMiniInUse() == 1) {
                                            channelFeeRate.setMiniFeeRate(useFeeRate);
                                        }
                                        if (merchantFeeRateEntity.getWapInUse() == 1) {
                                            channelFeeRate.setWapFeeRate(useFeeRate);
                                        }
                                    }
                                    return channelFeeRate;
                                }).collect(Collectors.toList());
                        listMchFeeRateResult.setChannelFeeRates(channelFeeRateResult);
                    }
                    // 阶梯费率
                    else if (FeeRateTypeEnum.LADDER.name().equalsIgnoreCase(merchantFeeRateEntity.getFeeRateType())) {
                        List<ListMchFeeRateResult.LadderFeeRate> ladderFeeRates = merchantFeeRateEntity.buildLadderFeeRates()
                                .stream()
                                .map(ladderFeeRate -> {
                                    ListMchFeeRateResult.LadderFeeRate ladderFeeRateResult = new ListMchFeeRateResult.LadderFeeRate()
                                            .setMin(ladderFeeRate.getMin())
                                            .setMax(ladderFeeRate.getMax());
                                    String feeRate = ladderFeeRate.getFee_rate();
                                    if (merchantFeeRateEntity.getAppInUse() == 1) {
                                        ladderFeeRateResult.setAppFeeRate(feeRate);
                                    }
                                    if (merchantFeeRateEntity.getB2cInUse() == 1) {
                                        ladderFeeRateResult.setBscFeeRate(feeRate);
                                    }
                                    if (merchantFeeRateEntity.getC2bInUse() == 1) {
                                        ladderFeeRateResult.setCsbFeeRate(feeRate);
                                    }
                                    if (merchantFeeRateEntity.getH5InUse() == 1) {
                                        ladderFeeRateResult.setH5FeeRate(feeRate);
                                    }
                                    if (merchantFeeRateEntity.getMiniInUse() == 1) {
                                        ladderFeeRateResult.setMiniFeeRate(feeRate);
                                    }
                                    if (merchantFeeRateEntity.getWapInUse() == 1) {
                                        ladderFeeRateResult.setWapFeeRate(feeRate);
                                    }
                                    return ladderFeeRateResult;
                                })
                                .collect(Collectors.toList());
                        listMchFeeRateResult.setLadderFeeRates(ladderFeeRates);
                    }
                    // 资金渠道阶梯费率
                    else if (FeeRateTypeEnum.CHANNEL_LADDER.name().equalsIgnoreCase(merchantFeeRateEntity.getFeeRateType())) {
                        List<ListMchFeeRateResult.ChannelLadderFeeRate> channelLadderFeeRates = merchantFeeRateEntity.buildChannelLadderFeeRates()
                                .stream()
                                .map(channelLadderFeeRate -> {
                                    ListMchFeeRateResult.ChannelLadderFeeRate channelLadderFeeRateResult = new ListMchFeeRateResult.ChannelLadderFeeRate()
                                            .setType(channelLadderFeeRate.getType());
                                    List<LadderFeeRate> ladderFeeRates = channelLadderFeeRate.getLadderFeeRates().stream().map(ladderFeeRate -> {
                                        ListMchFeeRateResult.LadderFeeRate ladderFeeRateResult = new ListMchFeeRateResult.LadderFeeRate()
                                                    .setMin(ladderFeeRate.getMin())
                                                    .setMax(ladderFeeRate.getMax());
                                            String feeRate = ladderFeeRate.getFee_rate();
                                            if (merchantFeeRateEntity.getAppInUse() == 1) {
                                                ladderFeeRateResult.setAppFeeRate(feeRate);
                                            }
                                            if (merchantFeeRateEntity.getB2cInUse() == 1) {
                                                ladderFeeRateResult.setBscFeeRate(feeRate);
                                            }
                                            if (merchantFeeRateEntity.getC2bInUse() == 1) {
                                                ladderFeeRateResult.setCsbFeeRate(feeRate);
                                            }
                                            if (merchantFeeRateEntity.getH5InUse() == 1) {
                                                ladderFeeRateResult.setH5FeeRate(feeRate);
                                            }
                                            if (merchantFeeRateEntity.getMiniInUse() == 1) {
                                                ladderFeeRateResult.setMiniFeeRate(feeRate);
                                            }
                                            if (merchantFeeRateEntity.getWapInUse() == 1) {
                                                ladderFeeRateResult.setWapFeeRate(feeRate);
                                            }
                                            return ladderFeeRateResult;
                                    }).collect(Collectors.toList());
                                    channelLadderFeeRateResult.setLadderFeeRate(ladderFeeRates);
                                    return channelLadderFeeRateResult;
                                })
                                .collect(Collectors.toList());
                        listMchFeeRateResult.setChannelLadderFeeRates(channelLadderFeeRates);
                    }
                    return listMchFeeRateResult;
                })
                .collect(Collectors.toList());
        return results;
    }

    /**
     * 获取生效中的门店套餐费率
     * @param mchSn
     * @param appIdAndNameMap
     * @return
     */
    private List<ListMchFeeRateResult> getMchStoreEffectComboFeeRates(String mchSn, String storeSn, Map<Long, String> appIdAndNameMap){
        // 查询门店费率列表
        StoreFeeRateQueryDalParam queryDalParam = StoreFeeRateQueryDalParam.builder()
                .merchantSn(mchSn)
                .storeSn(storeSn)
                .status(FeeRateStatusConst.IN_EFFECT)
                .build();
        List<StoreFeeRateEntity> storeFeeRateEntityList = storeFeeRateDao.selectList(queryDalParam);
        // 获取套餐信息
        List<Long> tradeComboIds = storeFeeRateEntityList.stream().map(StoreFeeRateEntity::getTradeComboId).distinct().collect(Collectors.toList());
        Map<Long, TradeComboEntity> tradeComboMap = getTradeComboMap(tradeComboIds);
        List<ListMchFeeRateResult> results = storeFeeRateEntityList.stream()
                .map(storeFeeRateEntity -> {
                    Long appId = storeFeeRateEntity.getAppId();
                    Long tradeComboId = storeFeeRateEntity.getTradeComboId();
                    TradeComboEntity tradeComboEntity = Optional.ofNullable(tradeComboMap.get(tradeComboId))
                            .orElse(new TradeComboEntity());
                    ListMchFeeRateResult listMchFeeRateResult = new ListMchFeeRateResult()
                            .setTradeAppName(appIdAndNameMap.get(appId))
                            .setTradeComboId(tradeComboId)
                            .setTradeComboName(tradeComboEntity.getName())
                            .setTradeComboShortName(tradeComboEntity.getShortName())
                            .setDescription(tradeComboEntity.getDescription())
                            .setLevel(ComboConfigLevelEnum.STORE)
                            .setSn(storeFeeRateEntity.getStoreSn())
                            .setPayWay(storeFeeRateEntity.getPayWay());

                    // 固定费率
                    if (FeeRateTypeEnum.FIXED.name().equalsIgnoreCase(storeFeeRateEntity.getFeeRateType())) {
                        String feeRetaText = new BigDecimal(storeFeeRateEntity.getFeeRate()).toString();
                        listMchFeeRateResult.setIsLadderFeeRate(0);
                        if (storeFeeRateEntity.getAppInUse() == 1) {
                            listMchFeeRateResult.setAppFeeRate(feeRetaText);
                        }
                        if (storeFeeRateEntity.getB2cInUse() == 1) {
                            listMchFeeRateResult.setBscFeeRate(feeRetaText);
                        }
                        if (storeFeeRateEntity.getC2bInUse() == 1) {
                            listMchFeeRateResult.setCsbFeeRate(feeRetaText);
                        }
                        if (storeFeeRateEntity.getH5InUse() == 1) {
                            listMchFeeRateResult.setH5FeeRate(feeRetaText);
                        }
                        if (storeFeeRateEntity.getMiniInUse() == 1) {
                            listMchFeeRateResult.setMiniFeeRate(feeRetaText);
                        }
                        if (storeFeeRateEntity.getWapInUse() == 1) {
                            listMchFeeRateResult.setWapFeeRate(feeRetaText);
                        }
                    }
                    // 阶梯费率
                    else if (FeeRateTypeEnum.LADDER.name().equalsIgnoreCase(storeFeeRateEntity.getFeeRateType())) {
                        List<ListMchFeeRateResult.LadderFeeRate> ladderFeeRates = storeFeeRateEntity.buildLadderFeeRates()
                                .stream()
                                .map(ladderFeeRate -> {
                                    ListMchFeeRateResult.LadderFeeRate ladderFeeRateResult = new ListMchFeeRateResult.LadderFeeRate()
                                            .setMin(ladderFeeRate.getMin())
                                            .setMax(ladderFeeRate.getMax());
                                    String feeRate = ladderFeeRate.getFee_rate();
                                    if (storeFeeRateEntity.getAppInUse() == 1) {
                                        ladderFeeRateResult.setAppFeeRate(feeRate);
                                    }
                                    if (storeFeeRateEntity.getB2cInUse() == 1) {
                                        ladderFeeRateResult.setBscFeeRate(feeRate);
                                    }
                                    if (storeFeeRateEntity.getC2bInUse() == 1) {
                                        ladderFeeRateResult.setCsbFeeRate(feeRate);
                                    }
                                    if (storeFeeRateEntity.getH5InUse() == 1) {
                                        ladderFeeRateResult.setH5FeeRate(feeRate);
                                    }
                                    if (storeFeeRateEntity.getMiniInUse() == 1) {
                                        ladderFeeRateResult.setMiniFeeRate(feeRate);
                                    }
                                    if (storeFeeRateEntity.getWapInUse() == 1) {
                                        ladderFeeRateResult.setWapFeeRate(feeRate);
                                    }
                                    return ladderFeeRateResult;
                                })
                                .collect(Collectors.toList());
                        listMchFeeRateResult.setLadderFeeRates(ladderFeeRates);
                    }
                    // 资金渠道费率
                    else if (FeeRateTypeEnum.CHANNEL.name().equalsIgnoreCase(storeFeeRateEntity.getFeeRateType())) {
                        List<FeeRateEntity.ChannelFeeRate> channelFeeRates = storeFeeRateEntity.buildChannelFeeRates();
                        List<ListMchFeeRateResult.ChannelFeeRate> channelFeeRateResult = channelFeeRates
                                .stream().map(feeRate -> {
                                    // 新旧版本中失效套餐的费率参数定义不一样，需要兼容老的费率参数
                                    String useFeeRate = StringUtil.isNotEmpty(feeRate.getFeeRate()) ? feeRate.getFeeRate() : feeRate.getFee();
                                    ListMchFeeRateResult.ChannelFeeRate channelFeeRate =  ListMchFeeRateResult.ChannelFeeRate.builder()
                                            .feeRate(useFeeRate)
                                            .max(feeRate.getMax())
                                            .type(feeRate.getType())
                                            .build();
                                    // 非银行卡需要将费率摊开到每个单独的sub_payway
                                    if (!Objects.equals(storeFeeRateEntity.getPayWay(), Payway.BANKCARD.getCode())) {
                                        if (storeFeeRateEntity.getAppInUse() == 1) {
                                            channelFeeRate.setAppFeeRate(useFeeRate);
                                        }
                                        if (storeFeeRateEntity.getB2cInUse() == 1) {
                                            channelFeeRate.setBscFeeRate(useFeeRate);
                                        }
                                        if (storeFeeRateEntity.getC2bInUse() == 1) {
                                            channelFeeRate.setCsbFeeRate(useFeeRate);
                                        }
                                        if (storeFeeRateEntity.getH5InUse() == 1) {
                                            channelFeeRate.setH5FeeRate(useFeeRate);
                                        }
                                        if (storeFeeRateEntity.getMiniInUse() == 1) {
                                            channelFeeRate.setMiniFeeRate(useFeeRate);
                                        }
                                        if (storeFeeRateEntity.getWapInUse() == 1) {
                                            channelFeeRate.setWapFeeRate(useFeeRate);
                                        }
                                    }
                                    return channelFeeRate;
                                }).collect(Collectors.toList());
                        listMchFeeRateResult.setChannelFeeRates(channelFeeRateResult);
                    }
                    // 资金渠道阶梯费率
                    else if (FeeRateTypeEnum.CHANNEL_LADDER.name().equalsIgnoreCase(storeFeeRateEntity.getFeeRateType())) {
                        List<ListMchFeeRateResult.ChannelLadderFeeRate> channelLadderFeeRates = storeFeeRateEntity.buildChannelLadderFeeRates()
                                .stream()
                                .map(channelLadderFeeRate -> {
                                    ListMchFeeRateResult.ChannelLadderFeeRate channelLadderFeeRateResult = new ListMchFeeRateResult.ChannelLadderFeeRate()
                                            .setType(channelLadderFeeRate.getType());
                                    List<LadderFeeRate> ladderFeeRates = channelLadderFeeRate.getLadderFeeRates().stream().map(ladderFeeRate -> {
                                        ListMchFeeRateResult.LadderFeeRate ladderFeeRateResult = new ListMchFeeRateResult.LadderFeeRate()
                                                    .setMin(ladderFeeRate.getMin())
                                                    .setMax(ladderFeeRate.getMax());
                                            String feeRate = ladderFeeRate.getFee_rate();
                                            if (storeFeeRateEntity.getAppInUse() == 1) {
                                                ladderFeeRateResult.setAppFeeRate(feeRate);
                                            }
                                            if (storeFeeRateEntity.getB2cInUse() == 1) {
                                                ladderFeeRateResult.setBscFeeRate(feeRate);
                                            }
                                            if (storeFeeRateEntity.getC2bInUse() == 1) {
                                                ladderFeeRateResult.setCsbFeeRate(feeRate);
                                            }
                                            if (storeFeeRateEntity.getH5InUse() == 1) {
                                                ladderFeeRateResult.setH5FeeRate(feeRate);
                                            }
                                            if (storeFeeRateEntity.getMiniInUse() == 1) {
                                                ladderFeeRateResult.setMiniFeeRate(feeRate);
                                            }
                                            if (storeFeeRateEntity.getWapInUse() == 1) {
                                                ladderFeeRateResult.setWapFeeRate(feeRate);
                                            }
                                            return ladderFeeRateResult;
                                    }).collect(Collectors.toList());
                                    channelLadderFeeRateResult.setLadderFeeRate(ladderFeeRates);
                                    return channelLadderFeeRateResult;
                                })
                                .collect(Collectors.toList());
                        listMchFeeRateResult.setChannelLadderFeeRates(channelLadderFeeRates);
                    }
                    return listMchFeeRateResult;
                })
                .collect(Collectors.toList());
        return results;
    }

    /**
     * 获取生效中的商户终端费率
     * @param mchSn
     * @param terminalSn
     * @param appIdAndNameMap
     * @return
     */
    private List<ListMchFeeRateResult> getMchTerminalEffectComboFeeRates(String mchSn, String terminalSn, Map<Long, String> appIdAndNameMap){
        // 查询终端费率列表
        TerminalFeeRateQueryDalParam queryDalParam = TerminalFeeRateQueryDalParam.builder()
                .merchantSn(mchSn)
                .terminalSn(terminalSn)
                .status(FeeRateStatusConst.IN_EFFECT)
                .build();
        List<TerminalFeeRateEntity> terminalFeeRateEntities = terminalFeeRateDao.selectList(queryDalParam);
        // 获取套餐信息
        List<Long> tradeComboIds = terminalFeeRateEntities.stream().map(TerminalFeeRateEntity::getTradeComboId).distinct().collect(Collectors.toList());
        Map<Long, TradeComboEntity> tradeComboMap = getTradeComboMap(tradeComboIds);

        List<ListMchFeeRateResult> results = terminalFeeRateEntities.stream()
                .map(terminalFeeRateEntity -> {
                    Long appId = terminalFeeRateEntity.getAppId();
                    Long tradeComboId = terminalFeeRateEntity.getTradeComboId();
                    TradeComboEntity tradeComboEntity = Optional.ofNullable(tradeComboMap.get(tradeComboId))
                            .orElse(new TradeComboEntity());
                    ListMchFeeRateResult listMchFeeRateResult = new ListMchFeeRateResult()
                            .setTradeAppName(appIdAndNameMap.get(appId))
                            .setTradeComboId(tradeComboId)
                            .setTradeComboName(tradeComboEntity.getName())
                            .setTradeComboShortName(tradeComboEntity.getShortName())
                            .setDescription(tradeComboEntity.getDescription())
                            .setLevel(ComboConfigLevelEnum.TERMINAL)
                            .setSn(terminalFeeRateEntity.getTerminalSn())
                            .setPayWay(terminalFeeRateEntity.getPayWay());

                    // 固定费率
                    if (FeeRateTypeEnum.FIXED.name().equalsIgnoreCase(terminalFeeRateEntity.getFeeRateType())) {
                        String feeRetaText = new BigDecimal(terminalFeeRateEntity.getFeeRate()).toString();
                        listMchFeeRateResult.setIsLadderFeeRate(0);
                        if (terminalFeeRateEntity.getAppInUse() == 1) {
                            listMchFeeRateResult.setAppFeeRate(feeRetaText);
                        }
                        if (terminalFeeRateEntity.getB2cInUse() == 1) {
                            listMchFeeRateResult.setBscFeeRate(feeRetaText);
                        }
                        if (terminalFeeRateEntity.getC2bInUse() == 1) {
                            listMchFeeRateResult.setCsbFeeRate(feeRetaText);
                        }
                        if (terminalFeeRateEntity.getH5InUse() == 1) {
                            listMchFeeRateResult.setH5FeeRate(feeRetaText);
                        }
                        if (terminalFeeRateEntity.getMiniInUse() == 1) {
                            listMchFeeRateResult.setMiniFeeRate(feeRetaText);
                        }
                        if (terminalFeeRateEntity.getWapInUse() == 1) {
                            listMchFeeRateResult.setWapFeeRate(feeRetaText);
                        }
                    }
                    // 阶梯费率
                    else if (FeeRateTypeEnum.LADDER.name().equalsIgnoreCase(terminalFeeRateEntity.getFeeRateType())) {
                        List<ListMchFeeRateResult.LadderFeeRate> ladderFeeRates = terminalFeeRateEntity.buildLadderFeeRates()
                                .stream()
                                .map(ladderFeeRate -> {
                                    ListMchFeeRateResult.LadderFeeRate ladderFeeRateResult = new ListMchFeeRateResult.LadderFeeRate()
                                            .setMin(ladderFeeRate.getMin())
                                            .setMax(ladderFeeRate.getMax());
                                    String feeRate = ladderFeeRate.getFee_rate();
                                    if (terminalFeeRateEntity.getAppInUse() == 1) {
                                        ladderFeeRateResult.setAppFeeRate(feeRate);
                                    }
                                    if (terminalFeeRateEntity.getB2cInUse() == 1) {
                                        ladderFeeRateResult.setBscFeeRate(feeRate);
                                    }
                                    if (terminalFeeRateEntity.getC2bInUse() == 1) {
                                        ladderFeeRateResult.setCsbFeeRate(feeRate);
                                    }
                                    if (terminalFeeRateEntity.getH5InUse() == 1) {
                                        ladderFeeRateResult.setH5FeeRate(feeRate);
                                    }
                                    if (terminalFeeRateEntity.getMiniInUse() == 1) {
                                        ladderFeeRateResult.setMiniFeeRate(feeRate);
                                    }
                                    if (terminalFeeRateEntity.getWapInUse() == 1) {
                                        ladderFeeRateResult.setWapFeeRate(feeRate);
                                    }
                                    return ladderFeeRateResult;
                                })
                                .collect(Collectors.toList());
                        listMchFeeRateResult.setLadderFeeRates(ladderFeeRates);
                    }
                 // 资金渠道费率
                    else if (FeeRateTypeEnum.CHANNEL.name().equalsIgnoreCase(terminalFeeRateEntity.getFeeRateType())) {
                        List<FeeRateEntity.ChannelFeeRate> channelFeeRates = terminalFeeRateEntity.buildChannelFeeRates();
                        List<ListMchFeeRateResult.ChannelFeeRate> channelFeeRateResult = channelFeeRates
                                .stream().map(feeRate -> {
                                    // 新旧版本中失效套餐的费率参数定义不一样，需要兼容老的费率参数
                                    String useFeeRate = StringUtil.isNotEmpty(feeRate.getFeeRate()) ? feeRate.getFeeRate() : feeRate.getFee();
                                    ListMchFeeRateResult.ChannelFeeRate channelFeeRate =  ListMchFeeRateResult.ChannelFeeRate.builder()
                                            .feeRate(useFeeRate)
                                            .max(feeRate.getMax())
                                            .type(feeRate.getType())
                                            .build();
                                    // 非银行卡需要将费率摊开到每个单独的sub_payway
                                    if (!Objects.equals(terminalFeeRateEntity.getPayWay(), Payway.BANKCARD.getCode())) {
                                        if (terminalFeeRateEntity.getAppInUse() == 1) {
                                            channelFeeRate.setAppFeeRate(useFeeRate);
                                        }
                                        if (terminalFeeRateEntity.getB2cInUse() == 1) {
                                            channelFeeRate.setBscFeeRate(useFeeRate);
                                        }
                                        if (terminalFeeRateEntity.getC2bInUse() == 1) {
                                            channelFeeRate.setCsbFeeRate(useFeeRate);
                                        }
                                        if (terminalFeeRateEntity.getH5InUse() == 1) {
                                            channelFeeRate.setH5FeeRate(useFeeRate);
                                        }
                                        if (terminalFeeRateEntity.getMiniInUse() == 1) {
                                            channelFeeRate.setMiniFeeRate(useFeeRate);
                                        }
                                        if (terminalFeeRateEntity.getWapInUse() == 1) {
                                            channelFeeRate.setWapFeeRate(useFeeRate);
                                        }
                                    }
                                    return channelFeeRate;
                                }).collect(Collectors.toList());
                        listMchFeeRateResult.setChannelFeeRates(channelFeeRateResult);
                    }
                    // 资金渠道阶梯费率
                    else if (FeeRateTypeEnum.CHANNEL_LADDER.name().equalsIgnoreCase(terminalFeeRateEntity.getFeeRateType())) {
                        List<ListMchFeeRateResult.ChannelLadderFeeRate> channelLadderFeeRates = terminalFeeRateEntity.buildChannelLadderFeeRates()
                                .stream()
                                .map(channelLadderFeeRate -> {
                                    ListMchFeeRateResult.ChannelLadderFeeRate channelLadderFeeRateResult = new ListMchFeeRateResult.ChannelLadderFeeRate()
                                            .setType(channelLadderFeeRate.getType());
                                    List<LadderFeeRate> ladderFeeRates = channelLadderFeeRate.getLadderFeeRates().stream().map(ladderFeeRate -> {
                                        ListMchFeeRateResult.LadderFeeRate ladderFeeRateResult = new ListMchFeeRateResult.LadderFeeRate()
                                                    .setMin(ladderFeeRate.getMin())
                                                    .setMax(ladderFeeRate.getMax());
                                            String feeRate = ladderFeeRate.getFee_rate();
                                            if (terminalFeeRateEntity.getAppInUse() == 1) {
                                                ladderFeeRateResult.setAppFeeRate(feeRate);
                                            }
                                            if (terminalFeeRateEntity.getB2cInUse() == 1) {
                                                ladderFeeRateResult.setBscFeeRate(feeRate);
                                            }
                                            if (terminalFeeRateEntity.getC2bInUse() == 1) {
                                                ladderFeeRateResult.setCsbFeeRate(feeRate);
                                            }
                                            if (terminalFeeRateEntity.getH5InUse() == 1) {
                                                ladderFeeRateResult.setH5FeeRate(feeRate);
                                            }
                                            if (terminalFeeRateEntity.getMiniInUse() == 1) {
                                                ladderFeeRateResult.setMiniFeeRate(feeRate);
                                            }
                                            if (terminalFeeRateEntity.getWapInUse() == 1) {
                                                ladderFeeRateResult.setWapFeeRate(feeRate);
                                            }
                                            return ladderFeeRateResult;
                                    }).collect(Collectors.toList());
                                    channelLadderFeeRateResult.setLadderFeeRate(ladderFeeRates);
                                    return channelLadderFeeRateResult;
                                })
                                .collect(Collectors.toList());
                        listMchFeeRateResult.setChannelLadderFeeRates(channelLadderFeeRates);
                    }
                    return listMchFeeRateResult;
                })
                .collect(Collectors.toList());
        return results;
    }

    /**
     * 获取 app 的 id 和 name 映射，key 为 id，value 为 name
     */
    private Map<Long, String> getAppIdAndNameMap(List<Long> appIds) {
        if (CollectionUtils.isEmpty(appIds)) {
            return Maps.newTreeMap();
        }

        return tradeAppDao.selectByIds(appIds).stream()
                .collect(Collectors.toMap(TradeAppEntity::getId, TradeAppEntity::getName));
    }

    /**
     * 获取 TradeComboEntity 的 id 和 TradeComboEntity 映射，key 为 id，value 为 TradeComboEntity
     */
    private Map<Long, TradeComboEntity> getTradeComboMap(List<Long> tradeComboIds) {
        if (CollectionUtils.isEmpty(tradeComboIds)) {
            return Maps.newTreeMap();
        }

        return tradeComboDao.selectByIds(tradeComboIds).stream()
                .collect(Collectors.toMap(TradeComboEntity::getId, tradeComboEntity -> tradeComboEntity));
    }

    /**
     * 获取基础费率列表
     */
    private List<ListMchFeeRateResult> getBasicFeeRates(String merchantId) {
        List<Map> merchantConfigs = tradeConfigService.getAnalyzedMerchantConfigs(merchantId);
        if (CollectionUtils.isEmpty(merchantConfigs)) {
            return new ArrayList<>();
        }

        return merchantConfigs.stream()
                .map(merchantConfig -> new ListMchFeeRateResult()
                        .setTradeAppName("支付业务")
                        .setTradeComboName("基础费率")
                        .setTradeComboShortName("基础费率")
                        .setDescription("基础费率")
                        .setIsLadderFeeRate(0)
                        .setPayWay(MapUtils.getInteger(merchantConfig, MerchantConfig.PAYWAY))
                        .setBscFeeRate(MapUtils.getString(merchantConfig, MerchantConfig.B2C_FEE_RATE))
                        .setCsbFeeRate(MapUtils.getString(merchantConfig, MerchantConfig.C2B_FEE_RATE))
                        .setAppFeeRate(MapUtils.getString(merchantConfig, MerchantConfig.APP_FEE_RATE))
                        .setMiniFeeRate(MapUtils.getString(merchantConfig, MerchantConfig.MINI_FEE_RATE))
                        .setH5FeeRate(MapUtils.getString(merchantConfig, MerchantConfig.H5_FEE_RATE))
                        .setWapFeeRate(MapUtils.getString(merchantConfig, MerchantConfig.WAP_FEE_RATE)))
                .collect(Collectors.toList());
    }

    /**
     * 获取商户配置应用方配置列表
     */
    private List<ListMchFeeRateResult> getScanCodeOrderFeeRates(String merchantId, Long appId, String appName) {
        List<Map<String, Object>> merchantAppConfigs = tradeConfigService.getMerchantAppConfigByMerchantIdAndApp(merchantId, String.valueOf(appId));
        if (CollectionUtils.isEmpty(merchantAppConfigs)) {
            return new ArrayList<>();
        }

        return merchantAppConfigs.stream()
                .map(merchantAppConfig -> new ListMchFeeRateResult()
                        .setTradeAppName(appName)
                        .setTradeComboName(appName)
                        .setTradeComboShortName(appName)
                        .setIsLadderFeeRate(0)
                        .setPayWay(MapUtils.getInteger(merchantAppConfig, MerchantAppConfig.PAYWAY))
                        .setBscFeeRate(MapUtils.getString(merchantAppConfig, MerchantAppConfig.B2C_FEE_RATE))
                        .setCsbFeeRate(MapUtils.getString(merchantAppConfig, MerchantAppConfig.C2B_FEE_RATE))
                        .setAppFeeRate(MapUtils.getString(merchantAppConfig, MerchantAppConfig.APP_FEE_RATE))
                        .setMiniFeeRate(MapUtils.getString(merchantAppConfig, MerchantAppConfig.MINI_FEE_RATE))
                        .setH5FeeRate(MapUtils.getString(merchantAppConfig, MerchantAppConfig.H5_FEE_RATE))
                        .setWapFeeRate(MapUtils.getString(merchantAppConfig, MerchantAppConfig.WAP_FEE_RATE)))
                .collect(Collectors.toList());
    }

    @Override
    public List<QueryMchApplyLogsResult> queryMchApplyLogs(String mchSn) {

        MchFeeRateApplyLogQueryDalParam queryDalParam = MchFeeRateApplyLogQueryDalParam.builder()
                .merchantSn(mchSn)
                .build();
        return merchantFeeRateApplyLogDao.selectList(queryDalParam).stream()
                .map(logEntity -> {
                    QueryMchApplyLogsResult queryMchApplyLogsResult = new QueryMchApplyLogsResult()
                            .setTradeAppName(logEntity.getTradeAppName())
                            .setTradeComboId(logEntity.getTradeComboId())
                            .setTradeComboShortName(logEntity.getTradeComboShortName())
                            .setPayWay(logEntity.getPayWay())
                            .setDescription(logEntity.getDescription())
                            .setAuditSn(logEntity.getAuditSn())
                            .setEffectiveDate(LocalDate.fromDateFields(logEntity.getBeginDate()).toString())
                            .setExpirationDate(LocalDate.fromDateFields(logEntity.getEndDate()).toString());
                    // 一般费率
                    if (FeeRateTypeEnum.FIXED.name().equalsIgnoreCase(logEntity.getFeeRateType())) {
                        queryMchApplyLogsResult
                                .setIsLadderFeeRate(0)
                                .setFeeRate(logEntity.getFeeRate());
                    }
                    // 阶梯费率
                    else if (FeeRateTypeEnum.LADDER.name().equalsIgnoreCase(logEntity.getFeeRateType())) {
                        List<QueryMchApplyLogsResult.LadderFeeRate> ladderFeeRates = logEntity.buildLadderFeeRates()
                                .stream()
                                .map(ladderFeeRate -> {
                                    return new QueryMchApplyLogsResult.LadderFeeRate()
                                            .setMin(ladderFeeRate.getMin())
                                            .setMax(ladderFeeRate.getMax())
                                            .setFeeRate(ladderFeeRate.getFee_rate());
                                })
                                .collect(Collectors.toList());

                        queryMchApplyLogsResult
                                .setIsLadderFeeRate(1)
                                .setLadderFeeRates(ladderFeeRates);
                    }
                    // 资金渠道费率
                    else if (FeeRateTypeEnum.CHANNEL.name().equalsIgnoreCase(logEntity.getFeeRateType())) {
                        List<QueryMchApplyLogsResult.ChannelFeeRate> channelFeeRates = logEntity.buildChannelFeeRates()
                                .stream()
                                .map(channelFeeRate -> {
                                    return new QueryMchApplyLogsResult.ChannelFeeRate()
                                            .setType(channelFeeRate.getType())
                                            .setFeeRate(channelFeeRate.getFeeRate());
                                })
                                .collect(Collectors.toList());

                        queryMchApplyLogsResult
                                .setChannelFeeRates(channelFeeRates);
                    }
                    // 资金渠道阶梯费率
                    else if (FeeRateTypeEnum.CHANNEL_LADDER.name().equalsIgnoreCase(logEntity.getFeeRateType())) {
                        List<QueryMchApplyLogsResult.ChannelLadderFeeRate> channelLadderFeeRates = logEntity.buildChannelLadderFeeRates()
                                .stream()
                                .map(channelLadderFeeRate -> {
                                    List<QueryMchApplyLogsResult.LadderFeeRate> ladderFeeRates = channelLadderFeeRate.getLadderFeeRates()
                                            .stream()
                                            .map(ladderFeeRate -> {
                                                return new QueryMchApplyLogsResult.LadderFeeRate()
                                                        .setMin(ladderFeeRate.getMin())
                                                        .setMax(ladderFeeRate.getMax())
                                                        .setFeeRate(ladderFeeRate.getFee_rate());
                                            })
                                            .collect(Collectors.toList());
                                    return new QueryMchApplyLogsResult.ChannelLadderFeeRate()
                                            .setType(channelLadderFeeRate.getType())
                                            .setLadderFeeRates(ladderFeeRates);
                                })
                                .collect(Collectors.toList());

                        queryMchApplyLogsResult
                                .setChannelLadderFeeRates(channelLadderFeeRates);
                    }
                    return queryMchApplyLogsResult;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<ListMchFeeStatusResult> getMerchantBasicAgreementFeeRates(ActivityBaseRequest request) {
        return merchantBasicAgreementFeeRateBiz.execute(request);
    }

    @Override
    public boolean deleteMchFeeRateById(Long mchFeeRateId) {
        MerchantFeeRateUpsertDalParam upsertDalParam = MerchantFeeRateUpsertDalParam.builder()
                .id(mchFeeRateId)
                .b2cInUse(0)
                .c2bInUse(0)
                .miniInUse(0)
                .wapInUse(0)
                .appInUse(0)
                .h5InUse(0)
                .status(FeeRateStatusConst.DELETED)
                .build();
        // 逻辑删除
        return merchantFeeRateDao.updateById(upsertDalParam) > 1;
    }

    @Override
    public void applyTradeCombo078FeeRate(String mchSn) {
        applyFeeRate(mchSn, 76l);
    }

    @Override
    public void applyTradeCombo078FeeRateAndLog(String mchSn, OpLogCreateRequest opLogCreateRequest) {
        applyTradeCombo078FeeRate(mchSn);
        Map<String, Object> before = new HashMap<>();
        Map<String, Object> after = new HashMap<>();
        before.put(OpLogConstant.WXPAY_EDU_ACTIVITY_STATUS, "开启");
        after.put(OpLogConstant.WXPAY_EDU_ACTIVITY_STATUS, "关闭");
        String merchantIdBySn = businssCommonService.getMerchantIdBySn(mchSn);
        before.put(OpLogConstant.MERCHANT_ID, merchantIdBySn);
        after.put(OpLogConstant.MERCHANT_ID, "--");
        businessOpLogBiz.sendWxPayActivityConfigLog(opLogCreateRequest.getOuterSceneTraceId(), merchantIdBySn, opLogCreateRequest.getPlatformCode(), opLogCreateRequest.getOpUserId(), opLogCreateRequest.getOpUserName(), StringUtils.isBlank(opLogCreateRequest.getRemark()) ? "申请微信高校教培活动" : opLogCreateRequest.getRemark(), before, after);
    }

    @Override
    public void cancelTradeCombo078FeeRate(String mchSn) {
        List<MerchantFeeRateEntity> entities = tradeCommonService.getEffectMerchantFeeRate(76l, mchSn);
        if(entities.isEmpty()){
            return;
        }
        if(tradeCommonService.isBelongToEducationTrainingIndustryByMerchantIndustryId(mchSn)){
            throw TradeManageBizException.createExc("此商户行业为教培，暂时不能取消当前套餐");
        }
        for (MerchantFeeRateEntity entity : entities) {
            endMerchantFeeRate(entity.getId());
        }
    }

    @Override
    public void cancelTradeCombo078FeeRateAndLog(String mchSn, OpLogCreateRequest opLogCreateRequest) {
        cancelTradeCombo078FeeRate(mchSn);
        Map<String, Object> before = new HashMap<>();
        Map<String, Object> after = new HashMap<>();
        before.put(OpLogConstant.WXPAY_EDU_ACTIVITY_STATUS, "关闭");
        after.put(OpLogConstant.WXPAY_EDU_ACTIVITY_STATUS, "开启");
        String merchantIdBySn = businssCommonService.getMerchantIdBySn(mchSn);
        before.put(OpLogConstant.MERCHANT_ID, merchantIdBySn);
        after.put(OpLogConstant.MERCHANT_ID, "");
        businessOpLogBiz.sendWxPayActivityConfigLog(opLogCreateRequest.getOuterSceneTraceId(), merchantIdBySn, opLogCreateRequest.getPlatformCode(), opLogCreateRequest.getOpUserId(), opLogCreateRequest.getOpUserName(), StringUtils.isBlank(opLogCreateRequest.getRemark()) ? "取消微信高校教培活动" : opLogCreateRequest.getRemark(), before, after);
    }

    @Override
    public void updateAlipayFormalFeeRate(String mchSn, String feeRate, String operator, String operatorName) {
        if(!FeeRateUtils.isValidFeeRate(feeRate)){
            throw TradeManageBizException.createExc("费率不合法");
        }
        String merchantId = tradeCommonService.getMerchantIdByMerchantSn(mchSn);
        Map merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, TradeConfigService.PAYWAY_ALIPAY2);
        if(merchantConfig == null){
            throw TradeManageBizException.createExc("配置不存在");
        }
        Boolean b2cFormal = MapUtil.getBoolean(merchantConfig, MerchantConfig.B2C_FORMAL, false);
        Boolean c2bFormal = MapUtil.getBoolean(merchantConfig, MerchantConfig.C2B_FORMAL, false);
        Boolean wapFormal = MapUtil.getBoolean(merchantConfig, MerchantConfig.WAP_FORMAL, false);
        if(!(b2cFormal && c2bFormal && wapFormal)){
            throw TradeManageBizException.createExc("商户支付宝不为直连，不允许修改费率");
        }
        //按照套餐模式来修改费率
        ApplyFeeRateRequest applyFeeRateRequest = new ApplyFeeRateRequest()
                .setMerchantSn(mchSn)
                .setPlatform("APP")
                .setOperator(operator)
                .setOperatorName(operatorName)
                .setTradeComboId(commonApolloConfig.getAlipayFormalComboId())
                .setApplyPartialPayway(true)
                .setApplyFeeRateMap(MapUtil.hashMap(
                        Payway.ALIPAY2.getCode() + "", feeRate
                ))
                .setAuditSn("商户修改支付宝直连费率");
        applyFeeRateOne(applyFeeRateRequest);
    }

    @Override
    public void syncMerchantFeeRateTag(String merchantSn, Integer payWay, boolean process) {
        List<MerchantFeeRateEntity> merchantFeeRates = tradeCommonService.getEffectMerchantFeeRate(merchantSn, payWay);
        if(merchantFeeRates.isEmpty()){
            log.debug("{} {} dont have effect combo", merchantSn, payWay);
            return;
        }
        String merchantId = tradeCommonService.getMerchantIdByMerchantSn(merchantSn);
        for (MerchantFeeRateEntity entity : merchantFeeRates) {
            Long comboId = entity.getTradeComboId();
            if(!(entity.getB2cInUse() == FeeRateStatusConst.IN_EFFECT && entity.getC2bInUse() == FeeRateStatusConst.IN_EFFECT && entity.getWapInUse() == FeeRateStatusConst.IN_EFFECT
                    && entity.getMiniInUse() == FeeRateStatusConst.IN_EFFECT)){
                log.debug("{} b2c - mini 不是所有的都在生效 {}", merchantSn, JsonUtil.encode(entity));
                continue;
            }
            TradeComboDetailEntity detail = tradeComboDetailDao.selectList(TradeComboDetailQueryDalParam.builder().comboId(comboId).payWay(payWay).build()).get(0);
            List<TradeComboDetailEntity.LadderFeeRate> detailLadderFeeRates = detail.buildLadderFeeRates();
            // 非阶梯费率
            Map merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, payWay);
            if(merchantConfig == null){
                log.debug("{} payway {} merchant config is null", merchantSn, payWay);
                continue;
            }
            Long version = MapUtil.getLong(merchantConfig, DaoConstants.VERSION);
            if(detailLadderFeeRates == null || detailLadderFeeRates.isEmpty()){
                // 非阶梯费率处理
                Map<String,Object> feeRateTag = (Map<String, Object>) BeanUtil.getNestedProperty(merchantConfig, MerchantConfig.PARAMS + "." + TransactionParam.FEE_RATE_TAG);
                boolean hasCombo = false;
                if(feeRateTag != null){
                    String b2cTag = BeanUtil.getPropString(feeRateTag, SubPayway.BARCODE.getCode().toString(), "");
                    String wapTag = BeanUtil.getPropString(feeRateTag, SubPayway.WAP.getCode().toString(), "");
                    if(b2cTag.contains(comboId + ":") && wapTag.contains(comboId + ":")){
                        hasCombo = true;
                    }
                }
                if(hasCombo){
                    log.debug("{} payway {} has combo {}", merchantSn, payWay, feeRateTag);
                    continue;
                }
                String b2cFeeRate = BeanUtil.getPropString(merchantConfig, MerchantConfig.B2C_FEE_RATE, "0.38");
                String c2bFeeRate = BeanUtil.getPropString(merchantConfig, MerchantConfig.C2B_FEE_RATE, "0.38");
                String wapFeeRate = BeanUtil.getPropString(merchantConfig, MerchantConfig.WAP_FEE_RATE, "0.38");
                String miniFeeRate = BeanUtil.getPropString(merchantConfig, MerchantConfig.MINI_FEE_RATE, "0.38");
                FixedFeeRate fixedFeeRate = detail.buildFixedFeeRate();
                String min = (fixedFeeRate != null) ? fixedFeeRate.getMin() : null;
                String max = (fixedFeeRate != null) ? fixedFeeRate.getMax() : null;
                if(!(FeeRateUtils.isBetween(b2cFeeRate, min, max) && FeeRateUtils.isBetween(c2bFeeRate, min, max)
                        &&FeeRateUtils.isBetween(wapFeeRate, min, max) && FeeRateUtils.isBetween(miniFeeRate, min, max))){
                    log.debug("{} payway {} merchant config fee rate is not valid min {}- max {} {} {} {} {}", merchantSn, payWay, min, max, b2cFeeRate, c2bFeeRate, wapFeeRate, miniFeeRate);
                    continue;
                }
                Map<String, Object> config = buildUpdateConfigWhenFix(detail, "fake");
                Map<String, Object> feeRateTagMap = MapUtil.getMap(config, TransactionParam.FEE_RATE_TAG);
                log.debug("{} payway {} merchant config fee rate tag, {}->{}, process {}", merchantSn, payWay, feeRateTag, feeRateTagMap, process);
                if(process){
                    tradeConfigService.updateMerchantConfigFeeRateTag(merchantId, payWay, version, feeRateTagMap, null);
                }
            }else {
                // 阶梯费率处理
                Object ladderStatus = BeanUtil.getNestedProperty(merchantConfig, MerchantConfig.PARAMS + "." + MerchantConfig.LADDER_STATUS);
                List<Map<String,Object>> ladderFeeRates = (List<Map<String, Object>>) BeanUtil.getNestedProperty(merchantConfig, MerchantConfig.PARAMS + "." + MerchantConfig.LADDER_FEE_RATES);
                if(!Objects.equals(ladderStatus, MerchantConfig.STATUS_OPENED)){
                    log.debug("{} payway {} merchant config is not ladder fee rate", merchantSn, payWay);
                    continue;
                }
                if(ladderFeeRates == null || ladderFeeRates.isEmpty()){
                    log.debug("{} payway {} merchant config ladder fee rate is empty", merchantSn, payWay);
                    continue;
                }
                Map<String,Object> feeRateTag = (Map<String, Object>) BeanUtil.getNestedProperty(merchantConfig, MerchantConfig.PARAMS + "." + TransactionParam.LADDER_FEE_RATE_TAG);
                boolean hasCombo = false;
                if(feeRateTag != null){
                    String b2cTag = BeanUtil.getPropString(feeRateTag, SubPayway.BARCODE.getCode().toString(), "");
                    String wapTag = BeanUtil.getPropString(feeRateTag, SubPayway.WAP.getCode().toString(), "");
                    if(b2cTag.contains(comboId + ":") && wapTag.contains(comboId + ":")){
                        hasCombo = true;
                    }
                }
                if(hasCombo){
                    log.debug("{} payway {} ladder has combo {}", merchantSn, payWay, feeRateTag);
                    continue;
                }
                Map<String, Object> config = buildUpdateConfigWhenLadder(detail, entity.getFeeRate());
                Map<String, Object> ladderFeeRateTagMap = MapUtil.getMap(config, "ladder_fee_rate_tag");
                List<Map<String, Object>> newLadderFeeRates  = (List<Map<String, Object>>) MapUtil.getObject(config, "ladder_fee_rates");
                newLadderFeeRates.forEach(l -> {
                    l.put("min", FeeRateUtils.yuan2cents(BeanUtil.getPropString(l, "min", "0")));
                    l.put("max", FeeRateUtils.yuan2cents(BeanUtil.getPropString(l, "max", "0")));
                });
                ladderFeeRates.forEach(l -> {
                    l.put("min", FeeRateUtils.yuan2cents(BeanUtil.getPropString(l, "min", "0")));
                    l.put("max", FeeRateUtils.yuan2cents(BeanUtil.getPropString(l, "max", "0")));
                });
                if(!(newLadderFeeRates.containsAll(ladderFeeRates) && ladderFeeRates.containsAll(newLadderFeeRates))){
                    log.debug("{} payway {} ladder fee rate is not same {} {}", merchantSn, payWay, ladderFeeRates, newLadderFeeRates);
                    continue;
                }
                log.debug("{} payway {} merchant config ladder fee rate tag, {}->{}, process {}", merchantSn, payWay, feeRateTag, ladderFeeRateTagMap, process);
                if(process){
                    tradeConfigService.updateMerchantConfigFeeRateTag(merchantId, payWay, version, null, ladderFeeRateTagMap);
                }
            }
        }

    }

    @Override
    public FeeRateResponse getMerchantFeeRateById(Long id) {
        MerchantFeeRateEntity feeRate = merchantFeeRateDao.selectById(id);
        if (Objects.isNull(feeRate)) {
            return null;
        }
        FeeRateResponse response = new FeeRateResponse();
        response.setId(feeRate.getId());
        response.setAppId(feeRate.getAppId());
        response.setMerchantSn(feeRate.getMerchantSn());
        response.setTradeComboId(feeRate.getTradeComboId());
        response.setPayWay(feeRate.getPayWay());
        response.setBeginDate(feeRate.getBeginDate());
        response.setEndDate(feeRate.getEndDate());
        response.setB2cInUse(feeRate.getB2cInUse());
        response.setC2bInUse(feeRate.getC2bInUse());
        response.setAppInUse(feeRate.getAppInUse());
        response.setMiniInUse(feeRate.getMiniInUse());
        response.setH5InUse(feeRate.getH5InUse());
        response.setWapInUse(feeRate.getWapInUse());
        response.setFeeRateType(feeRate.getFeeRateType());
        response.setStatus(feeRate.getStatus());
        response.setAuditSn(feeRate.getAuditSn());
        List<FeeRateEntity.LadderFeeRate> ladderFeeRates = feeRate.buildLadderFeeRates();
        List<FeeRateEntity.ChannelFeeRate> channelFeeRates = feeRate.buildChannelFeeRates();
        List<FeeRateEntity.ChannelLadderFeeRate> channelLadderFeeRates = feeRate.buildChannelLadderFeeRates();
        if (CollectionUtils.isNotEmpty(ladderFeeRates)) {
            response.setLadderFeeRates(ladderFeeRates.stream().map(ladderFeeRate -> {
                FeeRateResponse.LadderFeeRate dto = new FeeRateResponse.LadderFeeRate();
                dto.setMin(ladderFeeRate.getMin());
                dto.setMax(ladderFeeRate.getMax());
                dto.setFeeRate(ladderFeeRate.getFee_rate());
                return dto;
            }).collect(Collectors.toList()));
        } else if (CollectionUtils.isNotEmpty(channelFeeRates)) {
            response.setChannelFeeRates(channelFeeRates.stream()
                    .map(channelFeeRate -> {
                        FeeRateResponse.ChannelFeeRate dto = new FeeRateResponse.ChannelFeeRate();
                        dto.setType(channelFeeRate.getType());
                        dto.setFee(channelFeeRate.getFee());
                        dto.setFeeRate(channelFeeRate.getFeeRate());
                        dto.setMax(channelFeeRate.getMax());
                        return dto;
                    }).collect(Collectors.toList()));
        } else if (CollectionUtils.isNotEmpty(channelLadderFeeRates)) {
            response.setChannelLadderFeeRates(channelLadderFeeRates.stream()
                    .map(channelLadderFeeRate -> {
                        FeeRateResponse.ChannelLadderFeeRate dto = new FeeRateResponse.ChannelLadderFeeRate();
                        dto.setType(channelLadderFeeRate.getType());
                        // 转换阶梯费率
                        List<FeeRateResponse.LadderFeeRate> ladderDtos = channelLadderFeeRate.getLadderFeeRates().stream()
                                .map(ladderFeeRate -> {
                                    FeeRateResponse.LadderFeeRate ladderDto = new FeeRateResponse.LadderFeeRate();
                                    ladderDto.setMin(ladderFeeRate.getMin());
                                    ladderDto.setMax(ladderFeeRate.getMax());
                                    ladderDto.setFeeRate(ladderFeeRate.getFee_rate());
                                    return ladderDto;
                                }).collect(Collectors.toList());
                        dto.setLadderFeeRates(ladderDtos);
                        return dto;
                    }).collect(Collectors.toList()));
        } else {
            response.setFixedFeeRate(feeRate.getFeeRate());
        }
        return response;
    }

    private void updateFeeRateFromSp(String merchantId, String merchantSn, Integer payWay, Map<String, Object> request, String operator, String operatorName, String remark, Map<String, Object> beforeMerchantConfig){
        //更新xxx_status
        Map<String,Object> statusUpdates = new HashMap<>();
        List<String> statusKeys = Arrays.asList(MerchantConfig.B2C_STATUS, MerchantConfig.C2B_STATUS, MerchantConfig.WAP_STATUS, MerchantConfig.MINI_STATUS);
        for (String key : statusKeys) {
            if(request.containsKey(key) && !Objects.equals(request.get(key), beforeMerchantConfig.get(key))){
                statusUpdates.put(key, request.get(key));
            }
        }
        if(!statusUpdates.isEmpty()){
            statusUpdates.put(DaoConstants.ID, beforeMerchantConfig.get(DaoConstants.ID));
            tradeConfigService.updateMerchantConfig(statusUpdates);
        }
        //更新费率（不更新阶梯费率）
        List<String> feeRateKeys = Arrays.asList(MerchantConfig.B2C_FEE_RATE, MerchantConfig.C2B_FEE_RATE, MerchantConfig.WAP_FEE_RATE, MerchantConfig.MINI_FEE_RATE);
        boolean feeRateChange = feeRateKeys.stream().filter(key -> !Objects.equals(request.get(key), beforeMerchantConfig.get(key))).findAny().isPresent();
        if(!feeRateChange){
            //没有固定费率变更，不做处理
            return;
        }
        Map params = MapUtil.getMap(beforeMerchantConfig, MerchantConfig.PARAMS);
        Integer ladderStatus = MapUtil.getInteger(params, MerchantConfig.LADDER_STATUS);
        Map<String,String> feeRateTags = MapUtil.getMap(params, Objects.equals(ladderStatus, MerchantConfig.STATUS_OPENED) ? MerchantConfig.LADDER_FEE_RATE_TAG : TransactionParam.FEE_RATE_TAG);
        List<Integer> subPayWays = Arrays.asList(SubPayway.BARCODE.getCode(), SubPayway.QRCODE.getCode(), SubPayway.WAP.getCode(), SubPayway.MINI.getCode());
        boolean hasCombo = subPayWays.stream().filter(subPayWay -> {
            if(feeRateTags == null){
                return false;
            }
            String tag = feeRateTags.get(subPayWay + "");
            Long comboId = FeeRateUtils.getComboIdFromFeeRateTag(tag);
            return comboId != null;
        }).findAny().isPresent();
        //当前商户没有生效套餐，则生效一个套餐
        if(!hasCombo){
            //提前校验费率是否满足区间要求
            //大客户和其他组织使用不同的套餐
            String path = tradeCommonService.getMerchantOrganizationPathFromCrm(merchantId);
            boolean isKa = path != null && path.startsWith("00069");
            Long comboId = isKa ? commonApolloConfig.getKaComboIdForFeeRateUpdate() : commonApolloConfig.getCommonComboIdForFeeRateUpdate();
            //校验是否在区间内
            TradeComboDetailResult detail = tradeComboDetailService.listByComboId(comboId).stream().filter(r -> r.getPayway() == payWay).findAny().orElseGet(null);
            for (String key : feeRateKeys) {
                String feeRate = MapUtil.getString(request, key);
                if(feeRate == null){
                    TradeManageBizException.createExc(String.format("%s费率不能为空", key));
                }
                if(!FeeRateUtils.isValidFeeRate(feeRate)){
                    TradeManageBizException.createExc(String.format("%s费率不合法", feeRate));
                }
                double rate = Double.parseDouble(feeRate);
                if(rate < Double.parseDouble(detail.getFeeRateMin()) || rate > Double.parseDouble(detail.getFeeRateMax())){
                    TradeManageBizException.createExc(String.format("费率超出可以允许修改的范围 %s-%s，不允许修改", detail.getFeeRateMin(), detail.getFeeRateMax()));
                }
            }
            //生效套餐
            ApplyFeeRateRequest applyFeeRateRequest = new ApplyFeeRateRequest()
                    .setMerchantSn(merchantSn)
                    .setTradeComboId(comboId)
                    .setAuditSn(remark)
                    .setOperator(operator)
                    .setOperatorName(operatorName)
                    .setApplyPartialPayway(true)
                    .setApplyFeeRateMap(MapUtil.hashMap(
                            payWay + "", "0.6"
                    ))
                    .setCheck(true);
            applyFeeRateOne(applyFeeRateRequest);
        }
        //根据套餐来修改费率
        FeeRateUpdateRequest updateRequest = new FeeRateUpdateRequest();
        updateRequest.setMerchantId(merchantId);
        updateRequest.setPayWay(payWay);
        updateRequest.setBscFeeRate(MapUtil.getString(request, MerchantConfig.B2C_FEE_RATE));
        updateRequest.setCsbFeeRate(MapUtil.getString(request, MerchantConfig.C2B_FEE_RATE));
        updateRequest.setWapFeeRate(MapUtil.getString(request, MerchantConfig.WAP_FEE_RATE));
        updateRequest.setMiniFeeRate(MapUtil.getString(request, MerchantConfig.MINI_FEE_RATE));
        updateFeeRateWhenHasCombo(updateRequest);

    }



    /**
     * 打生效标签
     */
    private void tagEffect(String merchantId, TradeComboEntity tradeComboEntity) {
        TradeComboEntity.MarkLabelExtra markLabelExtra = tradeComboEntity.buildMarkLabelExtra();
        String tagId = markLabelExtra.getLabel_id();
        String tagValue = markLabelExtra.getEffective_label_value();
        tradeCommonService.tag(merchantId, tagId, tagValue);
    }

    /**
     * 打取消标签
     */
    private void tagCancel(String merchantId, TradeComboEntity tradeComboEntity) {
        TradeComboEntity.MarkLabelExtra markLabelExtra = tradeComboEntity.buildMarkLabelExtra();
        String tagId = markLabelExtra.getLabel_id();
        String tagValue = markLabelExtra.getCancel_label_value();
        tradeCommonService.tag(merchantId, tagId, tagValue);
    }

    /**
     * 打结束标签
     */
    private void tagEnd(String merchantId, TradeComboEntity tradeComboEntity) {
        TradeComboEntity.MarkLabelExtra markLabelExtra = tradeComboEntity.buildMarkLabelExtra();
        String tagId = markLabelExtra.getLabel_id();
        String tagValue = markLabelExtra.getEnd_label_value();
        tradeCommonService.tag(merchantId, tagId, tagValue);
    }


    /**
     * 设置花呗分期初始金额
     * @param merchantId
     * @param amount
     */
    private void updAliPayHuaBeiLimit(String merchantId, Long amount){
        if (StringUtil.isNotBlank(merchantId)){
            try{
                Map<String , Object> updInfo = new HashMap<String, Object>(){{
                    put("id",merchantId);
                    put("alipay_huabei_limit",amount);
                    put("operator","System");
                    put("remark","银行活动商户");
                }};
                creditPayUserInfoService.updateAliPayHuaBeiLimit(updInfo);
            }catch (Throwable e){
                log.error("商户id => {}，设置花呗分期起始金额异常", merchantId , e);
            }
        }
    }

    private void setHuabeiInitAmountByCombo(String merchantIid,Long tradeComboId, int FeeRateStatus){
        if(commonApolloConfig.isBankSpecialComboIds(tradeComboId)){
            Long amt = (FeeRateStatus == FeeRateStatusConst.IN_EFFECT) ? 10000L:30000L;
            updAliPayHuaBeiLimit(merchantIid,amt);
        }

    }

    private int getMerchantComboEffectCount(String merchantSn, Long comboId){
        MerchantFeeRateCountDalParam countDalParam = MerchantFeeRateCountDalParam.builder()
                .merchantSn(merchantSn)
                .tradeComboId(comboId)
                .status(FeeRateStatusConst.IN_EFFECT)
                .build();
        return merchantFeeRateDao.count(countDalParam);
    }

    private int getTerminalComboEffectCount(String merchantSn, String  terminalSn, Long comboId){
        TerminalFeeRateCountDalParam countDalParam = TerminalFeeRateCountDalParam.builder()
                .merchantSn(merchantSn)
                .terminalSn(terminalSn)
                .tradeComboId(comboId)
                .status(FeeRateStatusConst.IN_EFFECT)
                .build();
        return terminalFeeRateDao.count(countDalParam);
    }

    private int getStoreComboEffectCount(String merchantSn, String  storeSn, Long comboId){
        StoreFeeRateCountDalParam countDalParam = StoreFeeRateCountDalParam.builder()
                .merchantSn(merchantSn)
                .storeSn(storeSn)
                .tradeComboId(comboId)
                .status(FeeRateStatusConst.IN_EFFECT)
                .build();
        return storeFeeRateDao.count(countDalParam);
    }

    /**
     * 记录应用门店参数变更日志时候， 需要把阶梯相关字段放到params外面，日志才会正常的记录
     * @param storeAppConfig
     * @return
     */
    private Map cloneProcessLadderForLog(Map<String,Object> storeAppConfig){
        if(storeAppConfig == null){
            return Collections.emptyMap();
        }
        Map<String,Object> copy = CommonUtil.cloneMap(storeAppConfig);
        Map params = MapUtil.getMap(storeAppConfig, StoreAppConfig.PARAMS);
        if(!copy.containsKey(MerchantConfig.LADDER_STATUS)){
            copy.put(MerchantConfig.LADDER_STATUS, MapUtil.getObject(params, MerchantConfig.LADDER_STATUS));
        }
        if(!copy.containsKey(MerchantConfig.LADDER_FEE_RATES)){
            copy.put(MerchantConfig.LADDER_FEE_RATES, MapUtil.getObject(params, MerchantConfig.LADDER_FEE_RATES));
        }
        return copy;
    }

    private ActivityFeeRateParam getActivityByComboId(Long comboId, String merchantSn, String storeSn, String terminalSn){
        return feeRateBuildParams.buildActivityFeeRateParam(ActivityFeeRateParam.ActivityFeeRateRequest.builder()
                        .comboId(comboId)
                        .merchantSn(merchantSn)
                        .storeSn(storeSn)
                        .terminalSn(terminalSn)
                        .tradeComboEntity(tradeComboDao.selectById(comboId))
                .build());
    }

    private void effectOrInvalidActivity(ActivityFeeRateParam activityInfo, boolean isApply, String operator, String remark) {
        if (activityInfo == null || activityInfo.isNotActivity()) {
           return;
        }
        // 变更活动状态
        ActivityApplyEntity activityApplyEntity = activityInfo.getActivityApplyEntity();
        if (activityApplyEntity.getDiscount_quota_record_id() != null) {
            activityApplyDOMapper.updateQuotaStatusById(activityApplyEntity.getId(), activityApplyEntity.getDiscount_quota_status());
        }

        if (isApply) {
            ActivityFeeRateParam.ActivityFeeRateRequest request = activityInfo.getRequest();
            ChangeActivityFeeRateEnum changeActivityFeeRateEnum = request.getChangeActivityFeeRateEnum();
            // 交易考核恢复
            if (changeActivityFeeRateEnum == ChangeActivityFeeRateEnum.RESTORE_EFFECT_NOT_STANDARD) {
                applyBiz.updateEffectNotStandardEffectStatus(activityApplyEntity, operator, activityInfo.buildRemark(remark));
            } else {
                List<MerchantFeeRateEntity> feeRateList = merchantFeeRateDao.selectList(MerchantFeeRateQueryDalParam.builder()
                        .merchantSn(activityInfo.getRequest().getMerchantSn())
                        .tradeComboId(activityInfo.getRequest().getComboId())
                        .status(FeeRateStatusConst.IN_EFFECT)
                        .build());
                //重新构建最新的payWay, feeRate
                List<Integer> payWays = feeRateList.stream().map(MerchantFeeRateEntity::getPayWay).collect(Collectors.toList());
                Map<String, String> feeRateMap = feeRateList.stream().collect(Collectors.toMap(t -> t.getPayWay() + "", feeRateDo -> {
                    String feeRateValue = feeRateDo.getFeeRate();
                    if (Objects.equals(feeRateDo.getFeeRateType(), FeeRateTypeEnum.LADDER.name().toLowerCase())) {
                        ApplyLadderFeeRate ladderFeeRate = new ApplyLadderFeeRate();
                        ladderFeeRate.setFeeRateType(feeRateDo.getFeeRateType());
                        ladderFeeRate.setValue(JsonUtil.decode(feeRateDo.getFeeRate(), new TypeReference<List<ApplyLadderFeeRate.LadderFeeRate>>() {}));
                        feeRateValue = JsonUtil.encode(ladderFeeRate);
                    }
                    return feeRateValue;
                }));
                activityApplyEntity.setCombo_id(request.getComboId());
                activityApplyEntity.setPayway(JsonUtil.encode(payWays));
                activityApplyEntity.setFee_rate(JsonUtil.encode(feeRateMap));
                //普通活动子状态可能不存在
                if (Objects.nonNull(request.getSubStatusId())) {
                    activityApplyEntity.setActivity_sub_status_id(request.getSubStatusId());
                }
                applyBiz.updateApplyStatus(activityApplyEntity, ActivityConstants.EFFECT, operator, activityInfo.buildRemark(remark));
                //　首次报名生效, 更新下一次考核时间
                if (changeActivityFeeRateEnum == ChangeActivityFeeRateEnum.APPLY_TAKE_EFFECT) {
                    applyBiz.updateNextAssessmentTime(activityApplyEntity, true);
                }
            }

        } else {
            // 增加风控来源标识
            if (Objects.equals(activityInfo.getRequest().getOccurSource(), CancelActivityApplyRequest.OCCUR_SOURCE_RISK)) {
                ApplyExtraParam extra = Objects.nonNull(activityApplyEntity.buildJsonExtra()) ? activityApplyEntity.buildJsonExtra() : new ApplyExtraParam();
                extra.setOccurSource(activityInfo.getRequest().getOccurSource());
                activityApplyEntity.setExtra(JSONObject.toJSONString(extra));
            }
            int status = ActivityConstants.CANCEL;
            if (Objects.nonNull(activityInfo.getRequest().getApplyStatus())) {
                status = activityInfo.getRequest().getApplyStatus();
            }
            applyBiz.updateApplyStatus(activityApplyEntity, status, operator, activityInfo.buildRemark(remark));
        }

        // 发送活动变化kafka消息
        applyBiz.sendChangeFeeRateCompletedEvent(isApply, activityInfo, activityInfo.buildRemark(remark));

        // 发送活动通知
        feeRateCommonService.sendActivityNotice(activityInfo, activityApplyEntity.getMerchant_id(), isApply);
    }

    private void updateFeeRateWhenHasCombo(FeeRateUpdateRequest request) {
        UpdateFeeRateComboParam param = feeRateCommonService.buildUpdateFeeRateWhenHasComboParam(request);
        feeRateCommonService.updateFeeRateWhenHasCombo(param);
    }

    @Override
    public void updateMerchantConfigStatusAndFeeRateFromSp(Map<String, Object> request, String operator, String operatorName, String remark) {
        Integer payWay = MapUtil.getInteger(request, MerchantConfig.PAYWAY);
        String merchantId = MapUtil.getString(request, MerchantConfig.MERCHANT_ID);
        String merchantSn = tradeCommonService.getMerchantSnByMerchantId(merchantId);
        Map before = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, payWay);
        //部分支付方式走费率套餐的模式修改费率
        List<Integer> payWays = Arrays.asList(Payway.ALIPAY2.getCode(), Payway.WEIXIN.getCode(), Payway.UNIONPAY.getCode(), Payway.BESTPAY.getCode());
        if (!payWays.contains(payWay)) {
            tradeConfigService.updateMerchantConfigStatusAndFeeRate(merchantId, request);
        } else {
            //根据套餐去修改费率
            updateFeeRateFromSp(merchantId, merchantSn, payWay, request, operator, operatorName, remark, before);
        }
        Map after = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, payWay);
        //记录日志
        feeRateCommonService.sendBusinessLog(merchantId, operator, operatorName, remark, before, after);
        // 清缓存
        supportService.removeCachedParams(merchantSn);
    }

    /**
     * 取消套餐后置事件操作
     *
     * @param context
     */
    private void processCancelComboAfter(ActivityFeeRateParam context) {
        ActivityFeeRateParam.ActivityFeeRateRequest request = context.getRequest();
        String merchantSn = request.getMerchantSn();
        String merchantId = request.getMerchantId();
        Long tradeComboId = request.getComboId();
        TradeComboEntity tradeComboEntity = request.getTradeComboEntity();

        ComboConfigLevelEnum configLevel = ComboConfigLevelEnum.valueOf(StringUtils.upperCase(tradeComboEntity.getComboConfigLevel()));

        EnumMap<ComboConfigLevelEnum, Function<ActivityFeeRateParam, Boolean>> cancelCountMap = new EnumMap<>(ComboConfigLevelEnum.class);
        cancelCountMap.put(ComboConfigLevelEnum.MERCHANT, o -> {
            boolean result = getMerchantComboEffectCount(merchantSn, tradeComboId) == 0;
            if (result) {
                //如果是银行活动费率商户，花呗分期调整到30000分
                setHuabeiInitAmountByCombo(merchantId, tradeComboId, FeeRateStatusConst.DELETED);
                //主动取消套餐处理额度包活动
                commonActivityService.processCancelCombo(merchantSn, request.getAuditSn(), tradeComboId, request.isApplyPuHuiHint(), request.isSystemCancel());
            }
            return result;
        });
        cancelCountMap.put(ComboConfigLevelEnum.STORE, o -> getStoreComboEffectCount(merchantSn, request.getStoreSn(), tradeComboId) == 0);
        cancelCountMap.put(ComboConfigLevelEnum.TERMINAL, o -> getTerminalComboEffectCount(merchantSn, request.getTerminalSn(), tradeComboId) == 0);

        Boolean isCancel = cancelCountMap.get(configLevel).apply(context);
        if (Boolean.TRUE.equals(isCancel)) {
            // 打结取消标签
            tagCancel(merchantId, tradeComboEntity);
            // 费率活动处理
            effectOrInvalidActivity(context, Boolean.FALSE, request.getOperator(), request.getAuditSn());
        } else {
            if (context.isActivity()) {
                //生效中payWay列表
                List<MerchantFeeRateEntity> feeRateList = merchantFeeRateDao.selectList(MerchantFeeRateQueryDalParam.builder()
                        .merchantSn(request.getMerchantSn())
                        .tradeComboId(request.getComboId())
                        .status(FeeRateStatusConst.IN_EFFECT)
                        .build());
                Map<String, String> feeRateMap = feeRateList.stream().collect(Collectors.toMap(t -> t.getPayWay() + "", feeRateDo -> {
                    String feeRateValue = feeRateDo.getFeeRate();
                    if (Objects.equals(feeRateDo.getFeeRateType(), FeeRateTypeEnum.LADDER.name().toLowerCase())) {
                        ApplyLadderFeeRate ladderFeeRate = new ApplyLadderFeeRate();
                        ladderFeeRate.setFeeRateType(feeRateDo.getFeeRateType());
                        ladderFeeRate.setValue(JsonUtil.decode(feeRateDo.getFeeRate(), new TypeReference<List<ApplyLadderFeeRate.LadderFeeRate>>() {}));
                        feeRateValue = JsonUtil.encode(ladderFeeRate);
                    }
                    return feeRateValue;
                }));
                //已取消payWay列表
                List<String> payWayNames = Optional.ofNullable(context.getActivityApplyEntity().buildPayWay())
                        .orElse(Collections.emptyList()).stream().filter(code -> !feeRateMap.containsKey(code + ""))
                        .map(PayWayEnum::getNameByCode).collect(Collectors.toList());
                String remark = "取消通道：" + StringUtils.join(payWayNames, "、");
                applyBiz.updateApplyPayWays(context.getActivityApplyEntity(), feeRateMap, request.getOperator(), remark);
            }
        }
    }
}

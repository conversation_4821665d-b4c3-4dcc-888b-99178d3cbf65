package com.wosai.trade.impl;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.marekting.prepaid.command.storedOwnerCommand.QueryStoredFeeRateCommand;
import com.wosai.marekting.prepaid.representation.StoredFeeRateRepresentation;
import com.wosai.marekting.prepaid.service.StoredOwnerService;
import com.wosai.market.merchant.api.ServiceFeeRemoteService;
import com.wosai.market.merchant.dto.servicefee.StoreServiceFeeInfoRequest;
import com.wosai.market.merchant.dto.servicefee.StoreServiceFeeInfoResponse;
import com.wosai.market.tethys.api.dto.request.agreement.QueryFeeRateCommand;
import com.wosai.market.tethys.api.dto.response.agreement.FeeRateRepresentation;
import com.wosai.market.tethys.api.service.AgreementService;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.trade.config.apollo.CommonApolloConfig;
import com.wosai.trade.model.dal.ServiceFeeEffectiveQueryParam;
import com.wosai.trade.repository.dao.ServiceFeeEffectiveMapper;
import com.wosai.trade.repository.dao.ServiceFeeMapper;
import com.wosai.trade.repository.dao.entity.ServiceFeeEffectiveEntity;
import com.wosai.trade.repository.dao.entity.ServiceFeeEntity;
import com.wosai.trade.service.enums.ChargeCycleEnum;
import com.wosai.trade.service.enums.ChargeModeEnum;
import com.wosai.trade.service.enums.ComboConfigLevelEnum;
import com.wosai.trade.service.enums.ServiceFeeEffectiveStatusEnum;
import com.wosai.trade.service.exception.TradeManageBizException;
import com.wosai.trade.service.result.StoreBizServiceAgreement;
import com.wosai.trade.service.servicefee.model.ServiceFeeAgreement;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.model.MerchantAppConfig;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.core.service.TradeConfigService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 服务费聚合接口
 * Created by wujianwei on 2022/3/9.
 */
@Component
@Slf4j
public class ServiceAgreementFacade {

    @Autowired
    private StoredOwnerService storedOwnerService;
    @Autowired
    private AgreementService agreementService;
    @Autowired
    private ServiceFeeRemoteService serviceFeeRemoteService;
    @Autowired
    private StoreService storeService;
    @Autowired
    private CommonApolloConfig commonApolloConfig;
    @Autowired
    private ServiceFeeEffectiveMapper serviceFeeEffectiveMapper;
    @Autowired
    private TradeCommonService tradeCommonService;
    @Autowired
    private TradeConfigService tradeConfigService;
    @Autowired
    private ServiceFeeMapper serviceFeeMapper;

    private ExecutorService executorService = new ThreadPoolExecutor(10, 10, 1L, TimeUnit.MINUTES, new LinkedBlockingQueue(5), new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 从各个业务方获取服务费
     *
     * @param merchantId
     * @param storeIds
     * @return
     */
    @SneakyThrows
    public List<StoreBizServiceAgreement> getMerchantServiceAgreementFeeRates(String merchantId, List<String> storeIds) {
        //根据商户id查询所有的门店id
        ListResult storesResult = storeService.getSimpleStoreListByMerchantIdFromSlaveDb(merchantId, new PageInfo(1, 2000));
        List<Map> stores = storesResult.getRecords() != null ? storesResult.getRecords() : Collections.emptyList();
        Map<String, String> storeIdNameMap = stores.stream().collect(Collectors.toMap(s -> MapUtil.getString(s, DaoConstants.ID), s -> MapUtil.getString(s, Store.NAME)));
        if(storeIds == null || storeIds.isEmpty()){
            storeIds = new ArrayList<>(storeIdNameMap.keySet());
        }else{
            //校验是否匹配
            for (String storeId : storeIds) {
                if(!storeIdNameMap.containsKey(storeId)){
                    throw TradeManageBizException.createExc("门店与商户不匹配");
                }
            }
        }
        Map<String, StoreBizServiceAgreement> agreements = new HashMap<>();
        List<Future> futures = new LinkedList<>();
        //储值
        List<String> finalStoreIds = storeIds;

        futures.add(executorService.submit(() -> {
            try {
                QueryStoredFeeRateCommand command = new QueryStoredFeeRateCommand();
                command.setMerchantId(merchantId);
                command.setStoreIds(finalStoreIds);
                List<StoredFeeRateRepresentation> result = storedOwnerService.queryStoredFeeRate(command);
                if(result != null){
                    add(storeIdNameMap, agreements, result);
                }
            } catch (Exception e) {
                log.error("queryStoredFeeRate error " + e.getMessage(), e);
            }
        }));
        // 会员/券包费率查询
        futures.add(executorService.submit(() -> {
            try{
                QueryFeeRateCommand command = new QueryFeeRateCommand();
                command.setMerchantId(merchantId);
                command.setStoreIds(finalStoreIds);
                List<FeeRateRepresentation> result = agreementService.queryFeeRate(command);
                add(storeIdNameMap, agreements, result);
            }catch (Exception e){
                log.error("queryFeeRate error " + e.getMessage(), e);
            }
        }));
        //扫码点单相关
        futures.add(executorService.submit(() -> {
            try {
                StoreServiceFeeInfoRequest query = new StoreServiceFeeInfoRequest();
                query.setMerchantId(merchantId);
                query.setStoreIds(finalStoreIds);
                List<StoreServiceFeeInfoResponse> result = serviceFeeRemoteService.storesServiceFeeInfo(query);
                add(storeIdNameMap, agreements, result);
            }catch (Exception e){
                log.error("storeServiceFeeInfo error " + e.getMessage(), e);
            }
        }));
        futures.add(executorService.submit(()->{
            try {
                Map merchantAppConfig = tradeConfigService.getMerchantAppConfigByMerchantIdAndPaywayAndApp(merchantId, TradeConfigService.PAYWAY_ALIPAY2, commonApolloConfig.getFitnessAppId());
                if(merchantAppConfig == null){
                    return;
                }
                Map params = MapUtil.getMap(merchantAppConfig, MerchantAppConfig.PARAMS);
                Object feeRate = BeanUtil.getNestedProperty(params, TransactionParam.FITNESS_PARAMS + "." + TransactionParam.FITNESS_PARAMS_SETTLE_FEE_RATE);
                if (feeRate != null && feeRate instanceof String) {
                    List<StoreServiceFeeInfoResponse> result = new ArrayList<>();
                    for (String storeId : finalStoreIds) {
                        StoreServiceFeeInfoResponse storeServiceFeeInfoResponse = new StoreServiceFeeInfoResponse();
                        storeServiceFeeInfoResponse.setStoreId(storeId);
                        storeServiceFeeInfoResponse.setBizName("先享后付服务费");
                        storeServiceFeeInfoResponse.setFeeContent(feeRate.toString());
                        result.add(storeServiceFeeInfoResponse);
                    }
                    add(storeIdNameMap, agreements, result);
                }
            } catch (Exception e) {
                log.error("fitnessFee error " + e.getMessage(), e);
            }
        }));
        for (Future future : futures) {
            future.get();
        }

        return merge(merchantId, storeIdNameMap, new ArrayList<>(agreements.values()));
    }


    private synchronized void add(Map<String, String> storeIdNameMap, Map<String, StoreBizServiceAgreement> agreements, List records){
        if(records != null && records.size() > 0){
            Map<String, Long> externalTradeAppMap = commonApolloConfig.getExternalTradeAppMap();
            for (Object record : records) {
                String storeId = (String) BeanUtil.getBeanProperty(record, "storeId");
                if(storeId == null){
                    storeId = (String) BeanUtil.getBeanProperty(record, "store_id");
                }

                StoreBizServiceAgreement.Record r = new StoreBizServiceAgreement.Record();
                BeanUtils.copyProperties(record, r);
                StoreBizServiceAgreement agreement = agreements.get(storeId);
                if (agreement == null) {
                    agreement = new StoreBizServiceAgreement(storeId, storeIdNameMap.get(storeId));
                    agreements.put(storeId, agreement);
                }
                String profitShareRatio = r.getFeeContent();
                if (StringUtils.endsWith(profitShareRatio, "%")) {
                    profitShareRatio = StringUtils.substring(profitShareRatio, 0, profitShareRatio.length()-1);
                }
                ServiceFeeAgreement serviceFeeAgreement = new ServiceFeeAgreement();
                serviceFeeAgreement.setTradeAppId(externalTradeAppMap.get(r.getBizName()));
                serviceFeeAgreement.setTradeAppName(r.getBizName());
                serviceFeeAgreement.setServiceFeeName(r.getBizName());
                serviceFeeAgreement.setProfitShareRatio(profitShareRatio);
                serviceFeeAgreement.setActivityDesc(r.getActivityDesc());
                serviceFeeAgreement.setActivityContent(r.getActivityContent());
                serviceFeeAgreement.setChargeMode(ChargeModeEnum.USAGE); //老服务费兼容
                serviceFeeAgreement.setReceiverName(r.getBizName());//老服务费兼容
                agreement.getRecords().add(serviceFeeAgreement);
            }
        }
    }

    /**
     * 业务合并操作
     * 新列表覆盖老列表
     *
     * @param merchantId
     * @param storeIdNameMap
     * @param oldAgreementList
     * @return
     */
    public List<StoreBizServiceAgreement> merge(String merchantId, Map<String, String> storeIdNameMap, List<StoreBizServiceAgreement> oldAgreementList) {
        Map<String, StoreBizServiceAgreement> newMap = getNewStoreBizServiceAgreementList(merchantId, storeIdNameMap);
        overwriteRecordForFitness(oldAgreementList);
        if (newMap == null || newMap.isEmpty()) {
            return oldAgreementList;
        }
        for (StoreBizServiceAgreement oldAgreement : oldAgreementList) {
            StoreBizServiceAgreement newAgreement = newMap.get(oldAgreement.getStoreId());
            //新版里面无此门店服务费，则追加
            if (Objects.isNull(newAgreement)) {
                newMap.put(oldAgreement.getStoreId(), oldAgreement);
                continue;
            }
            List<ServiceFeeAgreement> records = newAgreement.getRecords();
            Map<Long, List<ServiceFeeAgreement>> newRecordMap = records.stream().collect(Collectors.groupingBy(ServiceFeeAgreement::getTradeAppId));
            for (ServiceFeeAgreement oldAgreementRecord : oldAgreement.getRecords()) {
                Long tradeAppId = oldAgreementRecord.getTradeAppId();
                //配置内无此映射关系　todo 基本不太可能。主要做兜底
                if (Objects.isNull(tradeAppId)) {
                    log.warn("配置内无此映射关系. tradeAppName:{}", oldAgreementRecord.getTradeAppName());
                    records.add(oldAgreementRecord);
                } else {
                    //新配置内无此业务
                    if (Objects.isNull(newRecordMap.get(tradeAppId))) {
                        records.add(oldAgreementRecord);
                    }
                }
            }
        }
        return new ArrayList<>(newMap.values());
    }

    private void overwriteRecordForFitness(Collection<StoreBizServiceAgreement> values){
        //先享后付 不属于saas服务费中的 但要求展示对应的费率。
        for (StoreBizServiceAgreement value : values) {
            List<ServiceFeeAgreement> records = value.getRecords();
            for (ServiceFeeAgreement record : records) {
                if (record.getTradeAppId() != null && commonApolloConfig.getFitnessAppId().equals(record.getTradeAppId().toString())) {
                    record.setTradeAppName("先享后付");
                }
            }
        }
    }

    private Map<String, StoreBizServiceAgreement> getNewStoreBizServiceAgreementList(String merchantId, Map<String, String> storeIdNameMap) {
        String merchantSn = tradeCommonService.getMerchantSnByMerchantId(merchantId);
        List<ServiceFeeEffectiveEntity> serviceFeeEffectiveEntityList = serviceFeeEffectiveMapper.queryByCondition(ServiceFeeEffectiveQueryParam.builder()
                .merchantSn(merchantSn)
                .statusList(ImmutableList.of(ServiceFeeEffectiveStatusEnum.ACTIVE.getStatusValue()))
                .build());
        if (CollectionUtils.isEmpty(serviceFeeEffectiveEntityList)) {
            return Collections.emptyMap();
        }
        //构建商户协议
        List<ServiceFeeAgreement> merchantAgreementList = buildMerchantServiceFeeAgreementList(serviceFeeEffectiveEntityList);
        //构建门店协议
        Map<String, List<ServiceFeeAgreement>> serviceFeeAgreementMap = buildStoreServiceFeeAgreementList(serviceFeeEffectiveEntityList);
        //循环主列表，将商户协议记录合并到每个门店协议记录内
        return storeIdNameMap.entrySet().stream().map(entry -> {
            String storeId = entry.getKey();
            String storeName = entry.getValue();
            StoreBizServiceAgreement storeBizServiceAgreement = new StoreBizServiceAgreement();
            storeBizServiceAgreement.setMerchantSn(merchantSn);
            storeBizServiceAgreement.setStoreId(storeId);
            storeBizServiceAgreement.setStoreName(storeName);
            List<ServiceFeeAgreement> storeAgreementList = serviceFeeAgreementMap.get(storeId);
            //无门店层级规则,直接添加商户层级并返回
            if (CollectionUtils.isEmpty(storeAgreementList)) {
                storeBizServiceAgreement.getRecords().addAll(merchantAgreementList);
                return storeBizServiceAgreement;
            }
            Map<String, List<ServiceFeeAgreement>> recordMap = storeAgreementList.stream().collect(Collectors.groupingBy(this::generatorDupKey));
            //覆盖 门店规则内无对应商户的业务方服务规则
            merchantAgreementList.forEach(merchantAgreement -> {
                String dupKey = generatorDupKey(merchantAgreement);
                List<ServiceFeeAgreement> records = recordMap.get(dupKey);
                if (CollectionUtils.isEmpty(records)) {
                    recordMap.put(dupKey, Collections.singletonList(merchantAgreement));
                }
            });
            recordMap.forEach((key, value) -> storeBizServiceAgreement.getRecords().addAll(value));
            return storeBizServiceAgreement;
        }).collect(Collectors.toMap(StoreBizServiceAgreement::getStoreId, Function.identity()));
    }

    /**
     * 构建商户
     *
     * @param list
     * @return
     */
    private List<ServiceFeeAgreement> buildMerchantServiceFeeAgreementList(List<ServiceFeeEffectiveEntity> list) {
        return list.stream().filter(effectiveDo -> Objects.equals(effectiveDo.getLevel(), ComboConfigLevelEnum.MERCHANT.name())
        ).map(this::buildServiceFeeAgreement).collect(Collectors.toList());
    }

    /**
     * 构建门店
     *
     * @param list
     * @return
     */
    private Map<String, List<ServiceFeeAgreement>> buildStoreServiceFeeAgreementList(List<ServiceFeeEffectiveEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        Map<String, List<ServiceFeeAgreement>> resultMap = Maps.newHashMap();
        for (ServiceFeeEffectiveEntity effectiveDo : list) {
            if (!Objects.equals(effectiveDo.getLevel(), ComboConfigLevelEnum.STORE.name())) {
                continue;
            }
            String storeId = effectiveDo.buildExtra().getStoreId();
            resultMap.computeIfAbsent(storeId, v -> Lists.newArrayList()).add(buildServiceFeeAgreement(effectiveDo));
        }
        return resultMap;
    }

    private ServiceFeeAgreement buildServiceFeeAgreement(ServiceFeeEffectiveEntity effectiveDo) {
        ServiceFeeEffectiveEntity.Extra extra = effectiveDo.buildExtra();
        String receiverName = null;
        if (CollectionUtils.isNotEmpty(extra.buildReceiverNameList())) {
            receiverName = extra.buildReceiverNameList().get(0);
        }
        ServiceFeeEntity feeDo = serviceFeeMapper.selectByPrimaryKey(effectiveDo.getFeeId());
        ServiceFeeAgreement agreement = ServiceFeeAgreement.builder()
                .tradeAppId(effectiveDo.getTradeAppId())
                .tradeAppName(extra.getTradeAppName())
                .serviceFeeId(effectiveDo.getFeeId())
                .serviceFeeName(extra.getServiceFeeName())
                .scenesType(effectiveDo.getScenesType())
                .scenesCode(effectiveDo.getScenesCode())
                .profitShareRatio(feeDo.isProfitShareRatio() ? effectiveDo.getProfitShareRatio() : null)
                .endTime(effectiveDo.getEndDate())
                .chargeAmount(effectiveDo.getChargeAmount())
                .chargeMode(extra.getChargeMode())
                .minCharge(effectiveDo.getMinCharge())
                .commission(extra.getCommission())
                .receiverName(receiverName)
                .build();
        if (Objects.nonNull(extra.getChargeCycle())) {
            agreement.setChargeCycle(feeDo.getChargeCycle());
            agreement.setChargeCycleNumber(feeDo.getChargeCycleNumber());
            agreement.setBizSn(effectiveDo.generatorDimensionNo());
            //终身版不展示
            if (ChargeCycleEnum.LIFETIME.getByName().equals(feeDo.getChargeCycle())) {
                agreement.setEndTime(null);
            }
        }
        return agreement;
    }

    /**
     * 生成门店覆盖商户层级key.
     *
     * @param agreement
     * @return
     */
    private String generatorDupKey(ServiceFeeAgreement agreement) {
        return agreement.getTradeAppId() + "-" + agreement.getScenesType() +"-" +  agreement.getScenesCode();
    }
}

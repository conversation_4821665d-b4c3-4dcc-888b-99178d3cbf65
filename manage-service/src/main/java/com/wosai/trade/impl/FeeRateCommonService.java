package com.wosai.trade.impl;


import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.sales.core.bean.org.OrgCommonConfig;
import com.wosai.sales.core.bean.org.OrgCommonConfigResp;
import com.wosai.sales.core.service.org.OrgCommonConfigService;
import com.wosai.trade.biz.businesslog.BusinessOpLogBiz;
import com.wosai.trade.config.apollo.CommonApolloConfig;
import com.wosai.trade.model.apollo.RestoreActivityComboConfig;
import com.wosai.trade.model.biz.ActivityFeeRateParam;
import com.wosai.trade.model.biz.ApplyComboDetailParam;
import com.wosai.trade.model.biz.ReplaceNoticeMessageParam;
import com.wosai.trade.model.biz.UpdateFeeRateComboParam;
import com.wosai.trade.model.dal.MerchantFeeRateUpsertDalParam;
import com.wosai.trade.model.dal.TradeComboDetailQueryDalParam;
import com.wosai.trade.model.enums.PayWayEnum;
import com.wosai.trade.repository.dao.MerchantFeeRateDao;
import com.wosai.trade.repository.dao.TradeAppDao;
import com.wosai.trade.repository.dao.TradeComboDao;
import com.wosai.trade.repository.dao.TradeComboDetailDao;
import com.wosai.trade.repository.dao.entity.*;
import com.wosai.trade.service.TradeComboDetailService;
import com.wosai.trade.service.activity.request.ActivityInfo;
import com.wosai.trade.service.activity.request.ActivityRule;
import com.wosai.trade.service.enums.ChangeActivityFeeRateEnum;
import com.wosai.trade.service.enums.FeeRateTypeEnum;
import com.wosai.trade.service.exception.TradeManageBizException;
import com.wosai.trade.service.request.ActivityBaseRequest;
import com.wosai.trade.service.request.ApplyFeeRateRequest;
import com.wosai.trade.service.request.FeeRateUpdateRequest;
import com.wosai.trade.service.result.ListMchFeeRateResult;
import com.wosai.trade.service.result.ListMchFeeStatusResult;
import com.wosai.trade.service.result.TradeComboDetailResult;
import com.wosai.trade.util.CommonUtil;
import com.wosai.trade.util.FeeRateUtils;
import com.wosai.trade.util.JsonUtil;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.meta.Payway;
import com.wosai.upay.core.meta.SubPayway;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.model.OpenPayComboReq;
import com.wosai.upay.service.CrmEdgeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import vo.ApiRequestParam;
import vo.UserVo;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class FeeRateCommonService {
    @Resource
    private MerchantFeeRateDao merchantFeeRateDao;
    @Resource
    private TradeAppDao tradeAppDao;
    @Resource
    private TradeComboDao tradeComboDao;
    @Resource
    private TradeComboDetailDao tradeComboDetailDao;
    @Resource
    private TradeConfigService tradeConfigService;
    @Resource
    private BusinessOpLogBiz businessOpLogBiz;
    @Resource
    private CommonApolloConfig commonApolloConfig;
    @Resource
    private TradeCommonService tradeCommonService;
    @Resource
    private TradeComboDetailService tradeComboDetailService;
    @Resource
    private OrgCommonConfigService orgCommonConfigService;
    @Resource
    private CrmEdgeService crmEdgeService;

    @Value("${config.aop.dev-code}")
    private String aopDevCode;
    @Value("${config.aop.activity-merchant-notice-apply-success}")
    private String merchantApplySccessAopTemplate;
    @Value("${config.aop.activity-merchant-notice-cancel}")
    private String merchantCancelAopTemplate;
    @Value("${config.aop.activity-sale-notice-apply-success}")
    private String saleApplySuccessAopTemplate;
    @Value("${config.aop.activity-sale-notice-cancel}")
    private String saleCancelAopTemplate;

    @Value("${config.aop.activity-merchant-notice-change-success}")
    private String merchantChangeSuccessAopTemplate;

    @Value("${config.aop.activity-sale-notice-change-success}")
    private String saleChangeSuccessAopTemplate;

    @Value("${config.crm-indirect-dev-code}")
    private String crmIndirectDevCode;

    //组织套餐配置默认缓存
    private static Cache<String, Map<Integer, ApplyComboDetailParam>> organizationComboConfigCache = CacheBuilder.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .maximumSize(5000)
            .build();

    /**
     * 获取商户所在的一级组织配置的间连扫码套餐配置
     * @param merchantSn
     * @return
     */
    public Map<Integer, ApplyComboDetailParam> getMerchantDefaultComboConfig(String merchantSn){
        try {
            Map<String, Object> merchant = tradeCommonService.getMerchantBySn(merchantSn);
            String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
            String industryId = BeanUtil.getPropString(merchant, Merchant.INDUSTRY);
            String path = tradeCommonService.getMerchantOrganizationPathFromCrm(merchantId);
            String level1Id = tradeCommonService.getOrganizationIdByCode(path.split(",")[0]);
            //根据行业&组织进行缓存
            String key = industryId + "*" + level1Id;
            Map<Integer, ApplyComboDetailParam> result = organizationComboConfigCache.get(key, () -> {
                OpenPayComboReq req = new OpenPayComboReq();
                req.setDevCode(crmIndirectDevCode);
                req.setIndustryId(industryId);
                UserVo userVo = new UserVo();
                userVo.setOrganizationId(level1Id);
                ApiRequestParam<OpenPayComboReq, Map<String, Object>> param = new ApiRequestParam<>(userVo, null, req, null);
                List<Map> payCombos = crmEdgeService.getPayCombos(param);
                return buildComboConfig(payCombos);
            });
            return result == null ? Collections.emptyMap() : result;
        } catch (Exception e) {
            log.error("getMerchantDefaultComboConfig error " + e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    public Map<Integer, ApplyComboDetailParam> buildComboConfig(List<Map> payCombos){
        if(payCombos == null || payCombos.isEmpty()){
            log.warn("can not find the default merchant config");
            return null;
        }
        // 正常情况下只有一个，获取第一个
        List<Map<String,Object>> list = (List<Map<String, Object>>) payCombos.get(0).get("records");
        if(list == null){
            log.warn("can not find the default merchant config, no records");
            return null;
        }
        Map<Integer, ApplyComboDetailParam> paywayConfig = new HashMap<>();
        for (Map<String, Object> detail : list) {
            long comboId = BeanUtil.getPropLong(detail, "trade_combo_id");
            int payway = BeanUtil.getPropInt(detail, "payway");
            List<Map<String,Object>> ladderFeeRates = (List<Map<String, Object>>) detail.get("ladder_fee_rates");
            List<Map<String,Object>> channelFeeRates = (List<Map<String, Object>>) detail.get("channel_fee_rates");
            List<Map<String,Object>> channelLadderFeeRates = (List<Map<String, Object>>) detail.get("channel_ladder_fee_rates");
            String feeRate = null;
            if(channelFeeRates != null && channelFeeRates.size() > 0){
                // 处理银行卡渠道费率
                List<Map<String,Object>> records = new ArrayList<>();
                Map<String,Object> config = CollectionUtil.hashMap("fee_type", FeeRateTypeEnum.CHANNEL.name().toLowerCase(), "value", records);
                boolean isFeeValid = true;
                for (Map<String, Object> channelFeeRate : channelFeeRates) {
                    String feeRateString = BeanUtil.getPropString(channelFeeRate, "fee_rate_default_value");
                    String type = MapUtil.getString(channelFeeRate, "type");
                    String feeRateMinString = BeanUtil.getPropString(channelFeeRate, "fee_rate_min");
                    String feeRateMaxString = BeanUtil.getPropString(channelFeeRate, "fee_rate_max");
                    if(feeRateMinString != null && FeeRateUtils.feeRateEquals(feeRateMinString, feeRateMaxString) && FeeRateUtils.isValidFeeRate(feeRateMinString)){
                        //属于固定费率，不需要设置默认值
                        feeRateString = feeRateMinString;
                    }
                    if(feeRateString == null || !FeeRateUtils.isValidFeeRate(feeRateString)){
                        isFeeValid = false;
                        break;
                    }
                    String useMax = BeanUtil.getPropString(channelFeeRate, "high_min");
                    String max = BeanUtil.getPropString(channelFeeRate, "high_max");
                    if (StringUtil.isNotBlank(max)) {
                        if (StringUtil.isNotBlank(useMax)) {
                            useMax = max;
                        } else if(!useMax.equals(max)){
                            // 手续费最高值无法确定
                            isFeeValid = false;
                            break;
                        }
                    }
                    records.add(CollectionUtil.hashMap("type", type, "max", useMax, "fee_rate", feeRateString));
                }
                //费率不合法，不做处理
                if(!isFeeValid){
                    continue;
                }
                feeRate = JsonUtil.encode(config);

            } else if (com.wosai.pantheon.util.CollectionUtil.isNotEmpty(channelLadderFeeRates)) {
                // 资金渠道阶梯费率
                List<Map<String,Object>> records = new ArrayList<>();
                Map<String,Object> config = CollectionUtil.hashMap("fee_type", FeeRateTypeEnum.CHANNEL_LADDER.name().toLowerCase(), "value", records);
                boolean isFeeValid = true;
                for (Map<String, Object> channelFeeRate : channelFeeRates) {
                    String type = MapUtil.getString(channelFeeRate, "type");
                    List<Map<String, Object>> cLadderFeeRates = (List<Map<String, Object>>) MapUtil.getObject(channelFeeRate, "ladder_fee_rates");
                    if (com.wosai.pantheon.util.CollectionUtil.isEmpty(cLadderFeeRates)) {
                        isFeeValid = false;
                        break;
                    }
                    List<Map<String, Object>> typeLadderFeeRate = new ArrayList<>();
                    for (Map<String, Object> ladderFeeRate : cLadderFeeRates) {
                        String feeRateString = BeanUtil.getPropString(ladderFeeRate, "fee_rate_default_value");
                        String feeRateMinString = BeanUtil.getPropString(ladderFeeRate, "fee_rate_min");
                        String feeRateMaxString = BeanUtil.getPropString(ladderFeeRate, "fee_rate_max");
                        if(feeRateMinString != null && FeeRateUtils.feeRateEquals(feeRateMinString, feeRateMaxString) && FeeRateUtils.isValidFeeRate(feeRateMinString)){
                            //属于固定费率，不需要设置默认值
                            feeRateString = feeRateMinString;
                        }
                        if(feeRateString == null || !FeeRateUtils.isValidFeeRate(feeRateString)){
                            isFeeValid = false;
                            break;
                        }
                        String max = BeanUtil.getPropString(ladderFeeRate, "max");
                        String min = BeanUtil.getPropString(ladderFeeRate, "min");
                        typeLadderFeeRate.add(CollectionUtil.hashMap("max", max != null ? Double.parseDouble(max) : null, "min", Double.parseDouble(min), "fee_rate", feeRateString));
                    }
                    if(!isFeeValid){
                        break;
                    }
                    records.add(CollectionUtil.hashMap("type", type, "ladder_fee_rates", typeLadderFeeRate));
                }
                //费率不合法，不做处理
                if(!isFeeValid){
                    continue;
                }
                feeRate = JsonUtil.encode(config);

            }else if(ladderFeeRates != null && ladderFeeRates.size() > 0){
                //处理阶梯费率
                List<Map<String,Object>> records = new ArrayList<>();
                Map<String,Object> config = CollectionUtil.hashMap("fee_type", "ladder", "value", records);
                boolean isFeeValid = true;
                for (Map<String, Object> ladderFeeRate : ladderFeeRates) {
                    String feeRateString = BeanUtil.getPropString(ladderFeeRate, "fee_rate_default_value");
                    String feeRateMinString = BeanUtil.getPropString(ladderFeeRate, "fee_rate_min");
                    String feeRateMaxString = BeanUtil.getPropString(ladderFeeRate, "fee_rate_max");
                    if(feeRateMinString != null && FeeRateUtils.feeRateEquals(feeRateMinString, feeRateMaxString) && FeeRateUtils.isValidFeeRate(feeRateMinString)){
                        //属于固定费率，不需要设置默认值
                        feeRateString = feeRateMinString;
                    }
                    if(feeRateString == null || !FeeRateUtils.isValidFeeRate(feeRateString)){
                        isFeeValid = false;
                        break;
                    }
                    String max = BeanUtil.getPropString(ladderFeeRate, "max");
                    String min = BeanUtil.getPropString(ladderFeeRate, "min");
                    records.add(CollectionUtil.hashMap("max", max != null ? Double.parseDouble(max) : null, "min", Double.parseDouble(min), "fee_rate", feeRateString));
                }
                //费率不合法，不做处理
                if(!isFeeValid){
                    continue;
                }
                feeRate = JsonUtil.encode(config);
            }else {
                //固定费率| 区间费率
                String feeRateString = BeanUtil.getPropString(detail, "fee_rate_default_value");
                String feeRateMinString = BeanUtil.getPropString(detail, "fee_rate_min");
                String feeRateMaxString = BeanUtil.getPropString(detail, "fee_rate_max");
                if(feeRateMinString != null && FeeRateUtils.feeRateEquals(feeRateMinString, feeRateMaxString) && FeeRateUtils.isValidFeeRate(feeRateMinString)){
                    //属于固定费率，不需要设置默认值
                    feeRateString = feeRateMinString;
                }
                if(feeRateString == null || !FeeRateUtils.isValidFeeRate(feeRateString)){
                    continue;
                }
                feeRate = feeRateString;
            }
            paywayConfig.put(payway, ApplyComboDetailParam.builder().comboId(comboId).payWay(payway).feeRate(feeRate).build());
        }
        return paywayConfig;
    }

    /**
     * 填充更新费率标识
     *
     * @param merchantId
     * @param list
     */
    public void fillUpdatableFeeRateFlag(String merchantId, List<ListMchFeeStatusResult> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        String path = "";
        try {
            path = tradeCommonService.getMerchantOrganizationPathFromCrm(merchantId);
        } catch (Exception e) {
            log.error("get organization error: " + e.getMessage(), e);
        }
        //行业合作 00052 区间费率可修改
        //代理 00001、拉卡拉 90001 特定套餐可修改
        for (ListMchFeeStatusResult result : list) {
            boolean updatable = false;
            Integer payWay = result.getPayWay();
            Long tradeComboId = result.getTradeComboId();
            if (!Objects.isNull(tradeComboId)) {
                if ((path.startsWith("00001") || path.startsWith("90001")) && commonApolloConfig.getCrmAgentLakalaUpdatableComboIds().contains(tradeComboId + "")) {
                    updatable = true;
                } else if (path.startsWith("00052")) {
                    List<TradeComboDetailResult> detailResults = tradeComboDetailService.listByComboId(tradeComboId);
                    TradeComboDetailResult detail = detailResults.stream().filter(d -> d.getPayway() == payWay).findAny().orElse(null);
                    if (detail != null && (detail.getLadderFeeRates() == null || detail.getLadderFeeRates().isEmpty()) && !Objects.equals(detail.getFeeRateMin(), detail.getFeeRateMax())) {
                        updatable = true;
                    }
                }
            }
            result.setUpdatableFeeRateFlag(updatable);
        }
    }

    /**
     * 构建费率变更入参
     *
     * @param request
     * @return
     */
    public UpdateFeeRateComboParam buildUpdateFeeRateWhenHasComboParam(FeeRateUpdateRequest request) {
        Integer payWay = request.getPayWay();
        String merchantId = request.getMerchantId();
        Map<String, Object> merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, payWay);
        if(merchantConfig == null){
            TradeManageBizException.createExc("商户配置不存在");
        }
        Map params = MapUtil.getMap(merchantConfig, MerchantConfig.PARAMS);
        Integer ladderStatus = MapUtil.getInteger(params, MerchantConfig.LADDER_STATUS);
        Map<String,String> feeRateTags = MapUtil.getMap(params, Objects.equals(ladderStatus, MerchantConfig.STATUS_OPENED) ? MerchantConfig.LADDER_FEE_RATE_TAG : TransactionParam.FEE_RATE_TAG);

        //获取组织ID
        String relationOriginId = tradeCommonService.findCustomerRelationOriginId(merchantId, commonApolloConfig.getCrmConfigCodeForFindOrganization());

        Map<String,Object> merchantConfigUpdate = new HashMap<>();
        Map<Long, TradeComboDetailResult> cache = new HashMap<>();
        Map<Long, String> comboMerchantFeeRates = new HashMap<>(); //combo: feeRate
        buildUpdatesWhenCrmFeeRateUpdate(payWay, SubPayway.BARCODE.getCode(), MerchantConfig.B2C_FEE_RATE , request.getBscFeeRate(), feeRateTags, cache, merchantConfigUpdate, comboMerchantFeeRates, relationOriginId);
        buildUpdatesWhenCrmFeeRateUpdate(payWay, SubPayway.QRCODE.getCode(), MerchantConfig.C2B_FEE_RATE , request.getCsbFeeRate(), feeRateTags, cache, merchantConfigUpdate, comboMerchantFeeRates, relationOriginId);
        buildUpdatesWhenCrmFeeRateUpdate(payWay, SubPayway.WAP.getCode(), MerchantConfig.WAP_FEE_RATE , request.getWapFeeRate(), feeRateTags, cache, merchantConfigUpdate, comboMerchantFeeRates, relationOriginId);
        buildUpdatesWhenCrmFeeRateUpdate(payWay, SubPayway.MINI.getCode(), MerchantConfig.MINI_FEE_RATE , request.getMiniFeeRate(), feeRateTags, cache, merchantConfigUpdate, comboMerchantFeeRates, relationOriginId);
        if(merchantConfigUpdate.isEmpty()){
            return null;
        }

        String merchantSn = tradeCommonService.getMerchantSnByMerchantId(merchantId);
        return UpdateFeeRateComboParam.builder()
                .merchantId(merchantId)
                .merchantSn(merchantSn)
                .payWay(payWay)
                .merchantConfig(merchantConfig)
                .merchantConfigUpdate(merchantConfigUpdate)
                .comboMerchantFeeRates(comboMerchantFeeRates)
                .build();
    }

    public void verifyFeeRateUpdateFromCrm(FeeRateUpdateRequest r) {
        ActivityBaseRequest baseRequest = new ActivityBaseRequest();
        baseRequest.setMerchantId(r.getMerchantId());
        List<ListMchFeeStatusResult> merchantBasicAgreementFeeRates = getMerchantBasicAgreementFeeRates(baseRequest);
        fillUpdatableFeeRateFlag(r.getMerchantId(), merchantBasicAgreementFeeRates);
        Map<Integer, Boolean> payWayUpdatable = merchantBasicAgreementFeeRates.stream().collect(Collectors.toMap(ListMchFeeStatusResult::getPayWay,
                ListMchFeeStatusResult::isUpdatableFeeRateFlag));
        Boolean updatable = payWayUpdatable.getOrDefault(r.getPayWay(), false);
        if(!updatable){
            TradeManageBizException.createExc("费率不允许修改");
        }
    }

    public List<ListMchFeeStatusResult> getMerchantBasicAgreementFeeRates(ActivityBaseRequest request) {
        String merchantId = request.getMerchantId();
        int [] payWays = {PayWayEnum.ZHIFUBAO.getCode(), PayWayEnum.WEIXIN.getCode(), PayWayEnum.YINLIAN.getCode(), PayWayEnum.YIZHIFU.getCode()};
        List<Map> merchantConfigs = tradeConfigService.getAnalyzedMerchantConfigsByPayWayList(merchantId, payWays);
        if (CollectionUtils.isEmpty(merchantConfigs)) {
            return new ArrayList<>();
        }
        Map<Long, String> appIdAndNameMap = tradeAppDao.selectAll().stream().collect(Collectors.toMap(TradeAppEntity::getId, TradeAppEntity::getName));
        List<ListMchFeeStatusResult> list = merchantConfigs.stream()
                .map(merchantConfig -> {
                    ListMchFeeStatusResult result = new ListMchFeeStatusResult();
                    Integer payWay = MapUtils.getInteger(merchantConfig, MerchantConfig.PAYWAY);
                    Boolean formal = MapUtil.getBoolean(merchantConfig, MerchantConfig.B2C_FORMAL, false);
                    result.setPayWay(payWay);
                    result.setBscFormal(formal);
                    result.setBscStatus(MapUtil.getInteger(merchantConfig, MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED));
                    result.setCsbStatus(MapUtil.getInteger(merchantConfig, MerchantConfig.C2B_STATUS, MerchantConfig.STATUS_OPENED));
                    result.setWapStatus(MapUtil.getInteger(merchantConfig, MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_OPENED));
                    result.setMiniStatus(MapUtil.getInteger(merchantConfig, MerchantConfig.MINI_STATUS, MerchantConfig.STATUS_OPENED));
                    result.setAppStatus(MapUtil.getInteger(merchantConfig, MerchantConfig.APP_STATUS, MerchantConfig.STATUS_OPENED));
                    result.setH5Status(MapUtil.getInteger(merchantConfig, MerchantConfig.H5_STATUS, MerchantConfig.STATUS_OPENED));

                    Integer ladderStatus = MapUtil.getInteger(merchantConfig, MerchantConfig.LADDER_STATUS);
                    boolean isLadderOpened = Objects.equals(ladderStatus, MerchantConfig.STATUS_OPENED)
                            || Objects.equals(MapUtil.getString(merchantConfig, MerchantConfig.FEE_RATE_TYPE), MerchantConfig.FEE_RATE_TYPE_LADDER);
                    if(isLadderOpened){
                        //阶梯
                        result.setIsLadderFeeRate(1);
                        List<Map<String,Object>> ladderFeeRates = (List<Map<String, Object>>) MapUtil.getObject(merchantConfig, MerchantConfig.LADDER_FEE_RATES);
                        result.setLadderFeeRates(ladderFeeRates.stream().map(ladderFeeRate ->
                                new ListMchFeeRateResult.LadderFeeRate()
                                        .setMin(MapUtil.getDouble(ladderFeeRate, MerchantConfig.LADDER_FEE_RATE_MIN))
                                        .setMax(MapUtil.getDouble(ladderFeeRate, MerchantConfig.LADDER_FEE_RATE_MAX))
                                        .setBscFeeRate(MapUtil.getString(ladderFeeRate, MerchantConfig.B2C_FEE_RATE))
                                        .setCsbFeeRate(MapUtil.getString(ladderFeeRate, MerchantConfig.C2B_FEE_RATE))
                                        .setWapFeeRate(MapUtil.getString(ladderFeeRate, MerchantConfig.WAP_FEE_RATE))
                                        .setMiniFeeRate(MapUtil.getString(ladderFeeRate, MerchantConfig.MINI_FEE_RATE))
                                        .setH5FeeRate(MapUtil.getString(ladderFeeRate, MerchantConfig.H5_FEE_RATE))
                                        .setAppFeeRate(MapUtil.getString(ladderFeeRate, MerchantConfig.APP_FEE_RATE))
                        ).collect(Collectors.toList()));

                    }else {
                        //固定费率
                        result.setIsLadderFeeRate(0);
                        result.setBscFeeRate(MapUtils.getString(merchantConfig, MerchantConfig.B2C_FEE_RATE));
                        result.setCsbFeeRate(MapUtils.getString(merchantConfig, MerchantConfig.C2B_FEE_RATE));
                        result.setWapFeeRate(MapUtils.getString(merchantConfig, MerchantConfig.WAP_FEE_RATE));
                        result.setMiniFeeRate(MapUtils.getString(merchantConfig, MerchantConfig.MINI_FEE_RATE));
                        //由于老的商户翼支付没有配置小程序费率，导致默认的小程序费率可能与翼支付门店码的费率不一致。由于翼支付并没有小程序，为了减少商户的误解，小程序的费率重新直接设置为门店码费率。
                        if(payWay == PayWayEnum.YIZHIFU.getCode()){
                            result.setMiniFeeRate(MapUtils.getString(merchantConfig, MerchantConfig.WAP_FEE_RATE));
                        }
                        //很多存量商户由于各种各样的原因，可能有h5 app费率，h5, app只有直连，为了减少商户的误解，故只有直连才返回
                        if(formal){
                            result.setAppFeeRate(MapUtils.getString(merchantConfig, MerchantConfig.APP_FEE_RATE));
                            result.setH5FeeRate(MapUtils.getString(merchantConfig, MerchantConfig.H5_FEE_RATE));
                        }
                    }
                    Map params = MapUtil.getMap(merchantConfig, MerchantConfig.PARAMS);
                    Map map = MapUtil.getMap(params, isLadderOpened ? MerchantConfig.LADDER_FEE_RATE_TAG : TransactionParam.FEE_RATE_TAG);
                    //套餐信息获取b2c的
                    String tag = MapUtil.getString(map, SubPayway.BARCODE.getCode() + "");
                    Long comboId = FeeRateUtils.getComboIdFromFeeRateTag(tag);
                    result.setTradeComboId(comboId);
                    if(comboId == null){
                        result.setTradeAppName("支付业务");
                        result.setTradeComboName("基础费率");
                        result.setTradeComboShortName("基础费率");
                        result.setDescription("基础费率");
                    }else{
                        TradeComboEntity entity = tradeComboDao.selectById(comboId);
                        result.setTradeAppName(appIdAndNameMap.get(entity.getTradeAppId()));
                        result.setTradeComboName(entity.getName());
                        result.setTradeComboShortName(entity.getShortName());
                        result.setDescription(entity.getDescription());
                    }
                    return result;
                }).collect(Collectors.toList());
        return list;
    }

    public void updateFeeRateWhenHasCombo(UpdateFeeRateComboParam updateFeeRateComboParam) {
        if (Objects.isNull(updateFeeRateComboParam)) {
            return;
        }
        Integer payWay = updateFeeRateComboParam.getPayWay();
        String merchantSn = updateFeeRateComboParam.getMerchantSn();
        Map<String, Object> merchantConfig = updateFeeRateComboParam.getMerchantConfig();
        Map<Long, String> comboMerchantFeeRates = updateFeeRateComboParam.getComboMerchantFeeRates();
        Map<String, Object> merchantConfigUpdate = updateFeeRateComboParam.getMerchantConfigUpdate();
        //更新底层费率
        merchantConfigUpdate.put(MerchantConfig.PAYWAY, payWay);
        merchantConfigUpdate.put(DaoConstants.ID, merchantConfig.get(DaoConstants.ID));
        merchantConfigUpdate.put(DaoConstants.VERSION, merchantConfig.get(DaoConstants.VERSION));
        tradeConfigService.updateMerchantConfig(merchantConfigUpdate);
        //更新merchant_fee_rate表里面的费率
        for (Long comboId : comboMerchantFeeRates.keySet()) {
            String feeRate = comboMerchantFeeRates.get(comboId);
            MerchantFeeRateEntity entity = merchantFeeRateDao.selectByMchSnAndComboIdAndPayWay(merchantSn, comboId, payWay);
            if (entity != null) {
                MerchantFeeRateUpsertDalParam param = MerchantFeeRateUpsertDalParam.builder()
                        .id(entity.getId())
                        .feeRate(feeRate)
                        .build();
                merchantFeeRateDao.updateById(param);
            }
        }
    }

    /**
     * 构建取消恢复套餐
     * 逻辑：
     * 1、优先走活动取消后恢复套餐
     * 2、若1不存在则走套餐取消恢复套餐
     * 3、若1、2都不存在则走配置兜底套餐
     *
     * @param merchantSn
     * @param operator
     * @param operatorName
     * @param remark
     * @param merchantFeeRateEntities
     * @return
     */
    public List<ApplyFeeRateRequest> buildRestoreApplyFeeRateRequests(
            String merchantSn, String operator, String operatorName, String remark,
            List<MerchantFeeRateEntity> merchantFeeRateEntities, ActivityFeeRateParam activityInfo) {
        // 待恢复套餐列表　key=payWay
        Map<Integer, ApplyComboDetailParam> restoreComboMap = Maps.newHashMap();
        restoreComboMap.putAll(getDefaultRestoreCombo());           //默认
        Map<Integer, ApplyComboDetailParam> configs = getMerchantDefaultComboConfig(merchantSn);
        if(configs == null || configs.isEmpty()){
            log.warn("restore config empty from pay business open: {}", merchantSn);
        }else{
            restoreComboMap.putAll(configs); // 从crm 配置的套餐
        }
        restoreComboMap.putAll(fetchRestoreCombos(merchantSn, activityInfo));  //商户层级
        // 优先活动取消后恢复套餐
        if (Objects.nonNull(activityInfo) && Objects.nonNull(activityInfo.getActivityEntity())) {
            ActivityEntity activityEntity = activityInfo.getActivityEntity();
            Map<Integer, ApplyComboDetailParam> afterCancelMap = getAfterCancelActivityRestoreCombo(activityEntity);
            restoreComboMap.putAll(afterCancelMap);
        }
        Map<Long, List<ApplyComboDetailParam>> applyFeeRateComboMap = merchantFeeRateEntities
                .stream().map(merchantFeeRateEntity -> {
                    ApplyComboDetailParam config = MapUtils.getObject(restoreComboMap, merchantFeeRateEntity.getPayWay());
                    if (Objects.isNull(config)) {
                        throw TradeManageBizException.createExc("当前待恢复套餐不存在. payWay:" + merchantFeeRateEntity.getPayWay());
                    }
                    return config;
                }).collect(Collectors.groupingBy(ApplyComboDetailParam::getComboId));
        long currentTime = System.currentTimeMillis();
        return applyFeeRateComboMap.entrySet().stream().map(entry -> {
            Long comboId = entry.getKey();
            List<ApplyComboDetailParam> comboDetailParams = entry.getValue();
            Map<String, String> applyFeeRateMap = Maps.newHashMap();
            comboDetailParams.forEach(detailParam -> applyFeeRateMap.put(detailParam.getPayWay() + "", detailParam.getFeeRate()));
            ApplyFeeRateRequest request = new ApplyFeeRateRequest();
            request.setMerchantSn(merchantSn);
            request.setAuditSn(remark);
            request.setTradeComboId(comboId);
            request.setApplyFeeRateMap(applyFeeRateMap);
            request.setOperator(operator);
            request.setOperatorName(operatorName);
            request.setApplyTimeMillis(currentTime);
            request.setApplyPartialPayway(true);
            return request;
        }).collect(Collectors.toList());
    }

    /**
     * 发送商户日志
     */
    public void sendBusinessLog(String merchantId, String operator, String operatorName, String remark, Map<String, Object> before, Map<String, Object> after) {
        sendBusinessLog(merchantId, "SPA", operator, operatorName, remark, before, after);
    }

    /**
     * 发送商户日志
     */
    public void sendBusinessLog(String merchantId, String platform, String operator, String operatorName, String remark, Map<String, Object> before, Map<String, Object> after){
        try {
            //日志中心，remark最大长度为200
            if(remark != null && remark.length() > 200){
                remark = remark.substring(0, 200);
            }
            Map<String, Object> cloneBefore = CommonUtil.cloneMap(before);
            Map<String, Object> cloneAfter = CommonUtil.cloneMap(after);

            Integer payWayCode = MapUtils.getInteger(cloneBefore, MerchantConfig.PAYWAY);
            if (cloneBefore != null) {
                cloneBefore.put(MerchantConfig.PAYWAY, covertPaydayName(payWayCode));
            }
            cloneAfter.put(MerchantConfig.PAYWAY, covertPaydayName(payWayCode));
            //　银行卡层级转换
            String bankCardFeeField = MerchantConfig.PARAMS + "." + TransactionParam.PARAMS_BANKCARD_FEE;
            convertMapLevel(cloneBefore, bankCardFeeField);
            convertMapLevel(cloneAfter, bankCardFeeField);
            businessOpLogBiz.sendFeeRateMerchantConfigBusinessLog(merchantId, platform, operator, operatorName, remark, cloneBefore, cloneAfter);
        } catch (Exception e) {
            log.error("记录 log 失败 " + merchantId, e);
        }
    }

    public String covertPaydayName(Integer payWay) {
        if (Objects.isNull(payWay)) {
            return StringUtils.EMPTY;
        }
        String payWayName = Payway.getNameByCode(payWay);
        if (Objects.isNull(payWayName)) {
            payWayName = payWay.toString();
        }
        return payWayName;
    }

    /**
     * 获取活动取消后恢复套餐
     *
     * @param activity
     * @return
     */
    private Map<Integer, ApplyComboDetailParam> getAfterCancelActivityRestoreCombo(ActivityEntity activity) {
        if (Objects.isNull(activity)) {
            return Collections.emptyMap();
        }
        ActivityRule.AfterCancelComboExt ext = activity.buildAfterCancelComboExt();
        if (Objects.isNull(ext)) {
            return Collections.emptyMap();
        }
        List<TradeComboDetailEntity> comboDetails = tradeComboDetailDao
                .selectList(TradeComboDetailQueryDalParam.builder().comboId(ext.getComboId()).build());
        if (CollectionUtils.isEmpty(comboDetails)) {
            return Collections.emptyMap();
        }
        return comboDetails.stream().map(detail -> {
            ApplyComboDetailParam combo = new ApplyComboDetailParam();
            combo.setComboId(detail.getComboId());
            combo.setPayWay(detail.getPayway());
            combo.setFeeRate(ext.getFeeRate());
            return combo;
        }).collect(Collectors.toMap(ApplyComboDetailParam::getPayWay, Function.identity()));
    }

    /**
     * 获取套餐取消后恢复套餐
     *
     * @param merchantSn 　商户号
     * @return
     */
    private Map<Integer, ApplyComboDetailParam> fetchRestoreCombos(String merchantSn, ActivityFeeRateParam activityInfo) {
        Map<String, Object> merchantDo = tradeCommonService.getMerchantBySn(merchantSn);
        if (Objects.isNull(merchantDo)) {
            throw TradeManageBizException.createExc("商户不存在. merchantSn:" + merchantSn);
        }
        Long activityId = null;
        if (Objects.nonNull(activityInfo) && Objects.nonNull(activityInfo.getActivityEntity())) {
            activityId = activityInfo.getActivityEntity().getId();
        }
        String industryId = MapUtils.getString(merchantDo, Merchant.INDUSTRY);
        String merchantId = MapUtils.getString(merchantDo, DaoConstants.ID);
        String originPath = tradeCommonService.findCustomerRelationOriginPath(merchantId, commonApolloConfig.getCrmConfigCodeForFindOrganization());
        RestoreActivityComboConfig.RestoreCombo restoreCombo = commonApolloConfig.getRestoreActivityCombo(RestoreActivityComboConfig.RestoreRule.builder()
                .industryIds(ImmutableList.of(industryId))
                .organization(originPath)
                .activityId(activityId)
                .build());
        return buildRestoreCombo(restoreCombo);
    }

    /**
     * 默认恢复套餐
     * @return
     */
    private Map<Integer, ApplyComboDetailParam> getDefaultRestoreCombo() {
        RestoreActivityComboConfig.RestoreCombo restoreCombo = commonApolloConfig.getDefaultRestoreActivityCombo();
        return buildRestoreCombo(restoreCombo);
    }

    /**
     * 根据套餐明细构建恢复套餐
     *
     * @param restoreCombo
     * @return
     */
    private Map<Integer, ApplyComboDetailParam> buildRestoreCombo(RestoreActivityComboConfig.RestoreCombo restoreCombo) {
        if (Objects.isNull(restoreCombo)) {
            return Collections.emptyMap();
        }
        return restoreCombo.getPayWays().stream().map(payWay -> ApplyComboDetailParam.builder()
                .comboId(restoreCombo.getComboId())
                .payWay(payWay)
                .feeRate(restoreCombo.getFeeRate())
                .build()).collect(Collectors.toMap(ApplyComboDetailParam::getPayWay, Function.identity()));
    }

    /**
     * 发送通知
     *
     * @param activityInfo
     * @param merchantId
     * @param isApply
     */
    public void sendActivityNotice(ActivityFeeRateParam activityInfo, String merchantId, boolean isApply) {
        if (Objects.isNull(activityInfo) || activityInfo.isNotActivity()) {
            return;
        }
        ChangeActivityFeeRateEnum changeActivityFeeRateEnum = activityInfo.getRequest().getChangeActivityFeeRateEnum();
        //更新活动费率
        if (Objects.equals(changeActivityFeeRateEnum, ChangeActivityFeeRateEnum.MODIFY)) {
            //不做啥事
        }
        //套餐切换逻辑
        else if (Objects.equals(changeActivityFeeRateEnum, ChangeActivityFeeRateEnum.CHANGE_COMBO)) {
            sendChangeActivityComboNotice(activityInfo, merchantId, isApply);
        } else if (Objects.equals(changeActivityFeeRateEnum,  ChangeActivityFeeRateEnum.RESTORE_EFFECT_NOT_STANDARD)
        || Objects.equals(changeActivityFeeRateEnum, ChangeActivityFeeRateEnum.EFFECT_NOT_STANDARD)) {
            log.info("考核活动，走后置通知. merchantId={},changeActivityFeeRateEnum={}",  merchantId, changeActivityFeeRateEnum);
        } else {
            sendActivityNotice0(activityInfo, merchantId, isApply);
        }
    }

    /**
     * 发送考核活动通知
     *
     * @param activityInfo
     * @param merchantId
     * @param assessmentResult
     */
    public void sendTradeAssessmentActivityNotice(ActivityEntity activityInfo, String merchantId, boolean assessmentResult) {
        ActivityInfo.Notice merNotice = activityInfo.buildMerchantNotice();
        if (merNotice != null) {
            String message = assessmentResult ? merNotice.getAssessmentPass() : merNotice.getAssessmentFail();
            if (StringUtils.isNotBlank(message)) {
                ReplaceNoticeMessageParam replaceNoticeMessageParam = ReplaceNoticeMessageParam.builder()
                        .message(message)
                        .merchantId(merchantId)
                        .build();
                message = tradeCommonService.replaceNoticeMessageSymbols(replaceNoticeMessageParam);
                String templateCode = assessmentResult ? merchantApplySccessAopTemplate : merchantCancelAopTemplate;
                tradeCommonService.sendAopNotice(merchantId, Collections.singletonList("TERMINALAPP"), aopDevCode, templateCode, null, MapUtil.hashMap("notice", message));
            }
        }
        ActivityInfo.Notice saleNotice = activityInfo.buildSaleNotice();
        if (saleNotice != null) {
            String message = assessmentResult ? saleNotice.getAssessmentPass() : saleNotice.getAssessmentFail();
            if (StringUtils.isNotBlank(message)) {
                String templateCode = assessmentResult ? saleApplySuccessAopTemplate : saleCancelAopTemplate;
                String bdUserId = tradeCommonService.getBdUserIdFromCrm(merchantId, commonApolloConfig.getCrmConfigCodeForFindSeller());
                ReplaceNoticeMessageParam replaceNoticeMessageParam = ReplaceNoticeMessageParam.builder()
                        .message(message)
                        .merchantId(merchantId)
                        .build();
                message = tradeCommonService.replaceNoticeMessageSymbols(replaceNoticeMessageParam);
                tradeCommonService.sentCrmNotice(bdUserId, Collections.singletonList("TERMINALCRM"), aopDevCode, templateCode, null, MapUtil.hashMap("notice", message));
            }
        }
    }

    /**
     * 发送活动通知
     *
     * @param activityInfo 活动信息
     * @param merchantId   商户id
     * @param isApply      生效或者失效
     */
    private void sendActivityNotice0(ActivityFeeRateParam activityInfo, String merchantId, boolean isApply) {
        if (activityInfo == null) {
            return;
        }
        ActivityInfo.Notice merNotice = activityInfo.getActivityEntity().buildMerchantNotice();
        if (merNotice != null) {
            String message = isApply ? merNotice.getApplySuccess() : merNotice.getApplyCancel();
            if (StringUtils.isNotBlank(message)) {
                ReplaceNoticeMessageParam replaceNoticeMessageParam = ReplaceNoticeMessageParam.builder()
                        .message(message)
                        .merchantId(merchantId)
                        .build();
                message = tradeCommonService.replaceNoticeMessageSymbols(replaceNoticeMessageParam);
                String templateCode = isApply ? merchantApplySccessAopTemplate : merchantCancelAopTemplate;
                tradeCommonService.sendAopNotice(merchantId, Collections.singletonList("TERMINALAPP"), aopDevCode, templateCode, null, MapUtil.hashMap("notice", message));
            }
        }
        ActivityInfo.Notice saleNotice = activityInfo.getActivityEntity().buildSaleNotice();
        if (saleNotice != null) {
            String message = isApply ? saleNotice.getApplySuccess() : saleNotice.getApplyCancel();
            if (StringUtils.isNotBlank(message)) {
                String templateCode = isApply ? saleApplySuccessAopTemplate : saleCancelAopTemplate;
                String bdUserId = tradeCommonService.getBdUserIdFromCrm(merchantId, commonApolloConfig.getCrmConfigCodeForFindSeller());
                ReplaceNoticeMessageParam replaceNoticeMessageParam = ReplaceNoticeMessageParam.builder()
                        .message(message)
                        .merchantId(merchantId)
                        .build();
                message = tradeCommonService.replaceNoticeMessageSymbols(replaceNoticeMessageParam);
                tradeCommonService.sentCrmNotice(bdUserId, Collections.singletonList("TERMINALCRM"), aopDevCode, templateCode, null, MapUtil.hashMap("notice", message));
            }
        }
    }

    /**
     * 发送切换活动套餐通知
     *
     * @param activityInfo 活动信息
     * @param merchantId   商户id
     * @param isApply      生效或者失效
     */
    private void sendChangeActivityComboNotice(ActivityFeeRateParam activityInfo, String merchantId, boolean isApply) {
        if (Objects.isNull(activityInfo) || activityInfo.isNotActivity()) {
            return;
        }
        // 切换活动套餐取消逻辑时不通知
        if (!isApply) {
            return;
        }
        TradeComboEntity comboDo = activityInfo.getRequest().getTradeComboEntity();
        TradeComboEntity.TakeEffectNotice effectNotice = comboDo.buildTakeEffectNotice();
        // 切换套餐通知为空不通知
        if (Objects.isNull(effectNotice)) {
            return;
        }
        if (StringUtils.isNotEmpty(effectNotice.getMerchant())) {
            String message = effectNotice.getMerchant();
            String templateCode = merchantChangeSuccessAopTemplate;
            tradeCommonService.sendAopNotice(merchantId, Collections.singletonList("TERMINALAPP"), aopDevCode, templateCode, null, MapUtil.hashMap("notice", message));
        }
        if (StringUtils.isNotEmpty(effectNotice.getSale())) {
            String message = effectNotice.getSale();
            String templateCode = saleChangeSuccessAopTemplate;
            String bdUserId = tradeCommonService.getBdUserIdFromCrm(merchantId, commonApolloConfig.getCrmConfigCodeForFindSeller());
            ReplaceNoticeMessageParam replaceNoticeMessageParam = ReplaceNoticeMessageParam.builder()
                    .message(message)
                    .merchantId(merchantId)
                    .build();
            message = tradeCommonService.replaceNoticeMessageSymbols(replaceNoticeMessageParam);
            tradeCommonService.sentCrmNotice(bdUserId, Collections.singletonList("TERMINALCRM"), aopDevCode, templateCode, null, MapUtil.hashMap("notice", message));
        }
    }


    private void buildUpdatesWhenCrmFeeRateUpdate(Integer payWay, Integer subPayWay, String key, String feeRate,
                                                  Map<String, String> feeRateTags, Map<Long, TradeComboDetailResult> cache,
                                                  Map<String, Object> merchantConfigUpdate, Map<Long, String> comboMerchantFeeRates,
                                                  String relationOriginId) {
        if (feeRate == null) {
            return;
        }
        if (feeRate != null && !FeeRateUtils.isValidFeeRate(feeRate)) {
            TradeManageBizException.createExc("费率格式不合法");
        }
        double rate = Double.parseDouble(feeRate);
        String tag = MapUtil.getString(feeRateTags, subPayWay + "");
        Long comboId = FeeRateUtils.getComboIdFromFeeRateTag(tag);
        if(comboId == null){
            TradeManageBizException.createExc("当前没有生效费率套餐，不允许修改");
        }
        TradeComboDetailResult detail = cache.get(comboId);
        if(detail == null){
            List<TradeComboDetailResult> details = tradeComboDetailService.listByComboId(comboId);
            detail = details.stream().filter(r -> r.getPayway() == payWay).findAny().orElseGet(null);
            //计算费率套餐 crm, base
            calcFeeRateAndUpdateDetail(relationOriginId, comboId, detail);
            cache.put(comboId, detail);
        }
        if(detail == null){
            TradeManageBizException.createExc("当前套餐不包含此支付方式，不允许修改");
        }
        if(detail.getLadderFeeRates() != null && !detail.getLadderFeeRates().isEmpty()){
            TradeManageBizException.createExc("当前生效的是阶梯费率，不允许修改");
        }
        if(rate < Double.parseDouble(detail.getFeeRateMin()) || rate > Double.parseDouble(detail.getFeeRateMax())){
            TradeManageBizException.createExc(String.format("费率超出可以允许修改的范围 %s-%s，不允许修改", detail.getFeeRateMin(), detail.getFeeRateMax()));
        }
        //可以更新，写入值
        merchantConfigUpdate.put(key, feeRate);
        comboMerchantFeeRates.put(comboId, feeRate);
    }

    /**
     * 计算费率套餐
     *
     * @param relationOriginId  组织ID
     * @param comboId           套餐Id
     * @param detail
     */
    private void calcFeeRateAndUpdateDetail(String relationOriginId, Long comboId, TradeComboDetailResult detail) {
        if (Objects.isNull(detail) || Objects.isNull(comboId) || Objects.isNull(relationOriginId)) {
            return;
        }
        OrgCommonConfigResp resp = orgCommonConfigService
                .getByOrgIdAndBizAndBizIdUp(relationOriginId, "费率套餐", comboId + "");
        if (Objects.isNull(resp) || CollectionUtils.isEmpty(resp.getOrgCommonConfigs())) {
            return;
        }
        OrgCommonConfig config = resp.getOrgCommonConfigs().get(0);
        List<Map<String, Object>> comboDetails = (List<Map<String, Object>>) MapUtil.getObject((Map<String, Object>)config.getBizValue(), "combo_details");
        if (CollectionUtils.isEmpty(comboDetails)) {
            return;
        }
        Optional<Map<String, Object>> comboDetailOpt = comboDetails.stream()
                .filter(d -> Objects.equals(detail.getId(), MapUtils.getLong(d, DaoConstants.ID)))
                .findAny();
        if (!comboDetailOpt.isPresent()) {
            return;
        }
        double feeRateMin = Double.parseDouble(detail.getFeeRateMin());
        double feeRateMax = Double.parseDouble(detail.getFeeRateMax());

        Map<String, Object> comboDetail = comboDetailOpt.get();
        double orgFeeRateMin = NumberUtils.toDouble(MapUtils.getString(comboDetail, "fee_rate_min_value"), feeRateMin);
        double orgFeeRateMax = NumberUtils.toDouble(MapUtils.getString(comboDetail, "fee_rate_max_value"), feeRateMax);
        //取基础配配置区间内
        if (orgFeeRateMin < feeRateMin) {
            orgFeeRateMin = feeRateMin;
        }
        if (orgFeeRateMax > feeRateMax) {
            orgFeeRateMax = feeRateMax;
        }
        //最后更新detail
        detail.setFeeRateMin(String.valueOf(orgFeeRateMin));
        detail.setFeeRateMax(String.valueOf(orgFeeRateMax));
    }


    private void convertMapLevel(Map<String, Object> map, String key) {
        if (MapUtils.isEmpty(map)) {
            return;
        }
        try {
            Object value = BeanUtil.getNestedProperty(map, key);
            if (Objects.nonNull(value)) {
                map.put(key, value);
            }
        } catch (Exception e) {
            log.error("convertMapLevel failed. map:{}, key:{}", JsonUtil.encode(map), key, e);
        }
    }
}

package com.wosai.trade.impl;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.middleware.hera.toolkit.trace.Trace;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pay.common.base.log.OperationLogRequest;
import com.wosai.shouqianba.withdrawservice.service.WithdrawService;
import com.wosai.sp.business.logstash.dto.ValidList;
import com.wosai.sp.business.logstash.dto.req.BsOpLogCreateReqDto;
import com.wosai.trade.biz.businesslog.BusinessOpLogBiz;
import com.wosai.trade.biz.businesslog.TemplateCodeEnum;
import com.wosai.trade.config.apollo.UpayWalletConfig;
import com.wosai.trade.constant.BusinessLogConstant;
import com.wosai.trade.service.RiskManageService;
import com.wosai.trade.service.exception.TradeManageBizException;
import com.wosai.trade.service.request.FreezeBalanceByRiskRequest;
import com.wosai.trade.service.request.UnfreezeBalanceByRiskRequest;
import com.wosai.trade.service.result.FreezeBalanceByRiskResponse;
import com.wosai.trade.service.result.UnfreezeBalanceByRiskResponse;
import com.wosai.trade.util.JsonUtil;
import com.wosai.trade.util.TraceUtil;
import com.wosai.upay.clearance.constant.RequestConstant;
import com.wosai.upay.clearance.model.Withdraw;
import com.wosai.upay.clearance.service.ClearanceService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.wallet.constant.ProviderWalletAccountTypeEnum;
import com.wosai.upay.wallet.model.EventLog;
import com.wosai.upay.wallet.request.GetFrozenOrderReq;
import com.wosai.upay.wallet.request.IsFinishedReq;
import com.wosai.upay.wallet.request.WalletQueryReq;
import com.wosai.upay.wallet.request.v3.FreezeBalanceReqV3;
import com.wosai.upay.wallet.request.v3.UnfreezeBalanceReqV3;
import com.wosai.upay.wallet.resp.FrozenOrder;
import com.wosai.upay.wallet.service.FreezeServiceV3;
import com.wosai.upay.wallet.service.WalletServiceV3;
import com.wosai.upay.wallet.util.SignUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.wosai.trade.constant.AcquirerConstants.getClearanceProviderName;
import static com.wosai.trade.model.constant.ManageConstant.UTF8;
import static com.wosai.trade.service.exception.enums.TradeManageRespCodeEnum.ILLEGAL_ARGUMENT;

/**
 * <AUTHOR>
 * @description 风控管理服务
 * @date 2025-08-07
 */
@Slf4j
@Service
@Validated
@AutoJsonRpcServiceImpl
public class RiskManageServiceImpl implements RiskManageService {
    private static final String DEFAULT_DRAWBACK_REMARK = "风控系统延迟提现";

    private static final String RISK_FREEZE_AMOUNT_DESC_FORMAT = "需延迟结算%s元，已延迟结算%s元";
    private static final String RISK_UNFREEZE_AMOUNT_DESC_FORMAT = "需解除延迟结算%s元";

    @Autowired
    private WalletServiceV3 walletService;

    @Resource
    private BusinessOpLogBiz businessOpLogBiz;

    @Resource
    private FreezeServiceV3 freezeService;

    @Resource
    private WithdrawService withdrawService;

    @Resource
    private UpayWalletConfig upayWalletConfig;

    @Resource
    private ClearanceService clearanceService;

    //可以驳回的结算状态列表
    private static final List<Long> CAN_DRAWBACK_OP_CHACK_STATUS_LIST = Arrays.asList(
            Withdraw.OP_CHECK_STATUS_CREATED,//待审核
            Withdraw.OP_CHECK_STATUS_OPERATOR_PASS//审核通过，待打款
    );

    /**
     * 查询指定收单机构下的余额
     *
     * @param merchantId
     * @param provider
     * @return
     */
    private long getProviderBalance(String merchantId, int provider, Integer walletAccountType) {
        WalletQueryReq walletQueryReq = new WalletQueryReq();
        walletQueryReq.setMerchantId(merchantId);
        walletQueryReq.setClearanceProvider(provider);
        walletQueryReq.setAccountType(walletAccountType);
        return walletService.getWalletBalance(walletQueryReq);
    }

    @Override
    @Trace
    public FreezeBalanceByRiskResponse freezeBalanceByRisk(FreezeBalanceByRiskRequest request) {
        OperationLogRequest logRequest = request.getLogRequest();
        checkOperationLogRequest(logRequest);

        int walletAccountType = request.getWalletAccountType();

        FreezeBalanceByRiskResponse result = new FreezeBalanceByRiskResponse();
        int clearanceProvider = request.getClearanceProvider();
        try {
            String merchantId = request.getMerchantId();
            long freezeAmount = request.getFreezeAmount();
            long balance = getProviderBalance(merchantId, clearanceProvider, walletAccountType);
            log.info("风控冻结余额: merchantId={}, 商户当前余额={}, 冻结金额={}", merchantId, balance, freezeAmount);

            List<String> withdrawIds = new ArrayList<>(10);
            if (balance < freezeAmount) {
                // 商户的余额小于冻结金额，则先将已发起提现但尚未报送收单机构的结算做失败退回余额
                ListResult results = withdrawService.findWithdraws(null, CollectionUtil.hashMap(
                        Withdraw.MERCHANT_ID, merchantId,
                        Withdraw.PROVIDER, clearanceProvider,
                        RequestConstant.OP_CHECK_STATUSES, CAN_DRAWBACK_OP_CHACK_STATUS_LIST
                ));
                if (results != null && results.getTotal() != 0) {
                    log.info("风控冻结余额: 需要驳回的结算记录个数={}, merchantId={}", results.getTotal(), merchantId);
                    for (Map withdraw : results.getRecords()) {
                        String withdrawId = BeanUtil.getPropString(withdraw, DaoConstants.ID);
                        int withdrawAmount = BeanUtil.getPropInt(withdraw, Withdraw.AMOUNT);

                        int accountType = BeanUtil.getPropInt(withdraw, Withdraw.EXTRA_PARAMS_WALLET_ACCOUNT_TYPE, ProviderWalletAccountTypeEnum.DEFAULT.getValue());
                        if (accountType != walletAccountType) {
                            log.warn("风控冻结余额: 该结算记录的余额类型不匹配, 无须驳回, merchantId={}, accountType={}", merchantId, accountType);
                            continue;
                        }
                        balance += withdrawAmount;
                        try {
                            withdrawService.withdrawOperatorBack(withdrawId, DEFAULT_DRAWBACK_REMARK, logRequest.getOperatorUserName());
                            withdrawIds.add(withdrawId);
                        } catch (Exception e) {
                            log.error("fail to withdrawOperatorBack, withdrawId:{}, error: {}", withdrawId, e.getMessage());
                        }
                        if (balance >= freezeAmount) {
                            break;
                        }
                    }

                    //等待结算记录驳回完成
                    for (String withdrawId : withdrawIds) {
                        log.info("风控冻结余额: 等待结算记录驳回余额完成, withdrawId={}, merchantId={}", withdrawId, merchantId);
                        waitForFreezeFinished(merchantId, withdrawId, 5);
                    }
                }
            }

            // 计算本次操作可直接冻结的金额
            balance = getProviderBalance(merchantId, clearanceProvider, walletAccountType);
            if (!withdrawIds.isEmpty()) {
                log.info("风控冻结余额: 结算记录驳回后的商户余额={}, merchantId={}", balance, merchantId);
            }
            long currentFreezeAmount = Math.min(balance, freezeAmount);

            //冻结
            freezeBalance(request, freezeAmount);
            result.setSuccess(true);
            result.setFreezeActionId(request.getActionId());
            result.setActualFreezeAmount(currentFreezeAmount);
            result.setWithdrawIds(withdrawIds);

            //记录商户日志
            String freezeDesc = String.format(RISK_FREEZE_AMOUNT_DESC_FORMAT, StringUtils.cents2yuan(freezeAmount), StringUtils.cents2yuan(currentFreezeAmount));
            Map<String, Object> before = CollectionUtil.hashMap(BusinessLogConstant.FREEZE_AMOUNT, null,
                    BusinessLogConstant.CLEARANCE_PROVIDER, getClearanceProviderName(request.getClearanceProvider()));
            Map<String, Object> after = CollectionUtil.hashMap(BusinessLogConstant.FREEZE_AMOUNT, freezeDesc,
                    BusinessLogConstant.CLEARANCE_PROVIDER, getClearanceProviderName(request.getClearanceProvider()));
            ValidList<BsOpLogCreateReqDto.Diff> diffValidList =
                    businessOpLogBiz.buildValidList(TemplateCodeEnum.RISK_FREEZE_WALLET_CONFIG, before, after);
            String sameSaveColumnCode = TemplateCodeEnum.RISK_FREEZE_WALLET_CONFIG.buildFieldFullName(BusinessLogConstant.CLEARANCE_PROVIDER);
            businessOpLogBiz.createOuterBusinessLogForAsync(logRequest, diffValidList, sameSaveColumnCode);
            log.info("风控冻结余额: 冻结完成, merchantId={}, result={}", merchantId, JsonUtil.encode(result));
        } catch (Exception e) {
            log.error("风控冻结余额失败, merchantId: {}, amount: {}, error: {}",
                    request.getMerchantId(), request.getFreezeAmount(), e.getMessage(), e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
        }

        return result;
    }

    @Override
    public UnfreezeBalanceByRiskResponse unfreezeBalanceByRisk(UnfreezeBalanceByRiskRequest request) {
        OperationLogRequest logRequest = request.getLogRequest();
        checkOperationLogRequest(logRequest);
        UnfreezeBalanceByRiskResponse result = new UnfreezeBalanceByRiskResponse();
        
        try {
            String merchantId = request.getMerchantId();

            unfreezeBalance(request);

            // 等待解冻完成
            isUnfreezeFinished(merchantId, request.getFreezeAccountId(), 5);

            GetFrozenOrderReq frozenOrderReq = new GetFrozenOrderReq();
            frozenOrderReq.setMerchantId(merchantId);
            frozenOrderReq.setActionId(request.getFreezeAccountId());
            FrozenOrder frozenOrder = freezeService.getFrozenOrder(frozenOrderReq);
            long actualUnfreezeAmount = request.getUnfreezeAmount() - frozenOrder.getAmount();
            result.setSuccess(true);
            result.setActualUnfreezeAmount(actualUnfreezeAmount);

            //记录商户日志
            String unfreezeDesc = String.format(RISK_UNFREEZE_AMOUNT_DESC_FORMAT, StringUtils.cents2yuan(request.getUnfreezeAmount()));
            Map<String, Object> before = CollectionUtil.hashMap(BusinessLogConstant.UNFREEZE_AMOUNT, null,
                    BusinessLogConstant.CLEARANCE_PROVIDER, getClearanceProviderName(request.getClearanceProvider()));
            Map<String, Object> after = CollectionUtil.hashMap(BusinessLogConstant.UNFREEZE_AMOUNT, unfreezeDesc,
                    BusinessLogConstant.CLEARANCE_PROVIDER, getClearanceProviderName(request.getClearanceProvider()));
            ValidList<BsOpLogCreateReqDto.Diff> diffValidList =
                    businessOpLogBiz.buildValidList(TemplateCodeEnum.RISK_UNFREEZE_WALLET_CONFIG, before, after);
            String sameSaveColumnCode = TemplateCodeEnum.RISK_FREEZE_WALLET_CONFIG.buildFieldFullName(BusinessLogConstant.CLEARANCE_PROVIDER);
            businessOpLogBiz.createOuterBusinessLogForAsync(logRequest, diffValidList, sameSaveColumnCode);
            log.info("风控解冻余额: 解冻完成, merchantId={}, result={}", merchantId, JsonUtil.encode(result));
        } catch (Exception e) {
            log.error("解冻余额失败, merchantId: {}, amount: {}, error: {}",
                    request.getMerchantId(), request.getUnfreezeAmount(), e.getMessage());
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
        }

        return result;
    }

    @Override
    @SuppressWarnings("unchecked")
    public void delayAllMerchantD1Withdraw(String merchantId, OperationLogRequest logRequest) {
        checkOperationLogRequest(logRequest);
        clearanceService.delayAllMerchantD1Withdraw(merchantId);
        //记录商户日志
        Map<String, Object> before = CollectionUtil.hashMap(BusinessLogConstant.DELAY_WITHDRAW, null);
        Map<String, Object> after = CollectionUtil.hashMap(BusinessLogConstant.DELAY_WITHDRAW, "延迟");
        ValidList<BsOpLogCreateReqDto.Diff> diffValidList =
                businessOpLogBiz.buildValidList(TemplateCodeEnum.RISK_DELAY_WITHDRAW_CONFIG, before, after);
        businessOpLogBiz.createOuterBusinessLogForAsync(logRequest, diffValidList, null);
    }

    @Override
    @SuppressWarnings("unchecked")
    public void cancelDelayAllMerchantD1Withdraw(String merchantId, OperationLogRequest logRequest) {
        checkOperationLogRequest(logRequest);
        clearanceService.cancelDelayAllMerchantD1Withdraw(merchantId);

        //记录商户日志
        Map<String, Object> before = CollectionUtil.hashMap(BusinessLogConstant.DELAY_WITHDRAW, "延迟");
        Map<String, Object> after = CollectionUtil.hashMap(BusinessLogConstant.DELAY_WITHDRAW, "取消延迟");
        ValidList<BsOpLogCreateReqDto.Diff> diffValidList =
                businessOpLogBiz.buildValidList(TemplateCodeEnum.RISK_CANCEL_DELAY_WITHDRAW_CONFIG, before, after);
        businessOpLogBiz.createOuterBusinessLogForAsync(logRequest, diffValidList, null);
    }

    /**
     * 检查商户日志请求数据
     *
     * @param logRequest
     */
    private void checkOperationLogRequest(OperationLogRequest logRequest) {
        if (null == logRequest) {
            throw new TradeManageBizException(ILLEGAL_ARGUMENT.getCode(), "OperationLogRequest不能为空");
        }
        if (org.apache.commons.lang3.StringUtils.isAnyBlank(logRequest.getSceneTemplateCode(),
                logRequest.getRemark(),
                logRequest.getOperatorUserName(),
                logRequest.getOperatorUserId(),
                logRequest.getOpObjectId(),
                logRequest.getPlatformCode(),
                logRequest.getSceneTemplateCode()
        )) {
            throw new TradeManageBizException(ILLEGAL_ARGUMENT.getCode(), "OperationLogRequest的参数无效");
        }
    }

    /**
     * 冻结余额
     *
     * @param request
     * @param amount
     * @return
     */
    private void freezeBalance(FreezeBalanceByRiskRequest request, Long amount) {
        FreezeBalanceReqV3 freezeBalanceReq = new FreezeBalanceReqV3();
        freezeBalanceReq.setMerchantId(request.getMerchantId());
        freezeBalanceReq.setType(request.getFrozenType());
        freezeBalanceReq.setActionId(request.getActionId());
        freezeBalanceReq.setAmount(amount);
        freezeBalanceReq.setExpireTime(null);
        freezeBalanceReq.setRemark(request.getRemark());
        freezeBalanceReq.setReason(request.getReason());
        freezeBalanceReq.setNonceStr(TraceUtil.generateUUID());
        freezeBalanceReq.setAccountType(request.getWalletAccountType());
        freezeBalanceReq.setClearanceProvider(request.getClearanceProvider());
        freezeBalanceReq.setAppId(upayWalletConfig.getAppId());

        String sign = SignUtil.getSign(freezeBalanceReq.toMap(), upayWalletConfig.getAppKey(), UTF8);
        freezeBalanceReq.setSign(sign);
        freezeService.freezeBalance(freezeBalanceReq);
    }

    /**
     * 解冻余额
     *
     * @param request
     */
    private void unfreezeBalance(UnfreezeBalanceByRiskRequest request) {
        UnfreezeBalanceReqV3 unfreezeBalanceReq = new UnfreezeBalanceReqV3();
        unfreezeBalanceReq.setMerchantId(request.getMerchantId());
        unfreezeBalanceReq.setClearanceProvider(request.getClearanceProvider());
        unfreezeBalanceReq.setAccountType(request.getWalletAccountType());
        unfreezeBalanceReq.setType(request.getFrozenType());
        unfreezeBalanceReq.setFreezeActionId(request.getFreezeAccountId());
        unfreezeBalanceReq.setActionId(request.getUnfreezeAccountId());
        unfreezeBalanceReq.setUnfreezeRemaining(request.getUnfreezeRemaining());
        unfreezeBalanceReq.setAmount(request.getUnfreezeAmount());
        unfreezeBalanceReq.setNonceStr(TraceUtil.generateUUID());
        unfreezeBalanceReq.setAppId(upayWalletConfig.getAppId());
        String sign = SignUtil.getSign(unfreezeBalanceReq.toMap(), upayWalletConfig.getAppKey(), UTF8);
        unfreezeBalanceReq.setSign(sign);
        freezeService.unfreezeBalance(unfreezeBalanceReq);
    }

    /**
     * 余额解冻完成
     *
     * @param merchantId
     * @param actionId
     * @param retryCount
     * @return
     */
    public boolean isUnfreezeFinished(String merchantId, String actionId, int retryCount) {
        boolean isFinished = false;
        long count = retryCount;
        try {
            while (count >= 0L) {
                count--;
                isFinished = walletService.isFinished(new IsFinishedReq(merchantId, actionId, null));
                if (isFinished) {
                    break;
                }
                TimeUnit.SECONDS.sleep(NumberUtils.LONG_ONE);
            }
        } catch (Exception e) {
            log.error("余额是否扣减完成异常, merchantId={},actionId={},retryCount={}",
                    merchantId, actionId, retryCount, e);
        }
        return isFinished;
    }

    private void waitForFreezeFinished(String merchantId, String withdrawId, int retryCount) {
        long count = retryCount;
        try {
            while (count >= 0) {
                count--;
                boolean isFinished = walletService.isFinished(new IsFinishedReq(merchantId, withdrawId, EventLog.TYPE_DRAW_BACK));
                if (isFinished) {
                    log.info("风控冻结余额: 结算记录驳回余额已完成, withdrawId={}, merchantId={}", withdrawId, merchantId);
                    break;
                }
                Thread.sleep(200);
            }
        } catch (Exception e) {
            log.error("风控冻结余额: 发送异常, withdrawId={}, error={}", withdrawId, e.getMessage(), e);
        }
    }
}
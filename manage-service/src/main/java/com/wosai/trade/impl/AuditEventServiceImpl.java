package com.wosai.trade.impl;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.databus.event.audit.AuditInstanceCreateEvent;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.profit.sharing.model.MerchantSharingOpenApply;
import com.wosai.profit.sharing.model.RelationInfo;
import com.wosai.profit.sharing.model.request.BatchApplyAddReceiverReq;
import com.wosai.profit.sharing.model.request.BatchApplyOpenSharingReq;
import com.wosai.profit.sharing.model.request.IsSharingOpenedReq;
import com.wosai.profit.sharing.model.request.QuerySharingApplyReq;
import com.wosai.profit.sharing.model.response.BatchApplyAddReceiverResp;
import com.wosai.profit.sharing.model.response.BatchApplyOpenSharingResp;
import com.wosai.profit.sharing.model.response.SharingApplyDto;
import com.wosai.profit.sharing.service.SharingOpenService;
import com.wosai.service.IMerchantGrayService;
import com.wosai.service.PaywayActivityService;
import com.wosai.service.enumeration.AccessTokenType;
import com.wosai.trade.biz.activity.AdjustPaySourceActivityFeeRateBiz;
import com.wosai.trade.biz.activity.ApplyActivityCheckBiz;
import com.wosai.trade.biz.activity.AuditHandleBiz;
import com.wosai.trade.biz.activity.ModifyActivityComboFeeRateBiz;
import com.wosai.trade.biz.activity.converter.ApplyActivityBuildParams;
import com.wosai.trade.biz.audit.AuditActionTypeEnum;
import com.wosai.trade.biz.audit.MchBankFeeRateBiz;
import com.wosai.trade.biz.withdraw.FastWithdrawPlanBiz;
import com.wosai.trade.constant.AcquirerConstants;
import com.wosai.trade.constant.MerchantGrayConstant;
import com.wosai.trade.constant.enums.ApplyActivityCheckSceneEnum;
import com.wosai.trade.repository.ActivityRepository;
import com.wosai.trade.repository.ApplyAuditRecordRepository;
import com.wosai.trade.repository.dao.SharingAuditTaskDao;
import com.wosai.trade.repository.dao.entity.ActivityEntity;
import com.wosai.trade.repository.dao.entity.ApplyAuditRecordEntity;
import com.wosai.trade.repository.dao.entity.SharingAuditTaskEntity;
import com.wosai.trade.service.AuditEventService;
import com.wosai.trade.service.activity.request.ApplyActivityRequest;
import com.wosai.trade.service.constant.SharingAuditConstant;
import com.wosai.trade.service.exception.TradeManageBizException;
import com.wosai.trade.service.result.ApplyAuditRecordResult;
import com.wosai.trade.util.AuditGetParamUtil;
import com.wosai.trade.util.JsonUtil;
import com.wosai.trade.util.ValidationUtils;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.common.util.JacksonUtil;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.MerchantConfigCustom;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.TradeConfigService;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
@AutoJsonRpcServiceImpl
public class AuditEventServiceImpl implements AuditEventService {

    @Autowired
    MerchantService merchantService;

    @Autowired
    TradeConfigService tradeConfigService;
    @Resource
    MchBankFeeRateBiz mchBankFeeRateBiz;
    @Resource
    private FastWithdrawPlanBiz fastWithdrawPlanBiz;
    @Resource
    private AdjustPaySourceActivityFeeRateBiz adjustPaySourceActivityFeeRateBiz;
    @Resource
    private ModifyActivityComboFeeRateBiz modifyActivityComboFeeRateBiz;
    @Resource
    private ApplyActivityCheckBiz applyActivityCheckBiz;
    @Resource
    private ApplyActivityBuildParams applyActivityBuildParams;
    @Autowired
    private SharingAuditTaskDao sharingAuditTaskDao;
    @Autowired
    private SharingOpenService sharingOpenService;
    @Autowired
    ApplyAuditRecordRepository applyAuditRecordRepository;
    @Autowired
    TradeCommonService tradeCommonService;
    @Resource
    private IMerchantGrayService iMerchantGrayService;
    @Resource
    private AuditHandleBiz auditHandleBiz;
    @Resource
    private ActivityRepository activityRepository;
    @Resource
    private PaywayActivityService paywayActivityService;

    @Override
    public void alipayUniversityActivity(Map<String, Object> request) {
        String mainMerchantSn = MapUtils.getString(request, "merchant_sn");//主体商户号
        if (mainMerchantSn == null) {
            throw TradeManageBizException.createExc("主体商户号不可为空");
        }

        Map mainMerchant = merchantService.getMerchantByMerchantSn(mainMerchantSn);
        if (mainMerchant == null || mainMerchant.isEmpty()) {
            throw TradeManageBizException.createExc("主体商户不存在。");
        }

        String mainMerchantId = MapUtils.getString(mainMerchant, DaoConstants.ID);
        Map merchantConfigCustom = tradeConfigService.getMerchantConfigCustomByMerchantIdAndStoreIdAndType(mainMerchantId, null, MerchantConfigCustom.TYPE_ALIPAY_STORE_ID);
        String b2cValue = MapUtils.getString(merchantConfigCustom, MerchantConfigCustom.B2C_VALUE);
        if (b2cValue == null) {
            throw TradeManageBizException.createExc("企业支付宝账号未提交审批「支付宝直连食堂奖励活动报名(非高校主体)」，请申请通过后重新提交本审批。");
        }
    }

    /**
     * 银行卡收款 审批校验
     */
    @Override
    public void bankCheck(Map<String, Object> request) {
        mchBankFeeRateBiz.verifyCreateParam(request);
    }

    /**
     * 刷卡活动报名申请　审批校验
     *
     * @param request
     */
    @Override
    public void verifyApplyActivityByChannelFeeRate(Map<String, Object> request) {
        applyActivityBuildParams.buildApplyActivityByChannelFeeRate(request);
    }

    @Override
    public void modifyPaySourceFeeRate(Map<String, Object> request) {
        adjustPaySourceActivityFeeRateBiz.verifyAuditParam(request);
    }

    /**
     * 微信教培支付源活动报名前置校验
     *
     * @param businessMap
     */
    @Override
    public void preCheckWeChatEduActivityApply(Map<String, Object> businessMap) {
        // 包装审批事件
        AuditInstanceCreateEvent event = new AuditInstanceCreateEvent();
        event.setBusinessMap(businessMap);
        event.setAuditId("0");
        event.setAuditSn("前置校验");
        event.setOperatorName("前置校验");
        // 构建活动报名请求
        ApplyActivityRequest request = auditHandleBiz.buildPaySourceApplyActivityRequest(event);
        // 查询活动
        ActivityEntity activityEntity = activityRepository.selectByPrimaryKey(request.getActivityId());
        // 基础校验
        applyActivityCheckBiz.applyBaseCheck(request);
        // 业务校验
        applyActivityCheckBiz.applyBizCheck(request, activityEntity, ApplyActivityCheckSceneEnum.APPLY);
        // 进件侧校验拦截
        paywayActivityService.preCheckForWechatEdu(businessMap);
    }

    @Override
    public void verifyModifyActivityFeeRate(Map<String, Object> request) {
        modifyActivityComboFeeRateBiz.verify(ModifyActivityComboFeeRateBiz.ChangeActivityComboParam.build("verify", request));
    }

    @Override
    public void verifyCancelApplyActivityFeeRate(Map<String, Object> request) {
        String action = WosaiMapUtils.getString(request, "action");
        String cancelType = WosaiMapUtils.getString(request, "cancel_merchant");
        AuditActionTypeEnum actionEnum = AuditActionTypeEnum.convert(action);

        if (!(AuditActionTypeEnum.CANCEL.equals(actionEnum) && "single".equalsIgnoreCase(cancelType))) {
            return;
        }
        Long activityId = WosaiMapUtils.getLong(request, "activity_id");
        ValidationUtils.check(Objects.nonNull(activityId), "商户取消活动ID不能为空");
        String merchantSn = AuditGetParamUtil.getMerchantSn(request);
        ValidationUtils.check(Objects.nonNull(merchantSn), "商户SN为空");
        List<String> payList = (List<String>) BeanUtil.getProperty(request, "pay_way_list");
        if (CollectionUtils.isNotEmpty(payList)) {
            applyActivityCheckBiz.verifyCancelPayWayEffectFeeRate(merchantSn, activityId, payList);
        }
    }


    /**
     * 自定义周期快速到账，审批前置校验
     *
     * @param request
     */
    @Override
    public void verifyFastWithdrawPlan(Map<String, Object> request) {
        log.info("verifyFastWithdrawPlan: request={}", JsonUtil.encode(request));
        String actionType = MapUtils.getString(request, "actionType");

        if (StringUtils.isNotBlank(actionType)) {
            AuditActionTypeEnum actionEnum = AuditActionTypeEnum.convert(request);
            if (!AuditActionTypeEnum.APPLY.equals(actionEnum)) {
                return;
            }
        }

        String merchantSn = AuditGetParamUtil.getMerchantSn(request);
        ValidationUtils.check(Objects.nonNull(merchantSn), "商户SN为空");

        Map merchant = merchantService.getMerchantBySn(merchantSn);
        String merchantId = MapUtils.getString(merchant, DaoConstants.ID);

        StringBuilder errorMsg = new StringBuilder(256);

        //检查商户是否配置了快速到账计划
        ImmutablePair<Boolean, String> pairResult = fastWithdrawPlanBiz.checkFastWithdrawPlanExist(merchantId);
        if (pairResult.getLeft()) {
            //商户配置了快速到账计划，则不允许申请自定义对账周期d0提现
            TradeManageBizException.createExc(pairResult.getRight());
        }
    }

    @Override
    public void submitSharingOpen(Map<String, Object> request) {
        submitSharingCommonCheck(request);
        Integer organization = MapUtils.getInteger(request, SharingAuditConstant.KEY_ORGANIZATION);
        List<String> merchantSnList = (List<String>) MapUtils.getObject(request, SharingAuditConstant.KEY_PAYER_MERCHANTS);
        if (CollectionUtils.isEmpty(merchantSnList)) {
            throw new TradeManageBizException("开通分账的商户号不能为空");
        }
        Long auditId = MapUtils.getLong(request, SharingAuditConstant.KEY_AUDIT_ID);
        SharingAuditTaskEntity auditTaskEntity = sharingAuditTaskDao.selectByAuditIdWithType(auditId, SharingAuditConstant.AUDIT_TYPE_OPEN_HAIKE_SHARING);
        BatchApplyOpenSharingReq batchApplyOpenHaikeSharingReq = new BatchApplyOpenSharingReq();
        batchApplyOpenHaikeSharingReq.setMerchantSns(merchantSnList);
        if (auditTaskEntity != null) {
            if (auditTaskEntity.getStatus() == SharingAuditConstant.AUDIT_STATUS_INIT) {
                log.warn(String.format("审批单号 %s 已经处理中,忽略此次提交", auditId));
            } else {
                SharingAuditTaskEntity update = new SharingAuditTaskEntity();
                update.setId(auditTaskEntity.getId());
                //可能被驳回后回到该节点 重新进行提交 这时候需要将状态重置
                BatchApplyOpenSharingResp batchApplyOpenHaikeSharingResp = sharingOpenService.submitOpenSharing(batchApplyOpenHaikeSharingReq);
                log.info("重新提交分账开通 resp", JacksonUtil.toJsonString(batchApplyOpenHaikeSharingResp));
                update.setStatus(SharingAuditConstant.AUDIT_STATUS_INIT);
                SharingAuditTaskEntity.BizParams bizParams = new SharingAuditTaskEntity.BizParams();
                bizParams.setSubmitHaikeOpenBizTaskMap(batchApplyOpenHaikeSharingResp.getMerchantApplyTaskMap());
                update.setBizParams(bizParams.toMap());
                update.setVersion(auditTaskEntity.getVersion());
                sharingAuditTaskDao.updateStatusAndBizParams(update);
            }
        } else {
            if (organization == TradeConfigService.PROVIDER_HAIKE_UNION_PAY) {
                BatchApplyOpenSharingResp batchApplyOpenHaikeSharingResp = sharingOpenService.submitOpenSharing(batchApplyOpenHaikeSharingReq);
                log.info("提交分账开通任务成功 resp", JacksonUtil.toJsonString(batchApplyOpenHaikeSharingResp));
                SharingAuditTaskEntity sharingAuditTaskEntity = new SharingAuditTaskEntity();
                sharingAuditTaskEntity.setStatus(SharingAuditConstant.AUDIT_STATUS_INIT);
                sharingAuditTaskEntity.setAuditId(auditId);
                sharingAuditTaskEntity.setAuditType(SharingAuditConstant.AUDIT_TYPE_OPEN_HAIKE_SHARING);
                SharingAuditTaskEntity.BizParams bizParams = new SharingAuditTaskEntity.BizParams();
                bizParams.setSubmitHaikeOpenBizTaskMap(batchApplyOpenHaikeSharingResp.getMerchantApplyTaskMap());
                sharingAuditTaskEntity.setBizParams(bizParams.toMap());
                sharingAuditTaskDao.insert(sharingAuditTaskEntity);
            } else {
                throw new TradeManageBizException("不支持的收单机构");
            }
        }
    }

    @Override
    public void submitSharingRelation(Map<String, Object> request) {
        submitSharingCommonCheck(request);
        List<Map> relations = (List<Map>) MapUtils.getObject(request, SharingAuditConstant.KEY_RELATIONS);
        if (CollectionUtils.isEmpty(relations)) {
            throw new TradeManageBizException("分账关系不能为空");
        }
        List<String> notSignMerchantSns = (List<String>) MapUtils.getObject(request, SharingAuditConstant.KEY_NOT_OPEN_SHARING_MERCHANT_SNS);
        SharingAuditTaskEntity.BizParams bizParams = new SharingAuditTaskEntity.BizParams();
        Integer organization = MapUtils.getInteger(request, SharingAuditConstant.KEY_ORGANIZATION);
        Long auditId = MapUtils.getLong(request, SharingAuditConstant.KEY_AUDIT_ID);
        bizParams.setOrganization(organization);

        int auditType = SharingAuditConstant.AUDIT_TYPE_RELATION_HAIKE_SHARING;
        if (CollectionUtils.isNotEmpty(notSignMerchantSns)) {
            auditType = SharingAuditConstant.AUDIT_TYPE_CHECK_ALL_MERCHANT_SIGN_FINISH;
            bizParams.setOriginalSubmitHaikeRelationRequest(request);
            bizParams.setNotOpenSharingMerchantSns(notSignMerchantSns);
        } else {
            BatchApplyAddReceiverReq batchApplyHaikeAddReceiverReq = new BatchApplyAddReceiverReq();
            List<RelationInfo> relationInfos = new ArrayList<>();
            batchApplyHaikeAddReceiverReq.setRelations(relationInfos);
            for (Map relation : relations) {
                String payerMerchantSn = MapUtil.getString(relation, SharingAuditConstant.KEY_PAYER_MERCHANT);
                String receiverMerchantSn = MapUtil.getString(relation, SharingAuditConstant.KEY_RECEIVER_MERCHANT);
                String sharingAgreements = MapUtil.getString(relation, SharingAuditConstant.KEY_SHARING_AGREEMENTS);
                RelationInfo relationInfo = new RelationInfo();
                relationInfo.setPayerMerchantSn(payerMerchantSn);
                relationInfo.setReceiverMerchantSn(receiverMerchantSn);
                relationInfo.setAgreementFileOssKey(sharingAgreements);
                relationInfos.add(relationInfo);
            }
            bizParams.setSubmitHaikeRelationBizTaskMap(applyHaikeAddReceiver(batchApplyHaikeAddReceiverReq));
        }

        initOrUpdateAuditTask(auditId, auditType, bizParams);
    }

    @Override
    public void submitSharingCheckSign(Map<String, Object> request) {
        List<String> notSignMerchantSns = (List<String>) MapUtils.getObject(request, SharingAuditConstant.KEY_NOT_OPEN_SHARING_MERCHANT_SNS);
        SharingAuditTaskEntity.BizParams bizParams = new SharingAuditTaskEntity.BizParams();
        Integer organization = MapUtils.getInteger(request, SharingAuditConstant.KEY_ORGANIZATION);
        Long auditId = MapUtils.getLong(request, SharingAuditConstant.KEY_AUDIT_ID);
        bizParams.setOrganization(organization);
        bizParams.setNotOpenSharingMerchantSns(notSignMerchantSns);
        int auditType = SharingAuditConstant.AUDIT_TYPE_CHECK_ALL_MERCHANT_SIGN_FINISH;
        initOrUpdateAuditTask(auditId, auditType, bizParams);
    }

    @Override
    public String displaySharingOpenStatusText(Map<String, Object> request) {
        Long auditId = MapUtil.getLong(request, SharingAuditConstant.KEY_AUDIT_ID);
        if (auditId == null) {
            return Strings.EMPTY;
        }
        SharingAuditTaskEntity sharingAuditTaskEntity = sharingAuditTaskDao.selectByAuditIdWithType(auditId, SharingAuditConstant.AUDIT_TYPE_OPEN_HAIKE_SHARING);
        if (sharingAuditTaskEntity == null) {
            return Strings.EMPTY;
        }
        SharingAuditTaskEntity.BizParams bizParams = sharingAuditTaskEntity.buildBizParams();
        if (bizParams == null) {
            return Strings.EMPTY;
        }
        Map<String, String> submitHaikeOpenBizTaskMap = bizParams.getSubmitHaikeOpenBizTaskMap();
        if (MapUtil.isEmpty(submitHaikeOpenBizTaskMap)) {
            return Strings.EMPTY;
        }

        QuerySharingApplyReq queryHaikeSharingApplyReq = new QuerySharingApplyReq();
        queryHaikeSharingApplyReq.setApplyIds(new ArrayList<>(submitHaikeOpenBizTaskMap.values()));
        List<SharingApplyDto> sharingApplyDtos = sharingOpenService.queryOpenHaikeSharing(queryHaikeSharingApplyReq);
        if (CollectionUtils.isNotEmpty(sharingApplyDtos)) {
            Map<String, List<String>> unfinishedOpenSharingMerchantMap = new HashMap<>();
            for (SharingApplyDto sharingApplyDto : sharingApplyDtos) {
                String merchantId = tradeCommonService.getMerchantIdByMerchantSn(sharingApplyDto.getMerchantSn());
                IsSharingOpenedReq isSharingOpenedReq = new IsSharingOpenedReq();
                isSharingOpenedReq.setMerchantId(merchantId);
                isSharingOpenedReq.setOrganization(sharingApplyDto.getOrganization());
                boolean sharingOpened = sharingOpenService.isSharingOpened(isSharingOpenedReq);
                //代表收钱吧的确认授权没有完成
                if (!sharingOpened) {
                    List<String> waitSignList = unfinishedOpenSharingMerchantMap.getOrDefault(sharingApplyDto.getMerchantSn(), new ArrayList<>());
                    waitSignList.add("《分账技术服务声明协议》未签约");
                    unfinishedOpenSharingMerchantMap.put(sharingApplyDto.getMerchantSn(), waitSignList);
                }
            }
            List<SharingApplyDto> unfinishedSharingOpenMerchants = sharingApplyDtos.stream().filter(o -> !Objects.equals(o.getStatus(), MerchantSharingOpenApply.STATUS_SUCCESS)).collect(Collectors.toList());
            String statusDisplay = "当前审批单已提交至海科审核，提交时间（%s），时效1个工作日，请耐心等待。";
            if (CollectionUtils.isNotEmpty(unfinishedSharingOpenMerchants)) {
                for (SharingApplyDto unfinishedSharingOpenMerchant : unfinishedSharingOpenMerchants) {
                    List<String> waitSignList = unfinishedOpenSharingMerchantMap.getOrDefault(unfinishedSharingOpenMerchant.getMerchantSn(), new ArrayList<>());
                    waitSignList.add("《海科融通用户分账服务协议》未签约");
                    unfinishedOpenSharingMerchantMap.put(unfinishedSharingOpenMerchant.getMerchantSn(), waitSignList);
                }
            }

            if (MapUtil.isNotEmpty(unfinishedOpenSharingMerchantMap)) {
                statusDisplay = "当前审批存在未签约商户，具体明细如下，请尽快联系商户签约。\n";
                statusDisplay += unfinishedOpenSharingMerchantMap.entrySet().stream().map(o -> o.getKey() + "：" + String.join(";", o.getValue())).collect(Collectors.joining("。\n"));
            } else {
                long time = System.currentTimeMillis();
                SharingAuditTaskEntity sharingRelationTask = sharingAuditTaskDao.selectByAuditIdWithType(auditId, SharingAuditConstant.AUDIT_TYPE_RELATION_HAIKE_SHARING);
                if (sharingRelationTask == null) {
                    time = sharingAuditTaskEntity.getMtime();
                } else {
                    time = sharingRelationTask.getCtime();
                }
                String format = new SimpleDateFormat("yyyy-MM-dd HH:mm").format(new Date(time));
                statusDisplay = String.format(statusDisplay, format);
            }
            return statusDisplay;
        }
        return Strings.EMPTY;
    }

    private void initOrUpdateAuditTask(long auditId, int auditType, SharingAuditTaskEntity.BizParams bizParams) {
        SharingAuditTaskEntity auditTaskEntity = sharingAuditTaskDao.selectByAuditIdWithType(auditId, auditType);
        if (auditTaskEntity != null) {
            if (auditTaskEntity.getStatus() == SharingAuditConstant.AUDIT_STATUS_INIT) {
                log.warn(String.format("审批单号 %s 已经处理中,忽略此次提交", auditId));
            } else {
                SharingAuditTaskEntity update = new SharingAuditTaskEntity();
                update.setId(auditTaskEntity.getId());
                //可能被驳回后回到该节点 重新进行提交 这时候需要将状态重置
                update.setStatus(SharingAuditConstant.AUDIT_STATUS_INIT);
                update.setBizParams(bizParams.toMap());
                update.setVersion(auditTaskEntity.getVersion());
                sharingAuditTaskDao.updateStatusAndBizParams(update);
            }
        } else {
            SharingAuditTaskEntity sharingAuditTaskEntity = new SharingAuditTaskEntity();
            sharingAuditTaskEntity.setStatus(SharingAuditConstant.AUDIT_STATUS_INIT);
            sharingAuditTaskEntity.setAuditId(auditId);
            sharingAuditTaskEntity.setAuditType(auditType);
            sharingAuditTaskEntity.setBizParams(bizParams.toMap());
            sharingAuditTaskDao.insert(sharingAuditTaskEntity);
        }
    }

    private Map applyHaikeAddReceiver(BatchApplyAddReceiverReq batchApplyHaikeAddReceiverReq) {
        BatchApplyAddReceiverResp batchApplyHaikeAddReceiverResp = sharingOpenService.submitAddReceiver(batchApplyHaikeAddReceiverReq);
        log.info("提交分账关系建立任务成功 resp", JacksonUtil.toJsonString(batchApplyHaikeAddReceiverResp));
        Map<String, String> haikeRelationBizTaskMap = new HashMap<>();
        for (Map.Entry<String, RelationInfo> relationInfoEntry : batchApplyHaikeAddReceiverResp.getRelationInfoMap().entrySet()) {
            String key = relationInfoEntry.getValue().buildRelationKey();
            haikeRelationBizTaskMap.put(key, relationInfoEntry.getKey());
        }
        return haikeRelationBizTaskMap;
    }

    @Override
    public List<ApplyAuditRecordResult> queryMerchantApplyAuditRecord(Map<String, Object> request) {
        List<ApplyAuditRecordResult> result = new ArrayList<>();
        String merchantSn = MapUtil.getString(request, SharingAuditConstant.KEY_PAYER_MERCHANT);
        String auditTemplateEvent = MapUtil.getString(request, SharingAuditConstant.KEY_AUDIT_TEMPLATE_EVENT);
        List<ApplyAuditRecordEntity> applyAuditRecordEntities = applyAuditRecordRepository.queryMerchantApplyAuditRecord(merchantSn, auditTemplateEvent);
        if (CollectionUtils.isNotEmpty(applyAuditRecordEntities)) {
            for (ApplyAuditRecordEntity applyAuditRecordEntity : applyAuditRecordEntities) {
                result.add(ApplyAuditRecordResult.builder().auditSn(applyAuditRecordEntity.getAuditSn())
                        .templateEvent(applyAuditRecordEntity.getTemplateEvent())
                        .merchantSn(applyAuditRecordEntity.getMerchantSn())
                        .status(applyAuditRecordEntity.getProcStatus())
                        .bizMap(JacksonUtil.toBeanQuietly(applyAuditRecordEntity.getExtra(), Map.class))
                        .build());
            }
        }
        return result;
    }

    private void submitSharingCommonCheck(Map<String, Object> request) {
        Integer organization = MapUtils.getInteger(request, SharingAuditConstant.KEY_ORGANIZATION);
        if (organization == null) {
            throw new TradeManageBizException("收单机构不能为空");
        }
        Long auditId = MapUtils.getLong(request, SharingAuditConstant.KEY_AUDIT_ID);
        if (auditId == null) {
            throw new TradeManageBizException("审批编号不能为空");
        }
    }

    @Override
    public Map<String, Object> verifyAcquirerChange(Map<String, Object> request) {
        log.info("切换收单机构前置校验, request = {}", JacksonUtil.toJsonString(request));
        String merchantId = MapUtil.getString(request, MerchantConfig.MERCHANT_ID);
        if (StringUtils.isBlank(merchantId)) {
            throw new TradeManageBizException("商户id不能为空");
        }

        String targetAcquirer = MapUtils.getString(request, AcquirerConstants.TARGET_ACQUIRER);
        if (StringUtils.isBlank(targetAcquirer)) {
            throw new TradeManageBizException("目标收单机构不能为空");
        }

        Map<String, Object> result = new HashMap<>();
        result.put(AcquirerConstants.ALLOWED, true);

        if (!AcquirerTypeEnum.UMB.getValue().equals(targetAcquirer)) {
            //非中投收单机构，无须校验
            log.info("切换收单机构前置校验: 目标收单机构不是中投, 无须校验, merchantId = {}", merchantId);
            return result;
        }


        //中投收单机构，需要校验商户是否配置了自定义结算周期
        int offsetHour = getGrayValue(merchantId, AccessTokenType.OFFSET_HOUR.getCode());
        if (offsetHour > 0) {
            result.put(AcquirerConstants.ALLOWED, false);
            int autoD0 = getGrayValue(merchantId, AccessTokenType.CUSTOMIZE_OFFSET_HOUR_MERCHANT_D0.getCode());
            if (autoD0 > 0) {
                log.info("切换收单机构前置校验: 当前商户已开通自定义结算周期快速到账, 不能切到到中投, merchantId = {}", merchantId);
                result.put(AcquirerConstants.MSG, "无法切换至中投，当前商户已开通自定义结算周期快速到账，请先引导商户关闭自定义结算周期快速到账");
                return result;
            }

            log.info("切换收单机构前置校验: 当前商户已设置自定义对账周期, 不能切到到中投, merchantId = {}", merchantId);
            result.put(AcquirerConstants.MSG, "无法切换至中投，当前商户已设置自定义对账周期，中投仅支持自然日对账");
            return result;
        }

        //检查商户是否配置了定时D0（快速到账计划）
        ImmutablePair<Boolean, String> pairResult = fastWithdrawPlanBiz.checkFastWithdrawPlanExist(merchantId);
        if (pairResult.getLeft()) {
            result.put(AcquirerConstants.ALLOWED, false);
            log.info("切换收单机构前置校验: 当前商户已开通定时D0, 不能切到到中投, merchantId = {}", merchantId);
            result.put(AcquirerConstants.MSG, "无法切换至中投，当前商户已开通定时D0，请先引导商户关闭定时D0后再切换通道");
            return result;
        }

        return result;
    }

    private int getGrayValue(String merchantId, int type) {
        Map<String, Object> updateMap = new HashMap<>();
        updateMap.put(MerchantConfig.MERCHANT_ID, merchantId);
        updateMap.put(MerchantGrayConstant.TYPE, type);
        Map<String, Object> config = iMerchantGrayService.query(updateMap);
        return MapUtils.getIntValue(config, MerchantGrayConstant.VALUE, 0);
    }
}


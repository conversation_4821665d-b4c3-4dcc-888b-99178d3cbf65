package com.wosai.trade.impl.inner;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.core.type.TypeReference;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.trade.biz.fee.ProviderDiffFeeTransactionBiz;
import com.wosai.trade.biz.fee.ProviderFeeRateSyncBiz;
import com.wosai.trade.biz.fee.model.ProviderFeeRateSyncResponse;
import com.wosai.trade.biz.fee.model.SyncForProviderResponse;
import com.wosai.trade.repository.dao.ProviderDiffFeeTransactionMapper;
import com.wosai.trade.repository.dao.entity.ProviderDiffFeeTransactionSummaryEntity;
import com.wosai.trade.service.exception.TradeManageBizException;
import com.wosai.trade.util.JsonUtil;
import com.wosai.trade.util.LocalDateTimeUtil;
import com.wosai.upay.job.model.ContractTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 　费率比对
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AutoJsonRpcServiceImpl
public class FeeRateDiffServiceImpl implements FeeRateDiffService {

    @Resource
    private ProviderDiffFeeTransactionBiz providerDiffFeeTransactionBiz;

    @Resource
    private ProviderFeeRateSyncBiz providerFeeRateSyncBiz;

    @Resource
    private ProviderDiffFeeTransactionMapper providerDiffFeeTransactionMapper;

    @Override
    public void providerSyncFinish(String syncFeeRateStr) {
        ProviderFeeRateSyncResponse response = JsonUtil.decode(syncFeeRateStr, ProviderFeeRateSyncResponse.class);
        if (Objects.nonNull(response)) {
            List<ProviderFeeRateSyncResponse.FeeRateResult> feeRateResultList = JsonUtil.decode(response.getFeeRate(),
                    new TypeReference<List<ProviderFeeRateSyncResponse.FeeRateResult>>() {
                    });
            response.setFeeRateList(feeRateResultList);
        }
        providerFeeRateSyncBiz.providerSyncFinish(response);
    }

    @Override
    public void diffAlarm() {
        providerDiffFeeTransactionBiz.diffAlarm();
    }

    @Override
    public Map<String, Object> queryTask(Long taskId) {
        ContractTask result = providerDiffFeeTransactionBiz.queryTask(taskId);
        return JsonUtil.decode(JsonUtil.encode(result), Map.class);
    }

    /**
     * 指定通道全量同步
     * 使用场景：内部判断基础业务、多业务并有校验逻辑
     *
     * @param merchantSn
     * @param provider
     * @return
     */
    @Override
    public List<SyncForProviderResponse> fullSyncForProvider(String merchantSn, Integer provider) {
        return providerDiffFeeTransactionBiz.fullSyncForProvider(merchantSn, provider);
    }

    /**
     * 指定通道、业务方同步
     * 　使用场景：明确知道要同步那个业务
     *
     * @param merchantSn
     * @param provider
     * @param appId
     */
    @Override
    public SyncForProviderResponse syncForProvider(String merchantSn, Integer provider, @Nullable Long appId) {
        return providerDiffFeeTransactionBiz.syncForProvider(merchantSn, provider, appId);
    }

    /**
     * 同步商户通道费率信息
     * 该方法会分页查询商户和通道的汇总信息，并对每个商户通道执行全量同步
     *
     * @param date 同步开始时间
     */
    @Override
    public void autoSync(String date) {
        Date beginDate;
        try {
            beginDate = LocalDateTimeUtil.toDate(LocalDateTimeUtil.parse(date, DatePattern.NORM_DATE_PATTERN));
        } catch (Exception e) {
            throw TradeManageBizException.createExc("时间格式不正确. date=" + date, e);
        }
        // 最大页数限制，防止无限循环
        final int maxPage = 1000;
        // 每页大小
        final int pageSize = 100;
        // 统计变量
        int successCount = 0;
        int failCount = 0;
        int totalCount = 0;

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.info("开始同步商户通道费率信息 beginDate={}", beginDate);
        for (int page = 0; page < maxPage; page++) {
            Pageable pageable = new PageRequest(page, pageSize);
            List<ProviderDiffFeeTransactionSummaryEntity> list = providerDiffFeeTransactionMapper.queryMerchantProviderSummary(beginDate,
                    pageable.getOffset(), pageable.getPageSize());
            // 如果查询结果为空，说明已经处理完所有数据，可以退出循环
            if (CollectionUtils.isEmpty(list)) {
                break;
            }
            // 对每个商户通道执行同步操作，增加异常处理避免单个失败影响整体流程
            for (ProviderDiffFeeTransactionSummaryEntity entity : list) {
                totalCount++;
                try {
                    fullSyncForProvider(entity.getMerchantSn(), entity.getProvider());
                    successCount++;
                } catch (Exception e) {
                    // 记录单个同步操作的异常，但不中断整个同步流程
                    failCount++;
                    log.error("同步商户[{}]通道[{}]费率信息失败",
                            entity.getMerchantSn(),
                            entity.getProvider(), e);
                }
            }
        }
        stopWatch.stop();
        log.info("同步商户通道费率信息结束...cost={}秒, 总数={}, 成功数={}, 失败数={}",
                stopWatch.getTotalTimeSeconds(), totalCount, successCount, failCount);
    }
}

package com.wosai.trade.impl.inner;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.trade.biz.fee.model.SyncForProviderResponse;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Nullable;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 　费率比对
 *
 * <AUTHOR>
 */
@JsonRpcService(value = "rpc/feeRateDiff")
@Validated
public interface FeeRateDiffService {

    /**
     * 通道同步完成回调手动调用接口
     *
     * @param syncFeeRate
     */
    void providerSyncFinish(String syncFeeRate);

    /**
     * 比对告警
     */
    void diffAlarm();

    /**
     * 任务状态查询
     *
     * @param taskId
     * @return
     */
    Map<String, Object> queryTask(Long taskId);

    /**
     * 指定通道全量同步
     * 使用场景：内部判断基础业务、多业务并有校验逻辑
     *
     * @param merchantSn
     * @param provider
     * @return
     */
    List<SyncForProviderResponse> fullSyncForProvider(String merchantSn, Integer provider);

    /**
     * 指定通道、业务方同步
     * 　使用场景：明确知道要同步那个业务
     *
     * @param merchantSn
     * @param provider
     * @param appId
     */
    SyncForProviderResponse syncForProvider(String merchantSn, Integer provider, @Nullable Long appId);

    /**
     * 每日差异三方通道自动同步
     *
     * @param date　yyyy-MM-dd
     */
    void autoSync(@Valid @NotNull(message = "参数不能不为空") String date);
}

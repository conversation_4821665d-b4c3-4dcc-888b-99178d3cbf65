package com.wosai.trade.repository.dao;

import com.wosai.trade.model.dal.RefundApprovalQueryDalParam;
import com.wosai.trade.model.dal.RefundApprovalQueryParam;
import com.wosai.trade.model.dal.UpdateRefundApprovalParam;
import com.wosai.trade.repository.dao.entity.RefundApprovalRecordEntity;

import java.util.List;

public interface RefundApprovalRecordEntityMapper {
    int deleteByPrimaryKey(Long id);

    int insert(RefundApprovalRecordEntity record);

    int insertSelective(RefundApprovalRecordEntity record);

    RefundApprovalRecordEntity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(RefundApprovalRecordEntity record);

    int updateByPrimaryKeyWithBLOBs(RefundApprovalRecordEntity record);

    int updateByStatus(UpdateRefundApprovalParam updateRefundApprovalParam);

    int updateByPrimaryKey(RefundApprovalRecordEntity record);

    List<RefundApprovalRecordEntity> selectList(RefundApprovalQueryDalParam refundApprovalQueryDalParam);

    List<RefundApprovalRecordEntity> selectListWithCondition(RefundApprovalQueryParam refundApprovalQueryParam);

}
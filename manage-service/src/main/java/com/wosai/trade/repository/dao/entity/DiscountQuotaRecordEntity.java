package com.wosai.trade.repository.dao.entity;

import com.wosai.trade.service.activity.model.DiscountQuotaRecordExtraInfo;
import com.wosai.trade.util.JsonUtil;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.Objects;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DiscountQuotaRecordEntity {
    private Long id;

    private Long activity_id;

    private Long discount_quota;

    private String discount_quota_fee_rate;

    private Long discount_quota_usable;

    private Date create_at;

    private Date update_at;

    private String extra;

    /**
     * 不可用时间
     */
    private Date quota_unavailable_time;


    public DiscountQuotaRecordExtraInfo buildExtra() {
        if (StringUtils.isBlank(extra)) {
            return null;
        }
        return JsonUtil.decode(extra, DiscountQuotaRecordExtraInfo.class);
    }

    public void addExtra(DiscountQuotaRecordExtraInfo extra) {
        this.extra = JsonUtil.encode(extra);
    }

    /**
     * 增加汇总数据
     *
     * @param totalTradePenCount
     * @param totalAmount
     */
    public void addSummary(long totalTradePenCount, long totalAmount) {
        DiscountQuotaRecordExtraInfo extraInfo = buildExtra();
        if (Objects.isNull(extraInfo)) {
            extraInfo = new DiscountQuotaRecordExtraInfo();
        }
        DiscountQuotaRecordExtraInfo.Summary summary = extraInfo.getSummary();
        if (Objects.isNull(summary)) {
            summary = DiscountQuotaRecordExtraInfo.Summary.builder()
                    .totalTradePenCount(totalTradePenCount)
                    .totalAmount(totalAmount)
                    .build();
            extraInfo.setSummary(summary);
        } else {
            summary.setTotalTradePenCount(summary.getTotalTradePenCount() + totalTradePenCount);
            summary.setTotalAmount(summary.getTotalAmount() + totalAmount);
        }
        addExtra(extraInfo);
    }
}
package com.wosai.trade.repository.dao;

import com.wosai.trade.repository.dao.entity.TradeStateEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description StoreTradeStateDao
 * @Date 2021/8/19 11:16 AM
 */
@Mapper
public interface StoreTradeStateDao {

    /**
     * insert
     *
     * @param entity param
     * @return int
     */
    int insert(TradeStateEntity entity);

    /**
     * queryStateByBizIdAndStoreId
     *
     * @param storeId    storeId
     * @param biz        biz
     * @return entity
     */
    TradeStateEntity queryStateByBizIdAndStoreId(@Param("storeId") String storeId, @Param("biz") Integer biz);

    /**
     * listStateByBizIdAndStoreIds
     *
     * @param storeIds    storeIds
     * @param biz         biz
     * @return entity list
     */
    List<TradeStateEntity> listStateByBizIdAndStoreIds(@Param("storeIds") List<String> storeIds,
                                                            @Param("biz") int biz);

    /**
     * 更新记录的 state值
     *
     * @param storeId     storeId
     * @param biz         biz
     * @param state       state
     * @param lastVersion lastVersion
     * @return affectRow
     */
    int updateStateByBizAndTypeAndStoreId(@Param("storeId") String storeId,
                                          @Param("biz") Integer biz,
                                          @Param("state") Long state,
                                          @Param("extra") Map extra,
                                          @Param("lastVersion") Long lastVersion);

}
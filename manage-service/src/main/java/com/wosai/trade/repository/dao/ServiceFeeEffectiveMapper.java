package com.wosai.trade.repository.dao;

import com.wosai.trade.model.dal.ServiceFeeEffectiveEndDateParam;
import com.wosai.trade.model.dal.ServiceFeeEffectiveQueryParam;
import com.wosai.trade.repository.dao.entity.ServiceFeeEffectiveEntity;
import com.wosai.trade.repository.dao.entity.ServiceFeeEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【service_fee_effective(服务费生效记录表)】的数据库操作Mapper
 * @createDate 2023-06-13 09:48:46
 * @Entity com.wosai.trade.repository.dao.entity.ServiceFeeEffectiveEntity
 */
public interface ServiceFeeEffectiveMapper {

    int deleteByPrimaryKey(Long id);

    int insertSelective(ServiceFeeEffectiveEntity record);

    ServiceFeeEffectiveEntity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ServiceFeeEffectiveEntity record);

    int countByCondition(ServiceFeeEffectiveQueryParam param);

    List<ServiceFeeEffectiveEntity> queryByCondition(ServiceFeeEffectiveQueryParam param);

    /**
     * 根据商户号查找生效规则
     *
     * @param merchantSn
     * @param serverFeeId
     * @return
     */
    ServiceFeeEffectiveEntity queryByMerchantStatus(@Param("merchantSn") String merchantSn,
                                                    @Param("serverFeeId") Long serverFeeId,
                                                    @Param("status") Integer status);

    /**
     * 根据结束时间查询主键id列表
     *
     * @return
     */
    List<ServiceFeeEffectiveEntity> queryPkIdByEndDate(ServiceFeeEffectiveEndDateParam param);

    /**
     * 根据商户号查询生效中收款业务方类型
     *
     * @param merchantSn
     * @return
     */
    ServiceFeeEntity queryActiveReceiverAccountBusinessExtra(
            @Param("merchantSn") String merchantSn,
            @Param("receiverAccountBusiness") String receiverAccountBusiness
    );
}

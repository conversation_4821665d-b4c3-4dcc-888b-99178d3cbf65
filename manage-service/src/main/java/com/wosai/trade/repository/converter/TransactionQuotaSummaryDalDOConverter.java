package com.wosai.trade.repository.converter;

import com.wosai.trade.model.dal.TransactionQuotaSummaryDalDO;
import com.wosai.trade.repository.dao.entity.TransactionQuotaSummaryEntity;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2020/4/17 Time: 4:34 下午
 */
public interface TransactionQuotaSummaryDalDOConverter {


    /**
     * convert
     * @param entity entity
     * @return return
     */
    static TransactionQuotaSummaryDalDO convert(TransactionQuotaSummaryEntity entity) {
        if (Objects.isNull(entity)) {
            return null;
        }

        return TransactionQuotaSummaryDalDO.builder()
                .id(entity.getId())
                .merchantSn(entity.getMerchantSn())
                .merchantId(entity.getMerchantId())
                .fixedQuota(entity.getFixedQuota())
                .temporaryQuota(entity.getTemporaryQuota())
                .totalQuota(entity.getTotalQuota())
                .bizType(entity.getBizType())
                .nextComputeDate(entity.getNextComputeDate())
                .ext(entity.getExt())
                .ctime(entity.getCtime())
                .mtime(entity.getMtime())
                .version(entity.getVersion())
                .build();
    }

    /**
     * convert
     * @param entities entities
     * @return return
     */
    static List<TransactionQuotaSummaryDalDO> convert(List<TransactionQuotaSummaryEntity> entities) {
        List<TransactionQuotaSummaryDalDO> summaryDalDOS;
        if (CollectionUtils.isEmpty(entities)) {
            summaryDalDOS = new ArrayList<>();
            return summaryDalDOS;
        }
        summaryDalDOS = new ArrayList<>(entities.size());

        for (TransactionQuotaSummaryEntity entity : entities) {
            summaryDalDOS.add(convert(entity));
        }

        return summaryDalDOS;
    }

}

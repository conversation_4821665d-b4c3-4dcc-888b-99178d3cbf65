package com.wosai.trade.repository.dao.entity;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class MerchantTradeAmountEntity {
    /**
     * 自增主键
     */
    private long id;

    /**
     * merchant_sn
     */
    private String merchantSn;

    /**
     * 商户入网时间
     */
    private long mchCtime;


    /**
     * 实收金额
     */
    private long receivedAmount;

    /**
     * 交易金额
     */
    private long tradeAmount;

    /**
     * 哪个月的报告
     */
    private String month;

    /**
     * 数据插入时间
     */
    private long createTime;
}

package com.wosai.trade.repository.dao.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.ImmutableList;

import java.util.Date;
import java.util.List;
import java.util.Map;

public class CrmFeeRateChangeEntity {
    //状态 0-初始化 10-已通知 20-变更中 30-变更失败 40-已取消 50-变更完成
    /**
     * 初始化
     */
    public static final int STATUS_INIT = 0;
    /**
     * 通知完成
     */
    public static final int STATUS_NOTIFY_COMPLETED = 10;
    /**
     * 变更中
     */
    public static final int STATUS_RUNNING = 20;
    /**
     * 验证/执行异常
     */
    public static final int STATUS_FAILED = 30;
    /**
     * 已取消
     */
    public static final int STATUS_CANCEL = 40;
    /**
     * 变更成功
     */
    public static final int STATUS_SUCCESS = 50;


    /**
     * 默认通知类型 crm
     */
    public static final Integer CRM_NOTIFY = 0;

    /**
     *  新增sp延迟生效，取applyId生效
     */
    public static final Integer SP_DELAY_TAKE_AFFECT_V1 = 1;

    /**
     *  新增sp延迟生效，根据applyfeerateone接口生效
     */
    public static final Integer SP_DELAY_TAKE_AFFECT_V2 = 2;

    /**
     * 未完结状态列表
     */
    public static final List<Integer> STATUS_UNDONE_LIST = ImmutableList.of(STATUS_INIT, STATUS_NOTIFY_COMPLETED, STATUS_RUNNING);

    /**
     * 已完结状态列表
     */
    public static final List<Integer> STATUS_DONE_LIST = ImmutableList.of(STATUS_CANCEL, STATUS_SUCCESS);


    public static final String FEE_RATE_PARAMS = "fee_rate_params";

    /**
     * 通知结果 key
     */
    public static final String NOTIFY_RESULT = "notify_result";

    /**
     * 执行结果 key
     */
    public static final String EXECUTE_RESULT = "execute_result";

    public static final String REQUEST = "request";
    public static final String OPERATOR = "operator";
    public static final String OPERATOR_NAME = "operatorName";


    /**
     * VALUE 内key值　更新前merchant_fee_rate主键Id列表
     */
    public static final String KEY_OLD_FEE_RATE_ID_LIST = "oldFeeRateIdList";
    /**
     * VALUE 内key值　生效的payways列表
     */
    public static final String KEY_PAY_WAYS = "payways";




    private Long id;

    private String merchantSn;

    private Integer payWay;

    private Integer status;

    private Integer type = 0;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone= "GMT+8")
    private Date ctime;

    private Date mtime;

    private Integer deleted;

    private Integer version;

    private Map<String, Object> value;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMerchantSn() {
        return merchantSn;
    }

    public void setMerchantSn(String merchantSn) {
        this.merchantSn = merchantSn;
    }

    public Integer getPayWay() {
        return payWay;
    }

    public void setPayWay(Integer payWay) {
        this.payWay = payWay;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    public Date getMtime() {
        return mtime;
    }

    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Map<String, Object> getValue() {
        return value;
    }

    public void setValue(Map<String, Object> value) {
        this.value = value;
    }

    /**
     * 活动报名根据是否延迟生效以及是否在同步完成后通知来计算CRM费率变更记录状态
     *
     * @param isDelaySyncFeeRate      是否延迟生效
     * @param feeRateSyncFinishNotify 是否在同步完成后通知
     * @return 计算得到的状态值，如果未满足任何条件则可能为null
     */
    public static Integer calcStatusByApplyActivity(boolean isDelaySyncFeeRate, boolean feeRateSyncFinishNotify) {
        Integer changeStatus = null;
        // 如果是延迟生效
        if (isDelaySyncFeeRate) {
            // 并且在同步完成后通知
            if (feeRateSyncFinishNotify) {
                // 状态为通知完成，这种情况是延迟生效，0点定时任务跳过通知，等待三方通道费率同步完成后通知商户费率变更
                changeStatus = CrmFeeRateChangeEntity.STATUS_NOTIFY_COMPLETED;
            } else {
                // 状态为初始状态，这种情况是延迟生效，0点定时任务通知并生效
                changeStatus = CrmFeeRateChangeEntity.STATUS_INIT;
            }
        } else {
            // 如果不是延迟生效（实时生效）
            // 并且在同步完成后通知
            if (feeRateSyncFinishNotify) {
                // 状态为成功，这种情况是实时生效，三方通道费率同步后通知商户费率变更，这里只是记录一条记录为了方便底层构建费率通知
                changeStatus = CrmFeeRateChangeEntity.STATUS_SUCCESS;
            }
        }
        return changeStatus;
    }
}
package com.wosai.trade.repository.dao.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wosai.trade.service.enums.FeeRateTypeEnum;
import com.wosai.trade.util.JsonUtil;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Data
public class TradeComboDetailEntity {

    private Long id;

    private Long comboId;

    private Integer payway;

    private String feeRate;

    @Deprecated
    private String feeRateMin;

    @Deprecated
    private String feeRateMax;

    private String subPaywayStatus;

    /**
     * 互斥套餐列表
     */
    private String mutexTradeCombo;

    private Map<String, Object> extendInfo;

    private Long ctime;

    private Long mtime;

    @Deprecated
    private String ladderFeeRates;

    /**
     * 资金通道费率列表
     */
    @Deprecated
    private String channelFeeRates;

    public SubPaywayStatus buildSubPaywayStatus() {
        if (StringUtils.isNotEmpty(subPaywayStatus)) {
            return JsonUtil.decode(subPaywayStatus, SubPaywayStatus.class);
        } else {
            return new SubPaywayStatus();
        }
    }

    public FeeRate buildFeeRate() {
        return JsonUtil.decode(feeRate, FeeRate.class);
    }

    public List<LadderFeeRate> buildLadderFeeRates() {
        String decodeLadderFeeRates = ladderFeeRates;
        FeeRate feeRateObject = buildFeeRate();
        if (feeRateObject != null && FeeRateTypeEnum.LADDER.name().equalsIgnoreCase(feeRateObject.getFeeRateType())) {
            decodeLadderFeeRates = feeRateObject.getValue();
        }
        if (StringUtils.isNotEmpty(decodeLadderFeeRates)) {
            List<LadderFeeRate> ladderFeeRateList = JsonUtil.decode(decodeLadderFeeRates, new TypeReference<List<LadderFeeRate>>() {
            });
            if (Objects.isNull(ladderFeeRateList)) {
                return Collections.emptyList();
            }
            return ladderFeeRateList.stream().peek(ladderFeeRate -> {
                if (Objects.nonNull(ladderFeeRate.getFeeRate())) {
                    ladderFeeRate.setFeeRateMin(ladderFeeRate.getFeeRate());
                    ladderFeeRate.setFeeRateMax(ladderFeeRate.getFeeRate());
                }
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    public List<ChannelFeeRate> buildChannelFeeRates() {
        String decodeChannelFeeRates = channelFeeRates;
        FeeRate feeRateObject = buildFeeRate();
        if (feeRateObject != null && FeeRateTypeEnum.CHANNEL.name().equalsIgnoreCase(feeRateObject.getFeeRateType())) {
            decodeChannelFeeRates = feeRateObject.getValue();
        }
        if (StringUtils.isNotEmpty(decodeChannelFeeRates)) {
            return JsonUtil.decode(decodeChannelFeeRates, new TypeReference<List<ChannelFeeRate>>() {});
        }
        return Collections.emptyList();
    }

    public List<ChannelLadderFeeRate> buildChannelLadderFeeRates() {
        FeeRate feeRateObject = buildFeeRate();
        if (feeRateObject != null && FeeRateTypeEnum.CHANNEL_LADDER.name().equalsIgnoreCase(feeRateObject.getFeeRateType())) {
            return JsonUtil.decode(feeRateObject.getValue(), new TypeReference<List<ChannelLadderFeeRate>>() {});
        }
        return Collections.emptyList();
    }

    public FixedFeeRate buildFixedFeeRate() {
        if (StringUtils.isNotEmpty(feeRate)) {
            FeeRate feeRateObject = buildFeeRate();
            if (feeRateObject != null && FeeRateTypeEnum.FIXED.name().equalsIgnoreCase(feeRateObject.getFeeRateType())) {
                return JsonUtil.decode(feeRateObject.getValue(), new TypeReference<FixedFeeRate>() {});
            }
        } else if (StringUtils.isNotEmpty(feeRateMin) && StringUtils.isNotEmpty(feeRateMax)) {
            FixedFeeRate feeRate = new FixedFeeRate();
            feeRate.setMin(feeRateMin);
            feeRate.setMax(feeRateMax);
            return feeRate;
        }
        return null;
    }

    public List<Long> buildMutexTradeCombo(){
        if (StringUtils.isNotEmpty(mutexTradeCombo)) {
            return JsonUtil.decode(mutexTradeCombo, new TypeReference<List<Long>>() {});
        } else {
            return null;
        }
    }

    /**
     * 获取费率类型
     *
     * @return
     */
    public FeeRateTypeEnum fetchFeeRateTypeEnum() {
        TradeComboDetailEntity.FeeRate fee = buildFeeRate();
        if (fee != null) {
            return FeeRateTypeEnum.valueOf(buildFeeRate().getFeeRateType().toUpperCase());
        }
        // TODO 套餐旧版本实现逻辑，等所有套餐存储数据格式都迁移后，移除代码
        FeeRateTypeEnum feeRateTypeEnum;
        // 资金通道费率
        if (org.apache.commons.lang.StringUtils.isNotEmpty(getChannelFeeRates())) {
            feeRateTypeEnum = FeeRateTypeEnum.CHANNEL;
        }
        // 固定费率
        else if (org.apache.commons.lang.StringUtils.isNotEmpty(getFeeRateMin())) {
            feeRateTypeEnum = FeeRateTypeEnum.FIXED;
        }
        // 阶梯费率
        else {
            feeRateTypeEnum = FeeRateTypeEnum.LADDER;
        }
        return feeRateTypeEnum;
    }

    @Accessors(chain = true)
    @Data
    public static class SubPaywayStatus {

        private Integer b2c_status;

        private Integer c2b_status;

        private Integer wap_status;

        private Integer mini_status;

        private Integer h5_status;

        private Integer app_status;

    }

    @Accessors(chain = true)
    @Data
    public static class FeeRate {
        /**
         * 费率类型，参考FeeRateTypeEnum
         */
        @JsonProperty(value = "fee_rate_type")
        String feeRateType;

        /**
         * 费率值
         */
        String value;
    }

    @Accessors(chain = true)
    @Data
    public static class LadderFeeRate {

        private Double min;

        private Double max;
        /**
         * 费率，非空
         * 费率区间调整后，这个做兼容使用
         */
        @JsonProperty(value = "fee_rate")
        private String feeRate;
        /**
         * 费率区间最小值
         */
        @JsonProperty(value = "fee_rate_min")
        private String feeRateMin;
        /**
         * 费率区间最大值
         */
        @JsonProperty(value = "fee_rate_max")
        private String feeRateMax;

    }

    @Data
    public static class ChannelFeeRate {
        /**
         * 资金渠道类型
         */
        private String type;
        /**
         * 单笔最高最小值 单位:元
         */
        @JsonProperty(value = "high_min")
        private String highMin;
        /**
         * 单笔最高最大值 单位:元
         */
        @JsonProperty(value = "high_max")
        private String highMax;
        /**
         * 费率最小值
         */
        @JsonProperty(value = "fee_rate_min")
        private String feeRateMin;
        /**
         * 费率最大值
         */
        @JsonProperty(value = "fee_rate_max")
        private String feeRateMax;
    }

    @Data
    public static class ChannelLadderFeeRate {
        /**
         * 资金渠道类型
         */
        private String type;

        /**
         * 阶梯费率
         */
        @JsonProperty(value = "ladder_fee_rates")
        private List<LadderFeeRate> ladderFeeRates;  
    }

    @Accessors(chain = true)
    @Data
    public static class FixedFeeRate {
        private String min;

        private String max;
    }

}

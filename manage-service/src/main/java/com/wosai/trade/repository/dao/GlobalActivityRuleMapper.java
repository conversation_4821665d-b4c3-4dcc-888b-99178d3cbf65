package com.wosai.trade.repository.dao;

import com.wosai.trade.model.dal.ActivityGlobalRuleQueryParam;
import com.wosai.trade.service.activity.request.GlobalActivityRuleResponse;
import com.wosai.trade.service.activity.request.GlobalActivityRuleRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface GlobalActivityRuleMapper {

    void insertGlobalActivityRule(GlobalActivityRuleRequest globalActivityRule);

    List<GlobalActivityRuleResponse> getGlobalActivityRule(ActivityGlobalRuleQueryParam activityGlobalRuleQueryParam);

    int getGlobalActivityRuleCount(ActivityGlobalRuleQueryParam activityGlobalRuleQueryParam);

    void updateGlobalActivityRuleById(@Param("id") Long id, @Param("status") int status);

    List<Map> queryRuleByIndustry(String industryId);

    void updateActivityRule(GlobalActivityRuleRequest globalActivityRule);


}

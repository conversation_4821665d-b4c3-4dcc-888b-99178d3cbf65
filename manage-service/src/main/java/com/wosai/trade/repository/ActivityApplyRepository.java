package com.wosai.trade.repository;

import com.wosai.trade.model.dal.IdOffsetPageable;
import com.wosai.trade.repository.dao.entity.ActivityApplyEntity;

import javax.annotation.Nullable;
import java.util.Date;
import java.util.List;

/**
 * Description: 活动报名申请记录处理类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/5/13
 */
public interface ActivityApplyRepository {

    ActivityApplyEntity selectByPrimaryKey(Long id);

    /**
     * 更新活动状态
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(ActivityApplyEntity record);

    /**
     * 更新扩展字段
     *
     * @param id
     * @param extra
     */
    void updateExtraById(Long id, String extra);

    /**
     * 根据SN获取商户生效的活动
     *
     * @param merchantSn
     * @return
     */
    List<ActivityApplyEntity> getEffectIngApplyBySn(String merchantSn);

    /**
     * 查询考核活动申请记录
     *
     * @param merchantSn
     * @param assessmentTime
     * @param statusList
     * @param pageable
     * @return
     */
    List<ActivityApplyEntity> queryAssessmentList(@Nullable String merchantSn,
                                                  Date assessmentTime,
                                                  List<Integer> statusList,
                                                  IdOffsetPageable pageable);

    /**
     * 更新考核时间
     *
     * @param update
     */
    void updateAssessmentTime(ActivityApplyEntity update);

    /**
     * 检查是否存在考核活动报名申请记录
     * 查询状态为4（生效中）和8的记录
     *
     * @param merchantSn 商户
     * @return 是否存在符合条件的记录
     */
    boolean existsAssessmentActivityApply(String merchantSn);

    /**
     * 查询生效中考核活动报名申请记录
     * 查询状态为4（生效中）和8的记录
     *
     * @param merchantSn 商户
     * @return 是否存在符合条件的记录
     */
    List<ActivityApplyEntity> queryAssessmentActivityApplyList(String merchantSn);

    /**
     * 根据父级ID查询活动申请记录
     *
     * @param parentId 父级ID
     * @return 活动申请记录列表
     */
    List<ActivityApplyEntity> selectByParentId(Long parentId);
}

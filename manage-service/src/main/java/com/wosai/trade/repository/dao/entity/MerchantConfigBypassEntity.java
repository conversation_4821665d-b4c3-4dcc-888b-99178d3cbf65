package com.wosai.trade.repository.dao.entity;

import java.util.Map;

import com.wosai.trade.service.result.MerchantConfigBypassQueryResult;

import lombok.Data;
import lombok.experimental.Accessors;

@Accessors(chain = true)
@Data
public class MerchantConfigBypassEntity {
    private Long id;
    private String merchantId;
    private Integer currentProvider;
    private Integer payway;
    private String b2cAgentName;
    private String c2bAgentName;
    private String wapAgentName;
    private String miniAgentName;
    private String appAgentName;
    private String h5AgentName;
    private Map<String, Object> params;
    private Long ctime;
    private Long mtime;
    private int deleted;
    private Long version;

    public MerchantConfigBypassQueryResult toQueryResult() {
        MerchantConfigBypassQueryResult queryResponse = new MerchantConfigBypassQueryResult();
        queryResponse.setId(getId());
        queryResponse.setMerchantId(getMerchantId());
        queryResponse.setCurrentProvider(getCurrentProvider());
        queryResponse.setPayway(getPayway());
        queryResponse.setB2cAgentName(getB2cAgentName());
        queryResponse.setC2bAgentName(getC2bAgentName());
        queryResponse.setWapAgentName(getWapAgentName());
        queryResponse.setMiniAgentName(getMiniAgentName());
        queryResponse.setAppAgentName(getAppAgentName());
        queryResponse.setH5AgentName(getH5AgentName());
        queryResponse.setParams(getParams());
        return queryResponse;
    }
}

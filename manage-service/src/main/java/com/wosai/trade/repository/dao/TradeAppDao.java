package com.wosai.trade.repository.dao;

import com.wosai.trade.model.dal.*;
import com.wosai.trade.repository.dao.entity.TradeAppEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface TradeAppDao {

    /**
     * insert
     * @param param param
     * @return int
     */
    int insert(TradeAppUpsertDalParam param);

    /**
     * update
     * @param param param
     * @return int
     */
    int update(TradeAppUpsertDalParam param);

    /**
     * list
     * @param param param
     * @return int
     */
    List<TradeAppEntity> list(TradeAppQueryDalParam param);

    /**
     * selectAll
     * @return 最多1000条
     */
    List<TradeAppEntity> selectAll();

    /**
     * updateStatus
     * @param param param
     * @return int
     */
    int updateStatus(TradeAppUpsertDalParam param);

    /**
     * query
     * @param param param
     * @return list
     */
    List<TradeAppEntity> query(TradeAppQueryDalParam param);

    /**
     * insert
     * @param id param
     * @return int
     */
    TradeAppEntity queryTradeAppById(Long id);

    /**
     * selectByIds
     *
     * @param ids ids
     * @return List<TradeAppEntity>
     */
    List<TradeAppEntity> selectByIds(@Param("ids") List<Long> ids);

    /**
     * count
     * @param param param
     * @return int
     */
    long count(TradeAppQueryDalParam param);

    /**
     * delete
     * @param id id
     */
    void delete(Long id);

}

package com.wosai.trade.repository.impl;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.sp.business.logstash.dto.PlatformEnum;
import com.wosai.trade.biz.businesslog.BusinessOpLogBiz;
import com.wosai.trade.biz.combo.UnionPayLadderComboApplyBiz;
import com.wosai.trade.config.apollo.CommonApolloConfig;
import com.wosai.trade.config.apollo.TradeParamsConfig;
import com.wosai.trade.constant.TransactionConstants;
import com.wosai.trade.model.constant.OpLogConstant;
import com.wosai.trade.service.FeeRateService;
import com.wosai.trade.service.TradeBizConfigService;
import com.wosai.trade.service.exception.TradeManageBizException;
import com.wosai.trade.service.request.ApplyFeeRateRequest;
import com.wosai.trade.service.request.OpLogCreateRequest;
import com.wosai.trade.service.request.ProviderMchQueryRequest;
import com.wosai.trade.service.request.TonglianApplePayConfigRequest;
import com.wosai.trade.service.result.ProviderMchQueryResult;
import com.wosai.trade.util.OssUtil;
import com.wosai.trade.util.ZipUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.bean.request.OpLogCreateRequestV2;
import com.wosai.upay.core.exception.CoreInvalidParameterException;
import com.wosai.upay.core.meta.Payway;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.BusinssCommonService;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.core.service.TradeConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class TradeBizConfigServiceImpl implements TradeBizConfigService {
    @Autowired
    TradeConfigService tradeConfigService;
    @Autowired
    FeeRateService feeRateService;
    @Autowired
    CommonApolloConfig commonApolloConfig;
    @Autowired
    private UnionPayLadderComboApplyBiz unionPayLadderComboApplyBiz;

    @Resource
    private BusinssCommonService businssCommonService;


    @Resource
    private BusinessOpLogBiz businessOpLogBiz;
    @Resource
    private TerminalService terminalService;
    @Resource
    private SupportService supportService;
    @Resource
    private TradeParamsConfig tradeParamsConfig;


    // 终端分类（收钱吧app终端 取值"106"）
    private static final String APP_BSC_CATEGORY = "106";


    // APP-BSC单笔交易限额为不限制
    private static final long UN_LIMIT_APP_BSC = -1L;

    @Override
    public Map<String, Object> updateCategoryMerchantSingleMax(String merchantSn, Long quota, String category) {
        return tradeConfigService.updateCategoryMerchantSingleMax(merchantSn, quota, category);
    }

    @Override
    public Map<String, Object> updateCategoryMerchantSingleMaxAndLog(String merchantSn, Long quota, String category, OpLogCreateRequest opLogCreateRequest) {
        String merchantId = businssCommonService.getMerchantIdBySn(merchantSn);
        Map tradeConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        Map params = WosaiMapUtils.getMap(tradeConfig, OpLogConstant.MERCHANT_CONFIG_PARAMS);
        Map categoryLimits = WosaiMapUtils.getMap(params, OpLogConstant.CATEGORY_MERCHANT_SINGLE_MAX_OF_TRAN);
        Long oldLimit = WosaiMapUtils.getLong(categoryLimits, APP_BSC_CATEGORY);
        Map<String, Object> result = updateCategoryMerchantSingleMax(merchantSn, quota, category);

        // 记录商户日志：APP-BSC单笔交易限额变更
        Map<String, Object> before = CollectionUtil.hashMap(OpLogConstant.MERCHANT_ID, merchantId, OpLogConstant.APP_BSC_LIMIT, oldLimit == null ? "无限额" : oldLimit);
        Map<String, Object> after = CollectionUtil.hashMap(OpLogConstant.MERCHANT_ID, "", OpLogConstant.APP_BSC_LIMIT, quota / 100);
        businessOpLogBiz.sendAppBSCLimitLog(opLogCreateRequest.getOuterSceneTraceId(), merchantId, opLogCreateRequest.getPlatformCode(), opLogCreateRequest.getOpUserId(), opLogCreateRequest.getOpUserName(), StringUtils.isBlank(opLogCreateRequest.getRemark()) ? "APP-BSC单笔交易限额变更" : opLogCreateRequest.getRemark(), before, after);
        return result;
    }

    @Override
    public Map getMerchantConfigByMerchantIdAndPayway(String merchantId, Integer payway) {
        return tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
    }

    @Override
    public Map<String, String> openUnionpay(@NotNull(message = "商户sn不能为空") String merchantSn) {
        Map<String, String> result = new HashMap<>();
        result.put("sn", merchantSn);
        try {
            unionPayLadderComboApplyBiz.handleApply(merchantSn, "云闪付1000元以上交易权限打开，生效0.6%费率；1000元及以下费率不变", "数据清洗接口调用申请");
            result.put("msg", "处理成功");
            result.put("success", "yes");

        } catch (Exception e) {
            log.error("商户 {} 云闪付打开1000元以上收款限制 异常", merchantSn, e);
            result.put("msg", e.getMessage());
            result.put("success", "no");
        }
        return result;
    }


    @Override
    public String openUnionpayFileHandle(@NotBlank(message = "文件路径不能为空") String ossPath) {
        //oss文件链接
        File file = OssUtil.downloadOssFile(ossPath);
        if (file == null) {
            throw new CoreInvalidParameterException("文件读取失败");
        }

        new Thread(() -> fileHandle(file)).start();

        return "文件处理中";
    }

    @Override
    public void openUnionPay(@NotBlank(message = "商户sn不能为空") String merchantSn, String operator) {
        if (StringUtils.isBlank(operator)) {
            operator = "system";
        }
        unionPayLadderComboApplyBiz.handleApply(merchantSn, "云闪付1000元以上交易权限打开，生效0.6%费率；1000元及以下费率不变", operator);
    }

    @Override
    public ProviderMchQueryResult providerMchQuery(ProviderMchQueryRequest request) {
        String merchantId = businssCommonService.getMerchantIdBySn(request.getMerchantSn());
        String terminalSn = getOneTerminalSn(merchantId);
        if (StringUtil.isEmpty(terminalSn)) {
            throw new TradeManageBizException("查询失败，商户下无可用设备");
        }
        Map<String, Object> allParams = null;
        try {
            allParams = supportService.getAllParams(null, terminalSn, request.getPayway(), request.getSubPayway());
        } catch (Exception e) {
            throw new TradeManageBizException("查询失败，请稍后再试");
        }
        Integer provider = MapUtil.getInteger(allParams, TransactionParam.PROVIDER);
        String tradeParamsKey = allParams.keySet().stream().filter(r -> r.endsWith("_trade_params")).findFirst().orElse(null);
        TradeParamsConfig.ProviderMchConfig providerMchConfig = tradeParamsConfig.getTradeParamsProviderMchKey().get(tradeParamsKey);
        // 使用默认的兜底逻辑
        if (providerMchConfig == null) {
            if (Objects.equals(request.getPayway(), Payway.WEIXIN.getCode())) {
                providerMchConfig = (provider == null) ? TransactionConstants.WEIXIN_DIRECT_PROVIDER_MCH_KEY : TransactionConstants.WEIXIN_INDIRECT_PROVIDER_MCH_KEY;
            } else if (Objects.equals(request.getPayway(), Payway.ALIPAY.getCode()) || Objects.equals(request.getPayway(), Payway.ALIPAY2.getCode())) {
                providerMchConfig = (provider == null) ? TransactionConstants.ALIPAY_DIRECT_PROVIDER_MCH_KEY : TransactionConstants.ALIPAY_INDIRECT_PROVIDER_MCH_KEY;
            } else {
                throw new TradeManageBizException("查询失败，该商户暂不支持");
            }
        }
        Map<String, Object> tradeParams = MapUtil.getMap(allParams, tradeParamsKey);
        String channelId = MapUtil.getString(tradeParams, providerMchConfig.getChannelIdKey());
        String mchId = MapUtil.getString(tradeParams, providerMchConfig.getMchKey());
        if ((Objects.equals(request.getPayway(), Payway.WEIXIN.getCode()) && (StringUtil.isEmpty(channelId) || StringUtil.isEmpty(mchId)))
                || (Objects.equals(request.getPayway(), Payway.ALIPAY.getCode()) && StringUtil.isEmpty(mchId))
                || (StringUtil.isEmpty(channelId) && StringUtil.isEmpty(mchId))) {
            throw new TradeManageBizException("查询失败，商户交易配置有误");
        }
        return new ProviderMchQueryResult()
                    .setType(provider == null ? ProviderMchQueryResult.Type.DIRECT : ProviderMchQueryResult.Type.INDIRECT)
                    .setChannelId(channelId)
                    .setMchId(mchId);
    }

    @Override
    public void openTonglianApplePay(TonglianApplePayConfigRequest request) {
        String merchantId = request.getMerchantId();
        Long comboId = request.getComboId();
        String providerMchId = request.getProviderMchId();
        String agentName = request.getAgentName();
        String operator = request.getOperator();
        int payway = Payway.APPLEPAY.getCode();
        Map merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
        if (merchantConfig == null) {
            tradeConfigService.createMerchantConfig(MapUtil.hashMap(
                    MerchantConfig.MERCHANT_ID, merchantId,
                    MerchantConfig.PAYWAY, payway
            ));
            merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
        }
        merchantConfig.put(MerchantConfig.PARAMS, MapUtil.hashMap(
                TransactionParam.TL_S2P_TRADE_PARAMS, MapUtil.hashMap(
                    TransactionParam.TL_S2P_PROVIDER_MCH_ID, providerMchId
                )
        ));
        merchantConfig.put(MerchantConfig.WAP_AGENT_NAME, agentName);
        merchantConfig.put(MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_TL_S2P);
        // update and log
        Map update = BeanUtil.getPart(merchantConfig, Arrays.asList(
                DaoConstants.ID, MerchantConfig.MERCHANT_ID, MerchantConfig.PAYWAY, MerchantConfig.PROVIDER, MerchantConfig.WAP_AGENT_NAME, MerchantConfig.PARAMS
        ));
        OpLogCreateRequestV2 log = new OpLogCreateRequestV2();
        log.setPlatformCode(PlatformEnum.SPA.getCode());
        log.setOpUserName(operator);
        log.setOpUserId(operator);
        log.setRemark("修改 apple pay 交易参数");
        log.setOuterSceneTraceId("");
        tradeConfigService.updateMerchantConfigAndLogV2(update, log);
        // update fee rate
        ApplyFeeRateRequest feeRateRequest = new ApplyFeeRateRequest();
        feeRateRequest.setTradeComboId(comboId);
        feeRateRequest.setOperator(operator);
        feeRateRequest.setMerchantSn(businssCommonService.getMerchantSnById(merchantId));
        feeRateService.applyFeeRateOne(feeRateRequest);
    }

    private String getOneTerminalSn(String merchantId){
        ListResult terminals = terminalService.findTerminals(new PageInfo(1, 1), CollectionUtil.hashMap(
                "merchant_id", merchantId,
                "status", Terminal.STATUS_ACTIVATED
        ));
        if(terminals != null && terminals.getRecords() != null && terminals.getRecords().size() > 0){
            Map<String,Object> terminal = terminals.getRecords().get(0);
            return MapUtil.getString(terminal, Terminal.SN);
        }
        return null;
    }

    public void fileHandle(File file) {
        //处理成功
        FileWriter successWriter = null;
        //处理失败
        FileWriter failWriter = null;

        String dateTime = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        String successPath = "/tmp/openUnionpaySuccess" + dateTime + ".csv";
        String failPath = "/tmp/openUnionpayFail" + dateTime + ".csv";

        try {
            ZipUtil.deletefile(successPath);
            ZipUtil.deletefile(failPath);
            successWriter = new FileWriter(successPath);
            failWriter = new FileWriter(failPath);

            //txt文件
            FileReader fileReader = new FileReader(file);
            BufferedReader br = new BufferedReader(fileReader);
            String lineContent = null;

            while ((lineContent = br.readLine()) != null) {
                String merchantSn = lineContent.split(",")[0];
                if (StringUtils.isNotBlank(merchantSn)) {
                    try {
                        Map<String, String> map = openUnionpay(merchantSn);
                        String success = map.get("success");
                        if (success.equals("yes")) {
                            //处理成功
                            String wr = String.format("%s,%s\n", merchantSn, "处理成功");
                            writeRes(successWriter, wr);
                        } else {
                            //处理失败
                            String wr = String.format("%s,%s\n", merchantSn, map.get("msg"));
                            writeRes(failWriter, wr);
                        }
                    } catch (Exception e) {
                        //处理失败
                        String wr = String.format("%s,%s\n", merchantSn, "处理异常");
                        writeRes(failWriter, wr);
                    }
                }
            }


            /**处理完成 上传文件oss*/
            String resultUrl = OssUtil.uploadFileToOSS(successPath);
            String failUrl = OssUtil.uploadFileToOSS(failPath);

            log.info("打开云闪付 处理成功 文件链接 {} ", resultUrl);
            log.info("打开云闪付 处理失败 文件链接 {} ", failUrl);

            br.close();
            fileReader.close();

        } catch (Exception e) {
            log.error("打开云闪付文件处理异常 ", e);
        } finally {
            try {
                if (successWriter != null) {
                    successWriter.flush();
                    successWriter.close();
                }
                if (failWriter != null) {
                    failWriter.flush();
                    failWriter.close();
                }
                ZipUtil.deletefile(successPath);
                ZipUtil.deletefile(failPath);
            } catch (Exception e) {
                log.error("删除文件失败  {} {}", successPath, failPath, e);
            }

            if (file != null) {
                file.delete();
            }
        }


    }


    private void writeRes(FileWriter writer, String msg) {
        try {
            writer.write(msg);
            writer.flush();
        } catch (IOException e) {
            log.error("文件写入失败", e);
        }
    }
}

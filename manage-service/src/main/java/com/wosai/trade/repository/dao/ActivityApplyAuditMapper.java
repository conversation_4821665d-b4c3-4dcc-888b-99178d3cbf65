package com.wosai.trade.repository.dao;

import com.wosai.trade.model.dal.activity.audit.result.AuditByApplyListResult;
import com.wosai.trade.repository.dao.entity.ActivityApplyAuditEntity;
import com.wosai.trade.service.request.PageInfo;
import org.apache.ibatis.annotations.Param;

import javax.annotation.Nullable;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【activity_apply_audit(活动报名审批明细记录表)】的数据库操作Mapper
* @createDate 2024-07-05 10:38:03
* @Entity com.wosai.trade.repository.dao.entity.ActivityApplyAuditEntity
*/
public interface ActivityApplyAuditMapper {

    int deleteByPrimaryKey(String id);

    int insert(ActivityApplyAuditEntity record);

    int insertSelective(ActivityApplyAuditEntity record);

    ActivityApplyAuditEntity selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(ActivityApplyAuditEntity record);

    int updateByPrimaryKey(ActivityApplyAuditEntity record);

    /**
     * 获取商户活动报名审批列表条件筛选统计
     *
     * @param merchantSn      商户sn
     * @param auditStatusList 审批状态
     * @param allQuery        查询当前所有审批记录列表
     * @return
     * @see com.wosai.trade.service.enums.ActivityApplyAuditStatusEnum
     */
    Long queryAuditByApplyListCount(@Param("merchantSn") String merchantSn,
                                   @Param("allQuery") boolean allQuery,
                                   @Param("auditStatusList") @Nullable List<Integer> auditStatusList);

    /**
     * 获取商户活动报名审批列表条件筛选
     *
     * @param merchantSn      商户sn
     * @param auditStatusList 审批状态
     * @param allQuery        查询当前所有审批记录列表
     * @return
     * @see com.wosai.trade.service.enums.ActivityApplyAuditStatusEnum
     */
    List<AuditByApplyListResult> queryAuditByApplyList(@Param("merchantSn") String merchantSn,
                                                       @Param("allQuery") boolean allQuery,
                                                       @Param("auditStatusList") @Nullable List<Integer> auditStatusList,
                                                       @Param("pageInfo") PageInfo pageInfo);

    /**
     * 获取报名关联的报名审批列表
     *
     * @param merchantSn 商户sn
     * @param auditApplyId 报名审批ID
     * @return
     */
    List<ActivityApplyAuditEntity> queryJoinApplyAuditList(@Param("merchantSn") String merchantSn,
                                                           @Param("auditApplyId") String auditApplyId);
}

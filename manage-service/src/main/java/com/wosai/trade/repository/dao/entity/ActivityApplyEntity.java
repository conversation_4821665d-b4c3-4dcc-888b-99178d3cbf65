package com.wosai.trade.repository.dao.entity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wosai.databus.event.audit.AuditInstanceCreateEvent;
import com.wosai.trade.model.dal.ApplyExtraParam;
import com.wosai.trade.service.activity.model.TradeAssessmentRule;
import com.wosai.trade.service.activity.request.ActivityEffectiveRule;
import com.wosai.trade.service.activity.request.ActivityExpirationRule;
import com.wosai.trade.service.activity.response.ApplyProcessInfo;
import com.wosai.trade.service.exception.TradeManageBizException;
import com.wosai.trade.util.ActivityTimeUtil;
import com.wosai.trade.util.JsonUtil;
import com.wosai.trade.util.LocalDateTimeUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivityApplyEntity {
    private Long id;

    private Long audit_id;

    private Long activity_id;

    private Long biz_id;

    private Integer activity_type;

    private String activity_name;

    private String merchant_sn;

    private String merchant_id;

    private String sn;

    private String sub_mch_id;

    private Integer status;

    private Date apply_success_time;

    private Date effective_time;

    private Date expiration_time;

    private String fee_rate;

    private String lowest_fee_rate;

    private Integer discount_quota_status;

    private Long discount_quota_record_id;

    private Date create_at;

    private Date update_at;

    private String effective_rule;

    private String expiration_rule;

    private String ladder_fee_rates;

    private String payway;

    private String extra;

    private String process;

    private String audit_info;

    private String remark;

    /**
     * 交易考核时间 为空时不考核
     */
    private Date assessment_time;

    private Long activity_sub_status_id;

    private Long combo_id;

    /**
     * 挂靠父id
     */
    private Long parent_id;

    /**
     * 审批模版id
     */
    private String auditTemplateId;

    /**
     * 交易考核规则 - 数据库存储的JSON字符串
     */
    private String trade_assessment_rule;

    /**
     * 是否延迟生效
     *
     * @return
     */
    public boolean hasDelayTakeEffect() {
        ApplyExtraParam extraObj = buildJsonExtra();
        return Objects.nonNull(extraObj) && extraObj.isDelayTakeEffect();
    }

    public ActivityEffectiveRule buildActivityEffectiveRule() {
        if (StringUtils.isNotBlank(effective_rule)) {
            return JSONObject.parseObject(effective_rule, ActivityEffectiveRule.class);
        }
        return null;
    }

    public ActivityExpirationRule buildActivityExpirationRule() {
        if (StringUtils.isNotBlank(expiration_rule)) {
            return JSONObject.parseObject(expiration_rule, ActivityExpirationRule.class);
        }
        return null;
    }

    public ApplyExtraParam buildJsonExtra() {
        if (StringUtils.isNotBlank(extra)) {
            return JSONObject.parseObject(extra, ApplyExtraParam.class);
        }
        return null;
    }

    public ApplyExtraParam.TradeAssessmentExtra buildTradeAssessmentExtra() {
        ApplyExtraParam extraObj = buildJsonExtra();
        if (Objects.nonNull(extraObj)) {
            return extraObj.getTradeAssessmentExtra();
        }
        return null;
    }

    public List<Long> fetchEffectNotStandardFeeRateIdList() {
        ApplyExtraParam.TradeAssessmentExtra extra = buildTradeAssessmentExtra();
        if (Objects.isNull(extra)) {
            return null;
        }
        return extra.getEffectNotStandardFeeRateIdList();
    }

    public void addTradeAssessmentExtra(ApplyExtraParam.TradeAssessmentExtra extra) {
        ApplyExtraParam extraObj = buildJsonExtra();
        if (Objects.isNull(extraObj)) {
            extraObj = new ApplyExtraParam();
        }
        extraObj.setTradeAssessmentExtra(extra);
        this.extra = JsonUtil.encode(extraObj);
    }

    public void cleanTradeAssessmentExtra() {
        ApplyExtraParam extraObj = buildJsonExtra();
        if (Objects.nonNull(extraObj)) {
            extraObj.setTradeAssessmentExtra(null);
            this.extra = JsonUtil.encode(extraObj);
        }
    }

    public List<ApplyProcessInfo> buildProcess() {
        if (StringUtils.isNotBlank(process)) {
            return JSONArray.parseArray(process, ApplyProcessInfo.class);
        }
        return null;
    }

    public AuditInstanceCreateEvent buildAuditInfo() {
        if (StringUtils.isNotBlank(audit_info)) {
            return JSONObject.parseObject(audit_info, AuditInstanceCreateEvent.class);
        }
        return null;
    }

    public List<Integer> buildPayWay() {
        if (StringUtils.isNotBlank(payway)) {
            return JSONArray.parseArray(payway, Integer.class);
        }
        return null;
    }

    public Map<String, String> buildFeeRate() {
        if (StringUtils.isNotBlank(fee_rate)) {
            return JSONObject.parseObject(fee_rate, Map.class);
        }
        return null;
    }

    /**
     * 构建交易考核规则对象
     *
     * @return TradeAssessmentRule对象
     */
    public TradeAssessmentRule buildTradeAssessmentRule() {
        return JsonUtil.decode(trade_assessment_rule, TradeAssessmentRule.class);
    }

    /**
     * 是否交易考核活动
     *
     * @return
     */
    public boolean isTradeAssessment() {
        TradeAssessmentRule rule = buildTradeAssessmentRule();
        return Objects.nonNull(rule) && Objects.nonNull(rule.getTradeAssessmentType());
    }

    /**
     * 生效时间
     *
     * @return
     */
    public Date fetchEffectiveTime() {
        return new Date(buildActivityEffectiveRule().getByTimeRule().getFixedTime());
    }

    /**
     * 获取失效时间
     *
     * @return
     */
    public Date fetchExpirationTime() {
        Date assessmentEndTime = fetchAssessmentEndTime();
        Date expirationTime = fetchExpirationTime0();
        log.info("获取失效时间. applyId={},activityId={},生效时间={},考核结束时间={},失效时间={}",
                getId(),
                getActivity_id(),
                LocalDateTimeUtil.getFormatDateTime(fetchEffectiveTime()),
                LocalDateTimeUtil.getFormatDateTime(assessmentEndTime),
                LocalDateTimeUtil.getFormatDateTime(expirationTime));
        if (assessmentEndTime == null) {
            return expirationTime;
        }
        if (expirationTime == null) {
            return assessmentEndTime;
        }
        // 取最小的
        return assessmentEndTime.before(expirationTime) ? assessmentEndTime : expirationTime;
    }

    /**
     * 获取失效时间
     *
     * @return
     */
    private Date fetchExpirationTime0() {
        if (ActivityTimeUtil.isNull(effective_time)) {
            throw TradeManageBizException.createExc("活动申请记录生效时间为空");
        }
        ActivityExpirationRule expirationRule = buildActivityExpirationRule();
        return ActivityTimeUtil.getExpirationTime(expirationRule, effective_time.getTime());
    }

    /**
     * 获取考核结束时间
     *
     * @return
     */
    private Date fetchAssessmentEndTime() {
        if (ActivityTimeUtil.isNull(effective_time)) {
            throw TradeManageBizException.createExc("活动申请记录生效时间为空");
        }
        TradeAssessmentRule rule = buildTradeAssessmentRule();
        return ActivityTimeUtil.getAssessmentEndTime(rule, effective_time.getTime());
    }
}
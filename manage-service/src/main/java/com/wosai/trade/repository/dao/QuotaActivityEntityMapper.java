package com.wosai.trade.repository.dao;

import com.wosai.trade.model.dal.ActivityQueryParam;
import com.wosai.trade.repository.dao.entity.ActivityEntity;
import com.wosai.trade.repository.dao.entity.QuotaActivityEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface QuotaActivityEntityMapper {
    int deleteByPrimaryKey(Long id);

    int insertSelective(QuotaActivityEntity record);

    QuotaActivityEntity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(QuotaActivityEntity record);

    int updateUpdateInfo(@Param("id") Long id, @Param("updateInfo") String updateInfo);

    int countQueryByCondition(ActivityQueryParam queryParam);

    List<QuotaActivityEntity> queryByCondition(ActivityQueryParam queryParam);

    QuotaActivityEntity selectAuditId(String auditId);

    List<Long> getExpireActivity();

    List<QuotaActivityEntity> usableActivity();

    List<QuotaActivityEntity> pageList(ActivityQueryParam param);
}
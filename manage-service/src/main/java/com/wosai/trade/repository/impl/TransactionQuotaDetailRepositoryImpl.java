package com.wosai.trade.repository.impl;

import com.wosai.trade.model.dal.*;
import com.wosai.trade.repository.TransactionQuotaDetailRepository;
import com.wosai.trade.repository.converter.TransactionQuotaDetailDalDOConverter;
import com.wosai.trade.repository.dao.TransactionQuotaDetailDao;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> Date: 2020/4/17 Time: 10:08 上午
 */
@Repository
public class TransactionQuotaDetailRepositoryImpl implements TransactionQuotaDetailRepository {

    @Resource
    private TransactionQuotaDetailDao transactionQuotaDetailDao;


    @Override
    public int insertOnDuplicateUpdate(TransactionQuotaDetailUpsertDalParam param) {
        return transactionQuotaDetailDao.insertOnDuplicateUpdate(param);
    }

    @Override
    public int batchInsertOnDuplicateUpdate(TransactionQuotaDetailBatchSaveDalParam param) {
        return transactionQuotaDetailDao.batchInsertOnDuplicateUpdate(param);
    }

    @Override
    public int insertIgnoreOnDuplicate(TransactionQuotaDetailUpsertDalParam param) {
        return transactionQuotaDetailDao.insertIgnoreOnDuplicate(param);
    }

    @Override
    public List<TransactionQuotaDetailDalDO> list(TransactionQuotaDetailQueryDalParam param) {
        return TransactionQuotaDetailDalDOConverter.convert(transactionQuotaDetailDao.list(param));
    }

    @Override
    public int updateStatus(TransactionQuotaDetailUpsertDalParam param) {
        return transactionQuotaDetailDao.updateStatus(param);
    }

    @Override
    public int updateQuota(TransactionQuotaDetailUpsertDalParam param) {
        return transactionQuotaDetailDao.updateQuota(param);
    }

    @Override
    public int updateStatusAndDate(TransactionQuotaDetailUpsertDalParam param) {
        return transactionQuotaDetailDao.updateStatusAndDate(param);
    }

    @Override
    public TransactionQuotaDetailDalDO query(TransactionQuotaDetailQueryDalParam param) {
        return TransactionQuotaDetailDalDOConverter.convert(transactionQuotaDetailDao.query(param));
    }
}

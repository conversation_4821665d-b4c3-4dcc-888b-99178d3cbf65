package com.wosai.trade.repository.dao;

import com.wosai.trade.model.dal.MerchantFeeRateCountDalParam;
import com.wosai.trade.model.dal.MerchantFeeRateQueryDalParam;
import com.wosai.trade.model.dal.MerchantFeeRateUpsertDalParam;
import com.wosai.trade.repository.dao.entity.MerchantFeeRateEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * The interface Merchant fee rate dao.
 *
 * <AUTHOR>
 */
@Mapper
public interface MerchantFeeRateDao {

    /**
     * Count int.
     *
     * @param param the param
     * @return the int
     */
    int count(MerchantFeeRateCountDalParam param);

    /**
     * Select list list.
     *
     * @param param the param
     * @return the list
     */
    List<MerchantFeeRateEntity> selectList(MerchantFeeRateQueryDalParam param);

    /**
     * Select by mch sn and combo id and pay way merchant fee rate entity.
     *
     * @param merchantSn   the merchant sn, non empty
     * @param tradeComboId the trade combo id
     * @param payWay       the pay way
     * @return the merchant fee rate entity
     */
    MerchantFeeRateEntity selectByMchSnAndComboIdAndPayWay(@Param("merchantSn") String merchantSn,
                                                           @Param("tradeComboId") long tradeComboId,
                                                           @Param("payWay") int payWay);

    /**
     * Select by id merchant fee rate entity.
     *
     * @param id the id
     * @return the merchant fee rate entity
     */
    MerchantFeeRateEntity selectById(@Param("id") long id);

    /**
     * Update by id int.
     *
     * @param param the param
     * @return the int
     */
    int updateById(MerchantFeeRateUpsertDalParam param);

    /**
     * Insert int.
     *
     * @param param the param
     * @return int int
     */
    int insert(MerchantFeeRateUpsertDalParam param);

    /**
     * Batch insert int.
     *
     * @param params the params
     * @return the int
     */
    int batchInsert(List<MerchantFeeRateUpsertDalParam> params);

    /**
     * 更新结束时间
     *
     * @param param
     * @return
     */
    int updateEndDate(MerchantFeeRateUpsertDalParam param);

    /**
     *　查找已开通多业务payWay列表
     *
     * @param appId
     * @param merchantSn
     * @return
     */
    List<Integer> findOpenTradeAppPayWayList(@Param("appId") Long appId,
                            @Param("merchantSn") String merchantSn,
                            @Param("payWays") List<Integer> payWays);

    /**
     * 查询最后一次关闭生效记录
     *
     * @param merchantSn
     * @param comboId
     * @return
     */
    MerchantFeeRateEntity selectLastDisabledByCombo(@Param("merchantSn") String merchantSn, @Param("comboId") Long comboId);

    /**
     * 查询历史记录
     *
     * @param merchantSn
     * @param beginDate
     * @return
     */
    List<MerchantFeeRateEntity> selectFeeRateHistory(@Param("merchantSn") String merchantSn,
                                                     @Param("beginDate") Date beginDate
    );
}

package com.wosai.trade.repository.dao;

import com.wosai.trade.model.dal.ActivityApplyQueryDalParam;
import com.wosai.trade.model.dal.ActivityApplyQueryDalResult;
import com.wosai.trade.model.dal.ActivityDashboardResult;
import com.wosai.trade.model.dal.IdOffsetPageable;
import com.wosai.trade.repository.dao.entity.ActivityApplyEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import javax.annotation.Nullable;
import java.util.Date;
import java.util.List;

/**
 * 所有外部调用改用repository
 *
 * @see com.wosai.trade.repository.ActivityApplyRepository
 */
@Mapper
public interface ActivityApplyDOMapper {
    int deleteByPrimaryKey(Long id);


    int insertSelective(ActivityApplyEntity record);

    ActivityApplyEntity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ActivityApplyEntity record);


    int countByActivityIdAndStatusAndSn(@Param("activityId") Long activityId, @Param("merchantSn") String merchantSn,
                                        @Param("subMerchantId") String subMerchantId, @Param("status") List<Integer> status);


    /**
     * 查询需要进行定时任务处理的商户申请任务
     *
     * @param param
     * @return
     */
    List<ActivityApplyEntity> listTaskActivityApplyDO(ActivityApplyQueryDalParam param);

    /**
     * 更新状态
     *
     * @param id
     * @return
     */
    @Deprecated
    // TODO: 2022/7/12  删掉
    int updateApplyStatusById(@Param("id") Long id, @Param("status") Integer status, @Param("process") String process);

    /**
     * 条件查询
     *
     * @param param
     * @return
     */
    int countByCondition(ActivityApplyQueryDalParam param);

    /**
     * 页面条件查询
     *
     * @param param
     * @return
     */
    List<ActivityApplyQueryDalResult> queryByCondition(ActivityApplyQueryDalParam param);

    /**
     * 获取商户最后一次参与的活动信息
     *
     * @param activityId
     * @param merchantSn
     * @return
     */
    ActivityApplyEntity getMerchantLastActivityApply(@Param("activityId") Long activityId, @Param("merchantSn") String merchantSn);

    /**
     * 获取商户、门店或终端最后一次参与的活动信息
     *
     * @param activityId
     * @param sn
     * @return
     */
    ActivityApplyEntity getLastActivityApply(@Param("activityId") Long activityId, @Param("sn") String sn);

    /**
     * 根据审批ID查询
     *
     * @param auditId
     * @return
     */
    ActivityApplyEntity queryByAuditId(Long auditId);

    /**
     * 活动费率校验
     *
     * @param startTime
     * @param endTime
     * @return
     */
    List<Long> getFeeRateCheckList(@Param("startTime") String startTime,
                                   @Param("endTime") String endTime);

    /**
     * 生效中的申请记录 4，5 8 生效中 取消中
     *
     * @param activityId
     * @param merchantId
     * @return
     */
    ActivityApplyEntity getEffectIngApply(@Param("activityId") Long activityId, @Param("merchantId") String merchantId);


    /**
     * 获取商户 可以取消的申请
     * 提交 处理中 前置通过  报名通过 生效中
     * 会存在List  支付源活动
     *
     * @param activityId
     * @param merchantSn
     * @return
     */
    List<ActivityApplyEntity> getApplyCanCancel(@Param("activityId") @Nullable Long activityId, @Param("merchantSn") String merchantSn);

    /**
     * 获取支付源活动报名列表
     *
     * @param activityId
     * @param merchantSn
     * @return
     */
    List<ActivityApplyEntity> getApplyListBySource(@Nullable @Param("activityId") Long activityId,
                                                   @Param("merchantSn") String merchantSn,
                                                   @Nullable @Param("statusList") List<Integer> statusList);


    /**
     * #########根据主键ID倒序#####获取近一条已取消的商户申请记录
     *
     * @param activityIdList
     * @param merchantSn
     * @return
     */
    ActivityApplyEntity getApplyCanceledByLastUpdateTime(@Param("merchantSn") String merchantSn,
                                                         @Param("activityIdList") List<Long> activityIdList);

    /**
     * 根据SN获取商户生效的活动
     *
     * @param merchantSn
     * @return
     */
    List<ActivityApplyEntity> getEffectIngApplyBySn(@Param("merchantSn") String merchantSn);

    /**
     * 生效中的申请记录 4，5 生效中 取消中
     *
     * @param activityId
     * @param merchantSn
     * @return
     */
    ActivityApplyEntity getEffectIngApplyByMerchantSn(@Param("activityId") Long activityId, @Param("merchantSn") String merchantSn);


    /**
     * 更新扩展字段
     *
     * @param id
     * @param extra
     * @return
     */
    int updateExtraById(@Param("id") Long id, @Param("extra") String extra);

    /**
     * 更新额度标示状态
     *
     * @param id
     * @param quotaStatus
     * @return
     */
    int updateQuotaStatusById(@Param("id") Long id, @Param("quotaStatus") Integer quotaStatus);

    /**
     * 根据活动ID列表与状态列表查询
     *
     * @param merchantSn
     * @param activityIdList
     * @return
     */
    List<ActivityApplyEntity> queryByActivityAndStatusList(@Param("merchantSn") String merchantSn,
                                                           @Param("activityIdList") @Nullable List<Long> activityIdList,
                                                           @Param("statusList") @Nullable List<Integer> statusList
    );

    /**
     * 活动申请记录数据看板
     *
     * @return
     */
    List<ActivityDashboardResult> queryByDashBoard(@Param("activityId") Long activityId);


    /**
     * 使用ID偏移分页
     *
     * @param activityId
     * @param statusList
     * @param pageable
     * @return
     */
    List<ActivityApplyEntity> queryByOffsetPage(@Param("activityId") @Nullable Long activityId,
                                                @Param("statusList") @Nullable List<Integer> statusList,
                                                @Param("pageable") IdOffsetPageable pageable);

    /**
     * 临时处理　线上数据在2万条左右。
     *
     * @return
     */
    @Deprecated
    List<ActivityApplyEntity> queryBySourceList(@Param("merchantSn") String merchantSn);

    /**
     * 根据商户编号和状态列表查询申请记录
     *
     * @param merchantSn 商户编号
     * @param statusList 状态列表
     * @return 申请记录列表
     */
    List<ActivityApplyEntity> queryByMerchantSnAndStatusList(@Param("merchantSn") String merchantSn,
                                                             @Param("statusList") List<Integer> statusList);

    /**
     * 查询考核活动申请记录
     *
     * @param merchantSn
     * @param assessmentTime
     * @param statusList
     * @return
     */
    List<ActivityApplyEntity> queryAssessmentList(@Nullable @Param("merchantSn") String merchantSn,
                                                  @Param("assessmentTime") Date assessmentTime,
                                                  @Param("statusList") List<Integer> statusList,
                                                  @Param("pageable") IdOffsetPageable pageable);

    /**
     * 更新考核时间
     *
     * @param update
     * @return
     */
    int updateAssessmentTime(ActivityApplyEntity update);

    /**
     * 根据父级ID查询活动申请记录
     *
     * @param parentId 父级ID
     * @return 活动申请记录列表
     */
    List<ActivityApplyEntity> selectByParentId(@Param("parentId") Long parentId);
}
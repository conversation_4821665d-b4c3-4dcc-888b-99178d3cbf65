package com.wosai.trade.repository.dao.entity;

import com.wosai.trade.constant.enums.ApplyAuditProcStatusEnum;
import com.wosai.trade.constant.enums.ApplyAuditSubProcStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 审批处理记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Getter
@Setter
public class ApplyAuditRecordEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 审批id
     */
    private String id;

    /**
     * 商户号
     */
    private String merchantSn;

    /**
     * 商户id
     */
    private String merchantId;

    /**
     * 审批sn
     */
    private String auditSn;

    /**
     * 审批模板id
     */
    private String auditTemplateId;

    /**
     * 审批事件
     */
    private String templateEvent;

    /**
     * 审批发起人名称
     */
    private String operatorName;

    /**
     * 审批通过后，trade-manage针对审批的后续处理状态 (0-待处理 1-处理中 2-处理成功 3-处理失败)
     *
     * @see ApplyAuditProcStatusEnum
     */
    private Integer procStatus;

    /**
     * 审批子处理状态, 依赖于template_event (0-未知 1-等待开通D0 2-商户D0开通中 3-商户变更二级机构号成功)
     *
     * @see ApplyAuditSubProcStatusEnum
     */
    private Integer subProcStatus;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 扩展参数
     */
    private String extra;

    /**
     * 备注
     */
    private String remark;

//    /**
//     * 业务哈希 用于判断是否是相同的审批
//     */
//    private String bizHash;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 更新时间
     */
    private Date mtime;

    /**
     * 是否删除 0:未删除, 1:已删除)
     */
    private Integer deleted;
}

package com.wosai.trade.repository.dao.entity;

import com.wosai.pantheon.util.MapUtil;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.Map;

@Data
@Accessors(chain = true)
public class TradeActivityEntity {

    public static final int ACTIVITY_LADDER = 0;

    public static final int ACTIVITY_FBFQ_RATE = 1;

    public static final int ACTIVITY_COMMON_RATE = 2;

    public static final int ACTIVITY_MICRO_PUHUI_RATE = 3;

    /**
     * 银行卡绑卡活动
     */
    public static final int ACTIVITY_BIND_BANKCARD = 4;


    /**
     * 微信行业活动报名，临时，后续归纳到统一的费率
     */
    public static final int ACTIVITY_TMP_WECHAT = 2;


    public static final int STATUS_OPEN = 1;
    public static final int STATUS_CLOSE = 0;
    public static final int STATUS_IN_PROGRESS = 2;

    /**
     * 已移动的数据状态
     */
    public static final int STATUS_MOVED = 10;

    public static final int TYPE_PREFERENTIAL_RATE = 1;
    public static final int TYPE_PREMIUM_RATE = 2;
    public static final int TYPE_COMMON_RATE = 3;

    /**
     * 小微普惠活动，extra字段记录数据插入的方式
     */
    public static final String EXTRA_INSERT_MODE = "insert_mode";
    public static final String TRADE_AMOUNT_BOUND_LOW = "trade_amount_bound_low";
    public static final String TRADE_AMOUNT_BOUND_HIGH = "trade_amount_bound_high";
    public static final String FROM_ACTIVITY_TYPE = "from_activity_type";  //转入普惠活动原来的活动类型

    public static final int INSERT_MODE_MICRO_PUHUI_MANUAL = 1;//通过接口手工生效普惠套餐
    public static final int INSERT_MODE_MICRO_PUHUI_APPLY = 2;//通过商家活动页申请生效普惠套餐
    public static final int INSERT_MODE_MICRO_PUHUI_NEW = 3;//新入网商户生效普惠活动套餐
    public static final int INSERT_MODE_MICRO_PUHUI_COMMON_AUDIT = 4;//通过普通审批(无城市、是否直营等限制)申请普惠活动套餐
    public static final int INSERT_MODE_MICRO_PUHUI_AGENT_AUDIT = 5;//通过代理审批（有城市限制）生效普惠活动套餐
    public static final int INSERT_MODE_MICRO_PUHUI_OTHER_ACTIVITY_IN = 6;//通过其他活动转入

    /**
     * extra字段 ，套餐id
     */
    public static final String EXTRA_COMBO_ID = "combo_id";

    public static final String EXTRA_TAG_NAME = "tag_name";

    private long id;
    private String merchantSn;
    private int type;
    private int status;
    private LocalDate beginDate;
    private LocalDate endDate;
    private Map<String, Object> auditInfo;
    private Map<String, Object> extra;
    private long ctime;
    private long mtime;


    public Long getExtraComboId(){
        return MapUtil.getLong(extra, EXTRA_COMBO_ID);
    }

}

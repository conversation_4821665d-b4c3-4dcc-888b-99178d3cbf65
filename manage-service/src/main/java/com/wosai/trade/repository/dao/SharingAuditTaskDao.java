package com.wosai.trade.repository.dao;


import com.wosai.trade.repository.dao.entity.SharingAuditTaskEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SharingAuditTaskDao {

    int insert(SharingAuditTaskEntity sharingAuditTaskEntity);

    /**
     * 根据状态查询分账审批任务
     *
     * @param status
     * @param limit
     * @return
     */
    List<SharingAuditTaskEntity> querySharingAuditTaskByStatus(@Param("status") List<Integer> status, @Param("limit") int limit);

    /**
     * 根据审批id查询任务
     * @param auditId
     * @return
     */
    SharingAuditTaskEntity selectByAuditIdWithType(@Param("audit_id") Long auditId, @Param("audit_type") Integer auditType);

    /**
     * 更新
     * @param sharingAuditTaskEntity
     */
    void updateStatusAndBizParams(SharingAuditTaskEntity sharingAuditTaskEntity);
}

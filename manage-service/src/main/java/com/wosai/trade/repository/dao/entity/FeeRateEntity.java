package com.wosai.trade.repository.dao.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wosai.trade.service.enums.FeeRateTypeEnum;
import com.wosai.trade.util.JsonUtil;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/2/16.
 */
@Accessors(chain = true)
@Data
public abstract class FeeRateEntity {
    private Long id;

    private Long appId;

    private String merchantSn;

    private Long tradeComboId;

    private Integer payWay;

    private Date beginDate;

    private Date endDate;

    private Integer b2cInUse;

    private Integer c2bInUse;

    private Integer appInUse;

    private Integer miniInUse;

    private Integer h5InUse;

    private Integer wapInUse;

    private String feeRateType;

    private String feeRate;

    private Integer status;

    private String auditSn;

    private Date createAt;

    private Date updateAt;

    public List<LadderFeeRate> buildLadderFeeRates() {
        if (FeeRateTypeEnum.LADDER.name().equalsIgnoreCase(feeRateType)) {
            return JsonUtil.decode(feeRate, new TypeReference<List<LadderFeeRate>>() {});
        } else {
            return Collections.emptyList();
        }
    }

    public List<ChannelFeeRate> buildChannelFeeRates() {
        if (FeeRateTypeEnum.CHANNEL.name().equalsIgnoreCase(feeRateType)) {
            return JsonUtil.decode(feeRate, new TypeReference<List<ChannelFeeRate>>() {});
        } else {
            return Collections.emptyList();
        }
    }

    public List<ChannelLadderFeeRate> buildChannelLadderFeeRates(){
        if (FeeRateTypeEnum.CHANNEL_LADDER.name().equalsIgnoreCase(feeRateType)) {
            return JsonUtil.decode(feeRate, new TypeReference<List<ChannelLadderFeeRate>>() {});
        } else {
            return Collections.emptyList();
        }
    }

    /**
     * 阶梯费率
     */
    @Accessors(chain = true)
    @Data
    public static class LadderFeeRate {

        private Double min;

        private Double max;

        private String fee_rate;

    }

    /**
     * 资金渠道费率
     */
    @Data
    public static class ChannelFeeRate {
        /**
         * 资金渠道类型
         */
        private String type;

        /**
         * 费率，名称定义错误，新的都将上送fee_rate
         */
        @Deprecated
        @JsonProperty(value = "fee")
        private String fee;

        /**
         * 费率
         */
        @JsonProperty(value = "fee_rate")
        private String feeRate;

        /**
         * 单笔最高
         */
        private String max;
    }

    /**
     * 资金渠道阶梯费率
     */
    @Data
    public static class ChannelLadderFeeRate {
        /**
         * 资金渠道类型
         */
        private String type;
        /**
         * 阶梯费率
         */
        @JsonProperty(value = "ladder_fee_rates")
        private List<LadderFeeRate> ladderFeeRates;
    }
}

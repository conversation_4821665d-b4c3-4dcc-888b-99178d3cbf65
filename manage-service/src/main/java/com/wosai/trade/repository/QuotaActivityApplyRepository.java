package com.wosai.trade.repository;

import com.wosai.trade.repository.dao.entity.QuotaActivityApplyEntity;

import java.util.List;

/**
 * Description: This is a description of the class.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/5/13
 */
public interface QuotaActivityApplyRepository {

    /**
     * 额度包插入方法
     *
     * @param record
     * @return
     */
    int insertSelective(QuotaActivityApplyEntity record);

    /**
     * 更新
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(QuotaActivityApplyEntity record);

    /**
     * 更新扩展字段
     *
     * @param record
     * @return
     */
    void updateExtra(QuotaActivityApplyEntity record);

    /**
     * 根据维度查询
     *
     * @param dimension  维度对象
     * @param statusList 状态列表
     * @return
     */
    List<QuotaActivityApplyEntity> queryByDimension(QuotaActivityApplyEntity.Dimension dimension, List<Integer> statusList);

    /**
     * 根据维度查询可取消的申请列表
     *
     * @param dimension 维度对象
     * @return
     */
    List<QuotaActivityApplyEntity> queryCanCancelByDimension(QuotaActivityApplyEntity.Dimension dimension);

    /**
     * 根据维度查询生效中的申请
     *
     * @param dimension
     * @return
     */
    QuotaActivityApplyEntity queryEffectiveByDimension(QuotaActivityApplyEntity.Dimension dimension);
}

package com.wosai.trade.repository.dao.entity;

import com.wosai.upay.common.util.JacksonUtil;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class SharingAuditTaskEntity {
    private Long id;
    private Long auditId;
    private Integer auditType;
    private Integer status;
    private Map<String, Object> bizParams;

    private Long ctime;
    private Long mtime;
    private Long version;
    private Boolean deleted;

    public SharingAuditTaskEntity.BizParams buildBizParams() {
        SharingAuditTaskEntity.BizParams bizParams = null;
        try {
            bizParams = JacksonUtil.toBeanQuietly(JacksonUtil.toJsonString(this.bizParams), SharingAuditTaskEntity.BizParams.class);
        } catch (Exception e) {

        }
        if(bizParams == null){
            bizParams = new SharingAuditTaskEntity.BizParams();
        }
        return bizParams;
    }

    public void setBizParams(SharingAuditTaskEntity.BizParams bizParams){
        this.bizParams = bizParams.toMap();
    }

    public void setBizParams(Map<String, Object> bizParams) {
        this.bizParams = bizParams;
    }

    public void setBizParams(byte[] bizParams) {
        String bizParamsStr = new String(bizParams);
        try {
            this.bizParams = JacksonUtil.toBeanQuietly(bizParamsStr, Map.class);
        } catch (Exception e) {

        }
    }

    @Data
    public static class BizParams {
        /**
         * 提交开通海科的商户号 对应的 业务任务id
         */
        private Map<String, String> submitHaikeOpenBizTaskMap;

        /**
         * 开通海科分账商户业务结果
         */
        private Map<String, String> haikeOpenBizResponse;

        /**
         * 海科建立分账关系请求
         * key: payer_merchant_sn + "-" + receiver_merchant_sn
         * value: 分账服务任务id
         */
        private Map<String, String> submitHaikeRelationBizTaskMap;

        /**
         * 海科建立分账关系请求
         * key: payer_merchant_sn + "-" + receiver_merchant_sn
         * value: 分账服务任务结果
         */
        private Map<String, String> submitHaikeRelationBizResponse;

        /**
         * 原始的上游请求建立分账关系。当商户都签约完后会由系统重新触发分账关心建立
         */
        private Map<String, Object> originalSubmitHaikeRelationRequest;

        /**
         * 未开通分账的商户
         */
        private List<String> notOpenSharingMerchantSns;

        /**
         * 收单机构
         */
        private Integer organization;

        /**
         * 未在时效性内开通分账标记
         */
        private boolean isNotInTimeOpenSharingFlag;

        public Map<String, Object> toMap() {
            Map<String, Object> result = null;
            result = JacksonUtil.toBeanQuietly(JacksonUtil.toJsonString(this), Map.class);
            if (result == null) {
                result = new HashMap<>();
            }
            return result;
        }
    }
}

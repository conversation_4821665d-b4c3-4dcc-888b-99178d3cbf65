package com.wosai.trade.repository.dao;

import com.wosai.trade.model.dal.RefundOrderSnQueryParam;
import com.wosai.trade.repository.dao.entity.RefundOrderSnRelationEntity;

import java.util.List;


public interface RefundOrderSnRelationEntityMapper {
    int deleteByPrimaryKey(Long id);

    int insert(RefundOrderSnRelationEntity record);

    int insertSelective(RefundOrderSnRelationEntity record);

    RefundOrderSnRelationEntity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(RefundOrderSnRelationEntity record);

    int updateByPrimaryKey(RefundOrderSnRelationEntity record);

    List<RefundOrderSnRelationEntity> selectWithCondition(RefundOrderSnQueryParam refundOrderSnQueryParam);

}
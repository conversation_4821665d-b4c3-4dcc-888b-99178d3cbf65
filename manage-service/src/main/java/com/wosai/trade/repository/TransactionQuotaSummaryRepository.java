package com.wosai.trade.repository;

import com.wosai.trade.model.dal.*;

import java.util.List;

/**
 * <AUTHOR> Date: 2020/4/17 Time: 9:06 上午
 */
public interface TransactionQuotaSummaryRepository {

    /**
     * insert
     *
     * @param param param
     * @return int
     */
    int insert(TransactionQuotaSummaryUpsertDalParam param);

    /**
     * insertOnDuplicateUpdate
     *
     * @param param param
     * @return int
     */
    int insertOnDuplicateUpdate(TransactionQuotaSummaryUpsertDalParam param);

    /**
     * batchInsertOnDuplicateUpdate
     *
     * @param param param
     * @return int
     */
    int batchInsertOnDuplicateUpdate(TransactionQuotaSummaryBatchUpsertDalParam param);

    /**
     * insertIgnoreOnDuplicate
     *
     * @param param param
     * @return int
     */
    int insertIgnoreOnDuplicate(TransactionQuotaSummaryUpsertDalParam param);

    /**
     * query
     *
     * @param param param
     * @return int
     */
    TransactionQuotaSummaryDalDO query(TransactionQuotaSummaryQueryDalParam param);

    /**
     * queryForUpdate
     *
     * @param param param
     * @return int
     */
    TransactionQuotaSummaryDalDO queryForUpdate(TransactionQuotaSummaryQueryDalParam param);

    /**
     * list
     *
     * @param param param
     * @return int
     */
    List<TransactionQuotaSummaryDalDO> list(TransactionQuotaSummaryQueryDalParam param);

    /**
     * listForUpdate
     *
     * @param param param
     * @return int
     */
    List<TransactionQuotaSummaryDalDO> listForUpdate(TransactionQuotaSummaryQueryDalParam param);

    /**
     * update
     *
     * @param param param
     * @return int
     */
    int update(TransactionQuotaSummaryUpsertDalParam param);

    /**
     * updateWithOptimisticLock
     *
     * @param param param
     * @return int
     */
    int updateWithOptimisticLock(TransactionQuotaSummaryUpsertDalParam param);
}

package com.wosai.trade.repository.dao;

import com.wosai.trade.repository.dao.entity.MerchantTradeAmountEntity;
import org.apache.ibatis.annotations.Param;
import org.mapstruct.Mapper;

import java.util.List;
import java.util.Map;


@Mapper
public interface MerchantTradeAmountDao {

    List<MerchantTradeAmountEntity> getAllOldMchTradeAmountPage(Map<String,Object> map);

    MerchantTradeAmountEntity queryMchTradeAmountByMchSnAndMonth(@Param("merchantSn") String merchantSn, @Param("month") String month);

    Integer getTradeAmountCountByMonth(@Param("month") String month);

}

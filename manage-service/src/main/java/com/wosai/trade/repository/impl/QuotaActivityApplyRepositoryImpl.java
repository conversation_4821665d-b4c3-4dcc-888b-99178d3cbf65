package com.wosai.trade.repository.impl;

import com.wosai.trade.biz.quota.QuotaActivityApplyChangeEventBiz;
import com.wosai.trade.repository.QuotaActivityApplyRepository;
import com.wosai.trade.repository.dao.QuotaActivityApplyEntityMapper;
import com.wosai.trade.repository.dao.entity.QuotaActivityApplyEntity;
import com.wosai.trade.service.activity.constant.ActivityConstants;
import com.wosai.trade.service.activity.constant.QuotaActivityConstants;
import com.wosai.trade.util.ValidationUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description: This is a description of the class.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/5/13
 */
@Repository
public class QuotaActivityApplyRepositoryImpl implements QuotaActivityApplyRepository {

    @Resource
    private QuotaActivityApplyEntityMapper quotaActivityApplyEntityMapper;
    @Resource
    private QuotaActivityApplyChangeEventBiz quotaActivityApplyChangeEventBiz;

    @Override
    public int insertSelective(QuotaActivityApplyEntity record) {
        int cnt = quotaActivityApplyEntityMapper.insertSelective(record);
        //增加变更事件
        quotaActivityApplyChangeEventBiz.execute(record.getId());
        return cnt;
    }

    @Override
    public int updateByPrimaryKeySelective(QuotaActivityApplyEntity record) {
        int cnt = quotaActivityApplyEntityMapper.updateByPrimaryKeySelective(record);
        //增加变更事件
        quotaActivityApplyChangeEventBiz.execute(record.getId());
        return cnt;
    }

    @Override
    public void updateExtra(QuotaActivityApplyEntity record) {
        ValidationUtils.check(Objects.nonNull(record), "record is null");
        int cnt = quotaActivityApplyEntityMapper.updateExtra(record);
        ValidationUtils.check(cnt == NumberUtils.INTEGER_ONE, "更新失败. id=" + record.getId());
        quotaActivityApplyChangeEventBiz.execute(record.getId());
    }

    /**
     * 根据维度查询
     *
     * @param dimension  　维度对象
     * @param statusList 　状态列表
     * @return
     */
    @Override
    public List<QuotaActivityApplyEntity> queryByDimension(QuotaActivityApplyEntity.Dimension dimension, List<Integer> statusList) {
        ValidationUtils.notNull(dimension, "dimension is null");
        ValidationUtils.notEmpty(statusList, "statusList is empty");
        ValidationUtils.check(StringUtils.isNotBlank(dimension.getMerchantSn()), "merchantSn is blank");
        List<QuotaActivityApplyEntity> list = quotaActivityApplyEntityMapper.getApplyByMerchantSnList(dimension.getMerchantSn(), statusList);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        // 过滤掉维度相同的记录
        return list.stream().filter(apply ->
                Objects.equals(apply.generateDimension(), dimension.md5())).collect(Collectors.toList());
    }

    /**
     * 根据维度查询可取消的记录
     *
     * @param dimension 维度对象
     * @return
     */
    @Override
    public List<QuotaActivityApplyEntity> queryCanCancelByDimension(QuotaActivityApplyEntity.Dimension dimension) {
        return queryByDimension(dimension, QuotaActivityConstants.PROCESSING_STATUS);
    }

    /**
     * 根据维度查询有效的记录
     *
     * @param dimension
     * @return
     */
    @Override
    public QuotaActivityApplyEntity queryEffectiveByDimension(QuotaActivityApplyEntity.Dimension dimension) {
        List<QuotaActivityApplyEntity> list = queryByDimension(dimension, Collections.singletonList(ActivityConstants.EFFECT));
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }
}

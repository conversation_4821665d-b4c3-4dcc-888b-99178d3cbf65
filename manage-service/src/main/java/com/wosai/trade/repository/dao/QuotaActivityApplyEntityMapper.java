package com.wosai.trade.repository.dao;

import com.wosai.trade.model.dal.ActivityApplyQueryDalParam;
import com.wosai.trade.model.dal.QuotaActivityDashBoardResult;
import com.wosai.trade.model.dal.QuotaRecordSharedDalResult;
import com.wosai.trade.repository.dao.entity.QuotaActivityApplyEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import javax.annotation.Nullable;
import java.util.List;

public interface QuotaActivityApplyEntityMapper {
    int deleteByPrimaryKey(Long id);

    int insertSelective(QuotaActivityApplyEntity record);

    QuotaActivityApplyEntity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(QuotaActivityApplyEntity record);

    int countByActivityIdAndStatusAndSn(@Param("activityId") Long activityId, @Param("merchantSn") String merchantSn,
                                        @Param("status") List<Integer> status);

    /**
     * 查询商户曾经生效过的活动
     * 现在不为生效中 not in (4,5)
     *
     * @return
     */
    List<QuotaActivityApplyEntity> onceInEffectByAIDAndSn(@Param("activityId") Long activityId, @Param("merchantSn") String merchantSn);


    int updateExtra(QuotaActivityApplyEntity record);
    /**
     * 条件查询
     *
     * @param param
     * @return
     */
    int countByCondition(ActivityApplyQueryDalParam param);

    /**
     * 页面条件查询
     *
     * @param param
     * @return
     */
    List<QuotaActivityApplyEntity> queryByCondition(ActivityApplyQueryDalParam param);

    /**
     * 更新关联申请ID
     *
     * @param id
     * @param bindApplyIds
     * @return
     */
    int updateBindApplyId(@Param("id") Long id, @Param("bindApplyIds") String bindApplyIds);

    /**
     * 更新关联申请ids
     *
     * @param ids
     * @param bindApplyIds
     * @return
     */
    int updateBindApplyIds(@Param("ids") List<Long> ids, @Param("bindApplyIds") String bindApplyIds);

    /**
     * 获取可以取消的申请记录 根据活动ID
     *
     * @param activityId
     * @return
     */
    List<Long> getApplyCanCancelByActivityId(@Param("activityId") Long activityId);

    /**
     * 获取未完成的申请记录 根据商户SN
     *
     * @param merchantSn
     * @return
     */
    List<QuotaActivityApplyEntity> getUnfinishedApplyByMerchantSn(@Param("merchantSn") String merchantSn,
                                                                  @Nullable @Param("type") Integer type);


    /**
     * 获取可以取消的申请记录 根据商户SN, 用于商户信息变动后，取消活动的场景
     *
     * @param merchantSn
     * @return
     */
    List<QuotaActivityApplyEntity> getApplyCanCancelByMerchantSnWhenMerchantInfoChange(@Param("merchantSn") String merchantSn);

    /**
     * 获取可以取消的申请记录的数量 根据活动ID
     *
     * @param activityId
     * @return
     */
    int countApplyCanCancelByActivityId(@Param("activityId") Long activityId);


    /**
     * 申请记录 生效
     *
     * @param startId
     * @param startTime
     * @param limit
     * @return
     */
    List<QuotaActivityApplyEntity> listApplyEffect(@Param("startId") Long startId, @Param("startTime") String startTime,
                                                   @Param("limit") int limit);

    /**
     * 申请记录 失效
     *
     * @param startId
     * @param endTime
     * @param limit
     * @return
     */
    List<QuotaActivityApplyEntity> listApplyInvalid(@Param("startId") Long startId, @Param("endTime") String endTime,
                                                    @Param("limit") int limit);

    /**
     * 查询商户生效中的活动 根据商户SN
     * 生效中 正常情况只会有一条
     *
     * @param merchantSn
     * @return
     */
    @Deprecated
    QuotaActivityApplyEntity getEffectIngApplyBySn(@Param("merchantSn") String merchantSn,
                                                   @Nullable @Param("type") Integer type);


    /**
     * 查询商户生效中的活动申请记录列表 根据商户sn
     * @param merchantSn
     * @param statusList
     * @return
     */
    List<QuotaActivityApplyEntity> getApplyByMerchantSnList(@Param("merchantSn") String merchantSn,
                                                            @Param("statusList") List<Integer> statusList);

    /**
     * 查询商户生效中的活动 根据商户ID
     *
     * @param merchantId
     * @return
     */
    @Deprecated
    QuotaActivityApplyEntity getEffectIngApplyByMerchantId(@Param("merchantId") String merchantId,
                                                           @Nullable @Param("type") Integer type);

    /**
     * 查询商户生效中的活动申请记录列表 根据商户ID
     *
     * @param merchantId
     * @return
     */
    List<QuotaActivityApplyEntity> getApplyByMerchantIdList(@Param("merchantId") String merchantId, @Param("statusList") List<Integer> statusList);


    /**
     * 申请记录ID 查询 生效申请
     *
     * @param quotaRecordId
     * @return
     */
    List<QuotaActivityApplyEntity> getEffectIngApplyByQuotaRecordId(@Param("quotaRecordId") Long quotaRecordId);

    /**
     * 共享活动 取一条
     *
     * @param quotaRecordId
     * @return
     */
    QuotaActivityApplyEntity getEffectIngApplyByQuotaRecordIdOne(@Param("quotaRecordId") Long quotaRecordId);

    /**
     * 额度记录ID 一致 申请数量
     *
     * @param quotaRecordId
     * @return
     */
    int countByQuotaRecordId(@Param("quotaRecordId") Long quotaRecordId);

    /**
     * 额度共享查询
     * @param quotaRecordIds
     * @return
     */
    List<QuotaRecordSharedDalResult> queryByQuotaShared(@Param("quotaRecordIds") List<Long> quotaRecordIds);

    /**
     * 获取参与活动商户列表
     *
     * @param page
     * @return
     */
    List<String> getJoinActivityMerchantIdList(@Param("page") Pageable page);

    /**
     * 查询共享额度活动申请记录
     *
     * @param merchantId
     * @param quotaRecordId
     * @param statusList
     * @return
     */
    QuotaActivityApplyEntity getApplyByQuotaIdStatus(@Param("merchantId") String merchantId,
                                               @Param("quotaRecordId") Long quotaRecordId,
                                               @Param("statusList") List<Integer> statusList);

    /**
     * 活动申请记录数据看板
     *
     * @return
     */
    List<QuotaActivityDashBoardResult> queryByDashBoard(@Param("activityId") Long activityId);

    int addMerchantCostQuota(@Param("merchantId") String merchantId,
                             @Param("quotaRecordId") Long quotaRecordId,
                             @Param("statusList") List<Integer> statusList,
                             @Param("costQuota") Long costQuota);

    /**
     * 消耗额度回退
     *
     * @param merchantId
     * @param quotaRecordId
     * @param costQuota
     * @return
     */
    int rollbackCostQuota(@Param("merchantId") String merchantId,
                          @Param("quotaRecordId") Long quotaRecordId,
                          @Param("costQuota") Long costQuota);
}
package com.wosai.trade.repository.dao;

import com.wosai.trade.model.dal.IdOffsetPageable;
import com.wosai.trade.repository.dao.entity.CrmFeeRateChangeEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import javax.annotation.Nullable;
import java.util.Date;
import java.util.List;

public interface CrmFeeRateChangeMapper {
    int deleteByPrimaryKey(Long id);

    int insertSelective(CrmFeeRateChangeEntity record);

    int updateByPrimaryKeySelective(CrmFeeRateChangeEntity record);

    CrmFeeRateChangeEntity findByPkId(@Param("id") Long id);

    /**
     * 根据商户号与状态查询
     *
     * @param merchantSn 商户号
     * @param payWay     支付源
     * @param statusList 状态
     * @return
     */
    List<CrmFeeRateChangeEntity> findByStatusList(@Nullable @Param("merchantSn") String merchantSn,
                                                  @Nullable @Param("payWay") Integer payWay,
                                                  @Param("statusList") List<Integer> statusList,
                                                  @Param("types") List<Integer> types);

    /**
     * 根据状态列表查询
     */
    List<CrmFeeRateChangeEntity> findLastUpdateInitMerchantList(
            @Param("beginTime") Date beginTime,
            @Param("endTime") Date endTime,
            @Param("statusList") List<Integer> statusList,
            @Param("page")Pageable page,
            @Param("types") List<Integer> types
    );

    /**
     * 分组查找商户列表
     *
     * @param statusList
     * @return
     */
    List<String> findMerchantSnByGroupList(
            @Param("beginTime") Date beginTime,
            @Param("statusList") List<Integer> statusList,
            @Param("page")Pageable page ,
            @Param("type") Integer type
    );

    /**
     * 查询可延迟生效交换记录列表
     *
     * @param pageable
     * @param statusList
     * @param typeList
     * @return
     */
    List<CrmFeeRateChangeEntity> queryDelayTakeAffectActivityApply(
            @Param("pageable") IdOffsetPageable pageable,
            @Param("statusList") List<Integer> statusList,
            @Param("typeList") List<Integer> typeList);
}
package com.wosai.trade.repository.impl;

import com.wosai.trade.biz.activity.quota.PublishApplyActivityBiz;
import com.wosai.trade.model.dal.IdOffsetPageable;
import com.wosai.trade.repository.ActivityApplyRepository;
import com.wosai.trade.repository.dao.ActivityApplyDOMapper;
import com.wosai.trade.repository.dao.entity.ActivityApplyEntity;
import com.wosai.trade.service.activity.constant.ActivityConstants;
import com.wosai.trade.service.activity.model.TradeAssessmentRule;
import com.wosai.trade.util.ValidationUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description: 活动报名申请记录处理类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/5/13
 */
@Slf4j
@Repository
public class ActivityApplyRepositoryImpl implements ActivityApplyRepository {

    @Resource
    private ActivityApplyDOMapper activityApplyDOMapper;
    @Resource
    private PublishApplyActivityBiz publishApplyActivityBiz;


    @Override
    public ActivityApplyEntity selectByPrimaryKey(Long id) {
        return activityApplyDOMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(ActivityApplyEntity record) {
        ValidationUtils.check(Objects.nonNull(record), "待更新记录不存在");
        ValidationUtils.check(Objects.nonNull(record.getId()), "待更新记录主键ID为空");
        int row = activityApplyDOMapper.updateByPrimaryKeySelective(record);
        //上送crm
        publishApplyActivityBiz.publishSourceApplyActivity(record.getId());
        return row;
    }

    @Override
    public void updateExtraById(Long id, String extra) {
        activityApplyDOMapper.updateExtraById(id, extra);
    }

    @Override
    public List<ActivityApplyEntity> getEffectIngApplyBySn(String merchantSn) {
        ValidationUtils.notNull(merchantSn, "merchantSn is null.");
        return activityApplyDOMapper.getEffectIngApplyBySn(merchantSn);
    }

    @Override
    public List<ActivityApplyEntity> queryAssessmentList(String merchantSn, Date assessmentTime,
                                                         List<Integer> statusList, IdOffsetPageable pageable) {
        return activityApplyDOMapper.queryAssessmentList(merchantSn, assessmentTime, statusList, pageable);
    }

    /**
     * 更新考核时间
     *
     * @param update
     */
    @Override
    public void updateAssessmentTime(ActivityApplyEntity update) {
        ValidationUtils.notNull(update, "update record is null.");
        ValidationUtils.notNull(update.getId(), "id is null.");
        ValidationUtils.notEmpty(update.getMerchant_sn(), "merchantSn is null.");
        int rows = activityApplyDOMapper.updateAssessmentTime(update);
        log.info("updateAssessmentTime id={},merchantSn={},rows={}", update.getId(), update.getMerchant_sn(), rows);
    }

    /**
     * 检查是否存在考核活动报名申请记录
     * 查询状态为4（生效中）和8的记录
     *
     * @param merchantSn 商户ID
     * @return 是否存在符合条件的记录
     */
    @Override
    public boolean existsAssessmentActivityApply(String merchantSn) {
        return CollectionUtils.isNotEmpty(queryAssessmentActivityApplyList(merchantSn));
    }

    /**
     * 检查是否存在考核活动报名申请记录
     * 查询状态为4（生效中）和8的记录
     *
     * @param merchantSn 商户ID
     * @return 是否存在符合条件的记录
     */
    @Override
    public List<ActivityApplyEntity> queryAssessmentActivityApplyList(String merchantSn) {
        ValidationUtils.notEmpty(merchantSn, "商户ID不能为空");
        // 定义要查询的状态列表：4（生效中）和8
        List<Integer> statusList = ActivityConstants.ASSESSMENT_APPLY_EFFECT_LIST;
        // 查询符合条件的记录数量
        List<ActivityApplyEntity> list = activityApplyDOMapper.queryByMerchantSnAndStatusList(merchantSn, statusList);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        // 检查活动是否为考核活动
        return list.stream().filter(activityApplyEntity -> {
            TradeAssessmentRule rule = activityApplyEntity.buildTradeAssessmentRule();
            return Objects.nonNull(rule) && Objects.nonNull(rule.getTradeAssessmentType());
        }).collect(Collectors.toList());
    }

    /**
     * 根据父级ID查询活动申请记录
     *
     * @param parentId 父级ID
     * @return 活动申请记录列表
     */
    @Override
    public List<ActivityApplyEntity> selectByParentId(Long parentId) {
        ValidationUtils.notNull(parentId, "父级ID不能为空");
        return activityApplyDOMapper.selectByParentId(parentId);
    }
}

package com.wosai.trade.repository.dao.entity;

import java.util.Date;

public class ActivityTransactionReportEntity {
    private String tsn;

    private String merchantId;

    private Long normalActivityId;

    private Long subStatusDetailId;

    private Long comboId;

    private Long quotaActivityId;

    private Long discountQuota;

    private String discountQuotaFeeRate;

    private Long discountQuotaUsable;

    private Long costQuota;

    private Date ctime;

    private Date mtime;

    private String feeRate;

    public String getTsn() {
        return tsn;
    }

    public void setTsn(String tsn) {
        this.tsn = tsn;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public Long getNormalActivityId() {
        return normalActivityId;
    }

    public void setNormalActivityId(Long normalActivityId) {
        this.normalActivityId = normalActivityId;
    }

    public Long getSubStatusDetailId() {
        return subStatusDetailId;
    }

    public void setSubStatusDetailId(Long subStatusDetailId) {
        this.subStatusDetailId = subStatusDetailId;
    }

    public Long getComboId() {
        return comboId;
    }

    public void setComboId(Long comboId) {
        this.comboId = comboId;
    }

    public Long getQuotaActivityId() {
        return quotaActivityId;
    }

    public void setQuotaActivityId(Long quotaActivityId) {
        this.quotaActivityId = quotaActivityId;
    }

    public Long getDiscountQuota() {
        return discountQuota;
    }

    public void setDiscountQuota(Long discountQuota) {
        this.discountQuota = discountQuota;
    }

    public String getDiscountQuotaFeeRate() {
        return discountQuotaFeeRate;
    }

    public void setDiscountQuotaFeeRate(String discountQuotaFeeRate) {
        this.discountQuotaFeeRate = discountQuotaFeeRate;
    }

    public Long getDiscountQuotaUsable() {
        return discountQuotaUsable;
    }

    public void setDiscountQuotaUsable(Long discountQuotaUsable) {
        this.discountQuotaUsable = discountQuotaUsable;
    }

    public Long getCostQuota() {
        return costQuota;
    }

    public void setCostQuota(Long costQuota) {
        this.costQuota = costQuota;
    }

    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    public Date getMtime() {
        return mtime;
    }

    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }

    public String getFeeRate() {
        return feeRate;
    }

    public void setFeeRate(String feeRate) {
        this.feeRate = feeRate;
    }
}
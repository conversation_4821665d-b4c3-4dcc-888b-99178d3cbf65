package com.wosai.trade.repository.dao;

import com.wosai.trade.model.dal.ActivityConditionDalResult;
import com.wosai.trade.model.dal.ActivityQueryParam;
import com.wosai.trade.model.dal.ActivityRefreshResult;
import com.wosai.trade.repository.dao.entity.ActivityEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ActivityDOMapper {
    int deleteByPrimaryKey(Long id);


    int insertSelective(ActivityEntity record);

    ActivityEntity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ActivityEntity record);


    List<ActivityConditionDalResult> queryByCondition(ActivityQueryParam query);

    int countQueryByCondition(ActivityQueryParam query);

    ActivityEntity selectByDingAuditId(String dingAuditId);


    /**
     * 根据活动ID 查询套餐ID
     *
     * @param id
     * @return
     */
    Long getComboIdByActivityId(Long id);


    int updateStatusAndComboId(@Param("id") Long id, @Param("status") Integer status, @Param("comboId") Long comboId, @Param("discountQuotaId") Long discountQuotaId);


    int updateDingAuditIdById(@Param("id") Long id, @Param("dingAuditId") String dingAuditId);

    /**
     * 根据套餐ID 查询活动配置
     *
     * @param comboId
     * @return
     */
    ActivityEntity getActivityDOByComboId(Long comboId);

    List<ActivityEntity> usableActivity();

    /**
     * 查询不可用活动 或者 存在额度配置的活动
     * @return
     */
    List<ActivityEntity> queryUnableOrExitQuota();

    /**
     * 活动主键ID查询
     *
     * @param ids
     * @return
     */
    List<ActivityEntity> queryByActivityIds(@Param("ids") List<Long> ids);

    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    List<ActivityEntity> pageList(ActivityQueryParam query);

    /**
     * 洗数专用
     *
     * @param id
     * @return
     */
    List<ActivityRefreshResult> queryRefreshList(@Param("id") Long id);

    /**
     * 洗数专用
     *
     * @param result
     * @return
     */
    int refreshActivity(ActivityRefreshResult result);


    /**
     * 根据payway 查询可用的活动
     * @param payways
     * @return
     */
    List<ActivityEntity> queryActivitiesByPayways(@Param("payways") List<Integer> payways);

    /**
     * 根据 activityName 查询activity
     * @param name
     * @return
     */
    List<Long> getActivityIdByName(@Param("name") String name);

    /**
     *  根据ids查询活动
     * @param ids
     * @return
     */
    List<ActivityEntity> queryActivityByIds(@Param("ids") List<Long> ids);

    /**
     * 根据活动类型查询
     * @param typeList
     * @param statusList
     * @return
     */
    List<ActivityEntity> queryByTypeList(@Param("typeList") List<Integer> typeList,
                                     @Param("statusList") List<Integer> statusList);
}
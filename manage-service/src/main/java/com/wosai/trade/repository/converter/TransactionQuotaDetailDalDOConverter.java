package com.wosai.trade.repository.converter;

import com.wosai.trade.model.dal.TransactionQuotaDetailDalDO;
import com.wosai.trade.model.enums.QuotaDetailTypeEnum;
import com.wosai.trade.repository.dao.entity.TransactionQuotaDetailEntity;
import com.wosai.trade.service.enums.QuotaDetailStatusEnum;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2020/4/17 Time: 4:34 下午
 */
public interface TransactionQuotaDetailDalDOConverter {


    /**
     * 转换convert
     * @param entity entity
     * @return return
     */
    static TransactionQuotaDetailDalDO convert(TransactionQuotaDetailEntity entity) {
        if (Objects.isNull(entity)) {
            return null;
        }

        return TransactionQuotaDetailDalDO.builder()
                .id(entity.getId())
                .merchantSn(entity.getMerchantSn())
                .quota(entity.getQuota())
                .type(QuotaDetailTypeEnum.getEnumByCode(entity.getType()))
                .subType(entity.getSubType())
                .beginDate(entity.getBeginDate())
                .endDate(entity.getEndDate())
                .status(QuotaDetailStatusEnum.getEnumByCode(entity.getStatus()))
                .bizType(entity.getBizType())
                .ext(entity.getExt())
                .ctime(entity.getCtime())
                .mtime(entity.getMtime())
                .build();
    }


    /**
     * 转换convert
     * @param entities entity
     * @return return
     */
    static List<TransactionQuotaDetailDalDO> convert(List<TransactionQuotaDetailEntity> entities) {
        List<TransactionQuotaDetailDalDO> detailDal;
        if (CollectionUtils.isEmpty(entities)) {
            detailDal = new ArrayList<>();
            return detailDal;
        }
        detailDal = new ArrayList<>(entities.size());

        for (TransactionQuotaDetailEntity entity : entities) {
            detailDal.add(convert(entity));
        }

        return detailDal;
    }
}

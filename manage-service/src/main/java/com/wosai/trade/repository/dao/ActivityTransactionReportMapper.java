package com.wosai.trade.repository.dao;

import com.wosai.trade.repository.dao.entity.ActivityTransactionReportEntity;
import org.apache.ibatis.annotations.Param;

public interface ActivityTransactionReportMapper {
    int deleteByPrimaryKey(String tsn);

    int insert(ActivityTransactionReportEntity record);

    int insertSelective(ActivityTransactionReportEntity record);

    ActivityTransactionReportEntity selectByPrimaryKey(String tsn);

    int updateByPrimaryKeySelective(ActivityTransactionReportEntity record);

    int updateByPrimaryKeyWithBLOBs(ActivityTransactionReportEntity record);

    int updateByPrimaryKey(ActivityTransactionReportEntity record);

    int count(@Param("tsn") String tsn);
}
package com.wosai.trade.repository.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.wosai.trade.model.dal.MerchantConfigBypassDalParam;
import com.wosai.trade.repository.dao.entity.MerchantConfigBypassEntity;

@Mapper
public interface MerchantConfigBypassDao {

    public void insert(MerchantConfigBypassDalParam byPassDalParam);

    public void updateById(MerchantConfigBypassDalParam byPassDalParam);

    public List<MerchantConfigBypassEntity> selectList(MerchantConfigBypassDalParam byPassDalParam);

    public long count(MerchantConfigBypassDalParam byPassDalParam);

    public long countByCurrentProvider(Integer currentProvider);

    public List<MerchantConfigBypassEntity> selectByMerchantId(String merchantId);

    public MerchantConfigBypassEntity selectById(Long id);

    public List<MerchantConfigBypassEntity> selectByIds(List<Long> ids);

    public MerchantConfigBypassEntity selectByMerchantIdAndPayway(String merchantId, Integer payway);

    public void deleteById(Long id);

    public void deleteByMerchantId(String merchantId);
}

package com.wosai.trade.repository.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.wosai.trade.model.dal.MerchantConfigBypassTradeInfoDalParam;
import com.wosai.trade.repository.dao.entity.MerchantConfigBypassTradeInfoEntity;

@Mapper
public interface MerchantConfigBypassTradeInfoDao {

    public void insert(MerchantConfigBypassTradeInfoDalParam bypassTradeInfoDalParam);
    
    public void batchInsert(List<MerchantConfigBypassTradeInfoDalParam> bypassTradeInfoDalParams);

    public List<MerchantConfigBypassTradeInfoEntity> selectList(MerchantConfigBypassTradeInfoDalParam bypassTradeInfoDalParam);

    public long count(MerchantConfigBypassTradeInfoDalParam bypassTradeInfoDalParam);

    public void deleteById(Long id);

}

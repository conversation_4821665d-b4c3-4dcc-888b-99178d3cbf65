package com.wosai.trade.repository.dao;

import com.wosai.trade.repository.dao.entity.ProviderDiffFeeTransactionEntity;
import com.wosai.trade.repository.dao.entity.ProviderDiffFeeTransactionSummaryEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【provider_diff_fee_transaction(通道费率差异比对流水记录)】的数据库操作Mapper
* @createDate 2024-08-23 11:28:15
* @Entity com.wosai.trade.repository.dao.entity.ProviderDiffFeeTransactionEntity
*/
public interface ProviderDiffFeeTransactionMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ProviderDiffFeeTransactionEntity record);

    int insertSelective(ProviderDiffFeeTransactionEntity record);

    ProviderDiffFeeTransactionEntity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProviderDiffFeeTransactionEntity record);

    int updateByPrimaryKey(ProviderDiffFeeTransactionEntity record);

    /**
     * 查找昨日差异交易流水列表
     *
     * @return
     */
    List<ProviderDiffFeeTransactionEntity> queryRangeCtimeList(@Param("begin") Long begin,
                                               @Param("end") Long end);

    /**
     * 查询指定时间范围内的商户和通道汇总信息
     *
     * @param mtime 开始时间
     * @param offset 分页偏移量
     * @param limit 分页大小
     * @return 商户和通道汇总列表
     */
    List<ProviderDiffFeeTransactionSummaryEntity> queryMerchantProviderSummary(
            @Param("mtime") Date mtime,
            @Param("offset") Integer offset,
            @Param("limit") Integer limit);

}

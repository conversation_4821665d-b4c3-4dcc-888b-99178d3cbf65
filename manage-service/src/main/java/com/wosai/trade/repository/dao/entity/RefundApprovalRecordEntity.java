package com.wosai.trade.repository.dao.entity;

public class RefundApprovalRecordEntity {
    private Long id;

    private String businessOrderSn;

    private String merchantId;

    private String orderStoreSn;

    private String refundStoreSn;

    private Integer tradeApp;

    private String applyUserId;

    private String auditUserId;

    private String authorizedUserIds;

    private Long applyAmount;

    private Integer status;

    private String auditRemark;

    private String remark;

    private String extra;

    private String auditLog;

    private Long ctime;

    private Long mtime;

    private Long auditTime;

    private String paySource;

    private Integer category;


    private String businessShowInfo;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBusinessOrderSn() {
        return businessOrderSn;
    }

    public void setBusinessOrderSn(String businessOrderSn) {
        this.businessOrderSn = businessOrderSn;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getOrderStoreSn() {
        return orderStoreSn;
    }

    public void setOrderStoreSn(String orderStoreSn) {
        this.orderStoreSn = orderStoreSn;
    }

    public String getRefundStoreSn() {
        return refundStoreSn;
    }

    public void setRefundStoreSn(String refundStoreSn) {
        this.refundStoreSn = refundStoreSn;
    }

    public Integer getTradeApp() {
        return tradeApp;
    }

    public void setTradeApp(Integer tradeApp) {
        this.tradeApp = tradeApp;
    }

    public String getApplyUserId() {
        return applyUserId;
    }

    public void setApplyUserId(String applyUserId) {
        this.applyUserId = applyUserId;
    }

    public String getAuditUserId() {
        return auditUserId;
    }

    public void setAuditUserId(String auditUserId) {
        this.auditUserId = auditUserId;
    }

    public String getAuthorizedUserIds() {
        return authorizedUserIds;
    }

    public void setAuthorizedUserIds(String authorizedUserIds) {
        this.authorizedUserIds = authorizedUserIds;
    }

    public Long getApplyAmount() {
        return applyAmount;
    }

    public void setApplyAmount(Long applyAmount) {
        this.applyAmount = applyAmount;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getAuditRemark() {
        return auditRemark;
    }

    public void setAuditRemark(String auditRemark) {
        this.auditRemark = auditRemark;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getExtra() {
        return extra;
    }

    public void setExtra(String extra) {
        this.extra = extra;
    }

    public String getAuditLog() {
        return auditLog;
    }

    public void setAuditLog(String auditLog) {
        this.auditLog = auditLog;
    }

    public Long getCtime() {
        return ctime;
    }

    public void setCtime(Long ctime) {
        this.ctime = ctime;
    }

    public Long getMtime() {
        return mtime;
    }

    public void setMtime(Long mtime) {
        this.mtime = mtime;
    }

    public Long getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(Long auditTime) {
        this.auditTime = auditTime;
    }

    public String getPaySource() {
        return paySource;
    }

    public void setPaySource(String paySource) {
        this.paySource = paySource;
    }

    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }

    public String getBusinessShowInfo() {
        return businessShowInfo;
    }

    public void setBusinessShowInfo(String businessShowInfo) {
        this.businessShowInfo = businessShowInfo;
    }
}
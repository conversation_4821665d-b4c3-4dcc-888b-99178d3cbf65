package com.wosai.trade.repository.dao;

import com.wosai.trade.repository.dao.entity.DiscountQuotaRuleEntity;

public interface DiscountQuotaRuleMapper {
    int deleteByPrimaryKey(Long id);


    int insertSelective(DiscountQuotaRuleEntity record);

    DiscountQuotaRuleEntity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DiscountQuotaRuleEntity record);




    DiscountQuotaRuleEntity selectByActivityId(Long activityId);


}
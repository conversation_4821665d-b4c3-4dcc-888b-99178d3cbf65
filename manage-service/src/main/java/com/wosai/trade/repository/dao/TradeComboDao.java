package com.wosai.trade.repository.dao;

import com.wosai.trade.model.dal.*;
import com.wosai.trade.repository.dao.entity.TradeComboEntity;
import com.wosai.trade.service.result.ListTradeCombosResult;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * The interface Trade combo dao.
 *
 * <AUTHOR>
 */
@Mapper
public interface TradeComboDao {

    /**
     * Count int.
     *
     * @param param the param
     * @return the int
     */
    int count(TradeComboCountDalParam param);

    /**
     * insert
     *
     * @param param param
     * @return int
     */
    int insert(TradeComboUpsertDalParam param);

    /**
     * update
     *
     * @param param param
     * @return int int
     */
    int updateById(TradeComboUpsertDalParam param);

    /**
     * selectList
     *
     * @param params params
     * @return List<TradeComboEntity>
     */
    List<TradeComboQueryDalResult> selectList(TradeComboQueryDalParam params);

    /**
     * queryTradeComboById
     *
     * @param id id
     * @return TradeComboEntity trade combo entity
     */
    TradeComboEntity selectById(@Param("id") long id);

    /**
     * queryTradeComboById
     *
     * @param ids ids
     * @return TradeComboEntity list
     */
    List<TradeComboEntity> selectByIds(@Param("ids") List<Long> ids);

    /**
     * 根据活动ID
     *
     * @param activityId
     * @return
     */
    List<TradeComboEntity> selectByActivityId(@Param("activityId") Long activityId);

    int deleteByPrimaryKey(@Param("id") Long id);

}

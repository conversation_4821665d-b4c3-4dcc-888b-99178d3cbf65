package com.wosai.trade.repository.dao.entity;

import com.alibaba.fastjson.JSON;
import com.wosai.trade.model.dal.ActivitySubStatusDetailEffectiveRule;
import com.wosai.trade.service.activity.request.ActivityRule;
import com.wosai.trade.util.JsonUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

@Data
public class ActivitySubStatusDetailEntity {
    private Long id;

    private String name;

    private Long activityId;

    private Long comboId;

    private String takeEffectNotice;

    private String description;

    private Date ctime;

    private Date mtime;

    private Integer version;

    private String tag;

    private String effectiveRule;

    public ActivityRule.TakeEffectNotice buildTakeEffectNotice() {
        if (StringUtils.isNotEmpty(takeEffectNotice)) {
            return JSON.parseObject(takeEffectNotice, ActivityRule.TakeEffectNotice.class);
        }
        return null;
    }

    public void addEffectiveRule(ActivitySubStatusDetailEffectiveRule effectiveRule) {
        this.effectiveRule = JsonUtil.encode(effectiveRule);
    }

    public ActivitySubStatusDetailEffectiveRule buildEffectiveRule() {
        if (StringUtils.isBlank(effectiveRule)) {
            return null;
        }
        return JsonUtil.decode(effectiveRule, ActivitySubStatusDetailEffectiveRule.class);
    }
}
package com.wosai.trade.repository.dao;

import com.wosai.trade.model.dal.*;
import com.wosai.trade.repository.dao.entity.TradeComboDetailEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * The interface Trade combo detail dao.
 *
 * <AUTHOR>
 */
@Mapper
public interface TradeComboDetailDao {

    /**
     * Batch insert int.
     *
     * @param params the params
     * @return the int
     */
    int batchInsert(List<TradeComboDetailUpsertDalParam> params);

    /**
     * Update by id int.
     *
     * @param param the param
     * @return the int
     */
    int updateById(TradeComboDetailUpsertDalParam param);

    /**
     * Select list by combo id list.
     *
     * @param param the param
     * @return the list
     */
    List<TradeComboDetailEntity> selectList(TradeComboDetailQueryDalParam param);

    /**
     * Select by id trade combo detail entity.
     *
     * @param id the id
     * @return the trade combo detail entity
     */
    TradeComboDetailEntity selectById(@Param("id") Long id);

    int deleteByPrimaryKey(@Param("id") Long id);
}

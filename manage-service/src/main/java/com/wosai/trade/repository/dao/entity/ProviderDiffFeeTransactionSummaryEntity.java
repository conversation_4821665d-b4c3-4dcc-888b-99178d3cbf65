package com.wosai.trade.repository.dao.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 通道费率差异比对流水记录汇总
 * @TableName provider_diff_fee_transaction
 */
@Data
public class ProviderDiffFeeTransactionSummaryEntity implements Serializable {
    
    /**
     * 商户sn
     */
    private String merchantSn;

    /**
     * 支付通道
     */
    private Integer provider;

    private static final long serialVersionUID = 1L;
}

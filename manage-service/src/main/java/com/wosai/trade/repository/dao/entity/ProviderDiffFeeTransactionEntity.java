package com.wosai.trade.repository.dao.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 通道费率差异比对流水记录
 * @TableName provider_diff_fee_transaction
 */
@Data
public class ProviderDiffFeeTransactionEntity implements Serializable {
    /**
     * 交易流水号
     */
    private String tsn;

    /**
     * 商户sn
     */
    private String merchantSn;

    /**
     * 商户id
     */
    private String merchantId;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 支付通道
     */
    private Integer provider;

    /**
     * pay_way
     */
    private Integer payWay;

    /**
     * 子商户号
     */
    private String cusId;

    /**
     * 原始金额 单位:分
     */
    private Long originalAmount;

    /**
     * 实收金额 单位:分
     */
    private Long recevieAmount;

    /**
     * 通道返回手续费 单位:分
     */
    private Long fee;

    /**
     * 根据费率计算手续费 单位:分
     */
    private Long realFee;

    /**
     * 交易流水展示费率 单位:百分位
     */
    private String realFeeRate;

    /**
     * level1_name
     */
    private String level1Name;

    /**
     * level1_name
     */
    private String level2Name;

    /**
     * 备注
     */
    private String remark;

    /**
     * 订单创建时间
     */
    private Long ctime;

    /**
     * 更新时间
     */
    private Date mtime;

    private static final long serialVersionUID = 1L;

    public Long diffFee() {
        return fee - realFee;
    }

}
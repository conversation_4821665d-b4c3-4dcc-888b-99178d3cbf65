package com.wosai.trade.repository.dao;

import com.wosai.trade.repository.dao.entity.TradeActivityEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Mapper
public interface TradeActivityDao {

    /**
     * insert entity
     * @param entity entity
     * @return int
     */
    int insert(TradeActivityEntity entity);

    /**
     * getEnableActivityByMerchantSnAndType
     * @param merchantSn sn
     * @param type type
     * @return entity
     */
    TradeActivityEntity getEnableActivityByMerchantSnAndType(@Param("merchantSn") String merchantSn, @Param("type") Integer type);

    /**
     *
     * @param merchantSn sn
     * @param type type
     * @return entity
     */
    List<TradeActivityEntity> getEnableActivityListByMerchantSnAndType(@Param("merchantSn") String merchantSn, @Param("type") Integer type);

    /**
     * allExpireMerchant
     * @param endDate endDate
     * @param limit limit
     * @return list
     */
    List<TradeActivityEntity> allExpireMerchant(@Param("endDate") String endDate, @Param("limit") int limit);

    List<TradeActivityEntity> selectInUsedList(@Param("type") int type,
                                               @Param("beginDate") String beginDate,
                                               @Param("endDate") String endDate,
                                               @Param("limit") int limit);

    /**
     *  updateActivityStatusByIdAndPreStatus
     * @param id id
     * @param preStatus preStatus
     * @param afterStatus afterStatus
     * @return int
     */
    int updateActivityStatusByIdAndPreStatus(@Param("id") Long id, @Param("preStatus") Integer preStatus, @Param("afterStatus") Integer afterStatus);

    int batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") int status);

    /**
     * updateExtraById
     *
     * @param id    id
     * @param extra extra
     * @return affected rows
     */
    int updateExtraById(@Param("id") long id, @Param("extra") Map<String, Object> extra);

    /**
     * 分页查询
     */
    List<TradeActivityEntity> queryTradeActivities(@Param("pageSize") int pageSize, @Param("id")long id, @Param("type") int type, @Param("endTime") long endTime);


    List<TradeActivityEntity> queryMerchantPendingApplyRecords(Map<String, Object> map);
}

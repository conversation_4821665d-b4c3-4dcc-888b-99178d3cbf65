package com.wosai.trade.repository.dao.entity;

import com.wosai.trade.service.result.MerchantConfigBypassSummaryStatisticsResult;

import lombok.Data;

@Data
public class MerchantConfigBypassSummaryStatisticsEntity {
    private Long startTime;
    private Long endTime;
    private Integer mchCount;
    private Integer count;
    private Integer failCount;
    private Integer succCount;
    private Integer initCount;
    private Integer tradeCount;
    private Long tradeAmount;
    private Integer clearanceCount;
    private Long clearanceAmount;

    public MerchantConfigBypassSummaryStatisticsResult toQueryResult() {
        MerchantConfigBypassSummaryStatisticsResult result = new MerchantConfigBypassSummaryStatisticsResult();
        result.setCount(count);
        result.setEndTime(endTime);
        result.setFailCount(failCount);
        result.setInitCount(initCount);
        result.setMchCount(mchCount);
        result.setStartTime(startTime);
        result.setSuccCount(succCount);
        result.setTradeAmount(tradeAmount);
        result.setTradeCount(tradeCount);
        result.setClearanceCount(clearanceCount);
        result.setClearanceAmount(clearanceAmount);
        return result;
    }
}

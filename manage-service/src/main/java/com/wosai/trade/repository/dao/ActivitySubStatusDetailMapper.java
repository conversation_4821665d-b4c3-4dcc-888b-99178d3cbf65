package com.wosai.trade.repository.dao;

import com.wosai.trade.repository.dao.entity.ActivitySubStatusDetailEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ActivitySubStatusDetailMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ActivitySubStatusDetailEntity record);

    int insertSelective(ActivitySubStatusDetailEntity record);

    ActivitySubStatusDetailEntity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ActivitySubStatusDetailEntity record);

    int updateByPrimaryKey(ActivitySubStatusDetailEntity record);

    /**
     * 根据套餐ID查询子状态
     *
     * @param comboId
     * @return
     */
    List<ActivitySubStatusDetailEntity> selectByComboId(@Param("comboId") Long comboId);

    /**
     * 查找当前套餐ID所对应的活动下的所有套餐列表
     *
     * @param comboId
     * @return
     */
    List<ActivitySubStatusDetailEntity> selectActivityComboList(@Param("comboId") Long comboId);

    /**
     * 查找活动下所有套餐列表
     *
     * @param activityId
     * @return
     */
    List<ActivitySubStatusDetailEntity> selectByActivityId(@Param("activityId") Long activityId);

    /**
     * 查找活动ID列表
     * @param comboId
     * @param comboName
     * @return
     */
    List<Long> selectActivityIdList(@Param("comboId") Long comboId, @Param("comboName") String comboName);


    List<ActivitySubStatusDetailEntity> selectByIdList(@Param("idList") List<Long> idList);


    List<ActivitySubStatusDetailEntity> selectNotEmptyTakeNotice();

    /**
     * 根据标记查找
     *
     * @param tag
     * @return
     */
    List<ActivitySubStatusDetailEntity> selectByTag(@Param("tag") String tag);

    /**
     * 刷数使用
     *
     * @return
     */
    List<ActivitySubStatusDetailEntity> findAll();
}
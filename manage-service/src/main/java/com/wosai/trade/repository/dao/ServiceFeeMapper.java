package com.wosai.trade.repository.dao;

import com.wosai.trade.model.dal.ServiceFeeQueryParam;
import com.wosai.trade.repository.dao.entity.ServiceFeeEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;
/**
 * <AUTHOR>
 * @description 针对表【service_fee(服务费配置表)】的数据库操作Mapper
 * @createDate 2023-06-13 09:48:46
 * @Entity com.wosai.trade.repository.dao.entity.ServiceFeeEntity
 */
public interface ServiceFeeMapper {

    int deleteByPrimaryKey(Long id);

    int insertSelective(ServiceFeeEntity record);

    ServiceFeeEntity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ServiceFeeEntity record);

    /**
     * 条件查询统计
     * @param param
     * @return
     */
    int countByCondition(@Param("param") ServiceFeeQueryParam param);

    /**
     * 条件查询
     * @param param
     * @return
     */
    List<ServiceFeeEntity> queryByCondition(@Param("param") ServiceFeeQueryParam param);

}

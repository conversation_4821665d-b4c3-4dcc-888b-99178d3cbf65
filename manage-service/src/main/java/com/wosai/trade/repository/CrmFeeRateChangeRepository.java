package com.wosai.trade.repository;

import com.wosai.trade.model.dal.IdOffsetPageable;
import com.wosai.trade.repository.dao.entity.CrmFeeRateChangeEntity;
import org.springframework.data.domain.Pageable;

import java.util.Date;
import java.util.List;

public interface CrmFeeRateChangeRepository {
    /**
     * 根据主键查询记录
     *
     * @param id
     * @return
     */
    CrmFeeRateChangeEntity findByPkId(Long id);


    /**
     * 查找有暂存记录的记录
     *
     * @param merchantSn 商户号
     * @param payWay     支付源
     * @return
     */
    CrmFeeRateChangeEntity findUnDoneByMerchant(String merchantSn, Integer payWay);

    /**
     * 根据商户号与状态查询
     *
     * @param merchantSn 商户号
     * @param payWay     支付源
     * @param statusList 状态
     * @return
     */
    List<CrmFeeRateChangeEntity> findByMerchantStatus(String merchantSn,
                                                      Integer payWay,
                                                      List<Integer> statusList, List<Integer> types);

    /**
     * 查找最近修改的初始化商户列表
     *
     * @return
     */
    List<CrmFeeRateChangeEntity> findLastUpdateInitMerchantList(Date endTime, Pageable pageable);


    /**
     * 根据状态查询最近修改的商户列表
     * @param endTime
     * @param pageable
     * @param statusList
     * @return
     */
    List<CrmFeeRateChangeEntity> findLastUpdateInitMerchantListV2(Date endTime, Pageable pageable,  List<Integer> statusList, List<Integer> types);


    /**
     * 查找未完成的商户SN列表
     *
     * @return
     */
    List<String> findUnDoneMerchantSnList(Pageable pageable);

    /**
     * 新增记录
     * @param entity
     */
    void insert(CrmFeeRateChangeEntity entity);

    /**
     * 更新
     * @param entity
     */
    void update(CrmFeeRateChangeEntity entity);


    List<CrmFeeRateChangeEntity> queryDelayTakeAffectActivityApply( IdOffsetPageable pageable,
                                                                    List<Integer> statusList,
                                                                    List<Integer> typeList);




}

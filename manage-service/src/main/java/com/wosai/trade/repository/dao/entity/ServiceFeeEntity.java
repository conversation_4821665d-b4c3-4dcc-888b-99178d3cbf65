package com.wosai.trade.repository.dao.entity;

import com.fasterxml.jackson.core.type.TypeReference;
import com.wosai.trade.biz.servicefee.constant.ServiceFeeConstant;
import com.wosai.trade.model.MarkLabel;
import com.wosai.trade.model.NumberRange;
import com.wosai.trade.model.enums.StatusEnum;
import com.wosai.trade.service.enums.ChargeCycleEnum;
import com.wosai.trade.service.enums.ChargeMethodEnum;
import com.wosai.trade.service.enums.ChargeModeEnum;
import com.wosai.trade.service.enums.ServiceFeeLevelEnum;
import com.wosai.trade.service.servicefee.model.ServiceFeeDirectInfo;
import com.wosai.trade.service.servicefee.model.ServiceFeeProcessInfo;
import com.wosai.trade.util.FeeRateUtils;
import com.wosai.trade.util.JsonUtil;
import com.wosai.trade.util.MoneyUtil;
import com.wosai.trade.util.ValidationUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.*;

@Data
public class ServiceFeeEntity {
    /**
     * 主键
     */
    private Long id;

    /**
     * 业务方id
     */
    private Long tradeAppId;

    /**
     * 名称
     */
    private String name;

    /**
     * 商户编码
     */
    private String itemCode;

    /**
     * 配置层级 merchant-商户 store-门店
     */
    private String level;

    /**
     * 类型 base-基础 activity-活动
     */
    private String type;

    /**
     * 场景类型 campus-校区
     */
    private String scenesType;

    /**
     * 场景编码列表 json数组模式
     */
    private String scenesCodes;

    /**
     * 状态 0禁用 1启用
     * @see StatusEnum
     */
    private Integer status;

    /**
     * 收费模式 usage-按使用收费 period-按时段收费 all-所有
     */
    private String chargeMode;

    /**
     * 收费方式 位图存储 第0位-分账 第1位-直接付费
     */
    private String chargeMethod;

    /**
     * 付费周期 year-按年 month-按月 quarter-按季 lifetime-终身
     */
    private String chargeCycle;

    /**
     * 付费周期期数
     */
    private Integer chargeCycleNumber;

    /**
     * 分账模型ID
     */
    private String feeModeId;

    /**
     * 分账收款业务ID
     */
    private String receiverId;

    /**
     * 分账收款业务名称
     */
    private String receiverName;

    /**
     * 分账基数类型 实收金额-received 自定义-custom
     */
    private String profitShareBaseType;

    /**
     * 分账基数统计指标 json数组格式
     */
    private String profitShareBaseMetrics;

    /**
     * 分账比例最小值
     */
    private String profitShareRatioMin;

    /**
     * 分账比例最大值
     */
    private String profitShareRatioMax;

    /**
     * 保底收费最小值 单位：分
     */
    private Long minChargeMin;

    /**
     * 保底收费最大值 单位：分
     */
    private Long minChargeMax;

    /**
     * 付费金额最小值 单位：分
     */
    private Long chargeAmountMin;

    /**
     * 付费金额最大值 单位：分
     */
    private Long chargeAmountMax;

    /**
     * 未续费收费规则id
     */
    private Long unrenewedId;

    /**
     * 未续费收费规则分账比例
     */
    private String unrenewedProfitShareRatio;

    /**
     * 未续费收费规则保底收费 单位：分
     */
    private Long unrenewedMinCharge;

    /**
     * 标签
     */
    private String tags;

    /**
     * 标记标签，map 转 json 字符串
     */
    private String markLabelExtra;

    /**
     * 简要描述
     */
    private String description;

    /**
     * 相关通知 json格式
     */
    private String notice;

    /**
     * 变更过程
     */
    private String process;

    /**
     * 扩展字段
     */
    private String extra;

    /**
     * 创建人
     */
    private String author;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 更新时间
     */
    private Date mtime;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 是否门店规则
     *
     * @return
     */
    public boolean isStore() {
        return Objects.equals(ServiceFeeLevelEnum.STORE.getByCode(), level);
    }

    /**
     * 是否为续费服务费
     *
     * @return
     */
    public boolean isPeriod() {
        return StringUtils.isNotBlank(chargeCycle);
    }

    /**
     * 是否为分账规则
     *
     * @return
     */
    public boolean isProfitShareRatio() {
        if (StringUtils.isNotEmpty(profitShareRatioMin) && MoneyUtil.yuan2cents(profitShareRatioMin) > NumberUtils.LONG_ZERO) {
            return true;
        }
        return StringUtils.isNotEmpty(profitShareRatioMax) && MoneyUtil.yuan2cents(profitShareRatioMax) > NumberUtils.LONG_ZERO;
    }


    public List<String> buildFeeModeIdList() {
        return JsonUtil.decode(feeModeId, new TypeReference<List<String>>() {
        });
    }

    public List<String> buildReceiverNameList() {
        return JsonUtil.decode(receiverName, new TypeReference<List<String>>() {
        });
    }


    public void setScenesCodesList(List<String> scenesCodes) {
        if (CollectionUtils.isEmpty(scenesCodes)) {
            return;
        }
        this.scenesCodes = JsonUtil.encode(scenesCodes);
    }

    public List<String> buildScenesCodes() {
        if (StringUtils.isEmpty(scenesCodes)) {
            return Collections.emptyList();
        }
        return JsonUtil.decode(scenesCodes, new TypeReference<List<String>>() {
        });
    }

    public ChargeModeEnum buildChargeMode() {
        return ChargeModeEnum.convert(chargeMode);
    }

    /**
     * 构建bit value
     *
     * @param chargeMethodEnum
     * @return
     */
    public void setChargeMethodBitValue(ChargeMethodEnum chargeMethodEnum, String bitValue) {
        if (Objects.isNull(chargeMethod)) {
            chargeMethod = ChargeMethodEnum.BIT_VALUE_DEFAULT;
        }
        StringBuilder sb = new StringBuilder(chargeMethod);
        sb.replace(chargeMethodEnum.getBitPos(), chargeMethodEnum.getBitPos() + 1, bitValue);
        chargeMethod = sb.toString();
    }

    /**
     * 构建chargeMethod
     *
     * @return
     */
    public List<ChargeMethodEnum> buildChargeMethodList() {
        if (StringUtils.isEmpty(chargeMethod) || StringUtils.equals(ChargeMethodEnum.BIT_VALUE_DEFAULT, chargeMethod)) {
            return Collections.emptyList();
        }
        List<ChargeMethodEnum> list = new ArrayList<>(ChargeMethodEnum.values().length);
        for (ChargeMethodEnum chargeMethodEnum : ChargeMethodEnum.values()) {
            String currBitVal = StringUtils.substring(chargeMethod, chargeMethodEnum.getBitPos(), chargeMethodEnum.getBitPos() + 1);
            if (Objects.equals(currBitVal, chargeMethodEnum.getBitValue())) {
                list.add(chargeMethodEnum);
            }
        }
        return list;
    }

    public ChargeCycleEnum buildChargeCycleEnum() {
        if (StringUtils.isEmpty(chargeCycle)) {
            return null;
        }
        return ChargeCycleEnum.valueOf(chargeCycle.toUpperCase());
    }

    public void setProfitShareBaseMetricsList(List<String> values) {
        if (CollectionUtils.isEmpty(values)) {
            return;
        }
        this.profitShareBaseMetrics = JsonUtil.encode(values);
    }

    public List<String> buildProfitShareBaseMetrics() {
        if (StringUtils.isEmpty(profitShareBaseMetrics)) {
            return Collections.emptyList();
        }
        return JsonUtil.decode(profitShareBaseMetrics, new TypeReference<List<String>>() {
        });
    }

    public void setTagsObject(List<String> tags) {
        this.tags = JsonUtil.encode(tags);
    }

    public List<String> buildTags() {
        if (StringUtils.isBlank(tags)) {
            return Collections.emptyList();
        }
        return JsonUtil.decode(tags, new TypeReference<List<String>>() {});
    }

    public void setMarkLabelExtraObject(MarkLabel markLabel) {
        if (Objects.isNull(markLabel)) {
            return;
        }
        this.markLabelExtra = JsonUtil.encode(markLabel);
    }

    public MarkLabel buildMarkLabelExtra() {
        if (StringUtils.isEmpty(markLabelExtra)) {
            return null;
        }
        return JsonUtil.decode(markLabelExtra, MarkLabel.class);
    }

    public void setNoticeObject(Notice notice) {
        if (Objects.isNull(notice)) {
            return;
        }
        this.notice = JsonUtil.encode(notice);
    }

    public Notice buildNotice() {
        if (StringUtils.isEmpty(notice)) {
            return null;
        }
        return JsonUtil.decode(notice, Notice.class);
    }

    public void setRenewalNotification(ServiceFeeDirectInfo.RenewalNotification renewal) {
        Notice noticeObj = buildNotice();
        if (Objects.isNull(noticeObj)) {
            noticeObj = new Notice();
        }
        noticeObj.setRenewal(renewal);
        setNoticeObject(noticeObj);
    }

    public ServiceFeeDirectInfo.RenewalNotification buildRenewalNotification() {
        Notice noticeObj = buildNotice();
        return Objects.isNull(noticeObj) ? null : noticeObj.getRenewal();
    }

    /**
     * 增加操作记录
     *
     * @param serviceFeeProcessInfo
     */
    public void addProcess(ServiceFeeProcessInfo serviceFeeProcessInfo) {
        List<ServiceFeeProcessInfo> serviceFeeProcessInfoList = JsonUtil.decode(this.process, new TypeReference<List<ServiceFeeProcessInfo>>() {
        });
        if (Objects.isNull(serviceFeeProcessInfoList)) {
            serviceFeeProcessInfoList = new ArrayList<>(1);
        }
        serviceFeeProcessInfoList.add(serviceFeeProcessInfo);
        this.process = JsonUtil.encode(serviceFeeProcessInfoList);
    }

    public List<ServiceFeeProcessInfo> buildProcess() {
        return JsonUtil.decode(this.process, new TypeReference<List<ServiceFeeProcessInfo>>() {
        });
    }

    public void setExtraObject(Extra extra) {
        if (Objects.isNull(extra)) {
            return;
        }
        this.extra = JsonUtil.encode(extra);
    }

    public Extra buildExtra() {
        if (StringUtils.isEmpty(extra)) {
            return null;
        }
        return JsonUtil.decode(extra, Extra.class);
    }

    public Extra ensureExtra() {
        Extra extra = buildExtra();
        if (Objects.isNull(extra)) {
            extra = new Extra();
        }
        return extra;
    }

    /**
     * 获取收款业务方类型
     *
     * @return
     */
    public String fetchReceiverAccountBusinessType() {
        Extra extra = ensureExtra();
        String receiverAccountBusinessType = extra.getReceiverAccountBusinessType();
        if (StringUtils.isBlank(receiverAccountBusinessType)) {
            receiverAccountBusinessType = ServiceFeeConstant.DEFAULT_ACCOUNT_BUSINESS_TYPE;
        }
        return receiverAccountBusinessType;
    }

    @Data
    public static class Notice {
        /**
         * 续费通知notice
         */
        private ServiceFeeDirectInfo.RenewalNotification renewal;
    }

    @Data
    public static class Extra {
        /**
         * 固定佣金
         */
        private NumberRange<Long> commission;
        /**
         * 收款账户类型
         */
        private String receiverAccountBusinessType;

        public void validateCommission(Long val) {
            if (Objects.isNull(commission)
                    || (Objects.isNull(commission.getMin())) && Objects.isNull(commission.getMax())) {
                return;
            }
            boolean result = FeeRateUtils.isBetween(val, commission.getMin(), commission.getMax());
            ValidationUtils.check(result, String.format("固定佣金超出区间范围 %s-%s", commission.getMin(), commission.getMax()));
        }
    }
}
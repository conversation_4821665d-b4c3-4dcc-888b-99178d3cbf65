package com.wosai.trade.controller;

import com.wosai.middleware.hera.toolkit.trace.RunnableWrapper;
import com.wosai.trade.biz.activity.quota.PublishApplyActivityBiz;
import com.wosai.trade.biz.servicefee.EnableServiceFeeRetryBiz;
import com.wosai.trade.service.CrmService;
import com.wosai.trade.service.TradeExchangeRateService;
import com.wosai.trade.service.exception.enums.TradeManageRespCodeEnum;
import com.wosai.trade.task.TransactionQuotaSyncTimeTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * 提供定时任务 xxl-job使用
 *
 * <AUTHOR> Date: 2020/10/13 Time: 10:35 上午
 */
@Slf4j
@RequestMapping("/trade/quota")
@RestController
public class TradeXxlJobController {


    @Resource
    private TransactionQuotaSyncTimeTask transactionQuotaSyncTimeTask;

    @Autowired
    private TradeExchangeRateService tradeExchangeRateService;
    @Resource
    private CrmService crmService;
    @Resource
    private EnableServiceFeeRetryBiz enableServiceFeeRetryBiz;

    @GetMapping("/compute")
    public ResponseEntity<String> computeQuota() {
        try {
            transactionQuotaSyncTimeTask.process();
        } catch (Throwable t) {
            return ResponseEntity
                    .status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(TradeManageRespCodeEnum.SERVER_ERROR.getMsg());
        }

        return ResponseEntity.ok(TradeManageRespCodeEnum.PROCESS_SUCCESS.getMsg());
    }


    @GetMapping("/dailyCalExchange")
    public ResponseEntity<String> dailyCalExchange() {
        try {
            tradeExchangeRateService.recordByDate(LocalDate.now().toString());
        } catch (Throwable t) {
            return ResponseEntity
                    .status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(TradeManageRespCodeEnum.SERVER_ERROR.getMsg());
        }

        return ResponseEntity.ok(TradeManageRespCodeEnum.PROCESS_SUCCESS.getMsg());
    }

    /**
     * 执行费率调整
     *
     * @return
     */
    @GetMapping("/crmAdjustFeeRate/execute")
    public ResponseEntity<String> crmAdjustFeeRateExecute() {
        new Thread(RunnableWrapper.of(() -> {
            try {
                crmService.crmAdjustFeeRateExecute();
            } catch (Throwable t) {
                log.error("crmAdjustFeeRateExecute fail", t);
            }
        })).start();
        return ResponseEntity.ok(TradeManageRespCodeEnum.PROCESS_SUCCESS.getMsg());
    }

    /**
     * 推送生效中的活动申请记录到CRM
     *
     * @return
     */
    @GetMapping("/crm/publishApplyActivity")
    public ResponseEntity<String> crmPublishApplyActivity() {
        new Thread(RunnableWrapper.of(() -> {
            try {
                crmService.publishApplyActivityByType(PublishApplyActivityBiz.SEND_CRM_KAFKA);
            } catch (Throwable t) {
                log.error("crmPublishApplyActivity fail", t);
            }
        })).start();
        return ResponseEntity.ok(TradeManageRespCodeEnum.PROCESS_SUCCESS.getMsg());
    }


    /**
     * 推送生效中的活动申请记录到CRM 和 bank
     *
     * @return
     */
    @GetMapping("/publishAllApplyActivity")
    public ResponseEntity<String> publishAllApplyActivity() {
        new Thread(RunnableWrapper.of(() -> {
            try {
                crmService.publishApplyActivityByType(PublishApplyActivityBiz.SEND_CRM_AND_BANK_KAFKA);
            } catch (Throwable t) {
                log.error("publishAllApplyActivity fail", t);
            }
        })).start();
        return ResponseEntity.ok(TradeManageRespCodeEnum.PROCESS_SUCCESS.getMsg());
    }



    @GetMapping("/bank/publishApplyActivity")
    public ResponseEntity<String> publishBankApplyActivity() {
        new Thread(RunnableWrapper.of(() -> {
            try {
                crmService.publishApplyActivityByType(PublishApplyActivityBiz.SEND_BANK_KAFKA);
            } catch (Throwable t) {
                log.error("publish bank applyActivity  fail", t);
            }
        })).start();
        return ResponseEntity.ok(TradeManageRespCodeEnum.PROCESS_SUCCESS.getMsg());
    }

    /**
     * 服务费生效重试
     *
     * @return
     */
    @GetMapping("/serviceFee/retryExecute")
    public ResponseEntity<String> serviceFeeRetryExecute() {
        new Thread(RunnableWrapper.of(() -> {
            try {
                enableServiceFeeRetryBiz.execute();
            } catch (Throwable t) {
                log.error("enableServiceFeeRetryBiz fail", t);
            }
        })).start();
        return ResponseEntity.ok(TradeManageRespCodeEnum.PROCESS_SUCCESS.getMsg());
    }


}




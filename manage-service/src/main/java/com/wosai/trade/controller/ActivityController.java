package com.wosai.trade.controller;

import com.wosai.databus.event.pay.TradeManageChangeApplyActivityEvent;
import com.wosai.middleware.hera.toolkit.trace.RunnableWrapper;
import com.wosai.middleware.hera.toolkit.trace.Trace;
import com.wosai.trade.biz.activity.*;
import com.wosai.trade.biz.activity.converter.ApplyActivityBuildParams;
import com.wosai.trade.biz.activity.model.UpdateActivityApplyExpirationRuleRequest;
import com.wosai.trade.impl.TradeActivityServiceImpl;
import com.wosai.trade.repository.ActivityApplyRepository;
import com.wosai.trade.repository.dao.ActivityDOMapper;
import com.wosai.trade.repository.dao.entity.ActivityApplyEntity;
import com.wosai.trade.repository.dao.entity.ActivityEntity;
import com.wosai.trade.service.activity.request.ActivityExpirationRule;
import com.wosai.trade.service.activity.request.ApplyActivityRequest;
import com.wosai.trade.service.activity.response.ApplyActivityResponse;
import com.wosai.trade.service.activity.response.ApplyProcessInfo;
import com.wosai.trade.service.constant.CommonConstant;
import com.wosai.trade.task.BindBankcardActivityScheduler;
import com.wosai.trade.task.MicroPuHuiActivityScheduler;
import com.wosai.trade.task.biz.ActivitySchedulerBiz;
import com.wosai.trade.task.biz.QuotaActivitySchedulerBiz;
import com.wosai.trade.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/activity")
public class ActivityController {
    @Autowired
    private TradeActivityServiceImpl tradeActivityService;
    @Autowired
    private MicroPuHuiActivityScheduler microPuhuiActivityScheduler;
    @Autowired
    private BindBankcardActivityScheduler bindBankcardActivityScheduler;
    @Autowired
    private ActivitySubStatusBiz activitySubStatusBiz;

    @Autowired
    private ActivityDashboardBiz activityDashboardBiz;
    @Resource
    private ActivitySchedulerBiz activitySchedulerBiz;
    @Resource
    private QuotaActivitySchedulerBiz quotaActivitySchedulerBiz;
    @Resource(name = "commonThreadPool")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;
    @Resource
    private ActivityApplyStatusMonitorBiz activityApplyStatusMonitorBiz;
    @Resource
    private ActivityDOMapper activityDOMapper;
    @Resource
    private ActivityApplyRepository activityApplyRepository;
    @Resource
    private ApplyActivityBuildParams applyActivityBuildParams;
    @Resource
    private TradeAssessmentActivityBiz tradeAssessmentActivityBiz;
    @Resource
    private ModifyApplyActivityBiz modifyApplyActivityBiz;
    @Resource
    private CacheActivity cacheActivity;
    @Resource
    private ChangeSourceActivityBiz changeSourceActivityBiz;

    /**
     * 重新加载活动缓存
     */
    @GetMapping(value = "/reloadCacheActivity")
    public void reloadCacheActivity() {
        cacheActivity.load();
    }

    @RequestMapping("/hbfq")
    public void hbfqRateRemit(String tradeMonth) {
        tradeActivityService.hbfqRateRemit(tradeMonth);
    }

    /**
     * 触发小微普惠活动每月处理申请记录的任务调度接口
     */
    @RequestMapping(value = "/microPuHuiFeeRateChangeAuto", method = RequestMethod.GET)
    @ResponseBody
    public String microPuHuiFeeRateChangeAuto() {
        //由于执行时间比较长，在新线程里面执行
        new Thread(() -> microPuhuiActivityScheduler.microPuHuiProcessAuto(MicroPuHuiActivityScheduler.TYPE_MICRO_PUHUI_FEE_RATE_CHANGE_AUTO)).start();
        return "processing";
    }

    /**
     * 触发小微普惠活动
     */
    @RequestMapping(value = "/microPuHuiNotifyBalanceAuto", method = RequestMethod.GET)
    @ResponseBody
    public String microPuHuiNotifyAuto() {
        //由于执行时间比较长，在新线程里面执行
        new Thread(() -> microPuhuiActivityScheduler.microPuHuiProcessAuto(MicroPuHuiActivityScheduler.TYPE_MICRO_PUHUI_NOTIFY_BALANCE_AUTO)).start();
        return "processing";
    }

    /**
     * 上个月新入网商户打交易金额可见性标签
     */
    @RequestMapping(value = "/microPuHuiTradeAmountProgressBarVisible", method = RequestMethod.GET)
    @ResponseBody
    public String microPuHuiTradeAmountProgressBarVisible() {
        //由于执行时间比较长，在新线程里面执行
        new Thread(() -> microPuhuiActivityScheduler.microPuHuiProcessAuto(MicroPuHuiActivityScheduler.TYPE_MICRO_PUHUI_TRADE_AMOUNT_PROGRESS_BAR)).start();
        return "processing";
    }


    /**
     * 银行卡绑卡政策每月切换
     */
    @RequestMapping(value = "/bindBankcardChangeAuto", method = RequestMethod.GET)
    @ResponseBody
    public String bindBankcardChangeAuto() {
        new Thread(() -> bindBankcardActivityScheduler.task()).start();
        return "processing";
    }

    /**
     * 活动每月切换任务
     */
    @RequestMapping(value = "/activityMonthAutoChange", method = RequestMethod.GET)
    @ResponseBody
    public String activityMonthAutoChange() {
        new Thread(() -> {
            //绑卡考核
            bindBankcardActivityScheduler.task();
            //普惠考核
            microPuhuiActivityScheduler.microPuHuiProcessAuto(MicroPuHuiActivityScheduler.TYPE_MICRO_PUHUI_FEE_RATE_CHANGE_AUTO);
        }).start();
        return "processing";
    }

    @GetMapping(value = "/rebateFailedTask")
    @ResponseBody
    public String rebateFailedTask() {
        new Thread(RunnableWrapper.of(() -> activitySubStatusBiz.executeRebateFailed())).start();
        return "processing";
    }

    /**
     * 活动数据看板
     *
     * @return
     */
    @GetMapping(value = "/dashboard")
    public String dashboard() {
        threadPoolTaskExecutor.execute(RunnableWrapper.of(() -> {
            try {
                MDC.put(ConstantUtil.TRACE_ID, StringUtils.replace(UUID.randomUUID().toString(), "-", ""));
                activityDashboardBiz.summaryActivity();
            } catch (Exception e) {
                log.error("活动数据看板, 费率套餐活动执行异常.", e);
            } finally {
                MDC.clear();
            }
            try {
                MDC.put(ConstantUtil.TRACE_ID, StringUtils.replace(UUID.randomUUID().toString(), "-", ""));
                activityDashboardBiz.summaryQuotaActivity();
            } catch (Exception e) {
                log.error("活动数据看板, 费率减免活动执行异常.", e);
            } finally {
                MDC.clear();
            }
        }));
        return "processing";
    }

    @GetMapping(value = "/activityApplyMonitor")
    public String activityApplyMonitor() {
        threadPoolTaskExecutor.execute(RunnableWrapper.of(() -> activityApplyStatusMonitorBiz.execute()));
        return "执行中";
    }

    /**
     * 费率套餐活动下线定时任务
     */
    @GetMapping(value = "/offlineTaskByActivity")
    public void offlineTaskByActivity() {
        activitySchedulerBiz.offlineTask();
    }

    /**
     * 费率套餐活动下线前通知
     */
    @GetMapping(value = "/prepOfflineNoticeTaskByActivity")
    public void prepOfflineNoticeTaskByActivity() {
        activitySchedulerBiz.prepOfflineNoticeTask();
    }

    /**
     * 费率减免活动下线前通知
     */
    @GetMapping(value = "/prepOfflineNoticeTaskByQuotaActivity")
    public void prepOfflineNoticeTaskByQuotaActivity() {
        quotaActivitySchedulerBiz.prepOfflineNoticeTask();
    }

    /**
     * 更新活动生效记录过期时间
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/updateActivityApplyExpirationRule")
    public String updateActivityApplyExpirationRule(@Valid @RequestBody UpdateActivityApplyExpirationRuleRequest request) {
        ActivityApplyEntity applyDo = activityApplyRepository.selectByPrimaryKey(request.getId());
        ValidationUtils.check(Objects.equals(request.getMerchantSn(), applyDo.getMerchant_sn()), "商户SN不正确");
        ActivityEntity activityDo = activityDOMapper.selectByPrimaryKey(applyDo.getActivity_id());
        ActivityExpirationRule expirationRule = activityDo.buildExpirationRule();
        List<ApplyProcessInfo> process = applyActivityBuildParams.buildProcess(applyDo, applyDo.getStatus(), request.getProcessInfo());
        ActivityApplyEntity updateDo = new ActivityApplyEntity();
        updateDo.setId(applyDo.getId());
        updateDo.setExpiration_rule(activityDo.getExpiration_rule());
        updateDo.setProcess(JsonUtil.encode(process));
        if (Objects.nonNull(expirationRule.getByTimeRule()) && Objects.nonNull(expirationRule.getByTimeRule().getFixedTime())) {
            updateDo.setExpiration_time(new Date(expirationRule.getByTimeRule().getFixedTime()));
        } else {
            updateDo.setExpiration_time(DateTimeUtils.parseDate(CommonConstant.DEFAULT_ZERO_DATE_TIME, DateTimeUtils.format));
        }
        activityApplyRepository.updateByPrimaryKeySelective(updateDo);
        return "ok";
    }

    /**
     * 交易考核活动定时任务
     *
     * @param param
     * @return
     */
    @Trace
    @PostMapping(value = "/xxlJob/tradeAssessmentActivity")
    public String tradeAssessmentActivity(@RequestBody Map<String, Object> param) {
        String merchantSn = MapUtils.getString(param, "merchantSn");
        String assessmentTime = MapUtils.getString(param, "assessmentTime"); //考核时间
        if (StringUtils.isEmpty(assessmentTime)) {
            assessmentTime = LocalDateTimeUtil.getFormatDateTime(LocalDateTimeUtil.getStartOfMonth(LocalDateTime.now()), LocalDateTimeUtil.YYYYMMDD);
        }
        log.info("交易考核定时任务. merchantSn:{},assessmentTime:{}", merchantSn, assessmentTime);
        final String finalAssessmentTime = assessmentTime;
        threadPoolTaskExecutor.execute(RunnableWrapper.of(() -> {
            try {
                tradeAssessmentActivityBiz.processTradeAssessmentTask(merchantSn, finalAssessmentTime);
            } catch (Exception e) {
                log.error("交易考核定时任务执行异常. merchantSn:{},assessmentTime:{}", merchantSn, finalAssessmentTime, e);
            }
        }));
        return "processing";
    }

    /**
     * 报名
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/upsert")
    public ApplyActivityResponse upsert(@RequestBody ApplyActivityRequest request) {
        return modifyApplyActivityBiz.apply(request);
    }

    /**
     * 处理支付源活动变更事件
     *
     * @param event
     */
    @Trace
    @PostMapping(value = "/changeSourceActivity")
    public ResponseEntity<String> changeSourceActivity(@RequestBody TradeManageChangeApplyActivityEvent event) {
        try {
            log.info("开始处理支付源活动变更事件. merchantSn:{},event:{}", event.getMerchantSn(), event);
            changeSourceActivityBiz.process(event);
        } catch (Exception e) {
            log.error("处理支付源活动变更事件 执行异常", e);
            return ResponseEntity
                    .status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(e.getMessage());
        }
        return ResponseEntity.ok("执行成功");
    }
}

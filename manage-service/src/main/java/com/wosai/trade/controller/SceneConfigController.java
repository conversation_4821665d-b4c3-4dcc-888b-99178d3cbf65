package com.wosai.trade.controller;

import com.alibaba.fastjson.JSON;
import com.aliyun.oss.model.CannedAccessControlList;
import com.wosai.core.crypto.exception.BizException;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.trade.service.SceneConfigService;
import com.wosai.trade.service.activity.enums.ApplyActivityPlatformEnum;
import com.wosai.trade.service.activity.request.BulkAddActivityRequest;
import com.wosai.trade.util.OssUtil;
import com.wosai.upay.core.model.log.TaskApplyLog;
import com.wosai.upay.core.service.LogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Semaphore;

@Slf4j
@RestController
@RequestMapping("/scene")
public class SceneConfigController {

    private static final String BUCKET_NAME = "wosai-images";

    private static final String SCENE_CONFIG_DIR = "trade-manage-service/batch";

    @Autowired
    private SceneConfigService sceneConfigService;

    @Autowired
    private LogService logService;

    private Semaphore semaphore = new Semaphore(1);

    @PostMapping("/uploadConfigFile")
    public String upload(@RequestParam("file") MultipartFile multipartFile) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        IOUtils.copy(multipartFile.getInputStream(), baos);
        byte[] content = baos.toByteArray();
        ByteArrayInputStream bais = new ByteArrayInputStream(content);

        String fileName = multipartFile.getOriginalFilename();
        log.info("read file {}", fileName);
        OssUtil.uploadIfNotExists(BUCKET_NAME, SCENE_CONFIG_DIR + "/" + fileName, bais, content.length, CannedAccessControlList.PublicRead);

        return "https://wosai-images.oss-cn-hangzhou.aliyuncs.com/trade-manage-service/batch/" + fileName;
    }

}
package com.wosai.trade.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.ImmutableList;
import com.wosai.middleware.hera.toolkit.trace.RunnableWrapper;
import com.wosai.middleware.hera.toolkit.trace.Trace;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.trade.biz.activity.SyncApplyActivityAuditBiz;
import com.wosai.trade.biz.activity.TransactionBiz;
import com.wosai.trade.biz.activity.quota.PublishApplyActivityBiz;
import com.wosai.trade.biz.withdraw.FuYouChangeDirectBiz;
import com.wosai.trade.constant.FeeRateStatusConst;
import com.wosai.trade.impl.MicroPuHuiService;
import com.wosai.trade.impl.TradeCommonService;
import com.wosai.trade.model.dal.*;
import com.wosai.trade.repository.ActivityApplyRepository;
import com.wosai.trade.repository.ActivityRepository;
import com.wosai.trade.repository.QuotaActivityRepository;
import com.wosai.trade.repository.converter.ActivityComboConverter;
import com.wosai.trade.repository.dao.*;
import com.wosai.trade.repository.dao.entity.*;
import com.wosai.trade.service.TradeActivityService;
import com.wosai.trade.service.activity.model.QuotaActivityProcessInfo;
import com.wosai.trade.service.activity.request.ActivityRule;
import com.wosai.trade.service.enums.FeeRateTypeEnum;
import com.wosai.trade.service.exception.TradeManageBizException;
import com.wosai.trade.task.MicroPuHuiActivityScheduler;
import com.wosai.trade.task.biz.ServiceFeeSchedulerBiz;
import com.wosai.trade.util.*;
import com.wosai.upay.transaction.model.Transaction;
import com.wosai.upay.transaction.service.UpayOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/temp")
public class TempController {

    private static final ExecutorService EXECUTOR_SERVICE = Executors.newFixedThreadPool(5);

    private static final Map<String, List<Map<String, Object>>> TAG_NAME_FEE_RATE_MAP = JSON.parseObject("{\"守土0.1%\":[{\"min\":0,\"max\":100,\"fee_rate\":\"0.1\"},{\"min\":100,\"fee_rate\":\"0.38\"}],\"守土0.25%\":[{\"min\":0,\"max\":100,\"fee_rate\":\"0.25\"},{\"min\":100,\"fee_rate\":\"0.38\"}],\"冰点0.1%\":[{\"min\":0,\"max\":100,\"fee_rate\":\"0.1\"},{\"min\":100,\"fee_rate\":\"0.38\"}],\"冰点0.25%\":[{\"min\":0,\"max\":100,\"fee_rate\":\"0.25\"},{\"min\":100,\"fee_rate\":\"0.38\"}],\"新增0%\":[{\"min\":0,\"max\":100,\"fee_rate\":\"0\"},{\"min\":100,\"fee_rate\":\"0.38\"}],\"新增0.1%\":[{\"min\":0,\"max\":100,\"fee_rate\":\"0.1\"},{\"min\":100,\"fee_rate\":\"0.38\"}],\"小微商户补贴\":[{\"min\":0,\"max\":100,\"fee_rate\":\"0.25\"},{\"min\":100,\"fee_rate\":\"0.38\"}]}", new TypeReference<Map<String, List<Map<String, Object>>>>() {
    });

    @Autowired
    private TradeComboDao tradeComboDao;
    @Autowired
    private TradeActivityDao tradeActivityDao;
    @Autowired
    private MerchantFeeRateDao merchantFeeRateDao;
    @Autowired
    MicroPuHuiService microPuHuiService;
    @Autowired
    TradeActivityService tradeActivityService;
    @Autowired
    TradeCommonService tradeCommonService;
    @Autowired
    MicroPuHuiActivityScheduler microPuHuiActivityScheduler;

    /**
     * 移动阶梯费率数据
     *
     * @param params 请求参数， key 为 tag_name，value 为 套餐id
     *               {
     *               "守土0.25%":1,
     *               "守土0.1%":2,
     *               "冰点0.25%":3,
     *               "冰点0.1%":4,
     *               "新增0.1%":5,
     *               "批量0.1%":6,
     *               "小微商户补贴":7
     *               }
     */
    @PostMapping("/moveLadderFeeRate")
    public String moveLadderFeeRate(@RequestParam("requestBeginDate") String requestBeginDate, // yyyy-MM-dd
                                    @RequestParam("requestEndDate") String requestEndDate, // yyyy-MM-dd
                                    @RequestBody Map<String, Long> params) {

        log.info("开始迁移阶梯费率数据，params => {}", JSON.toJSONString(params));

        EXECUTOR_SERVICE.execute(() -> {

            // 套餐列表 all
            Map<Long, TradeComboQueryDalResult> tradeComboEntityMap = tradeComboEntityMap();

            // 套餐明细列表 all
//        Map<Long, List<TradeComboDetailEntity>> tradeComboDetailEntityListMap = tradeComboDetailEntityMap();

            List<TradeActivityEntity> tradeActivityEntityList = tradeActivityDao
                    .selectInUsedList(TradeActivityEntity.ACTIVITY_LADDER, requestBeginDate, requestEndDate, 2000);

            while (tradeActivityEntityList.size() > 0) {
                log.info("需要迁移数据量 {} 条", tradeActivityEntityList.size());

                List<MerchantFeeRateUpsertDalParam> insertDalParamList = tradeActivityEntityList
                        .stream()
                        .map(tradeActivityEntity -> {
                            String merchantSn = tradeActivityEntity.getMerchantSn();
                            LocalDate beginDate = tradeActivityEntity.getBeginDate();
                            LocalDate endDate = tradeActivityEntity.getEndDate();

                            String auditSn = MapUtils.getString(tradeActivityEntity.getAuditInfo(), "audit_sn");

                            Map<String, Object> extraMap = tradeActivityEntity.getExtra();

                            String payWaysText = MapUtils.getString(extraMap, "payway");
                            List<Integer> payWays = JSON.parseArray(payWaysText, Integer.class);
                            if (CollectionUtils.isEmpty(payWays)) {
                                return null;
                            }
                            String tagName = MapUtils.getString(extraMap, TradeActivityEntity.EXTRA_TAG_NAME);
                            long tradeComboId = MapUtils.getLong(params, tagName);
                            String feeRate = JSON.toJSONString(TAG_NAME_FEE_RATE_MAP.get(tagName));

                            // 费率活动对应的费率套餐
                            TradeComboQueryDalResult tradeComboEntity = tradeComboEntityMap.get(tradeComboId);
                            long tradeAppId = tradeComboEntity.getTradeAppId();

                            return payWays.stream()
                                    .map(payWay -> MerchantFeeRateUpsertDalParam.builder()
                                            .appId(tradeAppId)
                                            .merchantSn(merchantSn)
                                            .tradeComboId(tradeComboId)
                                            .payWay(payWay)
                                            .beginDate(beginDate.toString())
                                            .endDate(endDate.toString())
                                            .b2cInUse(1)
                                            .c2bInUse(1)
                                            .wapInUse(1)
                                            .miniInUse(1)
                                            .appInUse(0)
                                            .h5InUse(0)
                                            .feeRateType(FeeRateTypeEnum.LADDER.name().toLowerCase())
                                            .feeRate(feeRate)
                                            .auditSn(auditSn)
                                            .status(FeeRateStatusConst.IN_EFFECT)
                                            .build())
                                    .collect(Collectors.toList());

                        })
                        .filter(CollectionUtils::isNotEmpty)
                        .flatMap(Collection::stream)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                // 批量写入
                if (CollectionUtils.isNotEmpty(insertDalParamList)) {
                    merchantFeeRateDao.batchInsert(insertDalParamList);
                    log.info("批量迁移数据量 {} 条", insertDalParamList.size());
                }

                // 标记旧数据已失效
                List<Long> tradeActivityEntityIds = tradeActivityEntityList.stream().map(TradeActivityEntity::getId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(tradeActivityEntityIds)) {
                    tradeActivityDao.batchUpdateStatus(tradeActivityEntityIds, TradeActivityEntity.STATUS_MOVED);
                    log.info("标记旧数据已失效 {} 条", tradeActivityEntityIds.size());
                }

                try {
                    TimeUnit.MILLISECONDS.sleep(20);
                } catch (InterruptedException e) {
                }
                // 下一轮数据
                tradeActivityEntityList = tradeActivityDao.selectInUsedList(TradeActivityEntity.ACTIVITY_LADDER, requestBeginDate, requestEndDate, 2000);
            }

            log.info("阶梯费率已全部迁移完成");
        });

        return "OK";
    }

    /**
     * 移动通用费率数据
     */
    @PostMapping("/moveCommonFeeRate")
    public String moveCommonFeeRate(@RequestParam("tradeAppId") Long tradeAppId,
                                    @RequestParam("tradeComboId") Long tradeComboId,
                                    @RequestParam("feeRate") String feeRate) {

        String effectiveStartDate = LocalDate.now().toString();
        String effectiveEndDate = LocalDate.now().plusYears(100).toString();

        List<TradeActivityEntity> tradeActivityEntityList = tradeActivityDao
                .selectInUsedList(TradeActivityEntity.ACTIVITY_COMMON_RATE, effectiveStartDate, effectiveEndDate, 2000);

        List<MerchantFeeRateUpsertDalParam> insertDalParamList = tradeActivityEntityList.stream()
                .map(tradeActivityEntity -> {

                    String merchantSn = tradeActivityEntity.getMerchantSn();
                    Integer payWay = MapUtils.getInteger(tradeActivityEntity.getExtra(), "payway");
                    String auditSn = MapUtils.getString(tradeActivityEntity.getAuditInfo(), "audit_sn");
                    LocalDate beginDate = tradeActivityEntity.getBeginDate();
                    LocalDate endDate = tradeActivityEntity.getEndDate();

                    return MerchantFeeRateUpsertDalParam.builder()
                            .appId(tradeAppId)
                            .merchantSn(merchantSn)
                            .tradeComboId(tradeComboId)
                            .payWay(payWay)
                            .beginDate(beginDate.toString())
                            .endDate(endDate.toString())
                            .b2cInUse(1)
                            .c2bInUse(1)
                            .wapInUse(1)
                            .miniInUse(1)
                            .appInUse(0)
                            .h5InUse(0)
                            .feeRateType(FeeRateTypeEnum.FIXED.name().toLowerCase())
                            .feeRate(feeRate)
                            .auditSn(auditSn)
                            .status(FeeRateStatusConst.IN_EFFECT)
                            .build();


                })
                .collect(Collectors.toList());

        // 批量写入
        if (CollectionUtils.isNotEmpty(insertDalParamList)) {
            merchantFeeRateDao.batchInsert(insertDalParamList);
        }

        // 标记旧数据已失效
        List<Long> tradeActivityEntityIds = tradeActivityEntityList.stream().map(TradeActivityEntity::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(tradeActivityEntityIds)) {
            tradeActivityDao.batchUpdateStatus(tradeActivityEntityIds, TradeActivityEntity.STATUS_MOVED);
        }

        return "OK";
    }

    private Map<Long, TradeComboQueryDalResult> tradeComboEntityMap() {
        return tradeComboDao.selectList(TradeComboQueryDalParam.builder().build())
                .stream()
                .collect(Collectors.toMap(TradeComboQueryDalResult::getId, entity -> entity));
    }


    /**
     * 临时处理小微普惠活动上海直辖市新入网直营的商户没有生效新增商户0.25套餐的接口
     */
    @PostMapping("/takeEffectComboNewLoginMch")
    public String takeEffectComboForNewLoginMicroPuhuiFailed(@RequestBody List<String> merchantIds) {
        for (String merchantId : merchantIds) {
            microPuHuiService.applyMicroPuHuiMchCombo(merchantId, TradeActivityEntity.INSERT_MODE_MICRO_PUHUI_MANUAL);
        }
        return "completed";
    }

    /**
     * 普惠活动依据交易金额自动切套餐手工形式
     *
     * @param merchantSns
     * @return
     */
    @PostMapping("/microPuhuiChangeFeeRateByMerchant")
    public String microPuhuiChangeFeeRateByMerchant(@RequestBody List<String> merchantSns) {
        for (String merchantSn : merchantSns) {
            microPuHuiActivityScheduler.microPuhuiFeeRateChangeByMerchant(merchantSn);
        }
        return "completed";
    }

    @Resource
    private ActivityDOMapper activityDOMapper;
    @Resource
    private ActivityRepository activityRepository;
    @Resource
    private ActivityApplyDOMapper activityApplyDOMapper;
    @Resource
    private TradeComboDetailDao comboDetailDao;
    @Resource
    private ActivitySubStatusDetailMapper activitySubStatusDetailMapper;
    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private ActivityApplyRepository activityApplyRepository;

    /**
     * 存量套餐与活动绑定
     * 1.清理现活动内套餐、套餐明细
     * 2.更新存量套餐ID到子状态记录
     * 3.更新存量套餐ID、交换记录到活动记录上
     *
     * @return
     */
    @Trace(operationName = "joinActivityCombo")
    @PostMapping("/joinActivityCombo")
    public String joinActivityCombo(@RequestParam(value = "id") Long activityId,
                                    @RequestParam(value = "comboId") Long comboId) {
        ActivityEntity activityEntity = activityDOMapper.selectByPrimaryKey(activityId);
        log.info("活动套餐数据清洗 begin...");
        StopWatch stopWatch = new StopWatch("活动套餐数据清洗");
        stopWatch.start();
        transactionTemplate.execute(status -> {
            try {
                cleanActivityCombo(activityEntity, comboId);
            } catch (Exception e) {
                log.error("活动套餐数据清洗 活动ID:{}", activityEntity.getId(), e);
                throw e;
            }
            return null;
        });
        stopWatch.stop();
        log.info("活动套餐数据清洗 end... 耗时:{}", stopWatch.getTotalTimeSeconds());
        return "completed";
    }

    /**
     * 1.清理现活动内套餐、套餐明细
     * 2.更新存量套餐ID到子状态记录
     * 3.更新存量套餐ID、交换记录到活动记录上
     *
     * @param activityEntity
     * @param comboId
     */
    private void cleanActivityCombo(ActivityEntity activityEntity, Long comboId) {
        log.info("兼容套餐数据清洗 activityId:{}, comboId:{}", activityEntity.getId(), comboId);
        if (Objects.equals(activityEntity.getCombo_id(), comboId)) {
            throw TradeManageBizException.createExc("当前活动套餐已绑定");
        }
        // 删除数据
        List<ActivitySubStatusDetailEntity> subStatuss = activitySubStatusDetailMapper.selectByActivityId(activityEntity.getId());
        if (subStatuss.size() > 1) {
            throw TradeManageBizException.createExc("只处理子状态为1");
        }
        ActivitySubStatusDetailEntity subStatus = subStatuss.get(0);
        tradeComboDao.deleteByPrimaryKey(activityEntity.getCombo_id());
        comboDetailDao.selectList(TradeComboDetailQueryDalParam.builder().comboId(activityEntity.getCombo_id()).build()).forEach(comboDetail -> comboDetailDao.deleteByPrimaryKey(comboDetail.getId()));

        TradeComboEntity comboDo = tradeComboDao.selectById(comboId);
        List<TradeComboDetailEntity> comboDetailDos = comboDetailDao.selectList(TradeComboDetailQueryDalParam.builder().comboId(comboId).build());
        ActivityRule.TradeCombo combo = ActivityComboConverter.convertComboDo(comboDo, comboDetailDos);
        //更新子状态记录
        ActivitySubStatusDetailEntity updateStatus = new ActivitySubStatusDetailEntity();
        updateStatus.setId(subStatus.getId());
        updateStatus.setComboId(comboId);
        updateStatus.setDescription("存量套餐与活动绑定");
        int rows = activitySubStatusDetailMapper.updateByPrimaryKeySelective(updateStatus);
        if (rows != 1) {
            throw TradeManageBizException.createExc("写入子状态记录表失败. activityId:" + activityEntity.getId() + "comboId:" + activityEntity.getCombo_id());
        }
        ActivityRule.SubStatusDetail subStatusDetail = new ActivityRule.SubStatusDetail();
        subStatusDetail.setName(StringUtils.EMPTY);
        //回写交换记录、套餐ID
        ActivityEntity updateActivityDo = new ActivityEntity();
        updateActivityDo.setId(activityEntity.getId());
        updateActivityDo.setCombo_id(comboId);
        updateActivityDo.setChange_sub_status(JSONObject.toJSONString(ImmutableList.of(subStatusDetail)));
        updateActivityDo.setChange_combo(JSONObject.toJSONString(ImmutableList.of(combo)));
        activityRepository.updateByPrimaryKeySelective(updateActivityDo);
        log.info("活动套餐数据清洗 activityId:{},comboId:{}", activityEntity.getId(), comboId);
    }

    @Resource
    private PublishApplyActivityBiz publishApplyActivityBiz;

    /**
     * 推送费率减免活动申请记录到CRM
     *
     * @param merchantId
     * @return
     */
    @PostMapping("/publishApplyActivityCrm")
    public String publishCrm(@RequestParam(value = "merchantId") String merchantId, @RequestParam(value = "type") String type) {
        publishApplyActivityBiz.execute0(merchantId, type);
        return "ok";
    }

    @Resource
    private UpayOrderService upayOrderService;
    @Resource
    private TransactionBiz transactionBiz;

    @GetMapping("/syncTransactionQuota")
    public String syncTransactionQuota(@RequestParam(value = "orderSn", required = false) String orderSn) {
        new Thread(RunnableWrapper.of(() -> {
            try {
                List<Map<String, Object>> transactionList = upayOrderService.getOriginalTransactionListByOrderSn(orderSn);
                if (Objects.isNull(transactionList) || CollectionUtils.isEmpty(transactionList)) {
                    log.error("updateQuota 订单不存在. tsn:{}", orderSn);
                }
                Map transaction = transactionList.get(0);
                Integer type = MapUtil.getInteger(transaction, Transaction.TYPE);
                if (!Objects.equals(type, Transaction.TYPE_PAYMENT)) {
                    throw TradeManageBizException.createExc("非支付类型流水");
                }
                transactionBiz.handleBiz(transaction);
            } catch (Exception e) {
                log.error("updateQuota 操作失败. tsn:{}", orderSn, e);
            }
        })).start();
        return "ok";
    }

    @Resource
    private SyncApplyActivityAuditBiz syncApplyActivityAuditBiz;

    /**
     * 同步支付源活动报名记录到audit记录 后期数据清理
     *
     * @return
     */
    @Deprecated
    @GetMapping("/syncSourceActivityApplyToAudit")
    public String syncSourceActivityApplyToAudit(@RequestParam(value = "merchantSn", required = false) String merchantSn) {
        new Thread(RunnableWrapper.of(() -> {
            try {
                syncApplyActivityAuditBiz.execute(merchantSn);
            } catch (Exception e) {
                log.error("syncSourceActivityApplyToAudit failed. merchantSn:{}", merchantSn, e);
            }
        })).start();
        return "ok";
    }

    /**
     * 同步支付源活动报名记录到audit记录 后期数据清理
     *
     * @return
     */
    @Deprecated
    @RequestMapping("/updateActivityApply")
    public String updateActivityApply(@RequestBody Map<String, Object> update) {
        String merchantSn = MapUtil.getString(update, "merchant_sn");
        String remark = MapUtil.getString(update, "remark");
        Long id = MapUtil.getLong(update, "id");
        ValidationUtils.notNull(id, "id为空");
        ValidationUtils.notEmpty(merchantSn, "merchant_sn为空");
        ValidationUtils.notEmpty(remark, "remark为空");
        ActivityApplyEntity applyDo = activityApplyDOMapper.selectByPrimaryKey(id);
        ValidationUtils.notNull(applyDo, "当前申请记录不存在");
        ValidationUtils.check(Objects.equals(merchantSn, applyDo.getMerchant_sn()), "商户号不正确");
        ActivityApplyEntity updateApplyDo = JsonUtil.decode(JsonUtil.encode(update), ActivityApplyEntity.class);
        log.info("updateActivityApply {}", JsonUtil.encode(updateApplyDo));
        activityApplyRepository.updateByPrimaryKeySelective(updateApplyDo);
        return "ok";
    }

    /**
     * 同步支付源活动报名记录到crm
     *
     * @return
     */
    @Deprecated
    @GetMapping("/syncSourceActivityApplyToCrm")
    public String syncSourceActivityApplyToCrm(@RequestParam(value = "merchantSn", required = false) String merchantSn) {
        new Thread(RunnableWrapper.of(() -> {
            try {
                syncApplyActivityAuditBiz.syncSourceActivityApplyToCrm(merchantSn);
            } catch (Exception e) {
                log.error("syncSourceActivityApplyToCrm failed. merchantSn:{}", merchantSn, e);
            }
        })).start();
        return "ok";
    }

    /**
     * audit表数据清理 测试时使用
     *
     * @return
     */
    @Deprecated
    @GetMapping("/deleteAudit")
    public String deleteAudit(@RequestParam(value = "id") String id,
                              @RequestParam(value = "merchantSn", required = false) String merchantSn) {
        syncApplyActivityAuditBiz.deleteAudit(merchantSn, id);
        return "ok";
    }

    /**
     * 获取商户某段时间手续费，用作测试
     *
     * @param merchantId
     * @return
     */
    @GetMapping("/getMerchantFee")
    public String getMerchantFee(@RequestParam(value = "merchantId") String merchantId, @RequestParam(value = "startDate") String startDate, @RequestParam(value = "endDate") String endDate) {
        return microPuHuiActivityScheduler.getMerchantLastMonthFeeSum(merchantId, startDate, endDate) + "";
    }

    @PostMapping("/cancelPuhui")
    public void cancelPuhui(@RequestParam(value = "file") MultipartFile file) {
        microPuHuiActivityScheduler.microPuHuiProcessAuto(MicroPuHuiActivityScheduler.TYPE_MICRO_PUHUI_FEE_RATE_CHANGE_AUTO, new MicroPuHuiActivityScheduler.PuhuiBatchFile(file));
    }

    /**
     * 服务费取消，用于测试
     */
    @RequestMapping("/serviceFee/invalid")
    public void invalidServiceFee() {
        ServiceFeeSchedulerBiz serviceFeeSchedulerBiz = SpringBeanUtil.getBean(ServiceFeeSchedulerBiz.class);
        EXECUTOR_SERVICE.execute(RunnableWrapper.of(serviceFeeSchedulerBiz::invalid));
    }

    @Resource
    private ServiceFeeEffectiveMapper serviceFeeEffectiveMapper;

    @RequestMapping("/serviceFee/updateEffectiveStatus")
    public void updateServiceFeeEffectiveStatus(@RequestBody Map<String, Object> update) {
        Long id = MapUtils.getLong(update, "id");
        ServiceFeeEffectiveEntity updateDo = new ServiceFeeEffectiveEntity();
        updateDo.setId(id);
        updateDo.setStatus(MapUtils.getInteger(update, "status"));
        serviceFeeEffectiveMapper.updateByPrimaryKeySelective(updateDo);
    }

    @Autowired
    private QuotaActivityRepository quotaActivityRepository;
    @Autowired
    private QuotaActivityEntityMapper quotaActivityEntityMapper;
    @Autowired
    private QuotaActivityApplyEntityMapper quotaActivityApplyEntityMapper;

    /**
     * 更新额度包信息
     * curl -X POST --location "http://localhost:8081/temp/quota/updateQuotaActivity" \
     * -H "Content-Type: application/json" \
     * -d '{
     * "activityId": 2443,
     * "type": 3
     * }'
     *
     * @param update
     */
    @RequestMapping("/quota/updateQuotaActivity")
    public void updateQuotaActivity(@RequestBody Map<String, Object> update) {
        new Thread(RunnableWrapper.of(() -> {
            log.info("更新额度包信息 update={}", JsonUtil.encode(update));
            Long activityId = MapUtils.getLong(update, "activityId");
            Integer type = MapUtils.getInteger(update, "type");
            Integer paywayCategory = MapUtils.getInteger(update, "paywayCategory");
            String merchantSn = MapUtils.getString(update, "merchantSn");
            if (Objects.isNull(activityId) || Objects.isNull(type)) {
                TradeManageBizException.createExc("updateQuotaActivity 参数错误");
            }
            QuotaActivityEntity quotaActivityEntity = quotaActivityEntityMapper.selectByPrimaryKey(activityId);
            if (Objects.isNull(quotaActivityEntity)) {
                TradeManageBizException.createExc("updateQuotaActivity activityId:" + activityId + "不存在");
            }
            QuotaActivityEntity updateDo = new QuotaActivityEntity();
            updateDo.setId(activityId);
            updateDo.setType(type);
            if (Objects.nonNull(paywayCategory)) {
                updateDo.setPayway_category(paywayCategory);
            }
            QuotaActivityProcessInfo processInfo = new QuotaActivityProcessInfo(
                    String.format("更新额度包信息. type=%d,paywayCategory=%d", type, paywayCategory),
                    System.currentTimeMillis(),
                    ConstantUtil.SYSTEM_NAME,
                    quotaActivityEntity.getStatus()
            );
            List<QuotaActivityProcessInfo> processList = quotaActivityEntity.buildProcess();
            processList.add(processInfo);
            updateDo.setProcess(JsonUtil.encode(processList));
            quotaActivityRepository.updateByPrimaryKeySelective(updateDo);

            //更新成功重新拉取
            quotaActivityEntity = quotaActivityEntityMapper.selectByPrimaryKey(activityId);
            quotaActivityEntity.setUpdate_info(null);
            quotaActivityEntity.setProcess(null);

            ActivityApplyQueryDalParam query = ActivityApplyQueryDalParam.builder()
                    .activityId(activityId)
                    .start(0)
                    .limit(500000)
                    .build();
            if (Objects.nonNull(merchantSn)) {
                query.setMerchantSnList(ImmutableList.of(merchantSn));
            }
            List<QuotaActivityApplyEntity> list = quotaActivityApplyEntityMapper.queryByCondition(query);
            for (QuotaActivityApplyEntity applyEntity : list) {
                QuotaActivityApplyEntity updateApplyEntity = new QuotaActivityApplyEntity();
                updateApplyEntity.setId(applyEntity.getId());
                updateApplyEntity.setType(type);
                updateApplyEntity.setActivity_info(JsonUtil.encode(quotaActivityEntity));
                //批量更新
                quotaActivityApplyEntityMapper.updateByPrimaryKeySelective(updateApplyEntity);
            }
            log.info("更新额度包信息成功. activityId={}, 报名申请记录数={}", activityId, list.size());
        })).start();
    }

    @RequestMapping("/now")
    public String nowTime() {
        return LocalDateTimeUtil.getFormatDateTime();
    }


    @Resource
    private FuYouChangeDirectBiz fuYouChangeDirectBiz;

    /**
     * 富友间清转直清
     *
     * @param request
     */
    @Trace
    @PostMapping("/fuYouToDirect")
    public void fuYouToDirect(@RequestBody Map<String, Object> request) {
        fuYouChangeDirectBiz.execute(
                MapUtil.getString(request, "merchantSn"),
                MapUtil.getString(request, "appid"),
                MapUtil.getString(request, "key"),
                MapUtil.getString(request, "operatorName"),
                MapUtil.getString(request, "remark")
        );
    }
}

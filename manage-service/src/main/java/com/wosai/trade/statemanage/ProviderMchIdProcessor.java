package com.wosai.trade.statemanage;

import com.lark.chatbot.message.TextMessage;
import com.wosai.pantheon.util.RetryUtil;
import com.wosai.pay.common.base.log.OperationLogRequest;
import com.wosai.pay.common.base.util.JsonUtil;
import com.wosai.pay.common.data.Criteria;
import com.wosai.pay.common.data.jdbc.JdbcVersionedRecordDao;
import com.wosai.pay.common.state.manager.dto.StateManagerDTO;
import com.wosai.pay.common.state.manager.registry.AbstractStateManagerProcessor;
import com.wosai.pay.common.state.manager.remote.BsOpLogCreateReqDto;
import com.wosai.pay.state.manager.api.response.StateManagerResponse;
import com.wosai.trade.client.LarkClient;
import com.wosai.trade.impl.SwitchServiceImpl;
import com.wosai.trade.repository.dao.ProviderMchIdStateDao;
import com.wosai.trade.service.enums.StateManagerBusinessTypeEnum;
import com.wosai.trade.service.exception.TradeManageBizException;
import com.wosai.trade.service.request.ClearanceProviderMchIdEntity;
import com.wosai.upay.common.exception.CommonInvalidParameterException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

import static com.wosai.trade.biz.businesslog.TemplateCodeEnum.CLEARANCE_PROVIDER_MCH_CONFIG;

@Service
public class ProviderMchIdProcessor extends AbstractStateManagerProcessor<ClearanceProviderMchIdEntity, ProviderMchIdState> {

    @Resource
    SwitchServiceImpl switchService;

    @Resource
    LarkClient larkClient;

    @Resource
    ProviderMchIdStateDao providerMchIdStateDao;

    @Override
    public String getSupportedEntityType() {
        return ClearanceProviderMchIdEntity.ENTITY_TYPE_PROVIDER_MCH;
    }

    @Override
    public Criteria generateCriteria(ClearanceProviderMchIdEntity entity) {
        Criteria criteria = new Criteria();
        if (entity != null) {
            if (entity.getProvider() != null) {
                criteria.with("provider").is(entity.getProvider());
            }
            if (entity.getProviderMchId() != null) {
                criteria.with("provider_mch_id").is(entity.getProviderMchId());
            }
        }
        return criteria;
    }

    @Override
    public ProviderMchIdState buildStateDO(ClearanceProviderMchIdEntity entity) {
        ProviderMchIdState providerMchIdState = new ProviderMchIdState();
        providerMchIdState.setProvider(entity.getProvider());
        providerMchIdState.setProviderMchId(entity.getProviderMchId());
        return providerMchIdState;
    }

    @Override
    public void afterStateChange(String business, ClearanceProviderMchIdEntity entity, StateManagerResponse<ClearanceProviderMchIdEntity> previousState, StateManagerResponse<ClearanceProviderMchIdEntity> currentState, OperationLogRequest operationLogRequest) {
        //总的状态发生改变
        if (!previousState.getState().equals(currentState.getState())) {
            boolean disable = previousState.getState().equals(Boolean.TRUE) && currentState.getState().equals(Boolean.FALSE);
            boolean enable = previousState.getState().equals(Boolean.FALSE) && currentState.getState().equals(Boolean.TRUE);
            RetryUtil.TimingStrategy.Builder builder = new RetryUtil.TimingStrategy.Builder()
                    .setRetry(3, 1000, 1.0)
                    .setJitter(true, 200);
            RetryUtil<Boolean> retryUtil = new RetryUtil<Boolean>()
                    .retry(builder.build())
                    .method(() -> {
                        if (disable) {
                            disableBiz(entity, currentState);
                        }
                        if (enable) {
                            enableBiz(entity, currentState);
                        }
                        return true;
                    })
                    .on(throwable -> true)
                    .until(Objects::nonNull);
            Boolean executeResult = retryUtil.execute();
            if (executeResult == null || !executeResult) {
                String errorMsg = String.format("变更状态失败 entity:%s,business:%s", JsonUtil.objectToJsonString(entity), previousState.getBusiness());
                larkClient.sendMsg(new TextMessage(errorMsg));
                throw new TradeManageBizException(errorMsg);
            }
        }
    }

    @Override
    public BsOpLogCreateReqDto.ValidList<BsOpLogCreateReqDto.Diff> getOpLogValidList(ClearanceProviderMchIdEntity entity, StateManagerResponse<ClearanceProviderMchIdEntity> oldStateResult, StateManagerResponse<ClearanceProviderMchIdEntity> curStateResult, StateManagerDTO<ClearanceProviderMchIdEntity> stateManagerDTO) {
        BsOpLogCreateReqDto.ValidList<BsOpLogCreateReqDto.Diff> validList = new BsOpLogCreateReqDto.ValidList<>();

        //业务
        BsOpLogCreateReqDto.Diff disableStatusDiff = new BsOpLogCreateReqDto.Diff();
        disableStatusDiff.setColumnCode(String.format("%s#%s", CLEARANCE_PROVIDER_MCH_CONFIG.getTableName(), oldStateResult.getBusiness()));
        disableStatusDiff.setValueBefore(oldStateResult.getState() ? "开启" : "关闭");
        disableStatusDiff.setValueAfter(curStateResult.getState() ? "开启" : "关闭");
        validList.add(disableStatusDiff);

        //话术备注
        BsOpLogCreateReqDto.Diff remarkDiff = new BsOpLogCreateReqDto.Diff();
        remarkDiff.setColumnCode(String.format("%s#%s", CLEARANCE_PROVIDER_MCH_CONFIG.getTableName(), CLEARANCE_PROVIDER_MCH_CONFIG.getFiledNameList().get(1)));
        StateManagerResponse<ClearanceProviderMchIdEntity> targetStateResult = stateManagerDTO.getEnabled()
                ? oldStateResult
                : curStateResult;
        StateManagerResponse.SubState targetSubState = findSubStateByType(
                targetStateResult,
                stateManagerDTO.getSubStateType());
        String action = stateManagerDTO.getEnabled() ? "移除" : "添加";
        String reasonDesc = String.format("%s禁用原因: %s", action, targetSubState.getDesc());
        remarkDiff.setValueAfter(reasonDesc);

        validList.add(remarkDiff);

        //收单机构商户号
        BsOpLogCreateReqDto.Diff providerMchidDiff = new BsOpLogCreateReqDto.Diff();
        providerMchidDiff.setColumnCode(String.format("%s#%s", CLEARANCE_PROVIDER_MCH_CONFIG.getTableName(), CLEARANCE_PROVIDER_MCH_CONFIG.getFiledNameList().get(2)));
        providerMchidDiff.setValueBefore(entity.getProviderMchId());
        //变相解决相同字段操作日志记录不展示问题
        providerMchidDiff.setValueAfter(entity.getProviderMchId() + " ");
        validList.add(providerMchidDiff);
        return validList;
    }


    /**
     * 查找匹配的子状态
     */
    private StateManagerResponse.SubState findSubStateByType(
            StateManagerResponse<ClearanceProviderMchIdEntity> stateResult, Integer subStateType) {
        return stateResult.getSubStateList().stream()
                .filter(x -> subStateType.equals(x.getType()))
                .findFirst()
                .orElse(new StateManagerResponse.SubState());
    }

    private void disableBiz(ClearanceProviderMchIdEntity entity, StateManagerResponse<ClearanceProviderMchIdEntity> currentState) {
        String business = currentState.getBusiness();
        if (StateManagerBusinessTypeEnum.BIZ_SETTLEMENT.getCode().equals(business)) {
            switchService.closeProviderMchIdSettlementStatus(entity.getProvider(), entity.getProviderMchId());
        } else if (StateManagerBusinessTypeEnum.BIZ_REFUND.getCode().equals(business)) {
            switchService.closeProviderMchIdRefundStatus(entity.getProvider(), entity.getProviderMchId());
        } else {
            throw new CommonInvalidParameterException("不支持该业务类型的关闭操作，类型: " + business);
        }

    }

    private void enableBiz(ClearanceProviderMchIdEntity entity, StateManagerResponse<ClearanceProviderMchIdEntity> currentState) {
        String business = currentState.getBusiness();
        if (StateManagerBusinessTypeEnum.BIZ_SETTLEMENT.getCode().equals(business)) {
            switchService.openProviderMchIdSettlementStatus(entity.getProvider(), entity.getProviderMchId());
        } else if (StateManagerBusinessTypeEnum.BIZ_REFUND.getCode().equals(business)) {
            switchService.openProviderMchIdRefundStatus(entity.getProvider(), entity.getProviderMchId());
        } else {
            throw new CommonInvalidParameterException("不支持该业务类型的开启操作，类型: " + business);
        }
    }

    @Override
    protected JdbcVersionedRecordDao<Long, ProviderMchIdState> getCustomStateDao() {
        return providerMchIdStateDao;
    }
}

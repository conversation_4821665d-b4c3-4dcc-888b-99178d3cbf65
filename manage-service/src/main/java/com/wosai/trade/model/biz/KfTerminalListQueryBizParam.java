package com.wosai.trade.model.biz;

import com.wosai.trade.model.client.coreb.FindStoresRequest;
import com.wosai.trade.model.client.coreb.FindTerminalsRequest;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.DaoConstants;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Date: 2021/12/28 Time: 11:00 上午
 */
@Data
@Builder
public class KfTerminalListQueryBizParam {

    private String merchantId;
    private String storeSn;
    private String storeName;
    private String deviceFingerprint;
    private PageInfo pageInfo;

    private String storeId;
    private List<String> storeIds;

    public boolean isDeviceFingerprintQuery() {
        return StringUtils.isNotBlank(deviceFingerprint);
    }

    public boolean isStoreSnQuery() {
        return StringUtils.isNotBlank(storeSn);
    }

    public boolean isStoreNameQuery() {
        return StringUtils.isNotBlank(storeName);
    }

    public void genStoreIds(ListResult storeListResult) {
        storeIds = storeListResult.getRecords().stream().map(map
                -> String.valueOf(map.get(DaoConstants.ID)))
                .collect(Collectors.toList());
    }

    public FindTerminalsRequest genFindTerminalsRequest() {
        return FindTerminalsRequest.builder()
                .merchantId(merchantId)
                .storeId(storeId)
                .deviceFingerprint(deviceFingerprint)
                .storeIds(storeIds)
                .build();
    }

    public FindStoresRequest genFindStoresRequest() {
        return FindStoresRequest.builder()
                .merchantId(merchantId)
                .storeName(storeName)
                .build();
    }
}

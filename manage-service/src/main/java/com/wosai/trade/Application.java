package com.wosai.trade;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.wosai.database.instrumentation.springboot.EnableDataSourceTranslate;
import com.wosai.databus.kafka.EventConsumerProperties;
import com.wosai.web.rpc.EnableJsonRpc;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableJsonRpc
@EnableDataSourceTranslate
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@MapperScan(basePackages = "com.wosai.trade.repository.dao")
@EnableApolloConfig
@EnableScheduling
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }

    @Bean
    public EventConsumerProperties eventConsumerProperties() {
        return new EventConsumerProperties();
    }


}

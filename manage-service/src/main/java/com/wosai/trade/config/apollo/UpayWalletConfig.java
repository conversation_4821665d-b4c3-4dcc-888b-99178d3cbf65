package com.wosai.trade.config.apollo;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.wosai.trade.constant.ApolloConstants;
import com.wosai.trade.util.JsonUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

@Slf4j
@Configuration
public class UpayWalletConfig {
    @ApolloConfig
    private Config config;

    private static WalletConfigModel walletConfigModel;

    public String getAppId() {
        return walletConfigModel.getAppId();
    }

    public String getAppKey() {
        return walletConfigModel.getAppKey();
    }

    @Setter
    @Getter
    public static class WalletConfigModel {
        private String appId;
        private String appKey;
    }

    @ApolloConfigChangeListener
    private void configChangeListener(ConfigChangeEvent changeEvent) {
        if (changeEvent.isChanged(ApolloConstants.UPAY_WALLET_CONFIG)) {
            reload();
        }
    }

    @PostConstruct
    private void init() {
        reload();
    }

    private void reload() {
        String apolloKey = ApolloConstants.UPAY_WALLET_CONFIG;
        try {
            String configStr = config.getProperty(apolloKey, null);
            log.info("[Apollo配置文件加载]>>>>>>{}: {}", apolloKey, configStr);
            if (StringUtils.isBlank(configStr)) {
                log.error("[Apollo配置文件加载]>>>>>>{}: 配置不存在", apolloKey);
                return;
            }

            walletConfigModel = JsonUtil.decode(configStr, WalletConfigModel.class);
        } catch (Exception e) {
            log.error("load config from apollo failed:key=[{}], error={}", apolloKey, e.getMessage(), e);
        }
    }
}
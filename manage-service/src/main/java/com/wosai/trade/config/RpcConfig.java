package com.wosai.trade.config;

import com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean;
import com.shouqianba.workflow.service.AuditService;
import com.shouqianba.workflow.service.CallBackService;
import com.wosai.aop.gateway.service.ClientSideNoticeService;
import com.wosai.aop.gateway.service.ClientSidePushService;
import com.wosai.aop.gateway.service.NoticeService;
import com.wosai.app.service.ManagerPasswordService;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.bankbusiness.service.BankRecommendMerchantService;
import com.wosai.bsm.enterprise.service.TradeNotifyService;
import com.wosai.business.log.service.BizOpLogService;
import com.wosai.cooperation.api.service.FinancialDataService;
import com.wosai.data.crow.api.service.TagIngestService;
import com.wosai.marekting.prepaid.service.StoredOwnerService;
import com.wosai.market.merchant.api.ServiceFeeRemoteService;
import com.wosai.market.tethys.api.service.AgreementService;
import com.wosai.period.service.PeriodPayBuyService;
import com.wosai.period.service.PeriodService;
import com.wosai.profit.sharing.service.BusinessOpenService;
import com.wosai.profit.sharing.service.SharingConfigService;
import com.wosai.profit.sharing.service.SharingOpenService;
import com.wosai.risk.service.IRiskQueryService;
import com.wosai.sales.core.service.IMerchantService;
import com.wosai.sales.merchant.business.service.common.OrganizationAppConfigService;
import com.wosai.sales.terminal.service.TerminalOrderService;
import com.wosai.service.IAccountReportService;
import com.wosai.service.IMerchantGrayService;
import com.wosai.service.PaywayActivityService;
import com.wosai.shouqianba.withdrawservice.service.ClearanceProviderService;
import com.wosai.shouqianba.withdrawservice.service.MerchantWithdrawConfigService;
import com.wosai.shouqianba.withdrawservice.service.WithdrawConfigService;
import com.wosai.sp.business.logstash.service.BusinessOpLogService;
import com.wosai.uc.v2.service.UcUserServiceV2;
import com.wosai.upay.charging.api.ChargingService;
import com.wosai.upay.clearance.service.ClearanceService;
import com.wosai.upay.core.service.*;
import com.wosai.upay.job.service.*;
import com.wosai.upay.remit.service.RemitOrderService;
import com.wosai.upay.scene.service.SceneConfigService;
import com.wosai.upay.service.CrmEdgeService;
import com.wosai.upay.signature.service.SignService;
import com.wosai.upay.transaction.service.GatewaySupportService;
import com.wosai.upay.transaction.service.OrderService;
import com.wosai.upay.transaction.service.UpayOrderService;
import com.wosai.upay.user.api.service.GroupService;
import com.wosai.upay.user.api.service.MspRefundTerminalService;
import com.wosai.upay.user.api.service.SpecialAuthWhitelistService;
import com.wosai.upay.user.api.service.UserService;
import com.wosai.upay.wallet.service.*;
import com.wosai.upay.clearance.service.LakalaPositionService;
import com.wosai.web.rpc.spring.CommonExceptionResolver;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * rpc 相关配置
 *
 * <AUTHOR>
 */
@Configuration
public class RpcConfig {

    @Bean
    public JsonProxyFactoryBean riskService(@Value("${spring.server.shouqianba-risk-service}") String url) {
        JsonProxyFactoryBean tradeConfigService = new JsonProxyFactoryBean();
        tradeConfigService.setServiceUrl(url + "rpc/riskquery");
        tradeConfigService.setServiceInterface(IRiskQueryService.class);
        tradeConfigService.setServerName("risk-service");
        tradeConfigService.setConnectionTimeoutMillis(500);
        tradeConfigService.setReadTimeoutMillis(10000);

        return tradeConfigService;
    }

    @Bean
    public JsonProxyFactoryBean tradeConfigService(@Value("${spring.server.core-business}") String url) {
        JsonProxyFactoryBean tradeConfigService = new JsonProxyFactoryBean();
        tradeConfigService.setServiceUrl(url + "rpc/tradeConfig");
        tradeConfigService.setServiceInterface(TradeConfigService.class);
        tradeConfigService.setServerName("core-business");
        tradeConfigService.setConnectionTimeoutMillis(500);
        tradeConfigService.setReadTimeoutMillis(10000);

        return tradeConfigService;
    }

    @Bean
    public JsonProxyFactoryBean supportService(@Value("${spring.server.core-business}") String url) {
        JsonProxyFactoryBean tradeConfigService = new JsonProxyFactoryBean();
        tradeConfigService.setServiceUrl(url + "rpc/support");
        tradeConfigService.setServiceInterface(SupportService.class);
        tradeConfigService.setServerName("core-business");
        tradeConfigService.setConnectionTimeoutMillis(500);
        tradeConfigService.setReadTimeoutMillis(10000);

        return tradeConfigService;
    }

    @Bean
    public JsonProxyFactoryBean merchantService(@Value("${spring.server.core-business}") String url) {
        JsonProxyFactoryBean tradeConfigService = new JsonProxyFactoryBean();
        tradeConfigService.setServiceUrl(url + "rpc/merchant");
        tradeConfigService.setServiceInterface(MerchantService.class);
        tradeConfigService.setServerName("core-business");
        tradeConfigService.setConnectionTimeoutMillis(500);
        tradeConfigService.setReadTimeoutMillis(10000);

        return tradeConfigService;
    }



    @Bean
    public JsonProxyFactoryBean businssCommonService(@Value("${spring.server.core-business}") String url) {
        JsonProxyFactoryBean tradeConfigService = new JsonProxyFactoryBean();
        tradeConfigService.setServiceUrl(url + "rpc/common");
        tradeConfigService.setServiceInterface(BusinssCommonService.class);
        tradeConfigService.setServerName("core-business");
        tradeConfigService.setConnectionTimeoutMillis(500);
        tradeConfigService.setReadTimeoutMillis(10000);

        return tradeConfigService;
    }


    @Bean
    public JsonProxyFactoryBean businessCommonService(@Value("${spring.server.core-business}") String url) {
        JsonProxyFactoryBean service = new JsonProxyFactoryBean();
        service.setServiceUrl(url + "rpc/common");
        service.setServiceInterface(BusinssCommonService.class);
        service.setServerName("core-business");
        service.setConnectionTimeoutMillis(500);
        service.setReadTimeoutMillis(10000);
        return service;
    }

    @Bean
    public JsonProxyFactoryBean storeService(@Value("${spring.server.core-business}") String url) {
        JsonProxyFactoryBean tradeConfigService = new JsonProxyFactoryBean();
        tradeConfigService.setServiceUrl(url + "rpc/store");
        tradeConfigService.setServiceInterface(StoreService.class);
        tradeConfigService.setServerName("core-business");
        tradeConfigService.setConnectionTimeoutMillis(500);
        tradeConfigService.setReadTimeoutMillis(10000);

        return tradeConfigService;
    }

    @Bean
    public JsonProxyFactoryBean terminalService(@Value("${spring.server.core-business}") String url) {
        JsonProxyFactoryBean tradeConfigService = new JsonProxyFactoryBean();
        tradeConfigService.setServiceUrl(url + "rpc/terminal");
        tradeConfigService.setServiceInterface(TerminalService.class);
        tradeConfigService.setServerName("core-business");
        tradeConfigService.setConnectionTimeoutMillis(500);
        tradeConfigService.setReadTimeoutMillis(3000);

        return tradeConfigService;
    }

    @Bean
    public JsonProxyFactoryBean signService(@Value("${spring.server.signature-proxy}") String url) {
        JsonProxyFactoryBean tradeConfigService = new JsonProxyFactoryBean();
        tradeConfigService.setServiceUrl(url + "rpc/sign");
        tradeConfigService.setServiceInterface(SignService.class);
        tradeConfigService.setServerName("signature-proxy");
        tradeConfigService.setConnectionTimeoutMillis(500);
        tradeConfigService.setReadTimeoutMillis(10000);

        return tradeConfigService;
    }

    @Bean
    public JsonProxyFactoryBean callBackService(@Value("${spring.server.sp-workflow}") String url) {
        JsonProxyFactoryBean callBackService = new JsonProxyFactoryBean();
        callBackService.setServiceUrl(url + "rpc/callback");
        callBackService.setServiceInterface(CallBackService.class);
        callBackService.setServerName("sp-workflow");
        callBackService.setConnectionTimeoutMillis(500);
        callBackService.setReadTimeoutMillis(10000);

        return callBackService;
    }

    @Bean
    public JsonProxyFactoryBean BusinessOpLogService(@Value("${spring.server.business-logstash}") String url) {
        JsonProxyFactoryBean tradeConfigService = new JsonProxyFactoryBean();
        tradeConfigService.setServiceUrl(url + "rpc/businessOpLog");
        tradeConfigService.setServiceInterface(BusinessOpLogService.class);
        tradeConfigService.setServerName("business-logstash");
        tradeConfigService.setConnectionTimeoutMillis(500);
        tradeConfigService.setReadTimeoutMillis(10000);
        return tradeConfigService;
    }

    @Bean
    public JsonProxyFactoryBean bizOpLogService(@Value("${spring.server.business-log}") String url) {
        JsonProxyFactoryBean tradeConfigService = new JsonProxyFactoryBean();
        tradeConfigService.setServiceUrl(url + "rpc/opLog");
        tradeConfigService.setServiceInterface(BizOpLogService.class);
        tradeConfigService.setServerName("business-log-service");
        tradeConfigService.setConnectionTimeoutMillis(500);
        tradeConfigService.setReadTimeoutMillis(10000);

        return tradeConfigService;
    }

    @Bean
    public JsonProxyFactoryBean tagIngestService(@Value("${spring.server.data-tag}") String serviceUrl) {
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(TagIngestService.class);
        trace.setServiceUrl(serviceUrl + "rpc/tag_ingests");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(4000);
        trace.setServerName("data-tag");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean remitOrderService(@Value("${spring.server.remit}") String serviceUrl) {
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(RemitOrderService.class);
        trace.setServiceUrl(serviceUrl + "rpc/order");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(4000);
        trace.setServerName("remit-order");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean noticeService(@Value("${spring.server.aop}") String serviceUrl) {
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(NoticeService.class);
        trace.setServiceUrl(serviceUrl + "rpc/v1/notice");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(4000);
        trace.setServerName("notice-service");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean clientSideNoticeService(@Value("${spring.server.aop}") String serviceUrl) {
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(ClientSideNoticeService.class);
        trace.setServiceUrl(serviceUrl + "rpc/clientSide/notice");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(4000);
        trace.setServerName("aop-push-service");
        return trace;
    }


    @Bean
    public JsonProxyFactoryBean clientSidePushService(@Value("${spring.server.aop}") String serviceUrl) {
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(ClientSidePushService.class);
        trace.setServiceUrl(serviceUrl + "rpc/clientSide/push");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(4000);
        trace.setServerName("app-push-service");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean merchantGroupService(@Value("${spring.server.merchant-user-service}") String serviceUrl) {
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(com.wosai.app.service.GroupService.class);
        trace.setServiceUrl(serviceUrl + "rpc/group");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(4000);
        trace.setServerName("merchant-user-service");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean userService(@Value("${spring.server.user-service}") String serviceUrl) {
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(UserService.class);
        trace.setServiceUrl(serviceUrl + "rpc/user");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(4000);
        trace.setServerName("user-service");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean specialAuthWhitelistService(@Value("${spring.server.user-service}") String serviceUrl) {
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(SpecialAuthWhitelistService.class);
        trace.setServiceUrl(serviceUrl + "rpc/specialAuthWhitelist");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(4000);
        trace.setServerName("user-service");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean mspRefundTerminalService(@Value("${spring.server.user-service}") String serviceUrl) {
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(MspRefundTerminalService.class);
        trace.setServiceUrl(serviceUrl + "rpc/mspRefundTerminal");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(4000);
        trace.setServerName("user-service");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean terminalOrderService(@Value("${spring.server.terminal-sale-service}") String serviceUrl) {
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(TerminalOrderService.class);
        trace.setServiceUrl(serviceUrl + "rpc/terminalOrder");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(4000);
        trace.setServerName("terminal-service");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean iMerchantService(@Value("${spring.server.sales-system-service}") String serviceUrl) {
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(IMerchantService.class);
        trace.setServiceUrl(serviceUrl + "rpc/merchant");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(4000);
        trace.setServerName("sales-system-service");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean IMerchantGrayService(@Value("${spring.server.upay-grayscale}") String serviceUrl) {
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(IMerchantGrayService.class);
        trace.setServiceUrl(serviceUrl + "rpc/merchantGray");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(5000);
        trace.setServerName("upay-grayscale-service");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean withdrawService(@Value("${spring.server.withdraw-service}") String serviceUrl) {
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(com.wosai.shouqianba.withdrawservice.service.WithdrawService.class);
        trace.setServiceUrl(serviceUrl + "rpc/withdraw");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(5000);
        trace.setServerName("shouqianba-withdraw-service");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean fastWithdrawPlanService(@Value("${spring.server.withdraw-service}") String serviceUrl) {
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(com.wosai.shouqianba.withdrawservice.service.FastWithdrawPlanService.class);
        trace.setServiceUrl(serviceUrl + "rpc/withdraw/fast");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(5000);
        trace.setServerName("shouqianba-withdraw-service");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean creditPayBackendService(@Value("${spring.server.credit-pay-backend-service}") String serviceUrl){
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(com.wosai.bsm.creditpaybackend.service.UserInfoService.class);
        trace.setServiceUrl(serviceUrl + "rpc/userInfo");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(5000);
        trace.setServerName("credit-pay-backend-service");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean customerRelationService(@Value("${spring.server.customer-relation-service}") String serviceUrl){
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(facade.ICustomerRelationFacade.class);
        trace.setServiceUrl(serviceUrl + "rpc/relation");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(5000);
        trace.setServerName("customer-relation-service");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean merchantOrganizationService(@Value("${spring.server.sales-system-service}") String serviceUrl){
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(com.wosai.sales.core.service.OrganizationService.class);
        trace.setServiceUrl(serviceUrl + "rpc/organization");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(5000);
        trace.setServerName("sales-system-service");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean faceRecordService(@Value("${spring.server.face-recognition-service}") String serviceUrl){
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(com.wosai.face.service.IFaceRecordService.class);
        trace.setServiceUrl(serviceUrl + "rpc/record");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(5000);
        trace.setServerName("face-recognition-service");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean orderService(@Value("${spring.server.upay-transaction-service}") String serviceUrl){
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(OrderService.class);
        trace.setServiceUrl(serviceUrl + "rpc/order");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(5000);
        trace.setServerName("face-recognition-service");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean upayOrderService(@Value("${spring.server.upay-transaction-service}") String serviceUrl){
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(UpayOrderService.class);
        trace.setServiceUrl(serviceUrl + "rpc/upayorder");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(5000);
        trace.setServerName("upay-transaction-service");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean GatewaySupportService(@Value("${spring.server.upay-transaction-service}") String serviceUrl){
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(GatewaySupportService.class);
        trace.setServiceUrl(serviceUrl + "rpc/gateway-support");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(5000);
        trace.setServerName("upay-transaction-service");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean walletService(@Value("${spring.server.upay-wallet}") String serviceUrl){
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(WalletService.class);
        trace.setServiceUrl(serviceUrl + "rpc/wallet");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(5000);
        trace.setServerName("upay-wallet");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean walletServiceV2(@Value("${spring.server.upay-wallet}") String serviceUrl){
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(WalletServiceV2.class);
        trace.setServiceUrl(serviceUrl + "rpc/walletV2");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(5000);
        trace.setServerName("upay-wallet");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean walletServiceV3(@Value("${spring.server.upay-wallet}") String serviceUrl){
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(WalletServiceV3.class);
        trace.setServiceUrl(serviceUrl + "rpc/walletV3");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(5000);
        trace.setServerName("upay-wallet");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean freezeServiceV3(@Value("${spring.server.upay-wallet}") String serviceUrl){
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(FreezeServiceV3.class);
        trace.setServiceUrl(serviceUrl + "rpc/freezeV3");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(5000);
        trace.setServerName("upay-wallet");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean merchantLimitAuthorizationService(@Value("${spring.server.upay-wallet}") String serviceUrl){
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(MerchantLimitAuthorizationService.class);
        trace.setServiceUrl(serviceUrl + "rpc/merchantLimitAuthorization");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(5000);
        trace.setServerName("upay-wallet");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean withdrawConfigService(@Value("${spring.server.withdraw-service}") String serviceUrl) {
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(WithdrawConfigService.class);
        trace.setServiceUrl(serviceUrl + "rpc/withdrawConfig");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(5000);
        trace.setServerName("shouqianba-withdraw-service");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean clearanceProviderService(@Value("${spring.server.withdraw-service}") String serviceUrl) {
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(ClearanceProviderService.class);
        trace.setServiceUrl(serviceUrl + "rpc/clearanceProvider");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(5000);
        trace.setServerName("shouqianba-withdraw-service");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean lakalaPositionService(@Value("${spring.server.clearance-service}") String serviceUrl) {
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(LakalaPositionService.class);
        trace.setServiceUrl(serviceUrl + "/rpc/lakalaPosition");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(5000);
        trace.setServerName("clearance-service");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean clearanceService(@Value("${spring.server.clearance-service}") String serviceUrl) {
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(ClearanceService.class);
        trace.setServiceUrl(serviceUrl + "/rpc/clearance");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(5000);
        trace.setServerName("clearance-service");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean tradeNotifyService(@Value("${spring.server.enterprise}") String serviceUrl) {
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(TradeNotifyService.class);
        trace.setServiceUrl(serviceUrl + "rpc/notify");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(5000);
        trace.setServerName("enterprise");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean groupService(@Value("${spring.server.user-service}") String serviceUrl) {
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(GroupService.class);
        trace.setServiceUrl(serviceUrl + "rpc/group");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(5000);
        trace.setServerName("user-service");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean storedOwnerService(@Value("${spring.server.marketing-saas-prepaid-card}") String serviceUrl) {
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(StoredOwnerService.class);
        trace.setServiceUrl(serviceUrl + "rpc/storedOwner");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(5000);
        trace.setServerName("marketing-saas-prepaid-card");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean agreementService(@Value("${spring.server.tethys}") String serviceUrl) {
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(AgreementService.class);
        trace.setServiceUrl(serviceUrl + "rpc/agreement");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(5000);
        trace.setServerName("tethys");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean serviceFeeRemoteService(@Value("${spring.server.market-merchant-service}") String serviceUrl) {
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(ServiceFeeRemoteService.class);
        trace.setServiceUrl(serviceUrl + "rpc/serviceFee");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(5000);
        trace.setServerName("market-merchant-service");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean compensationService(@Value("${spring.server.withdraw-service}") String serviceUrl) {
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(com.wosai.shouqianba.withdrawservice.service.CompensationService.class);
        trace.setServiceUrl(serviceUrl + "rpc/compensation");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(5000);
        trace.setServerName("shouqianba-withdraw-service");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean servicePaywayActivityService(@Value("${spring.server.merchant-contract-activity}") String serviceUrl) {
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(PaywayActivityService.class);
        trace.setServiceUrl(serviceUrl + "rpc/payway_activity");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(5000);
        trace.setServerName("market-merchant-service");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean logService(@Value("${spring.server.core-business}") String url) {
        JsonProxyFactoryBean logService = new JsonProxyFactoryBean();
        logService.setServiceUrl(url + "rpc/log");
        logService.setServiceInterface(LogService.class);
        logService.setServerName("core-business");
        logService.setConnectionTimeoutMillis(500);
        logService.setReadTimeoutMillis(10000);

        return logService;
    }


    @Bean
    public JsonProxyFactoryBean acquirerService(@Value("${spring.server.merchant-contract-job}") String url) {
        JsonProxyFactoryBean service = new JsonProxyFactoryBean();
        service.setServiceUrl(url + "rpc/acquirer");
        service.setServiceInterface(AcquirerService.class);
        service.setServerName("merchant-contract-job");
        service.setConnectionTimeoutMillis(500);
        service.setReadTimeoutMillis(10000);

        return service;
    }


    @Bean
    public JsonProxyFactoryBean lKlV3PosService(@Value("${spring.server.merchant-contract-job}") String url) {
        JsonProxyFactoryBean service = new JsonProxyFactoryBean();
        service.setServiceUrl(url + "rpc/v3_pos");
        service.setServiceInterface(LKlV3PosService.class);
        service.setServerName("merchant-contract-job");
        service.setConnectionTimeoutMillis(500);
        service.setReadTimeoutMillis(5000);

        return service;
    }

    @Bean
    public JsonProxyFactoryBean t9Service(@Value("${spring.server.merchant-contract-job}") String url) {
        JsonProxyFactoryBean service = new JsonProxyFactoryBean();
        service.setServiceUrl(url + "rpc/t9");
        service.setServiceInterface(T9Service.class);
        service.setServerName("merchant-contract-job");
        service.setConnectionTimeoutMillis(500);
        service.setReadTimeoutMillis(5000);

        return service;
    }


    @Bean
    public JsonProxyFactoryBean providerTradeParamsService(@Value("${spring.server.merchant-contract-job}") String url) {
        JsonProxyFactoryBean service = new JsonProxyFactoryBean();
        service.setServiceUrl(url + "rpc/providerParams");
        service.setServiceInterface(ProviderTradeParamsService.class);
        service.setServerName("merchant-contract-job");
        service.setConnectionTimeoutMillis(500);
        service.setReadTimeoutMillis(10000);
        return service;
    }

    @Bean
    public JsonProxyFactoryBean fuyouOperateService(@Value("${spring.server.merchant-contract-job}") String url) {
        JsonProxyFactoryBean service = new JsonProxyFactoryBean();
        service.setServiceUrl(url + "rpc/fuyou");
        service.setServiceInterface(FuyouOperateService.class);
        service.setServerName("merchant-contract-job");
        service.setConnectionTimeoutMillis(500);
        service.setReadTimeoutMillis(10000);
        return service;
    }

    @Bean
    public JsonProxyFactoryBean ContractTaskResultService(@Value("${spring.server.merchant-contract-job}") String url) {
        JsonProxyFactoryBean service = new JsonProxyFactoryBean();
        service.setServiceUrl(url + "rpc/contracTaskResult");
        service.setServiceInterface(ContractTaskResultService.class);
        service.setServerName("merchant-contract-job");
        service.setConnectionTimeoutMillis(500);
        service.setReadTimeoutMillis(5000);
        return service;
    }

    @Bean
    public JsonProxyFactoryBean ContractTaskService(@Value("${spring.server.merchant-contract-job}") String url) {
        JsonProxyFactoryBean service = new JsonProxyFactoryBean();
        service.setServiceUrl(url + "rpc/contractTask");
        service.setServiceInterface(ContractTaskService.class);
        service.setServerName("merchant-contract-job");
        service.setConnectionTimeoutMillis(500);
        service.setReadTimeoutMillis(5000);
        return service;
    }

    @Bean
    public JsonProxyFactoryBean auditService(@Value("${spring.server.sp-workflow}") String url) {
        JsonProxyFactoryBean service = new JsonProxyFactoryBean();
        service.setServiceUrl(url + "rpc/audit");
        service.setServiceInterface(AuditService.class);
        service.setServerName("sp-workflow");
        service.setConnectionTimeoutMillis(500);
        service.setReadTimeoutMillis(10000);

        return service;
    }

    @Bean
    public JsonProxyFactoryBean iKeeperService(@Value("${spring.server.sales-system-service}") String serviceUrl) {
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(com.wosai.sales.core.service.IKeeperService.class);
        trace.setServiceUrl(serviceUrl + "rpc/keeper");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(5000);
        trace.setServerName("sales-system-service");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean orgCommonConfigService(@Value("${spring.server.sales-system-service}") String serviceUrl) {
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(com.wosai.sales.core.service.org.OrgCommonConfigService.class);
        trace.setServiceUrl(serviceUrl + "rpc/orgCommonConfig");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(5000);
        trace.setServerName("sales-system-service");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean contractManagerService(@Value("${spring.server.merchant-contract-job}") String url) {
        JsonProxyFactoryBean service = new JsonProxyFactoryBean();
        service.setServiceUrl(url + "rpc/contractManager");
        service.setServiceInterface(ContractManagerService.class);
        service.setServerName("merchant-contract-job");
        service.setConnectionTimeoutMillis(500);
        service.setReadTimeoutMillis(10000);

        return service;
    }

    @Bean
    public JsonProxyFactoryBean organizationAppConfigService(@Value("${spring.server.merchant-business-open}") String url) {
        JsonProxyFactoryBean service = new JsonProxyFactoryBean();
        service.setServiceUrl(url + "rpc/organizationAppConfig");
        service.setServiceInterface(OrganizationAppConfigService.class);
        service.setServerName("merchant-business-open");
        service.setConnectionTimeoutMillis(500);
        service.setReadTimeoutMillis(10000);
        return service;
    }

    @Bean
    public JsonProxyFactoryBean iAccountReportService(@Value("${spring.server.transaction-report}") String url) {
        JsonProxyFactoryBean service = new JsonProxyFactoryBean();
        service.setServiceUrl(url + "rpc/account_report");
        service.setServiceInterface(IAccountReportService.class);
        service.setServerName("transaction-report");
        service.setConnectionTimeoutMillis(500);
        service.setReadTimeoutMillis(10000);
        return service;
    }

    @Bean
    public JsonProxyFactoryBean crmEdgeService(@Value("${spring.server.pay-business-open}") String url) {
        JsonProxyFactoryBean service = new JsonProxyFactoryBean();
        service.setServiceUrl(url + "rpc/crmEdgeService");
        service.setServiceInterface(CrmEdgeService.class);
        service.setServerName("pay-business-open");
        service.setConnectionTimeoutMillis(500);
        service.setReadTimeoutMillis(10000);

        return service;
    }

    @Bean
    public JsonProxyFactoryBean industryService(@Value("${spring.server.bank-info}") String url) {
        JsonProxyFactoryBean service = new JsonProxyFactoryBean();
        service.setServiceUrl(url + "rpc/industry");
        service.setServiceInterface(IndustryService.class);
        service.setServerName("bank-info");
        service.setConnectionTimeoutMillis(500);
        service.setReadTimeoutMillis(10000);
        return service;
    }

    @Bean
    public JsonProxyFactoryBean merchantConfigService(@Value("${spring.server.withdraw-service}") String url) {
        JsonProxyFactoryBean service = new JsonProxyFactoryBean();
        service.setServiceUrl(url + "rpc/merchantWithdrawConfig");
        service.setServiceInterface(MerchantWithdrawConfigService.class);
        service.setServerName("withdraw-service");
        service.setConnectionTimeoutMillis(500);
        service.setReadTimeoutMillis(10000);
        return service;
    }


    @Bean
    public JsonProxyFactoryBean financialDataService(@Value("${spring.server.data-cooperation}") String url) {
        JsonProxyFactoryBean service = new JsonProxyFactoryBean();
        service.setServiceUrl(url + "rpc/finance");
        service.setServiceInterface(FinancialDataService.class);
        service.setServerName("data-cooperation");
        service.setConnectionTimeoutMillis(500);
        service.setReadTimeoutMillis(10000);
        return service;
    }

    @Bean
    public JsonProxyFactoryBean businessOpenService(@Value("${spring.server.profit-sharing}") String url) {
        JsonProxyFactoryBean service = new JsonProxyFactoryBean();
        service.setServiceUrl(url + "rpc/businessOpen");
        service.setServiceInterface(BusinessOpenService.class);
        service.setServerName("profit-sharing");
        service.setConnectionTimeoutMillis(500);
        service.setReadTimeoutMillis(10000);
        service.setExceptionResolver(new CommonExceptionResolver());
        return service;
    }

    @Bean
    public JsonProxyFactoryBean sharingOpenService(@Value("${spring.server.profit-sharing}") String url) {
        JsonProxyFactoryBean service = new JsonProxyFactoryBean();
        service.setServiceUrl(url + "rpc/open");
        service.setServiceInterface(SharingOpenService.class);
        service.setServerName("profit-sharing");
        service.setConnectionTimeoutMillis(500);
        service.setReadTimeoutMillis(10000);
        service.setExceptionResolver(new CommonExceptionResolver());
        return service;
    }

    @Bean
    public JsonProxyFactoryBean sharingConfigService(@Value("${spring.server.profit-sharing}") String url) {
        JsonProxyFactoryBean service = new JsonProxyFactoryBean();
        service.setServiceUrl(url + "rpc/sharingConfig");
        service.setServiceInterface(SharingConfigService.class);
        service.setServerName("profit-sharing");
        service.setConnectionTimeoutMillis(500);
        service.setReadTimeoutMillis(10000);
        service.setExceptionResolver(new CommonExceptionResolver());
        return service;
    }

    @Bean
    public JsonProxyFactoryBean periodService(@Value("${spring.server.period-pay}") String url) {
        JsonProxyFactoryBean service = new JsonProxyFactoryBean();
        service.setServiceUrl(url + "rpc/period");
        service.setServiceInterface(PeriodService.class);
        service.setServerName("trade-period-pay");
        service.setConnectionTimeoutMillis(500);
        service.setReadTimeoutMillis(10000);
        return service;
    }

    @Bean
    public JsonProxyFactoryBean periodPayBuyService(@Value("${spring.server.period-pay}") String url) {
        JsonProxyFactoryBean service = new JsonProxyFactoryBean();
        service.setServiceUrl(url + "rpc/pay");
        service.setServiceInterface(PeriodPayBuyService.class);
        service.setServerName("trade-period-pay");
        service.setConnectionTimeoutMillis(500);
        service.setReadTimeoutMillis(10000);
        return service;
    }

    @Bean
    public JsonProxyFactoryBean sceneConfigService(@Value("${spring.server.scene-manage-service}") String url) {
        JsonProxyFactoryBean sceneConfigService = new JsonProxyFactoryBean();
        sceneConfigService.setServiceUrl(url + "rpc/sceneConfig");
        sceneConfigService.setServiceInterface(SceneConfigService.class);
        sceneConfigService.setServerName("scene-manage-service");
        sceneConfigService.setConnectionTimeoutMillis(500);
        sceneConfigService.setReadTimeoutMillis(10000);

        return sceneConfigService;
    }


    @Bean(name = "depositAuditService")
    public JsonProxyFactoryBean depositAppService(@Value("${spring.server.deposit-pay}") String url) {
        JsonProxyFactoryBean service = new JsonProxyFactoryBean();
        service.setServiceUrl(url + "rpc/audit");
        service.setServiceInterface(com.wosai.deposit.service.AuditService.class);
        service.setServerName("deposit-pay");
        service.setConnectionTimeoutMillis(500);
        service.setReadTimeoutMillis(10000);
        return service;
    }


    @Bean
    public JsonProxyFactoryBean bankRecommendMerchantService(@Value("${spring.server.bank-business-service}") String url) {
        JsonProxyFactoryBean service = new JsonProxyFactoryBean();
        service.setServiceUrl(url + "rpc/bank/recommend");
        service.setServiceInterface(BankRecommendMerchantService.class);
        service.setServerName("bank-business");
        service.setConnectionTimeoutMillis(500);
        service.setReadTimeoutMillis(10000);
        return service;
    }

    @Bean
    public JsonProxyFactoryBean merchantProviderParamsService(@Value("${spring.server.merchant-contract-access}") String url) {
        JsonProxyFactoryBean service = new JsonProxyFactoryBean();
        service.setServiceUrl(url + "rpc/merchantProviderParams");
        service.setServiceInterface(com.shouqianba.service.MerchantProviderParamsService.class);
        service.setServerName("merchant-contract-access");
        service.setConnectionTimeoutMillis(500);
        service.setReadTimeoutMillis(10000);
        return service;
    }

    @Bean
    public JsonProxyFactoryBean contractRelatedMappingConfigService(@Value("${spring.server.merchant-contract-access}") String url) {
        JsonProxyFactoryBean service = new JsonProxyFactoryBean();
        service.setServiceUrl(url + "rpc/config/mapping");
        service.setServiceInterface(com.shouqianba.service.ContractRelatedMappingConfigService.class);
        service.setServerName("merchant-contract-access");
        service.setConnectionTimeoutMillis(500);
        service.setReadTimeoutMillis(10000);
        return service;
    }

    @Bean
    public JsonProxyFactoryBean ucUserServiceV2(@Value("${spring.server.uc-user-service}") String url) {
        JsonProxyFactoryBean ucUserServiceV2 = new JsonProxyFactoryBean();
        ucUserServiceV2.setServiceUrl(url + "rpc/v2/ucUser");
        ucUserServiceV2.setServiceInterface(UcUserServiceV2.class);
        ucUserServiceV2.setServerName("uc-user-service");
        ucUserServiceV2.setConnectionTimeoutMillis(500);
        ucUserServiceV2.setReadTimeoutMillis(10000);
        return ucUserServiceV2;
    }


    @Bean
    public JsonProxyFactoryBean merchantUserServiceV2(@Value("${spring.server.merchant-user-service}") String serviceUrl) {
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(MerchantUserServiceV2.class);
        trace.setServiceUrl(serviceUrl + "rpc/merchantuserV2");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(4000);
        trace.setServerName("merchant-user-service");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean managerPasswordService(@Value("${spring.server.merchant-user-service}") String serviceUrl) {
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(ManagerPasswordService.class);
        trace.setServiceUrl(serviceUrl + "rpc/managerPassword");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(4000);
        trace.setServerName("merchant-user-service");
        return trace;
    }

    @Bean
    public JsonProxyFactoryBean chargingService(@Value("${spring.server.upay-charge-service}") String serviceUrl) {
        JsonProxyFactoryBean trace = new JsonProxyFactoryBean();
        trace.setServiceInterface(ChargingService.class);
        trace.setServiceUrl(serviceUrl + "rpc/charging");
        trace.setConnectionTimeoutMillis(500);
        trace.setReadTimeoutMillis(4000);
        trace.setServerName("upay-charging");
        return trace;
    }


    @Bean
    public JsonProxyFactoryBean batchService(@Value("${spring.server.merchant-contract-job}") String url) {
        JsonProxyFactoryBean tradeConfigService = new JsonProxyFactoryBean();
        tradeConfigService.setServiceUrl(url + "rpc/batch");
        tradeConfigService.setServiceInterface(BatchService.class);
        tradeConfigService.setServerName("merchant-contract-job");
        tradeConfigService.setConnectionTimeoutMillis(500);
        tradeConfigService.setReadTimeoutMillis(10000);

        return tradeConfigService;
    }
}

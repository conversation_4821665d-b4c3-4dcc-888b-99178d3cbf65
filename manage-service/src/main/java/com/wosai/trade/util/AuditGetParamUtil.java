package com.wosai.trade.util;

import com.google.common.collect.Lists;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.trade.biz.audit.enums.AuditSourceEnum;
import com.wosai.trade.service.exception.AuditBizException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Description:
 * <AUTHOR>
 * @Date: 2022/6/7 3:39 下午
 */
public class AuditGetParamUtil {
    /**
     * 审批来源
     */
    public static final String AUDIT_SOURCE = "audit_source";

    /**
     * 审批获取商户号
     *
     * @param businessMap
     * @return
     */
    public static String getMerchantSn(Map businessMap) {
        Map merchant = MapUtils.getMap(businessMap, "merchant");
        if (MapUtils.isEmpty(merchant)) {
            merchant = MapUtils.getMap(businessMap, "merchant_info");
        }
        if (MapUtils.isEmpty(merchant)) {
            merchant = MapUtils.getMap(businessMap, "merchant_sn");
        }
        if (MapUtils.isEmpty(merchant)) {
            merchant = MapUtils.getMap(businessMap, "merch_info");
        }
        if (MapUtils.isEmpty(merchant)) {
            throw new AuditBizException("商户信息未填写");
        }
        Map merchInfoSn = MapUtils.getMap(merchant, "merch_info.sn");
        return MapUtils.getString(merchInfoSn, "value");
    }

    public static String getMerchantSn(Map businessMap, String key) {
        if (MapUtils.isEmpty(businessMap)) {
            return null;
        }
        Map merchant = MapUtils.getMap(businessMap, key);
        if (MapUtils.isEmpty(merchant)) {
            return null;
        }
        Map merchInfoSn = MapUtils.getMap(merchant, "merch_info.sn");
        return MapUtils.getString(merchInfoSn, "value");
    }

    public static String getSn(Map businessMap) {
        Map map = MapUtils.getMap(businessMap, "sn");
        String value = (String) BeanUtil.getNestedProperty(map, "store_sn.value");
        if (StringUtils.isNotBlank(value)) {
            return value;
        }
        return null;
    }


    /**
     * 审批获取子商户号
     *
     * @param businessMap
     * @return
     */
    public static String getSubMerchantSn(Map businessMap) {
        String subMerchantSn = "";
        try {
            subMerchantSn = WosaiMapUtils.getString(businessMap, "sub_merchant_sn");
            if (StringUtils.isBlank(subMerchantSn)) {
                Map merchantInfos = MapUtils.getMap(businessMap, "merchant_sn");
                subMerchantSn = (String) MapUtils.getMap(merchantInfos, "extra.weixin_school_sn").get("value");
            }

        } catch (Exception e) {

        }
        return subMerchantSn;
    }

    /**
     * 活动报名来源是否为crm商户详情
     *
     * @param businessMap
     * @return
     */
    public static boolean isApplyActivityCrmMerchantInfo(Map businessMap) {
        if (MapUtils.isEmpty(businessMap)) {
            return false;
        }
        String value = StringUtils.upperCase(MapUtils.getString(businessMap, AUDIT_SOURCE));
        return Objects.equals(value, AuditSourceEnum.CRM_MERCHANT_INFO_FLAG.name());
    }

    /**
     * 获取list
     *
     * @param list
     * @param key
     * @param <T>
     * @return
     */
    public static <T> List<T> getList(List<Map<String, T>> list, String key) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<T> result = Lists.newArrayList();
        for (Map<String, T> map : list) {
            if (Objects.nonNull(map.get(key))) {
                result.add(map.get(key));
            }
        }
        return result;
    }
}

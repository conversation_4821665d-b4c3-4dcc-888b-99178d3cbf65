package com.wosai.trade.constant;

import com.wosai.enums.PayWayActivityAuditTemplateEventEnum;
import com.wosai.trade.service.activity.enums.ActivityTypeEnum;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2024-11-26
 */

public class AuditConstant {
    //备注默认最大长度
    public static final int REMARK_MAX_LENGTH = 100;

    /**
     * 审批失败的场景码集合文件名前缀
     */
    public static final String FAILED_SCENES_CODES_PREFIX = "failed_scenes_codes";

    /**
     * 拉卡拉小头寸失败原因文件名前缀
     */
    public static final String LAKALA_OPEN_SMALL_POSITION_FAILED_REASON = "lakala_open_small_position_failed_reason";

    //--------------------------- 审批事件名称 ---------------------------
    //刷卡活动报名申请
    public static final String EVENT_BANKCARD_CHANNEL_ACTIVITY_APPLY = "bankcard_channel_activity_apply";

    public static final int COMMENT_MESSAGE_MAX_LENGTH = 500; //最大长度
    public static final int COMMENT_RESULT_TYPE_FAILED = 0; //审批评论类型-失败
    public static final int COMMENT_RESULT_TYPE_SUCCESS = 1;//审批评论类型-成功

    //--------------------------- 审批模板内相关字段 ---------------------------
    //活动信息
    public static final String FIELD_ACTIVITY_INFO = "activity_info";
    //费率明细
    public static final String FIELD_FEET_RATE_LIST = "feet_rate_list";
    //阶梯费率明细
    public static final String FIELD_LADDER_LIST = "ladder_list";
    //费率
    public static final String FIELD_FEE_RATE = "fee_rate";
    //pay_way列表
    public static final String FIELD_PAY_WAY_LIST = "pay_way_list";
    //费率值
    public static final String FIELD_RATE = "rate";
    //资金渠道费率-单笔最高
    public static final String FIELD_MAX = "max";
    //备注
    public static final String FIELD_REMARK = "remark";
    // 场景编码列表
    public static final String SCENES_CODES = "scenes_codes";
    // 场景编码
    public static final String SCENES_CODE = "scenes_code";
    // 通道商户号
    public static final String FIELD_PROVIDER_MCH_ID = "provider_mch_id";
    // 附件连接地址
    public static final String FIELD_FILE_URL = "file_url";
    //商户sn
    public static final String FIELD_MERCHANT_SN = "merchant_sn";
    // 挂靠主商户
    public static final String FIELD_PARENT_MERCHANT = "parent_merchant";
    // 报名类型　
    public static final String FIELD_APPLY_TYPE = "apply_type";

    //--------------------------- 固定区间费率－文件上传 ---------------------------
    public static final int FILE_MERCHANT_SN_ROW = 0;
    public static final int FILE_PAY_WAY_NAME_ROW = 1;
    public static final int FIX_FILE_FEE_RATE_ROW = 2;
    public static final int FIX_FILE_REMARK_ROW = 3;

    // inherited-挂靠
    public static final String APPLY_TYPE_INHERITED = "inherited";


    /**
     * 通过审批模板获取挂靠支付源活动类型
     *
     * @param eventType
     * @return
     */
    public static Integer getSourceActivityTypeFromApprovalTemplate(String eventType) {
        if (Objects.equals(eventType, PayWayActivityAuditTemplateEventEnum.WECHAT_UNIVERSITY_ACTIVITY_RATE_PLATFORM_23_V2.getTemplateEvent())) {
            return ActivityTypeEnum.WECHAT.getCode();
        } else if (Objects.equals(eventType, PayWayActivityAuditTemplateEventEnum.ALIPAY_CAMPUS_POLICY_H2_RATE_PLATFORM_V2.getTemplateEvent())) {
            return ActivityTypeEnum.ALIPAY.getCode();
        }
        return null;
    }
}

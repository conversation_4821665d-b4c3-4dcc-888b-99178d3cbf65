package com.wosai.trade.constant;


import com.wosai.trade.model.apollo.ActivityCrmApplyAuditConfig;

/**
 * <AUTHOR> Date: 2020/4/16 Time: 4:07 下午
 */
public interface ApolloConstants {

    /**
     * 额度类型配置
     */
    String QUOTA_TYPE_CONFIG = "QuotaTypeConfig";

    /**
     * 通用配置
     */
    String COMMON_CONFIG = "CommonConfig";

    /**
     * 交易状态配置
     */
    String TRADE_STATE_CONFIG = "TradeStateConfig";

    /**
     * 新商户活动费率套餐id
     */
    String NEW_MCH_COMBO_IDS = "new_mch_trade_combo_ids";

    /**
     * 小微商户补贴套餐id
     */
    String MICRO_MCH_SUBSIDY_COMBO_IDS = "micro_mch_subsidy_combo_ids";

    /**
     * 费率套餐与业务方的映射
     */
    String TRADE_COMBO_PRODUCT_FLAG_MAP = "trade_combo_product_flag_map";

    /**
     * 套餐变更和aop通知配置映射
     */
    String APPLY_RATE_NOTIFY = "apply_rate_notify";


    /**
     * 存量商户适用的套餐ID以及规则
     */
    String OLD_MCH_COMBO_INFOS = "old_mch_trade_combo_infos";


    /**
     * 银行专项活动的id list
     */
    String BANK_SPECIAL_PROJECT_COMBO_IDS = "bank_special_project_combo_ids";

    /**
     * 小微普惠活动相关配置
     */
    String MICRO_PUHUI_ACTIVITY = "micro_puhui_activity";
    /**
     * 允许调用 getSign 的vendor app appid配置
     */
    String ALLOWED_VENDOR_APP_APPIDS = "allowed_vendor_app_appids";

    /**
     * 基础套餐
     */
    String BASE_TRADE_COMBOS = "base_trade_combos";

    /**
     * 教培行业套餐
     */
    String EDUCATION_TRAINING_COMBO_IDS = "education_training_combo_ids";

    /**
     * 标签变动时，需要写入到kafka的配置
     */
    String SENT_TO_KAFKA_TAG_IDS = "sent_to_kafka_tag_ids";

    /**
     * 测试环境邮件收件人
     */
    String BETA_MAIL_SEND_TO = "beta_mail_send_to";

    /**
     * 作业中心字段映射配置
     */
    String KF_FIELD_MAPPING_CONFIG = "KfFieldMappingConfig";

    /**
     * 银联云闪付阶梯套餐费率映射
     */
    String  UNION_PAY_LADDER_COMBOS = "union_pay_ladder_combos";

    String  UNION_PAY_LADDER_COMBOS_ID = "union_pay_ladder_combo_id";


    /**
     * 发送商户通知模板
     */
    String SEND_MERCHANT_INFO_TEMPLATE = "send_merchant_info_template";

    /**
     * 发送商户通知开发标识
     */
    String SEND_INFO_FLAG = "send_info_flag";

    /**
     *  汇率接口key
     */
    String JUHE_API_KEY = "juhe_api_key";

    /**
     * 支付宝直连套餐id
     */
    String ALIPAY_FORMAL_COMBO_ID = "alipay_formal_combo_id";

    /**
     * 费率修改时，没有套餐时，生效的通用套餐
     */
    String COMMON_COMBO_ID_FOR_FEE_RATE_UPDATE = "common_combo_id_for_fee_rate_update";

    /**
     * 大客户费率修改时，没有套餐时，生效的通用套餐
     */
    String KA_COMBO_ID_FOR_FEE_RATE_UPDATE = "ka_combo_id_for_fee_rate_update";

    /**
     * crm 服务商、拉卡拉渠道允许修改费率的套餐列表
     */
    String CRM_AGENT_LAKALA_UPDATABLE_COMBO_IDS = "crm_agent_lakala_updatable_combo_ids";


    /**
     * 银行绑卡活动考核套餐
     */
    String BIND_BANKCARD_COMBO_ID = "bind_bankcard_combo_id";

    /**
     * 套餐终止或者取消时，自动生效普惠的套餐配置
     */
    String CANCEL_OR_END_CHANGE_TO_PUHUI_COMBOS = "cancel_or_end_change_to_puhui_combos";
    /**
     * 支付源活动审批模版event
     */
    String PAY_SOURCE_ACTIVITY_TEMPLATE_EVENT = "pay_source_activity_template_event";

    /**
     * 调度器线程池数量配置
     */
    String SCHEDULER_TASK_THREAD_POOL_SIZE = "scheduler_task_thread_pool_size";

    /**
     * 备用通道切换签名key
     */
    String BYPASS_SIGN = "bypass_sign";

    /**
     * 备用通道不切换渠道
     */
    String BYPASS_EXCLUDE_ACQUIRER = "bypass_exclude_acquirer";

    /**
     * 套餐取消恢复配置
     */
    String RESTORE_COMBO_CONFIG = "restore_combo_config";

    /**
     * 微信高校活动
     */
    String WECHAT_UNIVERSITY_ACTIVITY = "wechat_university_activity_config";

    /**
     * 风控活动处置
     */
    String RISK_ACTIVITY_CONFIG = "risk_activity_config";

    /**
     *　业务方限制规则
     */
    String TRADE_APP_RULES = "trade_app_rules";

    /**
     * 需要延迟生效的活动ID
     */
    String DELAY_TAKE_AFFECT_AND_SEND_MESSAGE_AUDIT_TEMPLATES = "delay_take_affect_and_send_Message_audit_templates";

    /**
     * 分账指标
     */
    String PROFIT_SHARE_METRICS = "profit_share_metrics";

    /**
     * 外部业务方名称与trade_app关系映射
     */
    String EXTERNAL_TRADE_APP_MAPPING = "external_trade_app_mapping";

    /**
     * saas分账模型配置
     */
    String SAAS_SHARING_MODE_CONFIG = "saas_sharing_mode_config";

    /**
     * saas分账开通错误信息列表
     */
    String SAAS_PROFIT_FAIL_MESSAGE_LIST = "saas_profit_fail_message_list";
    /**
     * 按时段付费规则续费白名单列表　（在白名单内才能在续费列表展示）
     */
    String RENEWAL_SERVICE_FEE_ALLOW_LIST = "renewal_service_fee_allow_list";
    /**
     * 按时段服务规则，测试配置 {"商户ID":["服务规则ID"]}
     */
    String RENEWAL_SERVICE_FEE_TEST_CONFIG = "renewal_service_fee_test_config";
    /**
     * 额度减免活动支持的provider
     */
    String QUOTA_ACTIVITY_SUPPORT_PROVIDER_CONFIG = "quota_activity_support_provider_list_config";
    /**
     * crm活动报名审批收口相关审批模板配置
     *
     * @see ActivityCrmApplyAuditConfig
     */
    String ACTIVITY_CRM_APPLY_AUDIT_CONFIG = "activity_crm_apply_audit_config";

    /**
     * 阶段付appId
     */
    String FITNESS_APP_ID = "fitness_app_id";

    //商户d0 发aop消息 应用标识
    String D0_AOP_NOTIFY_DEVCODE = "d0_aop_notify_devCode";

    String PROJECT_FILTER_TRANSLATION_CONFIG = "project_filter_translation";

    String FEE_RATE_AUDIT_FILE_LINE_LIMIT = "fee_rate_audit_file_line_limit";
    /**
     * 线程池管理配置
     */
    String THREAD_POOL_MANAGER_CONFIG = "thread_pool_manager_config";

    String SHARING_AUDIT_ACCESS_KEY = "sharing_audit_access_key";

    String LAKALA_SHARING_AUDIT_ACCESS_KEY = "lakala_sharing_audit_access_key";

    String KEY_HAIKE_SHARING_AUDIT_QUERY_URL = "haike_sharing_audit_query_jump_url";


    String SHARING_AUDIT_ROBOT_LEVEL = "sharing_audit_robot_level";

    String SHARING_AUDIT_OPEN_EXPIRE_TIME = "sharing_audit_expire_time";

    String AUDIT_AUTO_D0_CONFIG = "audit_auto_d0_config";//申请自动D0审批的配置
    /**
     * 活动报名验证相关配置
     */
    String APPLY_ACTIVITY_VERIFY_CONFIG = "apply_activity_verify_config";


    String BUSINESS_REFUND_CONFIG = "business_refund_config";//业务退款配置
    /**
     * 额度包缓存耗时阀值配置
     */
    String QUOTA_INFO_CACHE_COST_THRESHOLD = "quota_info_cache_cost_threshold";
    /**
     * 银行卡的trade_app_id列表
     */
    String BANK_CARD_TRADE_APP_IDS = "bank_card.trade_app_ids";
    /**
     * saas分账收款方配置
     */
    String SAAS_SHARING_RECEIVER_CONFIG = "saas_sharing_receiver_config";
    /**
     * saas分账收款方通道切换预警开关
     */
    String SAAS_MONITOR_ACQUIRER_SWITCH = "saas_monitor_acquirer_switch";
    /**
     * 额度活动配置
     */
    String QUOTA_ACTIVITY_CONFIG = "quota_activity_config";
    /**
     * 当可用额度小于此阈值时，标记消耗完成时间。
     */
    String QUOTA_LOW_THRESHOLD = "quota_low_threshold";
    /**
     * 交易参数中的映射配置
     */
    String TRADE_PARAMS_PROVIDER_MCH_KEY = "trade_params_provider_mch_key";

    /**
     * 结算联动退款权限通道集合
     */
    String REFUND_TIED_CLOSE_KEY = "trade_refund_settlement_tied_close";

    /**
     * 余额服务配置
     */
    String UPAY_WALLET_CONFIG = "upay_wallet_config";
}

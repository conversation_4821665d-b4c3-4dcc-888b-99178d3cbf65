package com.wosai.trade.constant;

import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.clearance.constant.CommonClearanceConstant;

import java.util.Map;

/**
 * <AUTHOR>
 * @description 收单机构工具类
 * @date 2024-04-17
 */

public class AcquirerConstants {
    //目标收单机构
    public static final String TARGET_ACQUIRER = "target_acquirer";
    public static final String ALLOWED = "allowed";
    public static final String MSG = "msg";

    /**
     * 收单机构和通道映射
     */
    public static final Map<Integer, AcquirerTypeEnum> ACQUIRER_PROVIDER_MAP = CollectionUtil.hashMap(
            ProviderEnum.ALI_PAY.getValue(), AcquirerTypeEnum.ALI_PAY,
            ProviderEnum.WEI_XIN.getValue(), AcquirerTypeEnum.WEI_XIN,
            ProviderEnum.BESTPAY.getValue(), AcquirerTypeEnum.BEST_PAY,
            ProviderEnum.PROVIDER_LAKALA.getValue(), AcquirerTypeEnum.LKL,
            ProviderEnum.PROVIDER_CIBSHBANK.getValue(), AcquirerTypeEnum.CIB,
            ProviderEnum.PROVIDER_UMS.getValue(), AcquirerTypeEnum.UMS,
            ProviderEnum.PROVIDER_TONGLIAN.getValue(), AcquirerTypeEnum.TONG_LIAN,
            ProviderEnum.PROVIDER_PSBC.getValue(), AcquirerTypeEnum.PSBC,
            ProviderEnum.PROVIDER_CGB.getValue(), AcquirerTypeEnum.CGB,
            ProviderEnum.PROVIDER_CCB.getValue(), AcquirerTypeEnum.CCB,
            ProviderEnum.PROVIDER_HXB.getValue(), AcquirerTypeEnum.HXB,
            ProviderEnum.PROVIDER_ICBC.getValue(), AcquirerTypeEnum.ICBC,
            ProviderEnum.PROVIDER_LAKALA_V3.getValue(), AcquirerTypeEnum.LKL_V3,
            ProviderEnum.PROVIDER_TONGLIAN_V2.getValue(), AcquirerTypeEnum.TONG_LIAN_V2,
            ProviderEnum.PROVIDER_HAIKE.getValue(), AcquirerTypeEnum.HAI_KE,
            ProviderEnum.PROVIDER_FUYOU.getValue(), AcquirerTypeEnum.FU_YOU,
            ProviderEnum.PROVIDER_PAB.getValue(), AcquirerTypeEnum.PAB,
            ProviderEnum.PROVIDER_UMB.getValue(), AcquirerTypeEnum.UMB
    );

    public static Integer getProvider(String acquirer) {
        for (Map.Entry<Integer, AcquirerTypeEnum> entry : ACQUIRER_PROVIDER_MAP.entrySet()) {
            if (entry.getValue().getValue().equals(acquirer)) {
                return entry.getKey();
            }
        }
        return null;
    }

    public static String getClearanceProviderName(int clearanceProvider) {
        String providerName = CommonClearanceConstant.CLEARANCE_PROVIDER_DESC.get(clearanceProvider);
        if (providerName == null) {
            return String.valueOf(clearanceProvider);
        }
        return providerName;
    }
}

package com.wosai.trade.consumer;

import com.wosai.databus.LogEntry;
import com.wosai.databus.event.AbstractEvent;
import com.wosai.databus.event.pay.TradeManageChangeApplyActivityEvent;
import com.wosai.middleware.hera.toolkit.trace.ActiveSpan;
import com.wosai.middleware.hera.toolkit.trace.Trace;
import com.wosai.trade.biz.activity.ChangeSourceActivityBiz;
import com.wosai.trade.service.activity.enums.ActivityTypeEnum;
import com.wosai.trade.util.CommonUtil;
import com.wosai.trade.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.generic.GenericRecord;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.ByteBuffer;

/**
 * 支付源活动主体商户变动事件
 *
 * <AUTHOR>
 */
@Slf4j
@ConditionalOnProperty(name = "flags.consumer.run", matchIfMissing = true, havingValue = "true")
@Component
public class ChangeSourceActivityConsumer extends BaseConsumer {

    public static final String TOPIC = "databus.event.pay.manage.allin";

    @Resource
    private ChangeSourceActivityBiz changeSourceActivityBiz;

    @Trace
    @KafkaListener(topics = TOPIC)
    public void consume(ConsumerRecord<String, GenericRecord> record) {
        GenericRecord datum = record.value();
        if (datum == null) {
            log.error("getValue null. record:{}", record);
            return;
        }
        AbstractEvent event;
        try {
            ByteBuffer buffer = (ByteBuffer) datum.get(LogEntry.EVENT);
            event = JsonUtil.fromEventJsonBytes(buffer.array(), AbstractEvent.class);
            event.setSeq((Long) datum.get(LogEntry.SEQ));
            event.setTimestamp((Long) datum.get(LogEntry.TIMESTAMP));
            doHandleEvent(event);
        } catch (Exception e) {
            if (CommonUtil.lockEventClassException(e)) {
                return;
            }
            ActiveSpan.error(e);
            log.error("dataBus consume error. record:{}", record, e);
            throw e;
        }
    }

    private boolean canSourceApplyActivityEvent(AbstractEvent abstractEvent) {
        if (!(abstractEvent instanceof TradeManageChangeApplyActivityEvent)) {
            return false;
        }
        TradeManageChangeApplyActivityEvent event = (TradeManageChangeApplyActivityEvent) abstractEvent;
        return ActivityTypeEnum.payWayActivities.contains(event.getActivityType());
    }

    @Trace
    private void doHandleEvent(AbstractEvent abstractEvent) {
        if (!canSourceApplyActivityEvent(abstractEvent)) {
            return;
        }
        TradeManageChangeApplyActivityEvent event = (TradeManageChangeApplyActivityEvent) abstractEvent;
        if (!ActivityTypeEnum.payWayActivities.contains(event.getActivityType())) {
            log.info("非支付源活动不处理. event={}", JsonUtil.encode(event));
            return;
        }
        log.info("开始处理-支付源活动变动事件. event={}", JsonUtil.encode(event));
        try {
            changeSourceActivityBiz.process(event);
        } catch (Exception e) {
            log.info("开始处理-支付源活动变动事件异常. event={}", JsonUtil.encode(event), e);
        }
    }
}

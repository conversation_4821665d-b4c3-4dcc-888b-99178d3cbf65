package com.wosai.trade.consumer;

import com.wosai.databus.LogEntry;
import com.wosai.databus.event.AbstractEvent;
import com.wosai.databus.event.merchant.basic.MerchantBasicUpdateEvent;
import com.wosai.middleware.hera.toolkit.trace.ActiveSpan;
import com.wosai.middleware.hera.toolkit.trace.Trace;
import com.wosai.trade.biz.activity.CancelApplyActivityByMerchantIndustryBiz;
import com.wosai.trade.biz.activity.model.CancelApplyActivityByMerchantIndustryParam;
import com.wosai.trade.util.CommonUtil;
import com.wosai.trade.util.JsonUtil;
import com.wosai.trade.util.RedisLockUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.service.MerchantService;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.generic.GenericRecord;
import org.apache.commons.collections.MapUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.ByteBuffer;
import java.util.Map;

/**
 * 商户变更 consumer
 *
 * <AUTHOR>
 */
@Slf4j
@ConditionalOnProperty(name = "flags.consumer.run", matchIfMissing = true, havingValue = "true")
@Component
public class MerchantChangeConsumer extends BaseConsumer {

    @Resource
    private MerchantService merchantService;
    @Resource
    private CancelApplyActivityByMerchantIndustryBiz cancelApplyActivityByMerchantIndustryBiz;

    public static final String LOCK_INDUSTRY_PREFIX = "merchant:change:industry:";

    @Trace
    @KafkaListener(topics = "databus.event.merchant.basic.allin")
    public void consume(ConsumerRecord<String, GenericRecord> record) {
        GenericRecord datum = record.value();
        if (datum == null) {
            log.error("getValue null. record:{}", record);
            return;
        }
        AbstractEvent event;
        try {
            ByteBuffer buffer = (ByteBuffer) datum.get(LogEntry.EVENT);
            event = JsonUtil.fromEventJsonBytes(buffer.array(), AbstractEvent.class);
            event.setSeq((Long) datum.get(LogEntry.SEQ));
            event.setTimestamp((Long) datum.get(LogEntry.TIMESTAMP));
            doHandleEvent(event);
        } catch (Exception e) {
            if (CommonUtil.lockEventClassException(e)) {
                return;
            }
            ActiveSpan.error(e);
            log.error("dataBus consume error. record:{}", record, e);
            throw e;
        }
    }

    private void doHandleEvent(AbstractEvent event) {
        log.info("商户配置事件: {}", event.toJsonString());
        if (event instanceof MerchantBasicUpdateEvent) {
            MerchantBasicUpdateEvent merchantEvent = (MerchantBasicUpdateEvent) event;
            Map merchantInfo = merchantService.getMerchant(merchantEvent.getMerchantId());
            if (MapUtils.isEmpty(merchantInfo)) {
                log.info("商户配置事件，无此商户:{}", event.toJsonString());
                return;
            }
            String merchantSn = MapUtils.getString(merchantInfo, Merchant.SN);
            String industryId = MapUtils.getString(merchantInfo, Merchant.INDUSTRY);
            RedisLockUtil.tryLock(LOCK_INDUSTRY_PREFIX + merchantSn, 60, () -> {
                cancelApplyActivityByMerchantIndustryBiz.execute(CancelApplyActivityByMerchantIndustryParam.builder()
                        .merchantSn(merchantSn)
                        .industryId(industryId)
                        .build());
                return null;
            });
        }
    }
}

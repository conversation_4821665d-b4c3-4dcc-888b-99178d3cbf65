package com.wosai.trade.consumer;

import com.google.common.collect.ImmutableList;
import com.wosai.middleware.hera.toolkit.metrics.Timed;
import com.wosai.profit.sharing.constant.CommonConstant;
import com.wosai.trade.model.biz.EnabledServiceFeeRetryContext;
import com.wosai.trade.model.dal.ServiceFeeEffectiveQueryParam;
import com.wosai.trade.repository.dao.ServiceFeeEffectiveMapper;
import com.wosai.trade.repository.dao.entity.ServiceFeeEffectiveEntity;
import com.wosai.trade.service.ServiceFeeEffectiveService;
import com.wosai.trade.service.enums.ServiceFeeEffectiveStatusEnum;
import com.wosai.trade.util.ConstantUtil;
import com.wosai.trade.util.ValidationUtils;
import com.wosai.upay.core.service.BusinssCommonService;
import com.wosai.upay.job.avro.TradeAppAcquirerChange;
import io.confluent.kafka.serializers.KafkaAvroDeserializerConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.slf4j.MDC;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 多业务多通道消息监听
 * tywei
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "flags.consumer.run", matchIfMissing = true, havingValue = "true")
public class TradeAppAcquirerChangeConsumer implements InitializingBean {

    ExecutorService worker = Executors.newSingleThreadExecutor();
    ExecutorService handler = Executors.newFixedThreadPool(30);
    private static final String topic = "events_CUA_tradeApp-acquirer-change";

    @Autowired
    private KafkaProperties kafkaProperties;
    @Value("${spring.kafka.properties.schema.registry.url}")
    private String schemaRegistryUrl;
    private KafkaConsumer<String, TradeAppAcquirerChange> kafkaConsumer;
    private volatile int status = CommonConstant.RESOURCE_STATUS_RUNNING;


    @Resource
    private BusinssCommonService businssCommonService;
    @Resource
    private ServiceFeeEffectiveMapper serviceFeeEffectiveMapper;
    @Resource
    private ServiceFeeEffectiveService serviceFeeEffectiveService;

    @Override
    public void afterPropertiesSet() {
        startConsume();
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            try {
                destroy();
            } catch (Exception e) {
                throw new RuntimeException();
            }
        }));

    }

    public void destroy() throws Exception {
        status = CommonConstant.RESOURCE_STATUS_STOPPING;
        while (status != CommonConstant.RESOURCE_STATUS_STOPPED) {
            //wait kafka consumer close
            Thread.sleep(1000);
        }
    }

    public void startConsume() {
        Map<String, Object> props = kafkaProperties.buildConsumerProperties();
        props.put(KafkaAvroDeserializerConfig.SPECIFIC_AVRO_READER_CONFIG, "true");
        props.put(ConsumerConfig.ISOLATION_LEVEL_CONFIG, "read_committed");
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, "io.confluent.kafka.serializers.KafkaAvroDeserializer");
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, "io.confluent.kafka.serializers.KafkaAvroDeserializer");
        props.put(KafkaAvroDeserializerConfig.SCHEMA_REGISTRY_URL_CONFIG, schemaRegistryUrl);
        kafkaConsumer = new KafkaConsumer<>(props);
        kafkaConsumer.subscribe(Collections.singletonList(topic));
        worker.execute(() -> {
            while (true) {
                try {
                    if (status == CommonConstant.RESOURCE_STATUS_STOPPING) {
                        kafkaConsumer.close();
                        status = CommonConstant.RESOURCE_STATUS_STOPPED;
                        //退出
                        log.info("kafka consumer stopped");
                        break;
                    }
                    consume();
                } catch (Exception e) {
                    log.error("kafka consumer error: {}", e.getMessage(), e);
                    try {
                        Thread.sleep(5000);
                    } catch (InterruptedException e1) {
                    }
                }
            }
        });
    }

    @Timed
    public void consume() throws InterruptedException {
        //这里控制每次拿到多少批量数据的参数配置是: max.poll.records
        final ConsumerRecords<String, TradeAppAcquirerChange> consumerRecords = kafkaConsumer.poll(1000);
        int size = consumerRecords.count();
        if (size == 0) {
            return;
        }
        CountDownLatch latch = new CountDownLatch(size);
        //具体操作的地方
        for (ConsumerRecord<String, TradeAppAcquirerChange> record : consumerRecords) {
            handler.execute(TimedRunnable.of("TradeAppAcquirerChangeConsumer", () -> {
                try {
                    TradeAppAcquirerChange acquirerChange = record.value();
                    retryServiceFee(acquirerChange);
                } catch (Throwable t) {
                    log.error("consume record error: {}", t.getMessage(), t);
                } finally {
                    latch.countDown();
                }
            }));
        }
        latch.await();
    }

    private void retryServiceFee(TradeAppAcquirerChange event) {
        log.info("record TradeAppAcquirerChange={}", event);
        //不处理业务方不存在或为1（基础APP）
        if (Objects.isNull(event.getTradeAppId()) || Objects.equals("1", event.getTradeAppId().toString())) {
            return;
        }
//        log.info("record acquirerChange={}", event);
        String merchantSn = null;
        if (Objects.nonNull(event.getMerchantSn())) {
            merchantSn = event.getMerchantSn().toString();
        }
        if (Objects.isNull(merchantSn) && Objects.nonNull(event.getMerchantId())) {
            merchantSn = businssCommonService.getMerchantSnById(event.getMerchantId().toString());
        }
        ValidationUtils.check(Objects.nonNull(merchantSn), "商户号为空");
        ServiceFeeEffectiveQueryParam queryParam = ServiceFeeEffectiveQueryParam.builder()
                .merchantSn(merchantSn)
                .statusList(ImmutableList.of(ServiceFeeEffectiveStatusEnum.PENDING.getStatusValue()))
                .build();
        List<ServiceFeeEffectiveEntity> list = serviceFeeEffectiveMapper.queryByCondition(queryParam);
        for (ServiceFeeEffectiveEntity effectiveDo : list) {
            ServiceFeeEffectiveEntity.Extra extra = effectiveDo.buildExtra();
            EnabledServiceFeeRetryContext retryContext = extra.getRetryContext();
            if (Objects.isNull(retryContext) || !retryContext.isRetry()) {
                continue;
            }
            try {
                MDC.put(ConstantUtil.TRACE_ID, merchantSn + "-" + effectiveDo.getId());
                log.info("retryServiceFee merchantSn:{},feeId:{},effectiveId:{}",
                        merchantSn, effectiveDo.getFeeId(), effectiveDo.getId());
                ValidationUtils.validate(retryContext.getRequest());
                serviceFeeEffectiveService.enabled(retryContext.getRequest());
            } catch (Exception e) {
                log.error("retryServiceFee failed. merchantSn:{}", merchantSn, e);
            } finally {
                MDC.clear();
            }
        }
    }
}

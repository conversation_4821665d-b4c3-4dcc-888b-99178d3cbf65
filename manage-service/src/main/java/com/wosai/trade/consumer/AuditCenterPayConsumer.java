package com.wosai.trade.consumer;


import com.wosai.profit.sharing.constant.CommonConstant;
import com.wosai.trade.config.consumer.AopKafkaConsumerConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.generic.GenericRecord;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * AOP审批中心消费
 */
@Slf4j
@ConditionalOnProperty(name = "flags.consumer.run", matchIfMissing = true, havingValue = "true")
@Component
public class AuditCenterPayConsumer {

    public static final String TOPIC_NAME = "callback_OSP_audit-center-pay";
    private volatile int status = CommonConstant.RESOURCE_STATUS_RUNNING;
    @Resource
    private AopKafkaConsumerConfig config;
    @Resource
    private AuditInstanceEventConsumer auditInstanceEventConsumer;

    @PostConstruct
    public void startConsume() {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            try {
                destroy();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }));
        ExecutorService worker = new ThreadPoolExecutor(AopKafkaConsumerConfig.DEFAULT_CONCURRENCY, AopKafkaConsumerConfig.DEFAULT_CONCURRENCY, 0L, TimeUnit.MILLISECONDS, new SynchronousQueue<>());
        DefaultKafkaConsumerFactory<String, GenericRecord> consumerFactory = config.createConsumerFactory();
        Consumer<String, GenericRecord> kafkaConsumer = consumerFactory.createConsumer();
        kafkaConsumer.subscribe(Collections.singletonList(TOPIC_NAME));
        worker.execute(() -> {
            while (true) {
                try {
                    if (status == CommonConstant.RESOURCE_STATUS_STOPPING) {
                        kafkaConsumer.close();
                        status = CommonConstant.RESOURCE_STATUS_STOPPED;
                        //退出
                        log.info("kafka consumer stopped");
                        break;
                    }
                    consume(kafkaConsumer);
                } catch (Exception e) {
                    log.error("kafka consumer error: {}", e.getMessage(), e);
                    try {
                        Thread.sleep(5000);
                    } catch (InterruptedException e1) {
                    }
                }
            }
        });
    }

    private void consume(Consumer<String, GenericRecord> kafkaConsumer) {
        ConsumerRecords<String, GenericRecord> records = kafkaConsumer.poll(AopKafkaConsumerConfig.DEFAULT_POLL_TIMEOUT);
        if (records.count() <= 0) {
            return;
        }
        for (ConsumerRecord<String, GenericRecord> record : records) {
            auditInstanceEventConsumer.doProcess(record);
        }
    }

    public void destroy() throws Exception {
        status = CommonConstant.RESOURCE_STATUS_STOPPING;
        while (status != CommonConstant.RESOURCE_STATUS_STOPPED) {
            //wait kafka consumer close
            Thread.sleep(1000);
        }
    }
}

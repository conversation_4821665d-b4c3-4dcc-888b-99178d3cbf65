package com.wosai.trade.consumer;

import com.alibaba.fastjson.JSON;
import com.wosai.data.crow.api.service.TagIngestService;
import com.wosai.databus.LogEntry;
import com.wosai.databus.event.AbstractEvent;
import com.wosai.databus.event.merchant.contract.MerchantContractStatusChangeEvent;
import com.wosai.middleware.hera.toolkit.trace.ActiveSpan;
import com.wosai.middleware.hera.toolkit.trace.Trace;
import com.wosai.sales.core.service.OrganizationService;
import com.wosai.trade.config.apollo.CommonApolloConfig;
import com.wosai.trade.impl.MicroPuHuiService;
import com.wosai.trade.impl.TradeCommonService;
import com.wosai.trade.repository.dao.entity.TradeActivityEntity;
import com.wosai.trade.service.FeeRateService;
import com.wosai.trade.service.TradeActivityService;
import com.wosai.trade.util.JsonUtil;
import facade.ICustomerRelationFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.generic.GenericRecord;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.nio.ByteBuffer;

/**
 * <AUTHOR>
 * 新增商户入网满足条件的自动切普惠套餐
 */
@Slf4j
@ConditionalOnProperty(name = "flags.consumer.run", matchIfMissing = true, havingValue = "true")
@Component
public class NewMchContractConsumer extends BaseConsumer {
    public static final Logger logger = LoggerFactory.getLogger(NewMchContractConsumer.class);

    @Autowired
    FeeRateService feeRateService;

    @Autowired
    TagIngestService tagIngestService;

    @Autowired
    CommonApolloConfig commonApolloConfig;

    @Autowired
    ICustomerRelationFacade iCustomerRelationFacade;

    @Autowired
    OrganizationService organizationService;

    @Autowired
    TradeActivityService tradeActivityService;

    @Autowired
    TradeCommonService tradeCommonService;

    @Autowired
    MicroPuHuiService microPuHuiService;

    @KafkaListener(topics = "databus.event.merchant.contract.allin")
    @Trace(operationName = "merchant-contract-allin")
    protected void doProcess(ConsumerRecord<String, GenericRecord> record) {
        GenericRecord datum = record.value();
        AbstractEvent event = null;
        try {
            ByteBuffer buffer = (ByteBuffer) datum.get(LogEntry.EVENT);
            event = JsonUtil.fromEventJsonBytes(buffer.array(), AbstractEvent.class);
            event.setSeq((Long) datum.get(LogEntry.SEQ));
            event.setTimestamp((Long) datum.get(LogEntry.TIMESTAMP));
            logger.info("merchant contract event {}", JSON.toJSONString(event));
            handleEvent(event);
        } catch (Throwable e) {
            ActiveSpan.error(e);
            logger.error("merchant contract event consume error {}", record, e);
        }
    }

    private void handleEvent(AbstractEvent event) {
        MerchantContractStatusChangeEvent statusChangeEvent = (MerchantContractStatusChangeEvent) event;
        String merchantId = statusChangeEvent.getMerchantId();
        if (merchantId != null){
            microPuHuiService.applyMicroPuHuiMchCombo(merchantId, TradeActivityEntity.INSERT_MODE_MICRO_PUHUI_NEW);
        }
    }
}

package com.wosai.trade.consumer;

import com.wosai.databus.LogEntry;
import com.wosai.databus.avro.AvroEventEntry;
import com.wosai.databus.event.AbstractEvent;
import com.wosai.databus.event.period.PeriodPayCompleteEvent;
import com.wosai.middleware.hera.toolkit.trace.ActiveSpan;
import com.wosai.middleware.hera.toolkit.trace.Trace;
import com.wosai.period.enums.BizTypeEnum;
import com.wosai.trade.biz.servicefee.CancelServiceFeeBiz;
import com.wosai.trade.biz.servicefee.ServiceFeeAlarmEventBiz;
import com.wosai.trade.biz.servicefee.ServiceFeeVolcanoEventBiz;
import com.wosai.trade.constant.LockConst;
import com.wosai.trade.service.ServiceFeeEffectiveService;
import com.wosai.trade.service.enums.ChargeCycleEnum;
import com.wosai.trade.service.enums.ChargeMethodEnum;
import com.wosai.trade.service.enums.ComboConfigLevelEnum;
import com.wosai.trade.service.servicefee.request.CancelServiceFeeEffectiveRequest;
import com.wosai.trade.service.servicefee.request.EnabledServiceFeePeriodRequest;
import com.wosai.trade.service.servicefee.request.EnabledServiceFeeRequest;
import com.wosai.trade.util.ConstantUtil;
import com.wosai.trade.util.JsonUtil;
import com.wosai.trade.util.RedisLockUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.MDC;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.ByteBuffer;
import java.sql.Date;
import java.time.Instant;
import java.util.List;
import java.util.Objects;

/**
 * Description: 续费服务完成相关事件监听
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/30
 */
@Slf4j
@ConditionalOnProperty(name = "flags.consumer.run", matchIfMissing = true, havingValue = "true")
@Component
public class PeriodPayCompleteConsumer {
    @Resource
    private ServiceFeeEffectiveService serviceFeeEffectiveService;
    @Resource
    private ServiceFeeVolcanoEventBiz serviceFeeVolcanoEventBiz;
    @Resource
    private ServiceFeeAlarmEventBiz serviceFeeAlarmEventBiz;
    @Resource
    private CancelServiceFeeBiz cancelServiceFeeBiz;

    @Trace
    @KafkaListener(id = "PeriodPayCompleteConsumer",
            topics = "${spring.kafka.period-pay.topic}",
            groupId = "${spring.kafka.period-pay.group-id}",
            containerFactory = "periodPayCompleteContainerFactory")
    public void listener(List<AvroEventEntry> avroEventEntryList) {
        if (CollectionUtils.isEmpty(avroEventEntryList)) {
            return;
        }
        avroEventEntryList.forEach(avroEventEntry -> {
            log.info("record avroEventEntry={}", avroEventEntry);
            AbstractEvent event;
            try {
                ByteBuffer buffer = (ByteBuffer) avroEventEntry.get(LogEntry.EVENT);
                event = JsonUtil.fromEventJsonBytes(buffer.array(), AbstractEvent.class);
                handleEvent(event);
            } catch (Throwable e) {
                ActiveSpan.error(e);
                log.error("handleEventFail", e);
            }
        });
    }

    public void handleEvent(AbstractEvent abstractEvent) {
        if (!(abstractEvent instanceof PeriodPayCompleteEvent)) {
            return;
        }
        PeriodPayCompleteEvent event = (PeriodPayCompleteEvent) abstractEvent;
        //业务类型处理判定
        if (!BizTypeEnum.SAAS.name().equals(event.getBizType())) {
            return;
        }
        try {
            MDC.put(ConstantUtil.TRACE_ID, event.getMerchantSn());
            log.info("PeriodPayCompleteConsumer event:{}", JsonUtil.encode(event));
            String lockKey = LockConst.SERVICE_FEE_EFFECTIVE_PREFIX + event.getMerchantSn();
            if (serviceFeeEffectiveService.isEnabledPeriod(event.getOperatorType())) {
                EnabledServiceFeeRequest request = buildEnabledRequest(event);
                log.info("PeriodPayCompleteConsumer req:{}", JsonUtil.encode(request));
                Long effectiveId = RedisLockUtil.tryLock(lockKey, 120L, () -> serviceFeeEffectiveService.enabled(request));
                //发送支付成功事件
                serviceFeeVolcanoEventBiz.publishPayComplete(effectiveId, event.getOperatorType());
            } else if (cancelServiceFeeBiz.isCancel(event)) {
                RedisLockUtil.tryLock(lockKey, 120L, () -> {
                    cancelServiceFeeBiz.execute(buildCancelRequest(event));
                    return null;
                });
            } else {
                log.error("PeriodPayCompleteConsumer 无法处理该事件");
            }
        } catch (Exception e) {
            log.error("PeriodPayConsumer failed.", e);
            serviceFeeAlarmEventBiz.periodPayComplete(event, e);
        } finally {
            MDC.clear();
        }
    }

    public static EnabledServiceFeeRequest buildEnabledRequest(PeriodPayCompleteEvent event) {
        EnabledServiceFeePeriodRequest periodRequest = new EnabledServiceFeePeriodRequest();
        periodRequest.setServiceFeeId(Long.parseLong(event.getBizItemCode()));
        periodRequest.setDimensionNo(event.getBizSn());
        periodRequest.setMerchantSn(event.getMerchantSn());
        periodRequest.setStoreSn(event.getStoreSn());
        periodRequest.setLevel(ComboConfigLevelEnum.valueOf(event.getLevel()));
        periodRequest.setChargeCycle(ChargeCycleEnum.valueOf(event.getChargeCycle()));
        periodRequest.setOperatorType(event.getOperatorType());
        periodRequest.setOperatorId(ConstantUtil.SYSTEM_NAME);
        periodRequest.setOperatorName(ConstantUtil.SYSTEM_NAME);
        periodRequest.setChargeAmount(event.getChargeAmount());
        if (Objects.nonNull(event.getBeginDate()) && event.getBeginDate() > 0) {
            periodRequest.setBeginDate(Date.from(Instant.ofEpochMilli(event.getBeginDate())));
        }
        if (Objects.nonNull(event.getEndDate()) && event.getEndDate() > 0) {
            periodRequest.setEndDate(Date.from(Instant.ofEpochMilli(event.getEndDate())));
        }
        EnabledServiceFeeRequest request = new EnabledServiceFeeRequest();
        request.setChargeMethodEnum(ChargeMethodEnum.DIRECT);
        request.setPeriodRequest(periodRequest);
        return request;
    }

    private CancelServiceFeeEffectiveRequest buildCancelRequest(PeriodPayCompleteEvent event) {
        CancelServiceFeeEffectiveRequest request = new CancelServiceFeeEffectiveRequest();
        request.setMerchantSn(event.getMerchantSn());
        request.setServiceFeeId(Long.parseLong(event.getBizItemCode()));
        request.setDimensionNo(event.getBizSn());
        request.setOperatorId(ConstantUtil.SYSTEM_NAME);
        request.setOperatorName(ConstantUtil.SYSTEM_NAME);
        request.setRemark("续费失效");
        return request;
    }
}

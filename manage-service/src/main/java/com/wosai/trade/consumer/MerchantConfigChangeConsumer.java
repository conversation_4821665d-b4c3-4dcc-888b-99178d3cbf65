package com.wosai.trade.consumer;

import com.wosai.databus.LogEntry;
import com.wosai.databus.avro.AvroEventEntry;
import com.wosai.databus.event.AbstractEvent;
import com.wosai.databus.event.merchant.config.MerchantConfigChangeEvent;
import com.wosai.databus.jackson.EventAwareJackson2PersistenceHelper;
import com.wosai.middleware.hera.toolkit.trace.ActiveSpan;
import com.wosai.middleware.hera.toolkit.trace.RunnableWrapper;
import com.wosai.middleware.hera.toolkit.trace.Trace;
import com.wosai.trade.biz.activity.QuotaApplyActivityCheckBiz;
import com.wosai.trade.biz.activity.quota.CancelQuotaActivityApplyBiz;
import com.wosai.trade.config.ThreadPoolManager;
import com.wosai.trade.constant.LockConst;
import com.wosai.trade.util.RedisLockUtil;
import com.wosai.trade.util.SpringBeanUtil;
import com.wosai.upay.core.model.MerchantConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.ByteBuffer;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * merchant_config表数据变动事件消费
 *
 * <AUTHOR>
 * @date 2025/02/19
 */
@Slf4j
@ConditionalOnProperty(name = "flags.consumer.run", matchIfMissing = true, havingValue = "true")
@Component
public class MerchantConfigChangeConsumer {

    private static final EventAwareJackson2PersistenceHelper PERSISTENCE_HELPER = new EventAwareJackson2PersistenceHelper();
    @Resource
    private CancelQuotaActivityApplyBiz cancelQuotaActivityApplyBiz;

    /**
     * 监听Kafka消息，处理商户配置变更事件。
     * 该函数使用@KafkaListener注解监听指定的Kafka主题，当接收到消息时，会解析并处理商户配置变更事件。
     *
     * @param avroEventEntryList 从Kafka接收到的Avro事件条目列表。每个条目包含一个事件的数据。
     *                           如果列表为空，则函数直接返回，不进行任何处理。
     */
    @Trace
    @KafkaListener(id = "MerchantConfigChangeConsumer",
            topics = "${spring.kafka.merchant-config-change.topic}",
            groupId = "${spring.kafka.merchant-config-change.group-id}",
            containerFactory = "MerchantConfigChangeConsumerConfigFactory")
    public void listener(List<AvroEventEntry> avroEventEntryList) {
        // 如果接收到的消息列表为空，则直接返回
        if (CollectionUtils.isEmpty(avroEventEntryList)) {
            return;
        }

        // 使用CountDownLatch来等待所有事件处理完成
        CountDownLatch countDownLatch = new CountDownLatch(avroEventEntryList.size());

        // 遍历每个Avro事件条目，并提交到线程池进行处理
        for (AvroEventEntry avroEventEntry : avroEventEntryList) {
            ThreadPoolManager.executeMerchantConfigChangeConsumerTask(RunnableWrapper.of(() -> {
                AbstractEvent event;
                try {
                    // 从Avro事件条目中提取事件数据，并反序列化为AbstractEvent对象
                    ByteBuffer buffer = (ByteBuffer) avroEventEntry.get(LogEntry.EVENT);
                    event = (AbstractEvent) PERSISTENCE_HELPER.fromJsonBytes(buffer.array(), AbstractEvent.class);
                    // 如果事件是MerchantConfigChangeEvent类型，则调用handleEvent方法进行处理
                    if (event instanceof MerchantConfigChangeEvent) {
                        handleEvent((MerchantConfigChangeEvent) event);
                    }
                } catch (Throwable e) {
                    // 记录异常信息，并标记当前Span为错误状态
                    ActiveSpan.error(e);
                    log.error("merchant_config表数据变动事件消费异常", e);
                } finally {
                    countDownLatch.countDown();
                }
            }));
        }

        try {
            // 等待所有事件处理完成，最多等待10分钟
            countDownLatch.await(10, TimeUnit.MINUTES);
        } catch (Throwable e) {
            log.error("merchant_config表数据变动事件消费异常", e);
        }
    }

    /**
     * 处理商户配置变更事件
     * <p>
     * 功能说明：
     * 1. 校验商户ID有效性
     * 2. 过滤非移动支付方式变更事件
     *
     * @param event 商户配置变更事件对象，包含变更前后的配置数据
     */
    private void handleEvent(MerchantConfigChangeEvent event) {
        // 从变更后数据中提取核心参数
        String merchantId = MapUtils.getString(event.getAfter(), MerchantConfig.MERCHANT_ID);
        Integer payway = MapUtils.getInteger(event.getAfter(), MerchantConfig.PAYWAY);
        // 判断支付通道是否发生变化
        Integer beforeProvider = MapUtils.getInteger(event.getBefore(), MerchantConfig.PROVIDER);
        Integer afterProvider = MapUtils.getInteger(event.getAfter(), MerchantConfig.PROVIDER);
        if (Objects.equals(beforeProvider, afterProvider)) {
            return;
        }
        if (!SpringBeanUtil.isProd()) {
            log.info("merchant_config表数据变动事件消费 event={}", event);
        }
        /* 校验商户ID合法性 */
        if (StringUtils.isEmpty(merchantId)) {
            log.error("merchant_config表数据变动事件消费 merchantId为空, event={}", event);
            return;
        }
        /* 过滤非移动支付方式变更事件 */
        if (!QuotaApplyActivityCheckBiz.MOBILE_PAYWAY_LIST.contains(payway)) {
            return;
        }
        /* 使用分布式锁处理核心业务逻辑 */
        String lockName = LockConst.MERCHANT_CONFIG_EVENT_CONSUMER_LOCK_PREFIX + merchantId;
        RedisLockUtil.tryLock(lockName, LockConst.LOCK_EXPIRE_SEC, () -> {
            // 执行取消配额申请相关业务操作
            cancelQuotaActivityApplyBiz.cancelByChangeMobileProvider(merchantId, payway, afterProvider);
            return null;
        });
    }

}

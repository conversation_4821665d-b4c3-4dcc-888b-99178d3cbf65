package com.wosai.trade.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.google.common.collect.ImmutableMap;
import com.wosai.databus.LogEntry;
import com.wosai.databus.event.AbstractEvent;
import com.wosai.databus.event.order.basic.FTOrderBasicRefundEvent;
import com.wosai.databus.jackson.EventAwareJackson2PersistenceHelper;
import com.wosai.middleware.hera.toolkit.trace.ActiveSpan;
import com.wosai.middleware.hera.toolkit.trace.Trace;
import com.wosai.sales.terminal.bean.TerminalSalesOrder;
import com.wosai.sales.terminal.service.TerminalOrderService;
import com.wosai.trade.biz.audit.filter.NewMchFilter;
import com.wosai.trade.constant.ApolloConstants;
import com.wosai.trade.constant.FeeRateStatusConst;
import com.wosai.trade.model.constant.ManageConstant;
import com.wosai.trade.model.dal.MerchantFeeRateQueryDalParam;
import com.wosai.trade.repository.dao.MerchantFeeRateDao;
import com.wosai.trade.service.FeeRateService;
import com.wosai.trade.service.enums.FeeRateTypeEnum;
import com.wosai.trade.service.exception.AuditBizException;
import com.wosai.trade.service.request.CancelFeeRateRequest;
import com.wosai.trade.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.generic.GenericRecord;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.nio.ByteBuffer;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Slf4j
@ConditionalOnProperty(name = "flags.consumer.run", matchIfMissing = true, havingValue = "true")
@Component
public class TerminalOrderChangeEventConsumer extends BaseConsumer {

    @ApolloConfig
    private Config config;

    private static volatile Set<Long> NEW_MCH_COMBO_SET = new HashSet<>();

    @Autowired
    private MerchantFeeRateDao merchantFeeRateDao;

    @Autowired
    private TerminalOrderService terminalOrderService;

    @Autowired
    private NewMchFilter newMchFilter;

    @Autowired
    private FeeRateService feeRateService;

    public static final Logger logger = LoggerFactory.getLogger(TerminalOrderChangeEventConsumer.class);

    protected EventAwareJackson2PersistenceHelper persistenceHelper = new EventAwareJackson2PersistenceHelper();


    private static final String REFUND_TERMINAL = "zd";
    private static final String REFUND_SOURCE_TERMINAL = "terminal";
    private static final String ORDER_SN = "order_sn";

    @KafkaListener(topics = "databus.event.shouqianba.order.allin", groupId = "trade-manage-service-default-prod-TerminalOrderChange")
    @Trace(operationName = "shouqianba-order-consumer")
    public void doProcess(ConsumerRecord<String, GenericRecord> record) {
        GenericRecord datum = record.value();
        AbstractEvent event;
        String json = null;
        try {
            ByteBuffer buffer = (ByteBuffer) datum.get(LogEntry.EVENT);
            event = JsonUtil.fromEventJsonBytes(buffer.array(), AbstractEvent.class);
            event.setSeq((Long) datum.get(LogEntry.SEQ));
            event.setTimestamp((Long) datum.get(LogEntry.TIMESTAMP));
            json = JSON.toJSONString(event);
            logger.info("ft order event {}", json);
            if (!(event instanceof FTOrderBasicRefundEvent)) {
                return;
            }
            FTOrderBasicRefundEvent refundEvent = (FTOrderBasicRefundEvent) event;
            if ((!REFUND_TERMINAL.equals(refundEvent.getSource())) || (!REFUND_SOURCE_TERMINAL.equals(refundEvent.getSourceType()))) {
                return;
            }
            String sn = refundEvent.getSn();
            Map order = terminalOrderService.getOrder(ImmutableMap.of(ORDER_SN, sn));
            String merchantSn = MapUtils.getString(order, TerminalSalesOrder.MERCHANT_SN);
            String merchantId = MapUtils.getString(order, TerminalSalesOrder.MERCHANT_ID);
            if (merchantSn == null) {
                logger.info("ft refund terminal order query not found {}", sn);
                return;
            }

            MerchantFeeRateQueryDalParam feeRateQueryDalParam = MerchantFeeRateQueryDalParam.builder()
                    .merchantSn(merchantSn)
                    .feeRateType(FeeRateTypeEnum.LADDER.name().toLowerCase())
                    .status(FeeRateStatusConst.IN_EFFECT)
                    .build();
            merchantFeeRateDao.selectList(feeRateQueryDalParam)
                    .stream()
                    // 非（新增0.1%，新增0%） 不取消
                    .filter(merchantFeeRateEntity ->
                            !NEW_MCH_COMBO_SET.isEmpty() && NEW_MCH_COMBO_SET.contains(merchantFeeRateEntity.getTradeComboId()))
                    .forEach(merchantFeeRateEntity -> {
                        try {
                            // 这里用了子类的判断，其实一样
                            newMchFilter.supportOpen(merchantId);
                            logger.info("{} support ,not to cancel activity", merchantId);
                        } catch (AuditBizException ex) {
                            logger.info("{} not support ,to cancel activity {}", merchantId, ex.getMessage());
                            Long tradeComboId = merchantFeeRateEntity.getTradeComboId();

                            CancelFeeRateRequest cancelFeeRateRequest = new CancelFeeRateRequest()
                                    .setMerchantSn(merchantSn)
                                    .setTradeComboId(tradeComboId)
                                    .setAuditSn(ManageConstant.USER_SYSTEM)
                                    .setOperator(ManageConstant.USER_SYSTEM)
                                    .setOperatorName(ManageConstant.USER_SYSTEM);
                            feeRateService.cancelFeeRate(cancelFeeRateRequest);
                        }
                    });
        } catch (Throwable e) {
            ActiveSpan.error(e);
            logger.error("process event fail {} ", json, e);
        }
    }

    @PostConstruct
    private void init() {
        reload();
    }

    @ApolloConfigChangeListener
    private void changeListener(ConfigChangeEvent changeEvent) {
        if (changeEvent.isChanged(ApolloConstants.NEW_MCH_COMBO_IDS)) {
            reload();
        }
    }

    private void reload() {
        String comIdsText = config.getProperty(ApolloConstants.NEW_MCH_COMBO_IDS, "");
        if (StringUtils.isEmpty(comIdsText)) {
            NEW_MCH_COMBO_SET.clear();
        } else {
            try {
                NEW_MCH_COMBO_SET = JSON.parseObject(comIdsText, new TypeReference<HashSet<Long>>() {
                });
            } catch (Exception e) {
                log.error("key -> {} 解析数据异常：{}", ApolloConstants.NEW_MCH_COMBO_IDS, comIdsText, e);
            }

        }
    }
}

package com.wosai.trade.consumer;

import com.alibaba.fastjson.JSON;
import com.aliyun.oss.model.CannedAccessControlList;
import com.google.common.collect.ImmutableList;
import com.shouqianba.workflow.bean.CallBackBean;
import com.shouqianba.workflow.service.CallBackService;
import com.wosai.databus.LogEntry;
import com.wosai.databus.event.AbstractEvent;
import com.wosai.databus.event.audit.*;
import com.wosai.middleware.hera.toolkit.trace.ActiveSpan;
import com.wosai.middleware.hera.toolkit.trace.Trace;
import com.wosai.shouqianba.withdrawservice.service.CompensationService;
import com.wosai.trade.biz.activity.ActivityApplyAuditEventBiz;
import com.wosai.trade.biz.activity.AuditHandleBiz;
import com.wosai.trade.biz.activity.QuotaAuditHandleBiz;
import com.wosai.trade.biz.audit.*;
import com.wosai.trade.config.apollo.CommonApolloConfig;
import com.wosai.trade.constant.AuditConstant;
import com.wosai.trade.model.constant.ManageConstant;
import com.wosai.trade.repository.dao.entity.TradeActivityEntity;
import com.wosai.trade.service.ActivityService;
import com.wosai.trade.service.exception.TradeManageBizException;
import com.wosai.trade.util.JsonUtil;
import com.wosai.trade.util.OssUtil;
import com.wosai.trade.util.SpringBeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.generic.GenericRecord;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.net.URLDecoder;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@ConditionalOnProperty(name = "flags.consumer.run", matchIfMissing = true, havingValue = "true")
@Component
public class AuditInstanceEventConsumer extends BaseConsumer {
    public static final Logger logger = LoggerFactory.getLogger(AuditInstanceEventConsumer.class);
    @Autowired
    private CallBackService callBackService;

    @Autowired
    private MchFeeRateBiz mchFeeRateBiz;
    @Autowired
    private MchBankFeeRateBiz mchBankFeeRateBiz;

    @Autowired
    private AuditBiz auditBiz;

    @Autowired
    StringRedisTemplate stringRedisTemplate;

    @Autowired
    private NotifyTradeBiz notifyTradeBiz;

    @Autowired
    private AuditHandleBiz activityAuditHandleBiz;

    @Autowired
    private ActivityService activityService;
    @Autowired
    private QuotaAuditHandleBiz quotaAuditHandleBiz;

    @Autowired
    private CompensationService compensationService;
    @Autowired
    private CommonApolloConfig apolloConfig;

    @Autowired
    private AuditDepositBiz auditDepositBiz;

    @Autowired
    private ActivityApplyAuditEventBiz activityApplyAuditEventBiz;

    @Autowired
    private MerchantWithdrawRetainedAmountBiz merchantWithdrawRetainedAmountBiz;

    @Autowired
    DataCooperationMerchantConfigBiz merchantConfigBiz;

    @Autowired
    SharingAuditBiz sharingAuditBiz;

    @Autowired
    private StoreSceneSwitchBiz storeSceneSwitchBiz;

    @Resource
    private List<AbstractBusinessAuditApprovalProcessor> auditApprovalProcessorList;
    /**
     * 　供开发测试时跳过某个事件模板 完成测试
     * 使用方法：
     * 1、启动命令里添加一个新的group-id
     * 2、启动命令里添加需测试用的事件模板，多个以半角逗号分隔
     * 例：　-Dspring.kafka.consumer.group-id=trade-manage-service-wx-tyw -Dflags.consumer.run.AuditInstanceEventConsumer.ignoreTemplateEvents=
     */
    @Value("#{'${flags.consumer.run.AuditInstanceEventConsumer.ignoreTemplateEvents:}'.split(',')}")
    private List<String> ignoreTemplateEvents;

    private final String OSS_DOWNLOAD_PRE = "https://images.wosaimg.com/";

    private final String OSS_AUDIT_ERRORMSG = "portal/auditErrorMsg/";

    private static final String KEY_PREFIX = "trade_manage_data_bus_audit_handler_";

    /**
     * 数据总线消费
     *
     * @param record
     */
    @KafkaListener(topics = "databus.event.crm.audit.allin")
    @Trace
    protected void doProcess(ConsumerRecord<String, GenericRecord> record) {
        doProcess0(record);
    }

    protected void doProcess0(ConsumerRecord<String, GenericRecord> record) {
        logger.info("record key={},value={}", record.key(), record.value());
        GenericRecord datum = record.value();
        AbstractEvent event = null;
        try {
            ByteBuffer buffer = (ByteBuffer) datum.get(LogEntry.EVENT);
            event = JsonUtil.fromEventJsonBytes(buffer.array(), AbstractEvent.class);
            event.setSeq((Long) datum.get(LogEntry.SEQ));
            event.setTimestamp((Long) datum.get(LogEntry.TIMESTAMP));
            handleEvent(event);
        } catch (Throwable e) {
            ActiveSpan.error(e);
            handleEventFail(event, e);
        }
    }

    /**
     * 费率套餐审批
     */
    private static final String FEE_RATE_COMBO_APPROVAL = "fee_rate_combo_approval";


    /**
     * 审批事件（模板）名：优惠费率套餐
     */
    public static final String PREFERENTIAL_RATE_COMBO_APPROVAL = "trade_manager_preferential_rate_combo_approval";
    /**
     * 审批事件（模板）名：特优费率套餐
     */
    public static final String PREMIUM_RATE_COMBO_APPROVAL = "trade_premium_rate_application_combo_approval";
    /**
     * 审批事件（模板）名：市场通用费率套餐
     */
    public static final String COMMON_RATE_COMBO_APPROVAL = "trade_common_rate_application_combo_approval";

    /**
     * 审批事件（模板）名：银行卡费率套餐
     */
    public static final String BANK_RATE_COMBO_APPROVAL = "trade_bank_rate_application_combo_approval";

    /**
     * 审批事件（模板）名：沉睡商户恢复交易
     */
    public static final String OPEN_PAY = "trade_open_merchant_pay";
    /**
     * 审批事件（模板）名：单收款通道关闭
     */
    public static final String SWITCH_APPLY = "SWITCH_APPLY";
    /**
     * 审批事件（模板）名：自定义对账周期商户d0提现权限
     */
    public static final String CUSTOMIZE_OFFSET_HOUR_MERCHANT_D0 = "customize_offset_hour_merchant_d0";
    /**
     * 审批事件（模板）名：花呗分期费率活动
     */
    public static final String FBFQ_RATE_ACTIVITY = "fbfq_rate_activity";
    /**
     * 审批事件（模板）名：支付宝限制级审批增加主体pid校验
     */
    public static final String ALIPAY_RECEIPT_LIMIT_COLLECTION = "alipay_receipt_limit_collection";

    /**
     * 审批事件（模板）名： 存量商户参与普惠3w-6w
     */
    public static final String OLD_MCH_MICRO_PUHUI_ACTIVITY_3W_6W = "old_mch_micro_puhui_activity_3w_6w";

    /**
     * 审批事件（模板）名： 存量商户参与普惠2w-6w
     */
    public static final String OLD_MCH_MICRO_PUHUI_ACTIVITY_2W_6W = "old_mch_micro_puhui_activity_2w_6w";

    /**
     * 审批事件（模板）名： 商户提现备注申请
     */
    public static final String CUSTOMIZE_WITHDRAW_REMARK_SWITCH = "customize_withdraw_remark_switch";

    /**
     * 审批事件（模板）名：
     *
     * @param event 银联云闪付分级费率套餐。
     */
    public static final String UNION_PAY_LADDER_COMBOS_HANDLE = "union_pay_ladder_combos_handle";

    /**
     * 审批事件名（模板）名 :商户交易推送配置处理
     */
    public static final String HANDLE_TRADE_NOTIFY_CONFIG = "handle_trade_notify_config";

    /**
     * 审批事件名（模板）名 :微信场景值上送处理
     */
    public static final String HANDLE_STORE_SCENE_SWITCH = "handle_store_scene_switch";

    /**
     * 审批事件（模板）名： 银行合作商户d0提现
     */
    public static final String BANK_COOPERATION_CUSTOMIZE_OFFSET_HOUR_MERCHANT_D0 = "bank_cooperation_customize_offset_hour_merchant_d0";
    /**
     * 审批事件（模板）名： D1超时赔付  审批通过 审批驳回
     */
    public static final String D1_TIMEOUT_COMPENSATION = "d1_timeout_compensation";
    /**
     * 阶梯费率 活动申请审批  阶梯费率活动通用申请
     */
    public static final String LADDER_FEE_RATE_ACTIVITY_APPLY = "ladder_fee_rate_activity_apply";
    /**
     * 固定 或 区间费率 活动申请审批  费率活动通用申请
     */
    public static final String FIX_OR_RANGE_FEE_RATE_ACTIVITY_APPLY = "fix_or_range_fee_rate_activity_apply";
    /**
     * 刷卡活动报名申请
     */
    public static final String EVENT_BANKCARD_CHANNEL_ACTIVITY_APPLY = AuditConstant.EVENT_BANKCARD_CHANNEL_ACTIVITY_APPLY;
    /**
     * 活动创建 审批  商户端市场活动审批
     */
    public static final List<String> FEE_RATE_ACTIVITY_CREATE = ImmutableList.of("fee_rate_activity_create", "fee_rate_activity_create_2");



    /**
     * 额度减免活动 审批 费率减免活动审批
     */
    public static final String QUOTA_ACTIVITY_CREATE = "quota_activity_create";
    /**
     * 额度减免商户申请 审批 费率减免活动通用申请
     */
    public static final String QUOTA_ACTIVITY_APPLY = "quota_activity_apply";
    /**
     * 活动返佣成功批量处理
     */
    public static final String ACTIVITY_REBATE_SUCCESS = "activity_rebate_success";
    /**
     * 更新支付源费率
     */
    public static final String MODIFY_PAY_SOURCE_FEE_RATE = "modify_pay_source_fee_rate";
    /**
     * 更新支付源费率 针对阶梯区间费率
     */
    public static final String MODIFY_PAY_SOURCE_LADDER_FEE_RATE = "modify_pay_source_ladder_fee_rate";
    /**
     * 活动套餐费率变更
     */
    public static final String MODIFY_ACTIVITY_FEE_RATE = "modify_activity_fee_rate";
    /**
     * 服务费调整
     */
    public static final String ADJUST_SERVICE_FEE = "adjust_service_fee";

    /**
     * 自动提现留存金额
     */

    public static final String MERCHANT_WITHDRAW_RETAIN_AMOUNT = "merchant_withdraw_retain_amount";

    /**
     * 审批事件名（模板）名 :民生银行商户优惠费率白名单推送配置处理
     */
    public static final String CMBC_MERCHANT_WHITE_LIST = "cmbc_merchant_white_list";
    /**
     * 开通收钱吧扫码预授权
     */
    public static final String OPEN_SQB_DEPOSIT = "open_sqb_deposit";

    /**
     * 审批事件名（模板）名 : 浦发银行数据回流分行号配置
     */
    public static final String PUFA_MERCHANT_BRANCH_NUMBER_ADJUST = "merchant_branch_number_adjust";

    /**
     * 海科分账开通流程
     */
    public static final String HAIKE_SHARING_AUTO_OPEN_AUDIT = "HaikeSharingAutoOpenAudit";

    public void handleEvent(AbstractEvent event) {
        AuditInstanceEvent auditInstanceEvent = (AuditInstanceEvent) event;
        logger.info("auditEvent: {}", JSON.toJSON(auditInstanceEvent));

        if (!skipTemplateEvent(auditInstanceEvent)) {
            try {
                //redis保存审批编号，防止重复消费
                String key = String.format("%s%s%s%d", KEY_PREFIX, auditInstanceEvent.getAuditId(), auditInstanceEvent.getEventType(), event.getSeq());
                RedissonClient redisson = SpringBeanUtil.getBean(RedissonClient.class);
                if (!redisson.getBucket(key).trySet(1, 4, TimeUnit.HOURS)) {
                    return;
                }
            } catch (Exception e) {
                logger.error("redis 访问异常", e);
            }
        }
        String templateName = auditInstanceEvent.getTemplateEvent();
        if (StringUtils.isBlank(templateName)) {
            return;
        }
        try {
            //活动报名前置处理
            activityApplyAuditEventBiz.doEventBefore(auditInstanceEvent);
            //审批处理
            doHandleAudit(auditInstanceEvent);
        } finally {
            //活动报名后置处理
            activityApplyAuditEventBiz.doEventAfter(auditInstanceEvent);
        }
    }

    private boolean skipTemplateEvent(AuditInstanceEvent auditInstanceEvent) {
        return ignoreTemplateEvents.contains("*")
                || ignoreTemplateEvents.contains(auditInstanceEvent.getTemplateEvent());
    }


    public void doHandleAudit(AuditInstanceEvent event) {
        switch (event.getEventType()) {
            //审批创建
            case AuditInstanceEvent.EVENT_TYPE_AUDIT_CREATE:
                doHandleCreate(event);
                return;
            //审批通过
            case AuditInstanceEvent.EVENT_TYPE_AUDIT_APPROVE:
                doHandleApprove(event);
                return;
            //审批拒绝
            case AuditInstanceEvent.EVENT_TYPE_AUDIT_REJECT:
                doHandleReject(event);
                return;
            //审批撤销
            case AuditInstanceEvent.EVENT_TYPE_AUDIT_CANCEL:
                doHandleCancel((AuditInstanceCancelEvent) event);
                return;
        }
    }

    /**
     * 处理审批创建
     *
     * @param event
     */
    public void doHandleCreate(AuditInstanceEvent event) {
        AuditInstanceCreateEvent createEvent = (AuditInstanceCreateEvent) event;
        String templateName = createEvent.getTemplateEvent();
        if (paySourceActivity(templateName)) {
            logger.info("templateName，支付源活动 审批创建");
            activityAuditHandleBiz.paySourceAuditCreate(createEvent);
        } else if (Objects.equals(HAIKE_SHARING_AUTO_OPEN_AUDIT, templateName)) {
            logger.info("海科分账开通审批创建");
            sharingAuditBiz.safeHandlerSharingAuditCreate(createEvent);
        }
    }

    /**
     * 处理审批通过
     *
     * @param event
     */
    public void doHandleApprove(AuditInstanceEvent event) {
        AuditInstanceApproveEvent approveEvent = (AuditInstanceApproveEvent) event;
        String templateName = approveEvent.getTemplateEvent();
        // 比如定义了事件名为"商户名称变更" ， 事件名尽量设置的特殊且唯一
        if (OPEN_PAY.equals(templateName)) {
            //沉睡商户恢复交易
            logger.info("case trade_open_merchant_pay, 沉睡商户恢复交易");
            auditBiz.openMerchantPay(approveEvent);
        } else if (SWITCH_APPLY.equals(templateName)) {
            //单收款通道关闭
            logger.info("case SWITCH_APPLY, 单收款通道关闭");
            auditBiz.closePayway(approveEvent);
        }
        // 费率套餐审批
        else if (FEE_RATE_COMBO_APPROVAL.equals(templateName)) {
            mchFeeRateBiz.handleMchFeeRateAudit(approveEvent);
        }
        // 优惠费率套餐审批
        else if (PREFERENTIAL_RATE_COMBO_APPROVAL.equals(templateName)) {
            mchFeeRateBiz.handleGivenMchFeeRateAudit(approveEvent, TradeActivityEntity.TYPE_PREFERENTIAL_RATE);
        }
        // 特优费率套餐审批
        else if (PREMIUM_RATE_COMBO_APPROVAL.equals(templateName)) {
            mchFeeRateBiz.handleGivenMchFeeRateAudit(approveEvent, TradeActivityEntity.TYPE_PREMIUM_RATE);
        }
        // 市场通用费率套餐审批
        else if (COMMON_RATE_COMBO_APPROVAL.equals(templateName)) {
            mchFeeRateBiz.handleGivenMchFeeRateAudit(approveEvent, TradeActivityEntity.TYPE_COMMON_RATE);
        }
        // 银行卡费率套餐审批
        else if (BANK_RATE_COMBO_APPROVAL.equals(templateName)) {
            mchBankFeeRateBiz.handleBankMchFeeRateAudit(approveEvent);
        }
        // 花呗分期费率活动
        else if (FBFQ_RATE_ACTIVITY.equals(templateName)) {
            logger.info("fbfq_rate_activity 花呗分期费率活动");
            auditBiz.hbfqRateActivity(approveEvent);
        } else if (CUSTOMIZE_OFFSET_HOUR_MERCHANT_D0.equals(templateName)) {
            //自定义对帐周期d0提现商户白名单
            logger.info("case customize_offset_hour_merchant_d0， 自定义对账周期d0商户");
            auditBiz.addSystemD0WhiteList(approveEvent);
        } else if (ALIPAY_RECEIPT_LIMIT_COLLECTION.equals(templateName)) {
            logger.info("alipay_university_activity, 支付宝收款限制集审批");
            auditBiz.zfbReceiptLimitCollection(approveEvent);
        } else if (OLD_MCH_MICRO_PUHUI_ACTIVITY_3W_6W.equals(templateName)) {
            //存量商户参与普惠政策防守A
            logger.info("old_mch_micro_puhui_activity_3w_6w, 存量商户参与普惠政策");
            auditBiz.microPuhuiActivity(approveEvent, 3000000L, 6000000L);
        } else if (OLD_MCH_MICRO_PUHUI_ACTIVITY_2W_6W.equals(templateName)) {
            //存量商户参与普惠政策防守B
            logger.info("old_mch_micro_puhui_activity_2w_6w, 存量商户参与普惠政策");
            auditBiz.microPuhuiActivity(approveEvent, 2000000L, 6000000L);
        } else if (CUSTOMIZE_WITHDRAW_REMARK_SWITCH.equals(templateName)) {
            //商户提现备注申请
            logger.info("customize_withdraw_remark_switch， 商户提现备注申请");
            auditBiz.withdrawRemark(approveEvent);
        } else if (UNION_PAY_LADDER_COMBOS_HANDLE.equals(templateName)) {
            logger.info("union_pay_ladder_combos_handle, 商户银联云闪付阶梯套餐处理");
            auditBiz.unionPayLadderComboHandle(approveEvent);
        } else if (BANK_COOPERATION_CUSTOMIZE_OFFSET_HOUR_MERCHANT_D0.equals(templateName)) {
            //银行合作商户d0提现
            logger.info("bank_cooperation_customize_offset_hour_merchant_d0， 银行合作商户d0提现");
            auditBiz.addBankCooperationD0WhiteList(approveEvent);
        } else if (HANDLE_TRADE_NOTIFY_CONFIG.equals(templateName)) {
            logger.info("handle_trade_notify_config， 商户交易推送配置处理");
            notifyTradeBiz.handle(approveEvent);
        } else if (HANDLE_STORE_SCENE_SWITCH.equals(templateName)) {
            logger.info("handle_store_scene_switch，微信场景值上送处理");
            storeSceneSwitchBiz.handle(approveEvent);
        } else if (D1_TIMEOUT_COMPENSATION.equals(templateName)) {
            logger.info("d1_timeout_compensation，D1超时赔付 审批通过");
            compensationService.compensationD1Amount(approveEvent.getBusinessMap());
        } else if (LADDER_FEE_RATE_ACTIVITY_APPLY.equals(templateName)) {
            logger.info("ladder_fee_rate_activity_apply，阶梯费率活动通用申请 审批通过");
            activityAuditHandleBiz.auditApprove(approveEvent);
        } else if (FIX_OR_RANGE_FEE_RATE_ACTIVITY_APPLY.equals(templateName)) {
            logger.info("fix_or_range_fee_rate_activity_apply，费率活动通用申请 审批通过");
            activityAuditHandleBiz.auditApprove(approveEvent);
        } else if (EVENT_BANKCARD_CHANNEL_ACTIVITY_APPLY.equals(templateName)) {
            logger.info("{}，审批通过", templateName);
            activityAuditHandleBiz.auditApprove(approveEvent);
        } else if (FEE_RATE_ACTIVITY_CREATE.contains(templateName)) {
            feeRateActivityCreateEvent(approveEvent);
        } else if (paySourceActivity(templateName)) {
            logger.info("templateName，支付源活动 审批通过");
            activityAuditHandleBiz.paySourceAuditApprove(approveEvent);
        } else if (QUOTA_ACTIVITY_CREATE.equals(templateName)) {
            logger.info("quota_activity_create, 费率减免活动审批 审批通过");
            quotaAuditHandleBiz.createAuditApprove(approveEvent);
        } else if (QUOTA_ACTIVITY_APPLY.equals(templateName)) {
            logger.info("quota_activity_apply, 费率减免活动通用申请 审批通过");
            quotaAuditHandleBiz.applyOrCancelAuditApprove(approveEvent);
        } else if (ACTIVITY_REBATE_SUCCESS.equals(templateName)) {
            logger.info("activity_rebate_success, 返佣成功申请 审批通过");
            activityAuditHandleBiz.activityRebateSuccessApprove(approveEvent);
        } else if (MODIFY_PAY_SOURCE_FEE_RATE.equals(templateName)) {
            logger.info("modify_pay_source_fee_rate, 更新支付源费率申请 审批通过");
            activityAuditHandleBiz.modifyPaySourceFeeRate(approveEvent);
        } else if (MODIFY_PAY_SOURCE_LADDER_FEE_RATE.equals(templateName)) {
            logger.info("{}, 更新支付源费率申请 审批通过", MODIFY_PAY_SOURCE_LADDER_FEE_RATE);
            activityAuditHandleBiz.modifyPaySourceLadderFeeRate(approveEvent);
        } else if (MODIFY_ACTIVITY_FEE_RATE.equals(templateName)) {
            logger.info("{}, 活动套餐切换 审批通过", MODIFY_ACTIVITY_FEE_RATE);
            activityAuditHandleBiz.modifyActivityComboFeeRate(approveEvent);
        } else if (MERCHANT_WITHDRAW_RETAIN_AMOUNT.equals(templateName)) {
            merchantWithdrawRetainedAmountBiz.handle(approveEvent);
        } else if (CMBC_MERCHANT_WHITE_LIST.equals(templateName)) {
            logger.info("{}, 民生银行优惠费率白名单审批通过", CMBC_MERCHANT_WHITE_LIST);
            merchantConfigBiz.handle(approveEvent);
        } else if (PUFA_MERCHANT_BRANCH_NUMBER_ADJUST.equals(templateName)) {
            logger.info("{}, 浦发银行商户分行号处理审批通过", PUFA_MERCHANT_BRANCH_NUMBER_ADJUST);
            merchantConfigBiz.handle(approveEvent);
        } else if (ADJUST_SERVICE_FEE.equals(templateName)) {
            logger.info("{}, 服务费规则调整 审批通过", templateName);
            auditBiz.adjustServiceFee(approveEvent);
        } else if (OPEN_SQB_DEPOSIT.equals(templateName)) {
            logger.info("{}, 审批通过", templateName);
            auditDepositBiz.onOff(approveEvent);
        } else if(HAIKE_SHARING_AUTO_OPEN_AUDIT.equals(templateName)){
            logger.info("{}, 海科分账开通审批通过", templateName);
            sharingAuditBiz.safeHandlerSharingAuditFinish(approveEvent);
        }
        //新版业务处理 后面可写在这里
        for (AbstractBusinessAuditApprovalProcessor approvalProcessor : auditApprovalProcessorList) {
            if (approvalProcessor.can(event)) {
                approvalProcessor.processApprove(approveEvent);
                break;
            }
        }
    }

    /**
     * 处理审批驳回
     *
     * @param event
     */
    public void doHandleReject(AuditInstanceEvent event) {
        AuditInstanceRejectEvent rejectEvent = (AuditInstanceRejectEvent) event;
        String templateName = rejectEvent.getTemplateEvent();
        if (D1_TIMEOUT_COMPENSATION.equals(templateName)) {
            logger.info("d1_timeout_compensation，D1超时赔付 审批驳回");
            compensationService.compensationD1Amount(rejectEvent.getBusinessMap());
        } else if (paySourceActivity(templateName)) {
            logger.info("templateName，支付源活动 审批驳回");
            activityAuditHandleBiz.paySourceAuditClose(rejectEvent);
        } else if (FEE_RATE_ACTIVITY_CREATE.contains(templateName)) {
            logger.info("{}，商户端市场活动审批 审批驳回", templateName);
            activityService.createActivityAuditReject(rejectEvent);
        } else if (QUOTA_ACTIVITY_CREATE.equals(templateName)) {
            logger.info("quota_activity_create, 费率减免活动审批 审批驳回");
            quotaAuditHandleBiz.createAuditReject(rejectEvent);
        } else if(HAIKE_SHARING_AUTO_OPEN_AUDIT.equals(templateName)){
            logger.info("{}, 海科分账开通审批驳回", templateName);
            sharingAuditBiz.safeHandlerSharingAuditFinish(rejectEvent);
        }
    }

    public void doHandleCancel(AuditInstanceCancelEvent event) {
        String templateName = event.getTemplateEvent();
        logger.info("审批撤销处理 templateName:{}", templateName);
        if (FEE_RATE_ACTIVITY_CREATE.contains(templateName)) {
            activityService.createActivityAuditCancel(event);
        } else if (paySourceActivity(templateName)) {
            activityAuditHandleBiz.paySourceAuditClose(event);
        } else if (QUOTA_ACTIVITY_CREATE.equals(templateName)) {
            logger.info("quota_activity_create, 费率减免活动审批 审批驳回");
            quotaAuditHandleBiz.createAuditReject(event);
        } else if(HAIKE_SHARING_AUTO_OPEN_AUDIT.equals(templateName)){
            logger.info("{}, 海科分账开通审批取消", templateName);
            sharingAuditBiz.safeHandlerSharingAuditFinish(event);
        }
    }


    public void handleEventFail(AbstractEvent event, Throwable e) {
        logger.error("handleEventFail", e);
        try {
            if (Objects.isNull(event)) {
                return;
            }
            String message = null;
            if (Objects.nonNull(e)) {
                message = (e instanceof TradeManageBizException)
                        ? ((TradeManageBizException) e).getMsg()
                        : e.getMessage();
            }
            if (Objects.isNull(message)) {
                message = "系统错误";
            }
            if (message.length() > 500) {
                message = uploadStatementToOSS(message, OSS_AUDIT_ERRORMSG + UUID.randomUUID().toString());
            }
            AuditInstanceEvent auditInstanceEvent = (AuditInstanceEvent) event;
            //驳回审批
            CallBackBean callBackBean = CallBackBean.builder()
                    .auditId(Long.valueOf(auditInstanceEvent.getAuditId()))
                    .templateId(Long.valueOf(auditInstanceEvent.getTemplateId()))
                    .resultType(ManageConstant.AUDIT_EXECUTE_FAIL)
                    .message(message)
                    .build();
            callBackService.addComment(callBackBean);
        } catch (Exception e1) {
            logger.error("reject audit fail", e1);
        }

    }


    public String uploadStatementToOSS(String Content, String ossFilepath) {
        byte[] content = Content.getBytes(StandardCharsets.UTF_8);
        ByteArrayInputStream bais = new ByteArrayInputStream(content);
        OssUtil.uploadStaticsFile(ossFilepath, bais, content.length, CannedAccessControlList.PublicRead);
        String decode = URLDecoder.decode(ossFilepath);
        return OSS_DOWNLOAD_PRE + OssUtil.getStaticsFileUrl(decode);
    }

    private boolean paySourceActivity(String templateName) {
        List<String> paySource = apolloConfig.getPaySource();
        if (CollectionUtils.isEmpty(paySource)) {
            return false;
        }
        return paySource.contains(templateName);
    }

    /**
     * 费率活动创建事件处理
     */
    private void feeRateActivityCreateEvent(AuditInstanceApproveEvent approveEvent) {
        Map businessMap = approveEvent.getBusinessMap();
        String operateType = MapUtils.getString(businessMap, "operate_type");
        Long activityId = MapUtils.getLong(businessMap, "id");
        logger.info("{}，商户端市场活动 审批通过, operateType:{}", approveEvent.getTemplateEvent(), operateType);
        if (StringUtils.contains(operateType, "活动创建")) {
            activityService.createActivityAuditApproved(approveEvent.getAuditId());
        } else if (StringUtils.contains(operateType, "活动更新")) {
            activityService.auditUpdateActivityApproved(approveEvent.getAuditId(), activityId);
        }
    }

}
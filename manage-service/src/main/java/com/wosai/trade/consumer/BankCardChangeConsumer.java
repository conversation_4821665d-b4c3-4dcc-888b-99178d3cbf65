package com.wosai.trade.consumer;

import com.wosai.middleware.hera.toolkit.metrics.Timed;
import com.wosai.profit.sharing.constant.CommonConstant;
import com.wosai.trade.biz.activity.ChangeBankCardBiz;
import com.wosai.trade.util.SpringBeanUtil;
import com.wosai.upay.merchant.contract.avro.UpayBankCardChange;
import io.confluent.kafka.serializers.KafkaAvroDeserializerConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Created by zhudongquan on 18/6/28.
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "flags.consumer.run", matchIfMissing = true, havingValue = "true")
public class BankCardChangeConsumer implements InitializingBean {

    ExecutorService worker = Executors.newSingleThreadExecutor();
    ExecutorService handler = Executors.newFixedThreadPool(30);
    private static final String topic = "events.upay.upay-bank-card-change";


    @Autowired
    private KafkaProperties kafkaProperties;
    @Value("${spring.kafka.properties.schema.registry.url}")
    private String schemaRegistryUrl;
    @Autowired
    private ChangeBankCardBiz changeBankCardBiz;

    private KafkaConsumer<String, UpayBankCardChange> kafkaConsumer;
    private volatile int status = CommonConstant.RESOURCE_STATUS_RUNNING;

    @Override
    public void afterPropertiesSet() {
        startConsume();
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            try {
                destroy();
            } catch (Exception e) {
                throw new RuntimeException();
            }
        }));

    }

    public void destroy() throws Exception {
        status = CommonConstant.RESOURCE_STATUS_STOPPING;
        while (status != CommonConstant.RESOURCE_STATUS_STOPPED) {
            //wait kafka consumer close
            Thread.sleep(1000);
        }
    }

    public void startConsume() {
        Map<String, Object> props = kafkaProperties.buildConsumerProperties();
        props.put(KafkaAvroDeserializerConfig.SPECIFIC_AVRO_READER_CONFIG, "true");
        props.put(ConsumerConfig.ISOLATION_LEVEL_CONFIG, "read_committed");
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, "io.confluent.kafka.serializers.KafkaAvroDeserializer");
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, "io.confluent.kafka.serializers.KafkaAvroDeserializer");
        props.put(KafkaAvroDeserializerConfig.SCHEMA_REGISTRY_URL_CONFIG, schemaRegistryUrl);
        kafkaConsumer = new KafkaConsumer<>(props);
        kafkaConsumer.subscribe(Arrays.asList(topic));
        worker.submit(() -> {
            while (true) {
                try {
                    if (status == CommonConstant.RESOURCE_STATUS_STOPPING) {
                        kafkaConsumer.close();
                        status = CommonConstant.RESOURCE_STATUS_STOPPED;
                        //退出
                        log.info("kafka consumer stopped");
                        break;
                    }
                    consume();
                } catch (Exception e) {
                    log.error("kafka consumer error: {}", e.getMessage(), e);
                    try {
                        Thread.sleep(5000);
                    } catch (InterruptedException e1) {
                    }
                }
            }
        });
    }

    @Timed
    public void consume() throws InterruptedException {
        //这里控制每次拿到多少批量数据的参数配置是: max.poll.records
        final ConsumerRecords<String, UpayBankCardChange> consumerRecords = kafkaConsumer.poll(1000);
        int size = consumerRecords.count();
        if (size == 0) {
            return;
        }
        CountDownLatch latch = new CountDownLatch(size);
        //具体操作的地方
        for (ConsumerRecord<String, UpayBankCardChange> record : consumerRecords) {
            handler.submit(TimedRunnable.of("kafka-consumer", () -> {
                        try {
                            UpayBankCardChange cardChange = record.value();

                            handleBiz(cardChange);

                        } catch (Throwable t) {
                            log.error("consume record error: {}", t.getMessage(), t);
                        } finally {
                            latch.countDown();
                        }
                    })
            );
        }
        latch.await();
    }


    public void handleBiz(UpayBankCardChange change) {
        try {
            if (change == null
                    || change.getMerchantSn() == null) {
                return;
            }
            if (!SpringBeanUtil.isProd()) {
                log.info("切换银行卡消息. change={}", change);
            }
            changeBankCardBiz.changeCard(change.getMerchantSn().toString(), change.getMerchantId().toString());

        } catch (Exception e) {
            log.error("切换银行卡处理系统异常 商户sn {}", change.getMerchantSn().toString(), e);
            throw e;
        }
    }

}

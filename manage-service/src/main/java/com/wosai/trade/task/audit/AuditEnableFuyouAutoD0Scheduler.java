package com.wosai.trade.task.audit;

import com.wosai.databus.event.audit.AuditInstanceApproveEvent;
import com.wosai.trade.biz.audit.CustomizeWithdrawCycleD0WhiteBiz;
import com.wosai.trade.client.FuyouOperateClient;
import com.wosai.trade.client.WithdrawClient;
import com.wosai.trade.config.apollo.AuditAutoD0ApolloConfig;
import com.wosai.trade.constant.ExpireTimeConstant;
import com.wosai.trade.constant.RedisKeyConstant;
import com.wosai.trade.constant.enums.ApplyAuditProcStatusEnum;
import com.wosai.trade.constant.enums.ApplyAuditSubProcStatusEnum;
import com.wosai.trade.repository.ApplyAuditRecordRepository;
import com.wosai.trade.repository.dao.entity.ApplyAuditRecordEntity;
import com.wosai.trade.task.DefaultJobSupport;
import com.wosai.trade.util.JsonUtil;
import com.wosai.trade.util.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.wosai.trade.constant.AuditConstant.COMMENT_RESULT_TYPE_FAILED;
import static com.wosai.trade.consumer.AuditInstanceEventConsumer.CUSTOMIZE_OFFSET_HOUR_MERCHANT_D0;
import static com.wosai.trade.util.LocalDateTimeUtil.getFormatDateTime;

/**
 * <AUTHOR>
 * @description 开通富友自动D0-调度任务
 * @date 2024/11/27
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "flags.jobs.enabled", matchIfMissing = true, havingValue = "true")
public class AuditEnableFuyouAutoD0Scheduler extends DefaultJobSupport {
    @Resource
    private ApplyAuditRecordRepository applyAuditRecordRepository;
    @Resource
    private WithdrawClient withdrawClient;
    @Resource
    private FuyouOperateClient fuyouOperateClient;
    @Resource
    private CustomizeWithdrawCycleD0WhiteBiz customizeWithdrawCycleD0WhiteBiz;
    @Resource
    private AuditAutoD0ApolloConfig auditAutoD0ApolloConfig;

    /**
     * 每天1点到3点，半小时检测一次，给昨日21:00前的待生效的自动D0审批，进行开通自动D0
     */
    @Scheduled(cron = "0 0/30 1-3 * * ?")
    public void fuyouD0DetectJobHandler() {
        //执行检测
        doJob();
    }

    /**
     * 开通富友自动D0
     */
    public void enableFuyouAutoD0() {
        //加载昨天到当前时间的待执行或执行中的审批记录
        List<ApplyAuditProcStatusEnum> statusList = Arrays.asList(ApplyAuditProcStatusEnum.PENDING, ApplyAuditProcStatusEnum.IN_PROGRESS);
        LocalDateTime startDate = LocalDateTimeUtil.getStartOfDay().minusDays(2);
        List<ApplyAuditRecordEntity> result = applyAuditRecordRepository.queryByTemplateEventAndProcStatusList(CUSTOMIZE_OFFSET_HOUR_MERCHANT_D0,
                statusList, startDate);
        //过滤出D0开通成功的审批记录
        List<ApplyAuditRecordEntity> pendingRecordList = result.stream()
                .filter(record -> record.getSubProcStatus() == ApplyAuditSubProcStatusEnum.OPEN_D0_SUCCESS.getStatus())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pendingRecordList)) {
            log.info("{}: 没有需要开通富友自动D0的审批", getLogPrefix());
            return;
        }

        log.info("{}: 待开通自动D0审批数量={}", getLogPrefix(), pendingRecordList.size());

        for (ApplyAuditRecordEntity record : pendingRecordList) {
            String merchantSn = record.getMerchantSn();
            AuditInstanceApproveEvent approveEvent = JsonUtil.decode(record.getExtra(), AuditInstanceApproveEvent.class);
            if (null == approveEvent) {
                log.info("extra信息不能为null, , merchantSn={}, auditSn={}", merchantSn, record.getAuditSn());
                continue;
            }

            /**
             * 如果不是昨日limitHour前已经变更过二级机构号的记录，则不处理
             * 原因：
             *   变更商户的二级机构号是为了调大商户的D0提现额度。富友通道调额度的时效是：当日21:30之前进大额机构号的，当天22点调完，过了21:30，可能要到次日22点调；
             *   因此开通自动D0的时候，只把昨天21点前就已经同步过二级机构号的审批，进行开通自动D0, 21点之后同步二级机构号的，留到D+2日处理
             */
            int limitHour = auditAutoD0ApolloConfig.getFuyouOpenAutoD0StartTimeLimitHour();
            LocalDateTime beginTime = LocalDateTimeUtil.getStartOfYesterday().plusHours(limitHour);
            LocalDateTime recordCreateTime = LocalDateTimeUtil.valueOf(record.getCtime());
            if (recordCreateTime.isAfter(beginTime)) {
                log.info("只会处理昨日{}点前成功变更二级机构号的自动D0审批记录, 该记录创建时间={}, 本次跳过处理, merchantSn={}, auditSn={}",
                        limitHour, getFormatDateTime(recordCreateTime), merchantSn, record.getAuditSn());
                continue;
            }

            //再次检测富友商户的二级机构号是否已变更成功
            if (!fuyouOperateClient.isSubInsCdAlreadySync(merchantSn)) {
                log.info("该商户的二级机构号未变更成功, 无法开通自动D0, merchantSn={}", merchantSn);
                updateApplyAuditRecordProcStatus(record, ApplyAuditProcStatusEnum.FAILURE, "该商户的二级机构号未变更成功, 无法开通自动D0");
                customizeWithdrawCycleD0WhiteBiz.addComment(approveEvent, COMMENT_RESULT_TYPE_FAILED, "自定义结算周期快速到账开通失败: 该商户的二级机构号未变更成功");
                continue;
            }

            //开通自动D0
            try {
                customizeWithdrawCycleD0WhiteBiz.openAutoD0(record.getMerchantId(), approveEvent);
                //更新审批记录状态为成功
                updateApplyAuditRecordProcStatus(record, ApplyAuditProcStatusEnum.SUCCESS, "");
                log.info("开通富友自动D0成功, merchantSn={}", merchantSn);
            } catch (Exception e) {
                log.error("开通富友自动D0异常, merchantSn={}, error={}", merchantSn, e.getMessage(), e);
                //更新审批记录状态为失败
                updateApplyAuditRecordProcStatus(record, ApplyAuditProcStatusEnum.FAILURE, e.getMessage());
                customizeWithdrawCycleD0WhiteBiz.addComment(approveEvent, COMMENT_RESULT_TYPE_FAILED, "自定义结算周期快速到账开通失败: " + e.getMessage());
            }
        }

        log.info("{}: 处理完成", getLogPrefix());
    }

    private void updateApplyAuditRecordProcStatus(ApplyAuditRecordEntity record, ApplyAuditProcStatusEnum procStatus, String errorMsg) {
        try {
            ApplyAuditRecordEntity updateData = new ApplyAuditRecordEntity();
            updateData.setId(record.getId());
            updateData.setProcStatus(procStatus.getStatus());
            updateData.setErrorMsg(errorMsg);
            applyAuditRecordRepository.updateByPrimaryKeySelective(updateData);
        } catch (Exception e) {
            log.error("更新审批处理状态失败, merchantSn={}, record={}, procStatus={}", record.getMerchantId(), JsonUtil.encode(record), procStatus);
        }
    }

    @Override
    protected void process() {
        enableFuyouAutoD0();
    }

    @Override
    protected String getLockKey() {
        return RedisKeyConstant.AUDIT_OPEN_FUYOU_AUTO_D0_JOB_LOCK;
    }

    @Override
    protected long getLockExpiredSeconds() {
        return ExpireTimeConstant.HALF_HOUR_IN_SECONDS;
    }

    @Override
    protected String errorMsgIfGetLockFailed() {
        return "已有[开通富友自动D0-调度任务]在执行中，忽略本次执行";
    }

    @Override
    protected int getRetryTimes() {
        //失败重试次数, 2表示失败后再重试2次
        return 1;
    }

    @Override
    protected String getLogPrefix() {
        return "开通富友自动D0-调度任务";
    }
}

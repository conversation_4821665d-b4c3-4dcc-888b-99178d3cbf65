<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.wosai.pantheon</groupId>
        <artifactId>uranus</artifactId>
        <version>1.1.8</version>
    </parent>

    <groupId>com.wosai.trade</groupId>
    <artifactId>manage</artifactId>
    <version>1.13.15</version>
    <packaging>pom</packaging>

    <modules>
        <module>manage-api</module>
        <module>manage-service</module>
    </modules>

    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <core-business.version>3.9.42</core-business.version>
        <pay-common.version>1.2.10</pay-common.version>
        <sonar.exclusions>
            <!--排除目录-->
            **/model/**,**/constant/**,**/config/**,**/entity/**,**/enums/**,**/exception/**
            ,**/repository/impl/**,**/client/*

            <!--排除单个文件-->
            ,**/util/*TraceLogFilter.java,**/trade/*Application.java,**/trade/*InitialBean.java
            ,**/impl/*TransactionQuotaServiceImpl.java,
        </sonar.exclusions>
    </properties>


    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.wosai.middleware</groupId>
                <artifactId>jsonrpc4j</artifactId>
                <version>2.2.4-alpha</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.12</version>
            </dependency>

            <dependency>
                <groupId>com.wosai.sales</groupId>
                <artifactId>third-api-spi</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.shouqianba</groupId>
                <artifactId>merchant-contract-access-api</artifactId>
                <version>1.7.35</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.wosai.middleware</groupId>
                        <artifactId>zeus-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.wosai.middleware</groupId>
                        <artifactId>jsonrpc4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.github.briandilley.jsonrpc4j</groupId>
                        <artifactId>jsonrpc4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>video-service-api</artifactId>
                        <groupId>com.wosai.app</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.ccb</groupId>
                        <artifactId>mis</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.wosai</groupId>
                        <artifactId>mpay-sdk-homebrew</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>kotlin-stdlib</artifactId>
                        <groupId>org.jetbrains.kotlin</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>kotlin-stdlib-common</artifactId>
                        <groupId>org.jetbrains.kotlin</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>kotlin-stdlib-jdk8</artifactId>
                        <groupId>org.jetbrains.kotlin</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-boot-starter</artifactId>
                        <groupId>org.springframework.boot</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>hibernate-validator</artifactId>
                        <groupId>org.hibernate.validator</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>asm</artifactId>
                        <groupId>org.ow2.asm</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>tomcat-embed-el</artifactId>
                        <groupId>org.apache.tomcat.embed</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-databind</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-annotations</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.datatype</groupId>
                        <artifactId>jackson-datatype-jsr310</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.wosai.cua</groupId>
                <artifactId>kafka-msg-entity</artifactId>
                <version>1.0.0</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.5</version>
                <executions>
                    <execution>
                        <id>jacoco-initialize</id>
                        <goals>
                            <goal>prepare-agent</goal>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>central</id>
            <name>maven-virtual-dev</name>
            <url>https://jfrog.wosai-inc.com/artifactory/maven-virtual-dev</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>maven-virtual-dev</name>
            <url>https://jfrog.wosai-inc.com/artifactory/maven-virtual-dev</url>
        </snapshotRepository>
    </distributionManagement>

</project>

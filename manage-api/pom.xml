<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>manage</artifactId>
        <groupId>com.wosai.trade</groupId>
        <version>1.13.15</version>
    </parent>
    <artifactId>manage-api</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.wosai.pay</groupId>
            <artifactId>pay-common-base</artifactId>
            <version>${pay-common.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>
        <!--JsonRpc4j-->
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>jsonrpc4j</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-common-databus-api</artifactId>
            <version>1.0.5</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.wosai.sales</groupId>
            <artifactId>sales-system-gateway-api</artifactId>
            <version>1.1.0-SNAPSHOT</version>
            <scope>provided</scope>
        </dependency>
        <!--context-->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <!--validator-->
        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-common-validation</artifactId>
        </dependency>
        <!--lombok-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>upay-common</artifactId>
            <version>1.0.2-SNAPSHOT</version>
            <scope>provided</scope>
        </dependency>


        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>sales-system-databus</artifactId>
            <version>1.0.4-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>wosai-common-databus</artifactId>
                    <groupId>com.wosai.pantheon</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wosai.sales</groupId>
            <artifactId>third-api-spi</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>core-business-api</artifactId>
            <version>${core-business.version}</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>scene-api</artifactId>
            <version>1.2.7</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.pay</groupId>
            <artifactId>pay-common-state-manager-api</artifactId>
            <version>1.2.10</version>
        </dependency>
    </dependencies>

</project>

package com.wosai.databus.event.pay;

import com.wosai.trade.service.enums.ChangeActivityFeeRateEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 活动取消事件类
 *
 * <AUTHOR>
 *
 * @date 2025/8/21
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class TradeManageChangeApplyActivityEvent extends TradeManageEvent {

    private Long activityId;

    private Long applyId;

    private Integer activityType;
    /**
     * 是否报名　true-报名 false-取消
     */
    private Boolean isApply;
    /**
     * 备注
     */
    private String remark;

    /**
     * 是否活动内切换套餐
     */
    private ChangeActivityFeeRateEnum changeActivityFeeRateEnum;

    public static String objectType(){
        return OBJECT_TYPE_SOURCE_ACTIVITY;
    }

    public static String eventType(){
        return EVENT_TYPE_CHANGE;
    }

    static {
        registerType(module(), objectType(), eventType(), TradeManageChangeApplyActivityEvent.class);
    }

    public TradeManageChangeApplyActivityEvent() {
        setObjectType(objectType());
        setEventType(eventType());
    }
}

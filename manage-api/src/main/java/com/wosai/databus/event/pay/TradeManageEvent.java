package com.wosai.databus.event.pay;


import com.wosai.databus.event.AbstractEvent;

import java.util.UUID;

/**
 * 交易管理事件
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/12/1.
 */
public abstract class TradeManageEvent extends AbstractEvent {

    public static final String OBJECT_TYPE_PUI_HUI = "pu_hui";
    public static final String OBJECT_TYPE_TAG = "tag";
    public static final String OBJECT_TYPE_QUOTA_ACTIVITY = "quota_activity";
    /**
     * 支付源活动
     */
    public static final String OBJECT_TYPE_SOURCE_ACTIVITY = "source_activity";
    public static final String EVENT_TYPE_PARTICIPATE = "participate";
    public static final String EVENT_TYPE_TAG_CHANGE = "tag_change";
    public static final String EVENT_TYPE_QUOTA_APPLY_CHANGE = "quota_activity_apply_change"; //额度包活动报名记录变化事件
    /**
     * 变动事件
     */
    public static final String EVENT_TYPE_CHANGE = "change";

    public static String module() {
        return MODULE_PAY;
    }

    public TradeManageEvent() {
        setModule(module());
        setSn(UUID.randomUUID().toString());
        setTimestamp(System.currentTimeMillis());
    }

    private long seq;

    private long timestamp;

    private String merchantSn;

    private String merchantId;

    /**
     * 业务编号
     */
    private String sn;

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    @Override
    public long getSeq() {
        return seq;
    }

    @Override
    public void setSeq(long seq) {
        this.seq = seq;
    }

    @Override
    public long getTimestamp() {
        return timestamp;
    }

    @Override
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public String getMerchantSn() {
        return merchantSn;
    }

    public void setMerchantSn(String merchantSn) {
        this.merchantSn = merchantSn;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }
}

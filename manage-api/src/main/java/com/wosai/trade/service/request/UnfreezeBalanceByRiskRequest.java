package com.wosai.trade.service.request;

import com.wosai.pay.common.base.log.OperationLogRequest;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 风控解冻余额请求
 * @date 2025-08-07
 */
@Setter
@Getter
public class UnfreezeBalanceByRiskRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 商户ID
     */
    @NotBlank(message = "商户ID不能为空")
    private String merchantId;

    /**
     * 收单机构
     * 枚举定义见：Withdraw.PROVIDER_HAIKE
     */
    @NotNull(message = "收单机构不能为空")
    private Integer clearanceProvider;

    /**
     * 余额账户类型  1：普通账户 2：外卡账户 3：分账账户
     * 枚举定义见余额服务: ProviderWalletAccountTypeEnum
     *
     */
    @NotNull(message = "余额账户类型不能为空")
    private Integer walletAccountType;

    /**
     * 冻结类型
     * 见：FrozenOrder.TYPE_RISK
     */
    @NotNull(message = "冻结类型不能为null")
    private Integer frozenType;

    /**
     * 冻结时的actionId
     */
    @NotBlank(message = "freezeAccountId不能为空")
    private String freezeAccountId;

    /**
     * 本次要解冻的actionId
     */
    @NotBlank(message = "unfreezeAccountId不能为空")
    private String unfreezeAccountId;

    /**
     * 冻结金额（单位：分）
     */
    @NotNull(message = "解冻金额不能为空")
    @Positive(message = "解冻金额必须大于0")
    private Long unfreezeAmount;

    /**
     * 是否解冻剩余金额
     */
    @NotNull(message = "是否解冻剩余金额不能为空")
    private Boolean unfreezeRemaining;

    /**
     * 操作日志请求参数
     */
    @NotNull(message = "logRequest不能为空")
    private OperationLogRequest logRequest;
}
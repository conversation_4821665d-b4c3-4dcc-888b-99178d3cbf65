package com.wosai.trade.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.pay.common.base.log.OperationLogRequest;
import com.wosai.trade.service.request.FreezeBalanceByRiskRequest;
import com.wosai.trade.service.request.UnfreezeBalanceByRiskRequest;
import com.wosai.trade.service.result.FreezeBalanceByRiskResponse;
import com.wosai.trade.service.result.UnfreezeBalanceByRiskResponse;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @description 风控管理服务
 * @date 2025-08-07
 */
@JsonRpcService(value = "rpc/risk/manage")
public interface RiskManageService {
    /**
     * 风控冻结商户余额
     * 
     * @param request 冻结请求参数
     * @return 冻结结果
     */
    FreezeBalanceByRiskResponse freezeBalanceByRisk(@Valid FreezeBalanceByRiskRequest request);

    /**
     * 风控解冻商户余额
     * 
     * @param request 解冻请求参数
     * @return 解冻结果
     */
    UnfreezeBalanceByRiskResponse unfreezeBalanceByRisk(@Valid UnfreezeBalanceByRiskRequest request);

    /**
     * 延迟商户结算
     * @param merchantId 商户id
     */
    void delayAllMerchantD1Withdraw(String merchantId, OperationLogRequest logRequest);

    /**
     * 取消延迟结算
     *
     * @param merchantId 商户id
     */
    void cancelDelayAllMerchantD1Withdraw(String merchantId, OperationLogRequest logRequest);
}
package com.wosai.trade.service.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 通联收银宝Apple Pay配置请求参数
 * 
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
public class TonglianApplePayConfigRequest {

    /**
     * 商户ID
     */
    @NotBlank(message = "商户ID不能为空")
    @JsonProperty("merchant_id")
    private String merchantId;

    /**
     * 收银宝商户号
     */
    @NotBlank(message = "收银宝商户号不能为空")
    @JsonProperty("provider_mch_id")
    private String providerMchId;

    /**
     * 套餐ID
     */
    @NotNull(message = "套餐ID不能为空")
    @JsonProperty("combo_id")
    private Long comboId;


    /**
     * agentName
     */
    @NotBlank(message = "agentName不能为空")
    @JsonProperty("agent_name")
    private String agentName;

    /**
     * operator
     */
    @NotBlank(message = "operator不能为空")
    @JsonProperty("operator")
    private String operator;
}

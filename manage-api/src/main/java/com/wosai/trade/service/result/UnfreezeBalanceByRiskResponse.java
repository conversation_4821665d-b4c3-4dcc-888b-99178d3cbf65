package com.wosai.trade.service.result;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2025-08-07
 */
@Setter
@Getter
public class UnfreezeBalanceByRiskResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 实际解冻金额（单位：分）
     */
    private long actualUnfreezeAmount;

}
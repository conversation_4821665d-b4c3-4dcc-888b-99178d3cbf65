package com.wosai.trade.service.activity.request;

import com.wosai.databus.event.audit.AuditInstanceCreateEvent;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 商户活动申请
 * <AUTHOR>
 * @Date: 2022/5/23 10:50 下午
 */
@Getter
@Setter
public class ApplyActivityRequest {
    /**
     * 活动ID
     */
    @NotNull(message = "申请活动的ID不能为空")
    private Long activityId;
    /**
     * 商户SN
     */
    private String merchantSn;
    /**
     * 商户ID
     */
    private String merchantId;
    /**
     * 挂靠主体商户信息
     */
    private MasterInfo masterInfo;
    /**
     * 审批ID
     */
    private Long auditId;

    private String auditSn;


    /**
     * 申请支付费率
     */
    private List<ApplyPayFeeRate> applyPayFeeRates;

    /**
     * 子商户号
     */
    private String subMerchantSn;
    /**
     * 优惠额度 单位元
     */
    private Double discountQuota;
    /**
     * 优惠费率
     */
    private String discountQuotaFeeRate;
    /**
     * 活动最低费率
     */
    private String lowestFeeRate;
    /**
     * 优惠额度记录ID 如多商户共享额度 先创建优惠额度记录  申请活动时 此字段 为记录的ID
     */
    private Long discountQuotaRecordId;

    /**
     * 审批信息
     */
    private AuditInstanceCreateEvent auditInstanceEvent;

    /**
     * 门店SN/终端SN/商户SN 层级
     */
    private String sn;

    /**
     * 操作人
     */
    private String operator;
    /**
     * 申请原因
     */
    private String remark;

    /**
     * 源报名申请记录ID
     * 主要用于报名恢复使用
     */
    private Long sourceApplyId;

    /**
     * 子状态ID
     */
    private Long subStatusDetailId;

    /**
     * 套餐ID
     */
    private Long comboId;

    /**
     * 审批模版id
     */
    private String auditTemplateId;
    /**
     * 操作平台
     */
    private Integer platform;


    public Long fetchParentApplyId() {
        return Objects.isNull(masterInfo) ? null : masterInfo.getApplyId();
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Data
    public static class ApplyPayFeeRate {
        /**
         * 支付方式
         */
        @NotNull(message = "支付方式不能为空")
        private Integer payWay;
        /**
         * 费率
         */
        private String feeRate;
    }

    /**
     * 支付源活动挂靠主体商户信息
     */
    @Data
    public static class MasterInfo {
        /**
         * 主体－商户sn 支付活动挂靠时会使用
         */
        private String merchantSn;
        /**
         * 主体-报名申请记录id 支付活动挂靠时会使用
         */
        private Long applyId;

        public MasterInfo() {
        }

        public MasterInfo(String merchantSn, Long applyId) {
            this.merchantSn = merchantSn;
            this.applyId = applyId;
        }
    }
}

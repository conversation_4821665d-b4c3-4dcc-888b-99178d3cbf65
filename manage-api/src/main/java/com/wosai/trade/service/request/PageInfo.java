package com.wosai.trade.service.request;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PageInfo {

    /**
     * 每页显示数量
     */
    @JsonProperty(value = "page_size")
    private Integer pageSize;
    
    /**
     * 分页数，正整数
     */
    @JsonProperty(value = "page")
    private Integer page;

    /**
     * 开始时间
     */
    @JsonProperty(value = "date_start")
    private Long dateStart;

    /**
     * 结束时间
     */
    @JsonProperty(value = "date_end")
    private Long dateEnd;
    
    /**
     * 排序方式
     */
    @JsonProperty(value = "order_by")
    private List<OrderBy> orderBy;

    @Data
    @Builder
    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public static class OrderBy {
        private String field;
        private OrderType order;

        @Getter
        public enum OrderType {
            ASC(1), DESC(-1);
            private final int value;

            OrderType(int order) {
                value = order;
            }

            public static OrderType fromNumber(int number) {
                for (OrderType b : OrderType.values()) {
                    if (b.value == number) {
                        return b;
                    }
                }
                return null;
            }

            @JsonCreator
            public static OrderType fromNumber(String orderType) {
                if (NumberUtils.isDigits(orderType)) {
                    return fromNumber(NumberUtils.toInt(orderType));
                }
                return OrderType.valueOf(orderType);
            }
        }
    }
    
    public Integer getPageStart() {
        return null != page && null != pageSize ? (page -1)*pageSize : 0;
    }

    public static PageInfo ofOne() {
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPage(NumberUtils.INTEGER_ONE);
        pageInfo.setPageSize(NumberUtils.INTEGER_ONE);
        return pageInfo;
    }

    public static PageInfo of(Integer page, Integer pageSize) {
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPage(page);
        pageInfo.setPageSize(pageSize);
        return pageInfo;
    }
}

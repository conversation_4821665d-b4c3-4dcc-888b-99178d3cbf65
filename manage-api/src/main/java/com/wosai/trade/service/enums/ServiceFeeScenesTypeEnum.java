package com.wosai.trade.service.enums;

/**
 * Description: 场景
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023/5/6
 */
public enum ServiceFeeScenesTypeEnum implements ItemCode {

    CAMPUS("校园"),
    CAMPUS_PACKAGE("校园-打包"),
    CAMPUS_DELIVERY_ASSISTANT("校园-配送管理助手"),
    SPECIFIED_FUNCTION("指定功能"),
    MALL_TYPE("商城类型(小程序、H5)"),
    PAYWAY("支付payway"),
    WECHAT_STORE_INDUSTRY("微信小店行业"),
    ;


    private final String desc;

    ServiceFeeScenesTypeEnum(String desc) {
        this.desc = desc;
    }

    @Override
    public String getByCode() {
        return this.name();
    }

    @Override
    public String getByName() {
        return this.desc;
    }
}

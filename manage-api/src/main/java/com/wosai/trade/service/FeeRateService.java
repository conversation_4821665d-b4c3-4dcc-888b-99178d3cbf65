package com.wosai.trade.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.trade.service.request.*;
import com.wosai.trade.service.result.*;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 商户费率 service
 *
 * <AUTHOR>
 */
@JsonRpcService(value = "rpc/mchFeeRate")
@Validated
public interface FeeRateService {

    /**
     * 批量生效费率
     */
    List<BulkApplyFeeRateResult> bulkApplyFeeRate(@Valid @NotEmpty(message = "请求参数不能为空") List<BulkApplyFeeRateRequest> params);

    /**
     * 单个商户费率生效
     * yapi: https://yapi.wosai-inc.com/project/1539/interface/api/73282
     */
    void applyFeeRateOne(@Valid @NotNull(message = "请求参数不能为空") ApplyFeeRateRequest applyFeeRateRequest);

    /**
     * 预先校验商户是否可以适用本费率套餐；
     * @param merchantSn
     * @param tradeComboEntityAndList
     */
    @Deprecated
    void preApplyFeeRate(@Valid @NotNull(message="商户sn不能为空") String merchantSn, Map<String,Object> tradeComboEntityAndList,Long applyTimeMillis);

    /**
     * 预先校验商户是否可以适用本费率套餐；
     * @param merchantSn
     * @param tradeComboEntityAndList
     * @param applyFeeRateMap paywayString: fee_rate 指定通道，指定费率
     */
    @Deprecated
    void preApplyFeeRate(@Valid @NotNull(message="商户sn不能为空") String merchantSn, Map<String,Object> tradeComboEntityAndList, Long applyTimeMillis, Map<String, String> applyFeeRateMap);

    /**
     * 预先校验商户是否可以适用本费率套餐；
     * @param merchantSn
     * @param tradeComboEntityAndList
     * @param applyFeeRateMap paywayString: fee_rate 指定通道，指定费率
     * @param auditTemplateId 审批id， 非审批申请id
     */
    void preApplyFeeRate(@Valid @NotNull(message="商户sn不能为空") String merchantSn, Map<String,Object> tradeComboEntityAndList, Long applyTimeMillis, Map<String, String> applyFeeRateMap, String auditTemplateId);

    /**
     * 取消商户费率
     */
    void cancelFeeRate(@Valid @NotNull(message = "请求参数不能为空") CancelFeeRateRequest request);

    /**
     * 结束商户级别费率
     */
    void endMerchantFeeRate(@NotNull(message = "费率id不能为空") Long feeRateId);

    /**
     * 结束门店级别费率
     */
    void endStoreFeeRate(@NotNull(message = "费率id不能为空") Long feeRateId);

    /**
     * 结束终端级别费率
     */
    void endTerminalFeeRate(@NotNull(message = "费率id不能为空") Long feeRateId);

    /**
     * 开启套餐活动，失败抛异常
     */
    void applyFeeRate(@NotEmpty(message = "商户sn不能为空") String mchSn, Long tradeComboId);

    /**
     * 获取商户费率列表
     *
     * @param mchSn 商户sn
     * @return result
     */
    List<ListMchFeeRateResult> listMchFeeRates(@NotEmpty(message = "商户sn不能为空") String mchSn);

    /**
     * 获取商户所有生效某套餐费率列表
     *
     * @param mchSn 商户sn
     * @return result
     */
    List<ListMchFeeRateResult> listMchEffectFeeRates(@NotEmpty(message = "商户sn不能为空") String mchSn);


    /**
     * 获取门店生效套餐费率列表
     *
     * @param sn 商户sn
     * @return result
     */
    List<ListMchFeeRateResult> listStoreEffectFeeRates(@NotEmpty(message = "商户sn不能为空") String mchSn, @NotEmpty(message = "门店sn不能为空") String sn);


    /**
     * 获取终端生效套餐费率列表
     *
     * @param sn 商户sn
     * @return result
     */
    List<ListMchFeeRateResult> listTerminalEffectFeeRates(@NotEmpty(message = "商户sn不能为空") String mchSn, @NotEmpty(message = "终端sn不能为空") String sn);

    /**
     * 查询商户费率审批记录
     *
     * @param mchSn 商户sn
     * @return result
     */
    List<QueryMchApplyLogsResult> queryMchApplyLogs(@NotEmpty(message = "商户sn不能为空") String mchSn);


    /**
     * 获取商户移动支付手续费
     * @param request
     * @link https://yapi.wosai-inc.com/project/1539/interface/api/101650
     */
    List<ListMchFeeStatusResult> getMerchantBasicAgreementFeeRates(ActivityBaseRequest request);


    /**
     * 关闭/删除商户费率
     *
     * @param mchFeeRateId 商户费率id
     * @return true
     */
    boolean deleteMchFeeRateById(@NotNull(message = "商户费率id不能为空") Long mchFeeRateId);

    /**
     * 生效微信教培整改0.78费率套餐
     * @param mchSn
     */
    void applyTradeCombo078FeeRate(@NotEmpty(message = "商户sn不能为空") String mchSn);


    /**
     * 生效微信教培整改0.78费率套餐,添加日志
     * @param mchSn
     * @param opLogCreateRequest
     */
    void applyTradeCombo078FeeRateAndLog(@NotEmpty(message = "商户sn不能为空") String mchSn,OpLogCreateRequest opLogCreateRequest);



    /**
     * 取消微信教培整改0.78费率套餐
     * @param mchSn
     */
    void cancelTradeCombo078FeeRate(@NotEmpty(message = "商户sn不能为空") String mchSn);

    /**
     * 生效微信教培整改0.78费率套餐,添加日志
     * @param mchSn
     * @param opLogCreateRequest
     */

    void cancelTradeCombo078FeeRateAndLog(@NotEmpty(message = "商户sn不能为空") String mchSn, OpLogCreateRequest opLogCreateRequest);

    /**
     * 修改支付宝直连商户费率
     * @param mchSn
     * @param feeRate
     * @param operator
     * @param operatorName
     */
    void updateAlipayFormalFeeRate(@NotEmpty(message = "商户sn不能为空") String mchSn, String feeRate, String operator, String operatorName);


    /**
     * 修改费率接口, 适用于sp
     * @param request 字段参考merchant_config表
     *  merchant_id
     *  payway
     *  b2c_fee_rate
     *  b2c_status
     *  ...
     *  @param operator
     *  @param operatorName
     *  @param remark
     */
    void updateMerchantConfigStatusAndFeeRateFromSp(Map<String, Object> request, String operator, String operatorName, String remark);


    /**
     * 同步套餐相关信息到core-b
     * @param merchantSn
     * @param payWay
     * @param process 是否真正执行
     */
    void syncMerchantFeeRateTag(String merchantSn, Integer payWay, boolean process);

    /**
     * 根据主键查询
     *
     * @param id
     * @return
     */
    FeeRateResponse getMerchantFeeRateById(@Valid @NotNull(message = "id不能为空") Long id);
}

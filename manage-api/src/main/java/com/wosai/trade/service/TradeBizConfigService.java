package com.wosai.trade.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.trade.service.request.OpLogCreateRequest;
import com.wosai.trade.service.request.ProviderMchQueryRequest;
import com.wosai.trade.service.request.TonglianApplePayConfigRequest;
import com.wosai.trade.service.result.ProviderMchQueryResult;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 提供给业务方的底层配置接口
 */
@JsonRpcService(value = "rpc/trade_config")
@Validated
public interface TradeBizConfigService {
    /**
     * 风控接口  商户 category终端类型限额配置
     * 更新商户category终端类型限额配置，quota为null 则清除商户 category终端类型限额配置
     */
    Map<String, Object> updateCategoryMerchantSingleMax(@NotNull(message = "商户sn不能为空") String merchantSn,
                                                        Long quota, @NotNull(message = "终端类目不能为空") String category);


    /**
     * 风控接口  商户 category终端类型限额配置,添加日志
     * 更新商户category终端类型限额配置，quota为null 则清除商户 category终端类型限额配置
     */
    Map<String, Object> updateCategoryMerchantSingleMaxAndLog(@NotNull(message = "商户sn不能为空") String merchantSn,
                                                              Long quota, @NotNull(message = "终端类目不能为空") String category, OpLogCreateRequest opLogCreateRequest);

    /**
     * 获取原始的商户交易参数
     *
     * @param merchantId
     * @param payway
     * @return
     */
    Map getMerchantConfigByMerchantIdAndPayway(@NotNull(message = "商户id不能为空") String merchantId, Integer payway);



    Map<String, String> openUnionpay(@NotNull(message = "商户sn不能为空") String merchantSn);

    String openUnionpayFileHandle(@NotBlank(message = "文件路径不能为空")String ossPath);

    /**
     * 云闪付1000元以上交易权限打开
     * 提供外部RPC接口
     *
     * @param merchantSn 商户SN
     * @param operator   操作人
     */
    void openUnionPay(@NotBlank(message = "商户sn不能为空") String merchantSn, String operator);

    /**
     * 渠道商户信息查询
     *
     * @param request
     * @return
     */
    ProviderMchQueryResult providerMchQuery(@Valid ProviderMchQueryRequest request);


    /**
     *
     * @param request
     */
    void openTonglianApplePay(TonglianApplePayConfigRequest request);
}

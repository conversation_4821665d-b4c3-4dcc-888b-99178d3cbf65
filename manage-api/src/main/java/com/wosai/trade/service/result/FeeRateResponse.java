package com.wosai.trade.service.result;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 费率响应实体类
 *
 * <AUTHOR>
 *
 * @date 2025/8/26
 **/
@Data
public class FeeRateResponse {
    private Long id;

    private Long appId;

    private String merchantSn;

    private Long tradeComboId;

    private Integer payWay;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDate;

    private Integer b2cInUse;

    private Integer c2bInUse;

    private Integer appInUse;

    private Integer miniInUse;

    private Integer h5InUse;

    private Integer wapInUse;

    private String feeRateType;

    private Integer status;

    private String auditSn;
    /**
     * 固定费率
     */
    private String fixedFeeRate;

    private List<LadderFeeRate> ladderFeeRates;

    private List<ChannelFeeRate> channelFeeRates;

    private List<ChannelLadderFeeRate> channelLadderFeeRates;

    /**
     * 阶梯费率
     */
    @Data
    public static class LadderFeeRate {

        private Double min;

        private Double max;

        /**
         * 费率
         */
        private String feeRate;
    }

    /**
     * 资金渠道费率
     */
    @Data
    public static class ChannelFeeRate {
        /**
         * 资金渠道类型
         */
        private String type;

        /**
         * 费率，名称定义错误，新的都将上送fee_rate
         */
        @Deprecated
        private String fee;

        /**
         * 费率
         */
        private String feeRate;

        /**
         * 单笔最高
         */
        private String max;
    }

    /**
     * 资金渠道阶梯费率
     */
    @Data
    public static class ChannelLadderFeeRate {
        /**
         * 资金渠道类型
         */
        private String type;
        /**
         * 阶梯费率
         */
        private List<LadderFeeRate> ladderFeeRates;
    }
}

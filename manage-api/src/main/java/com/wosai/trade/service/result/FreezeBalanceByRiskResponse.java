package com.wosai.trade.service.result;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 风控冻结余额响应
 * @date 2025-08-07
 */
@Setter
@Getter
public class FreezeBalanceByRiskResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 冻结操作ID
     */
    private String freezeActionId;

    /**
     * 实际冻结金额（单位：分）
     */
    private long actualFreezeAmount;

    /**
     * 撤销的提现ID列表
     */
    private List<String> withdrawIds;
}
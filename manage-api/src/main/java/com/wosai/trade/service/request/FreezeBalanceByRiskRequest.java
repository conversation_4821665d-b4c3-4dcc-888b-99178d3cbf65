package com.wosai.trade.service.request;

import com.wosai.pay.common.base.log.OperationLogRequest;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 风控冻结余额请求
 * @date 2025-08-07
 */
@Setter
@Getter
public class FreezeBalanceByRiskRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 商户ID
     */
    @NotBlank(message = "商户ID不能为空")
    private String merchantId;

    /**
     * 收单机构
     * 枚举定义见：Withdraw.PROVIDER_HAIKE
     */
    @NotNull(message = "收单机构不能为空")
    private Integer clearanceProvider;

    /**
     * 余额账户类型  1：普通账户 2：外卡账户 3：分账账户
     * 枚举定义见余额服务: ProviderWalletAccountTypeEnum
     *
     */
    @NotNull(message = "余额账户类型不能为空")
    private Integer walletAccountType;

    /**
     * 冻结类型
     * 见：FrozenOrder.TYPE_RISK
     */
    @NotNull(message = "冻结类型不能为null")
    private Integer frozenType;

    /**
     * 冻结金额（单位：分）
     */
    @NotNull(message = "冻结金额不能为空")
    @Positive(message = "冻结金额必须大于0")
    private Long freezeAmount;

    /**
     * 冻结记录id
     */
    @NotBlank(message = "actionId不能为空")
    private String actionId;

    /**
     * 冻结原因
     */
    @NotBlank(message = "冻结原因不能为空")
    private String reason;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空")
    private String remark;

    /**
     * 操作日志请求参数
     */
    @NotNull(message = "logRequest不能为空")
    private OperationLogRequest logRequest;
}